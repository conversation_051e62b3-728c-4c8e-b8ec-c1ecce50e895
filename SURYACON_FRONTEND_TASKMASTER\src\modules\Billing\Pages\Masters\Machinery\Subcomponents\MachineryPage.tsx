import MasterCard from "../../../../../../components/Reusble/Billing/Masters/MasterCard";
import { DeletePopup } from "../../../../../../components/Reusble/Global/DeletePopup";
import TMMMNav from "../../../../../../components/Reusble/TMMMNav";
import { Loader } from "../../../../../../assets/loader";
import styles from "../Styles/Machinery.module.css";
import {
  checkAndDownloadImages,
  initializeDatabase,
  isValidValue,
  slicedData,
  withInternetCheck,
} from "../../../../../../functions/functions";
import { closePopup } from "../../../../../../redux/features/Modules/Reusble/popupSlice";
import { resetFormMachineryData } from "../../../../../../redux/features/Modules/TaskMaster/Slices/MastersSlice";
import {
  useAppDispatch,
  useAppSelector,
} from "../../../../../../redux/hooks/Modules/Reduxhooks/ReduxHooks";
import { useEffect, useRef, useState } from "react";
import {
  useDeleteMachineryByToolIdMutation,
  useLazyGetMachineryDesiginationDetailByIdQuery,
} from "../../../../../../redux/api/Modules/Billing/Billingapi";
import { useToast } from "../../../../../../hooks/ToastHook";
import { useParams } from "react-router-dom";
import { useNestedPouchSearch } from "../../../../../../functions/useNestedLocalSearch";
import MachineryDiscard from "./MachineryDiscard";
import { useSelector } from "react-redux";
import { RootState } from "../../../../../../redux/store";
import {
  clearFetchedMasters,
  SetCategoryId,
  setSearchData,
  setFetchedMastersDesignation,
} from "../../../../../../redux/features/Modules/Masters";
const MachineryPage = () => {
  const currentOpenPopup = useAppSelector((state) => state.popup.popups);
  const formData = useAppSelector(
    (state) => state.masterForm.formMachineryData
  );
  const data = useSelector(
    (state: RootState) => state.masterReduxSlice.fetchedData
  );
          const searchedData = useSelector((state: RootState) => state.masterReduxSlice.searchedData);

  //to detect changes in the localdb
  // const detectChanges = useAppSelector((state) => state.backupSlice.isOpen);
  const localChange = useAppSelector(
    (state) => state.backupSlice.isLocalChange
  );
  const searchKey = useAppSelector((state) => state.taskMaster.searchKey);
  const [searchLocalKey, setSearchLocalKey] = useState("");

  const [machineryCardData, setMachineryCardData] = useState<any>([]);
  const [searchmachineryCardData, setSearchMachineryCardData] = useState<any>(
    []
  );
  const [editMachineryData, setEditMachineryData] = useState<any>({});
  const [page, setPage] = useState<number>(1);
  const dispatch = useAppDispatch();
  const { machineryCategoryId } = useParams();
  const showToast = useToast();

  //on clicking the dots this will get the all details of single machine
  const [getMachineryDesignationDetails] =
    useLazyGetMachineryDesiginationDetailByIdQuery();

  //delete api
  const [deleteMachineryDesignation] = useDeleteMachineryByToolIdMutation();

  const getDatafromDb = async (p: any, id: string) => {
    const dbName = await initializeDatabase("MachinaryDesignation");
    const fetchedData = await window.electron.getDocumentByParentId({
      dbName,
      page: p,
      catId: id,
      categoryId: "machineryCategoryId",
      isDeletedNext: false,
      needSorting: true,
    });
    console.log(fetchedData, "This is the data>>>>>> ");
    console.log("check for machinery data here bro >>", fetchedData);
    if (p === 1) {
      dispatch(setFetchedMastersDesignation({ data: fetchedData, page: p }));
    } else {
      const newData = [...data, ...fetchedData];
      dispatch(setFetchedMastersDesignation({ data: newData, page: p }));
    }


    const imageDownload = withInternetCheck(() =>
      checkAndDownloadImages("MachinaryDesignation", fetchedData, dispatch)
    );
    imageDownload();
  };

  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const target = e.target as HTMLDivElement;

    if (target) {
      const { scrollHeight, clientHeight, scrollTop } = target;

      if (scrollTop + clientHeight >= scrollHeight - 1) {
        console.log("page changed", page);
        setPage((prev) => prev + 1);
      }
    }
  };

  //to download the images of the data that is fetched during the search
  useEffect(() => {
    if (searchedData?.length > 0) {
      const imageDownload = withInternetCheck(() =>
        checkAndDownloadImages(
          "MachinaryDesignation",
          searchedData,
          dispatch
        )
      );
      imageDownload();
    }
  }, [searchedData]);

  //   useEffect(() => {
  //     if(localChange){
  //  setPage(1);
  //     }

  //   }, [detectChanges]);

  useEffect(() => {
    if (page && machineryCategoryId) {
      getDatafromDb(page, machineryCategoryId);
    }
  }, [page]);

  useEffect(() => {
    setSearchLocalKey(searchKey);
  }, [searchKey]);

  useNestedPouchSearch({
    pathRecord: "MachinaryDesignation",
    searchKey: searchLocalKey,
    setData: setSearchData,
    setPage,
    key: "name",
    extraSearchParams: {
      catId: machineryCategoryId,
      categoryId: "machineryCategoryId",
      isDeletedNext: false,
    },
  });

  useEffect(() => {
    return () => {
      dispatch(clearFetchedMasters());
    };
  }, []);

  useEffect(() => {
    dispatch(SetCategoryId(machineryCategoryId as string));
  }, [machineryCategoryId]);

  const navRef = useRef<HTMLDivElement>(null);
  const mainContentRef = useRef<HTMLDivElement>(null);
  const [width, setWidth] = useState<null | number>(null);
  const clientFunction = () => {
    const mainContentWidth =
      mainContentRef?.current?.getBoundingClientRect()?.width;
    // console.log('details of card view container in useEffect details',details,);
    // setWidth(details?.width);
    console.log("inner width");
    // if (window.innerWidth < 1200) {
    navRef.current?.style.setProperty("width", `${mainContentWidth}px`);
    // }
  };
  const condition =
    mainContentRef.current &&
    mainContentRef.current.getBoundingClientRect().width < 1200;
  useEffect(() => {
    clientFunction();
    const handleResize = () => clientFunction();
    window.addEventListener("resize", handleResize);
    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, [window.innerWidth, condition]);
  console.log(data, "machinarydata");
  return (
    <>
      {/* <SummaryFields /> */}
      <div ref={navRef}>
        <TMMMNav Label={"Machinery"} TargetForm={"AddMachineryForm"} />
      </div>
      <div
        ref={mainContentRef}
        className={styles.main_content_wrapper}
        style={{ position: "relative", zIndex: 0, marginTop: "2rem" }}
      >
        <div className={styles.cardview} onScroll={(e) => handleScroll(e)}>
          <div className={`${styles.inner_cardview} ${styles.inner_cardview2}`}>
            {data && data?.length > 0 ? (
              (searchedData?.length > 0
                ? searchedData
                : data
              ).map((item: any) => (
                <MasterCard
                  variant="machinery"
                  callbackEditData={async () => {
                    const response = await getMachineryDesignationDetails({
                      machineryId: item?._id,
                    });
                    setEditMachineryData(response?.data?.data);
                    return response;
                  }}
                  editData={editMachineryData}
                  cardBlockSize="222px"
                  data={{
                    _id: item?._id,
                    title: item?.name,
                    images: item?.images?.[0],
                    items: [
                      { title: "Brands", name: item?.Brands },
                      { title: "Grades", name: item?.Grades },
                      { title: "Tools", name: item?.tools },
                      {
                        title: "Fuel",
                        name:
                          item?.Fueltype?.charAt(0).toUpperCase() +
                          item?.Fueltype?.slice(1),
                      },
                    ],
                    brandDetails: item?.BrandDetails,
                  }}
                />
              ))
            ) : (
              <div className={styles.loader_loading}>
                <img
                  src={Loader.suryaconLogo}
                  alt="Loading..."
                  className={styles.loader_loading_image}
                />
              </div>
            )}
          </div>
        </div>
      </div>

      {currentOpenPopup["deleteMachine"] && (
        <DeletePopup
          header="Are you sure you want to delete this Machine?"
          height="calc(100% - 7.25rem)"
          heightupperlimit="0"
          callbackDelete={async () => {
            await deleteMachineryDesignation({
              machineryId: formData?._id,
            }).unwrap();
            showToast({
              messageContent: `Machine Deleted Successfully!`,
              type: "success",
            });
            dispatch(closePopup("deleteMachine"));
            dispatch(resetFormMachineryData());
          }}
          onClose={() => {
            dispatch(closePopup("deleteMachine"));
            dispatch(resetFormMachineryData());
          }}
        >
          <MachineryDiscard
            formData={formData}
            // initialFormData={initialFormData}
            // formMode={formMode}
            // deletedFormData={deletedFormData}
            // deletedGradeData={deletedGradeData}
          />
        </DeletePopup>
      )}
    </>
  );
};

export default MachineryPage;
