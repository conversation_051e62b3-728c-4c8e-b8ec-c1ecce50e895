import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { FC } from "react";
import {
  FinalData,
  Task,
  taskDetailandFinalData,
} from "../../../../../api/Modules/Billing/Interfaces/BillingInterfaces";
import {
  InitialStateProp,
  TransformedRoutesProp,
} from "../../../../../Interfaces/Modules/Billing/ProjectPlanning/ProjectPlanning";
import { addOrUpdateFetchedMasters } from "../../../Masters";
import { RootState } from "../../../../../store";

const initialState: InitialStateProp = {
  planningProgress: 0,
  editMode: false,
  isEdit: false,

  Todeletedata: {
    Tasks: [],
    Subtasks: [],
    machinaries: [],
    tools: [],
    manpowers: [],
    materials: [],
  },
  deletingtask: [],
  // state to store all subtasks data here
  allSubtasksdata: [] as any[], // <-- changed from []
  // state to store the progress bar data here
  progressTowerData: null,
  // state tp stpre tjhe se;ected ;pcatopm tasl
  selectedLocationTaskId: null,
  selectedMaterialId: null,
  // state to store the selected subtaskid
  selectedLocationSubTaskId: null,
  selectedLocationId: null,
  selectedLocationTaskIdData: {},
  // state to store the current task basic details here
  currentTaskbasicDetails: {} as any,
  // state to store basic details of subtasks of selected task
  allSubtasksBasicDetails: [] as any,
  curretSelectedData: {},
  selectedprojectRate: "",
  selectedprojectestimate: "",
  towerRoutesRefreshFlag: 0,
};
const projectPlanningSlice = createSlice({
  name: "projectPlanning",
  initialState,
  reducers: {
    addProgressTowerDataByProject: (
      state,
      action: PayloadAction<Task[] | null>
    ) => {
      state.progressTowerData = action.payload;
    },
    selectLocationTaskId: (state, action: PayloadAction<string>) => {
      state.selectedLocationTaskId = action.payload;
    },
    selectLocationSubTaskId: (state, action: PayloadAction<string>) => {
      state.selectedLocationSubTaskId = action.payload;
    },
    setselectedLocationId: (state, action: PayloadAction<string>) => {
      state.selectedLocationId = action.payload;
    },
    setallSubtasksData: (state, action) => {
      console.log("subtasks data coming to state scheck here", action.payload);

      // If the payload is an empty array, clear the state
      if (Array.isArray(action.payload) && action.payload.length === 0) {
        state.allSubtasksdata = [];
        return;
      }

      // action.payload is an array of subtasks
      const incomingSubtasks = Array.isArray(action.payload)
        ? action.payload
        : [action.payload];
      const existingSubtasks = state.allSubtasksdata || [];

      // Only append new subtasks whose taskId matches the selectedLocationTaskId
      const filteredIncoming = incomingSubtasks.filter(
        (subtask: any) => subtask?.taskId === state.selectedLocationTaskId
      );

      // Create a map for quick lookup by _id
      const subtaskMap = new Map<string, any>();
      existingSubtasks.forEach((subtask: any) => {
        if (subtask && subtask._id) {
          subtaskMap.set(subtask._id, subtask);
        }
      });

      // Add or replace only filtered subtasks from payload
      filteredIncoming.forEach((subtask: any) => {
        if (subtask && subtask._id) {
          subtaskMap.set(subtask._id, subtask);
        }
      });

      // Update state with merged subtasks
      state.allSubtasksdata = Array.from(subtaskMap.values());
    },
    setSelectedMaterialId: (state, action) => {
      state.selectedMaterialId = action.payload;
    },
    selectLocationTaskIdData: (
      state,
      action: PayloadAction<taskDetailandFinalData>
    ) => {
      state.selectedLocationTaskIdData = action.payload;
    },
    setSubtaskInPlanning: (state, action) => {
      console.log("this is the action", action.payload);

      // Ensure finalData array exists
      if (Array.isArray(state.selectedLocationTaskIdData?.finalData)) {
        state.selectedLocationTaskIdData.finalData = [
          ...state.selectedLocationTaskIdData.finalData,
          action.payload,
        ];
      } else {
        state.selectedLocationTaskIdData.finalData = [action.payload];
      }

      // Remove from Todeletedata.Subtasks if it exists there
      if (Array.isArray(state.Todeletedata?.Subtasks)) {
        state.Todeletedata.Subtasks = state.Todeletedata.Subtasks.filter(
          (taskId) => taskId !== action.payload?._id
        );
      }
    },

    toggleEditMode: (state) => {
      state.editMode = !state.editMode;
    },
    setEditMode: (state, action: PayloadAction<boolean>) => {
      state.editMode = action.payload;
    },
    clearTaskData: (state, action) => {
      const { towerId, taskId } = action.payload;
      console.log(taskId, state.progressTowerData, "ths is task id");
      const splitTaskId = taskId?.split("-")[0];
      state.progressTowerData = state.progressTowerData?.filter(
        (e: any) => e._id !== splitTaskId
      );
    },

    setPlanningProgress: (state, action) => {
      state.planningProgress = action.payload;
    },

    setCurrentSelectedData: (state, action) => {
      state.curretSelectedData = action.payload;
    },
    setSubtaskBasicDetails: (state, action) => {
      const { subtasks = [], deletedSubtasks = [] } = action.payload || {};
      console.log(action.payload, "this is action .payload");
      // Remove deletedSubtasks (array) if present in state.allSubtasksBasicDetails
      if (
        Array.isArray(deletedSubtasks) &&
        Array.isArray(state.allSubtasksBasicDetails)
      ) {
        state.allSubtasksBasicDetails = state.allSubtasksBasicDetails.filter(
          (subtask: any) => !deletedSubtasks.includes(subtask._id)
        );
      }

      // Add or update subtasks in allSubtasksBasicDetails
      if (Array.isArray(subtasks)) {
        subtasks.forEach((subtask: any) => {
          const index = state.allSubtasksBasicDetails.findIndex(
            (item: any) => item._id === subtask._id
          );
          if (index !== -1) {
            state.allSubtasksBasicDetails[index] = {
              ...state.allSubtasksBasicDetails[index],
              ...subtask,
            };
          } else if (subtask?.task_id === state.selectedLocationTaskId) {
            state.allSubtasksBasicDetails.push(subtask);
          }
        });
      }

      // Remove from Todeletedata.Subtasks if it exists there
      if (
        Array.isArray(state.Todeletedata?.Subtasks) &&
        Array.isArray(subtasks)
      ) {
        const subtaskIds = subtasks.map((s: any) => s._id);
        state.Todeletedata.Subtasks = state.Todeletedata.Subtasks.filter(
          (taskId) => !subtaskIds.includes(taskId)
        );
      }
    },
    addOrUpdateTowerRoutes: (state, action) => {
      const towerRoutesInState = state.progressTowerData ?? [];
      const { tasks = [], deletedTasks = [] } = action.payload || {};

      const updatedRoutes = [...towerRoutesInState];

      // Remove deleted tasks
      if (Array.isArray(deletedTasks)) {
        deletedTasks.forEach((taskId: string) => {
          const taskIndex = updatedRoutes.findIndex(
            (t: any) => t._id === taskId
          );
          if (taskIndex !== -1) {
            updatedRoutes.splice(taskIndex, 1);
          }
        });
      }

      // Update or add tasks
      if (Array.isArray(tasks)) {
        tasks.forEach((task: any) => {
          if (task?.Tower_id === state.selectedLocationId) {
            const taskIndex = updatedRoutes.findIndex(
              (t: any) => t._id === task._id
            );
            if (taskIndex !== -1) {
              updatedRoutes[taskIndex] = {
                ...updatedRoutes[taskIndex],
                ...task,
              };
            } else {
              updatedRoutes.push(task);
            }
          }
        });
      }

      state.progressTowerData = updatedRoutes;
    },
    deleteTowerRoutes: (state, action) => {
      const towerRoutesInState = state.progressTowerData ?? [];
      const deletedTasks = Array.isArray(action.payload) ? action.payload : [];
      console.log(
        JSON.stringify(deletedTasks),
        JSON.stringify(towerRoutesInState),
        "this is data in state"
      );
      const updatedRoutes = towerRoutesInState.filter(
        (task: any) => !deletedTasks.includes(task._id)
      );

      state.progressTowerData = updatedRoutes;
      if (deletedTasks.includes(state.selectedLocationTaskId)) {
        const firstId = towerRoutesInState[0]?._id;
        if (firstId) state.selectedLocationTaskId=firstId;
      }
    },

    clearDeleteSubtaskInPlanning: (state) => {
      state.deletingtask = [];
      state.Todeletedata = {
        Tasks: [],
        Subtasks: [],
        machinaries: [],
        tools: [],
        manpowers: [],
        materials: [],
      };
    },

    deleteSubtaskInPlanning: (state, action) => {
      const { towerId, taskId, subtaskId } = action.payload;
      if (state.allSubtasksdata) {
        console.log(subtaskId, "this is subtask id to delete asdf");
        // Find the subtask to delete for Todeletedata
        const subtaskDetail = state.allSubtasksdata.find(
          (item: any) => item._id == subtaskId
        );

        console.log(subtaskDetail, subtaskId, "hi done");

        const machinaryData = (subtaskDetail?.machinaryId ?? []).map(
          (item: any) => item._id
        );
        const manpowerData = (subtaskDetail?.manpowerId ?? []).map(
          (item: any) => item._id
        );
        const materialData = (subtaskDetail?.materialId ?? []).map(
          (item: any) => item._id
        );
        const toolData = (subtaskDetail?.tool_id ?? []).map(
          (item: any) => item._id
        );

        // Remove the subtask from the correct array
        state.allSubtasksdata = state.allSubtasksdata?.filter(
          (item: any) => item?._id != subtaskId
        );
        state.allSubtasksBasicDetails = state.allSubtasksBasicDetails?.filter(
          (item: any) => item?._id !== subtaskId
        );
        state.Todeletedata = {
          Tasks: [],
          Subtasks: [...state.Todeletedata.Subtasks, subtaskId].filter(Boolean),
          machinaries: [...state.Todeletedata.machinaries, ...machinaryData],
          tools: [...state.Todeletedata.tools, ...toolData],
          manpowers: [...state.Todeletedata.manpowers, ...manpowerData],
          materials: [...state.Todeletedata.materials, ...materialData],
        };

        state.deletingtask = [taskId];

        console.log(
          JSON.parse(JSON.stringify(state.allSubtasksdata)),
          JSON.parse(JSON.stringify(machinaryData)),
          subtaskId,
          subtaskDetail,
          "this is subtask detail to delete"
        );
      }
    },
    setToDeleteData: (state, action) => {
      state.Todeletedata = action.payload;
    },
    setTowerBasicDetails: (state, action) => {
      state.currentTaskbasicDetails = action.payload;
    },
    updateTaskBasicDetail: (state, action) => {
      const taskDetailInState = {
        ...state.currentTaskbasicDetails,
      };
      console.log(
        "updateTaskBasicDetail state:",
        taskDetailInState,
        action.payload
      );
      console.log("  ", action.payload);
      if (taskDetailInState?._id == action.payload?._id) {
        state.currentTaskbasicDetails = action.payload;
      }
    },
    setDeletingTaskId: (state, action) => {
      state.deletingtask = action.payload;
    },
    updatesubtaskDetail: (state, action) => {
      // Find the index of the subtask with the same _id and taskId
      const updatedSubtasks = state.allSubtasksdata.map((subtask: any) => {
        if (
          subtask._id === action.payload._id &&
          subtask?.taskId === action.payload?.taskId
        ) {
          return { ...subtask, ...action.payload };
        }
        return subtask;
      });
      state.allSubtasksdata = updatedSubtasks;
    },
    setIsEditPlanning: (state, action: PayloadAction<boolean>) => {
      state.isEdit = action.payload;
    },

    setAllTaskBasicDetails: (state, action: PayloadAction<FinalData[]>) => {
      state.allSubtasksBasicDetails = action.payload;
    },

    addorUpdateAllsubTaskBasicDetails: (state, action) => {
      const subtasksInPayload = Array.isArray(action.payload)
        ? action.payload
        : [action.payload];

      // Update or add each subtask from the payload
      subtasksInPayload.forEach((payloadSubtask: any) => {
        const index = state.allSubtasksBasicDetails.findIndex(
          (subtask: any) => subtask._id === payloadSubtask._id
        );
        if (index !== -1) {
          // Update existing
          state.allSubtasksBasicDetails[index] = {
            ...state.allSubtasksBasicDetails[index],
            ...payloadSubtask,
          };
        } else {
          // Add new
          if (payloadSubtask?.task_id === state.selectedLocationTaskId) {
            state.allSubtasksBasicDetails.push(payloadSubtask);
          }
        }
      });
    },

    setSetProjectRate: (state, action) => {
      state.selectedprojectRate = action.payload;
    },
    setselectedprojectestimate: (state, action) => {
      state.selectedprojectestimate = action.payload;
    },
    setTaskplanningPercentage: (
      state,
      action: PayloadAction<{ taskId: string; percentage: number }>
    ) => {
      if (state.progressTowerData) {
        state.progressTowerData = state.progressTowerData.map((task: any) =>
          task._id === action.payload.taskId
            ? { ...task, progressPercentage: action.payload.percentage }
            : task
        );
      }
    },

    setTowerRoutesRefreshFlag: (state, action) => {
      state.towerRoutesRefreshFlag = action.payload;
    },
  },
});

export const {
  addProgressTowerDataByProject,
  selectLocationTaskId,
  selectLocationSubTaskId,
  selectLocationTaskIdData,
  setallSubtasksData,
  setSelectedMaterialId,
  setToDeleteData,
  setselectedLocationId,
  setSubtaskInPlanning,
  deleteSubtaskInPlanning,
  addOrUpdateTowerRoutes,
  setTowerBasicDetails,
  setAllTaskBasicDetails,
  toggleEditMode,
  setEditMode,
  setCurrentSelectedData,
  clearTaskData,
  setSetProjectRate,
  setselectedprojectestimate,
  setTaskplanningPercentage,
  setTowerRoutesRefreshFlag,
  updateTaskBasicDetail,
  setIsEditPlanning,
  setPlanningProgress,
  updatesubtaskDetail,
  addorUpdateAllsubTaskBasicDetails,
  clearDeleteSubtaskInPlanning,
  setSubtaskBasicDetails,
  setDeletingTaskId,
  deleteTowerRoutes,
} = projectPlanningSlice.actions;

export default projectPlanningSlice.reducer;

export const selectIsApprovalEnabled = (state: RootState) => {
  const allTowerRoutes = state.projectPlanning.progressTowerData || [];
  return (
    allTowerRoutes.length > 0 &&
    allTowerRoutes.every((step: any) => step.progressPercentage === 100)
  );
};
