.reported_container {
  inline-size: 95%;
  /* margin-block-start: 15px; */
  border: 1px solid #00000047;
  border-radius: 24px;
  position: relative;
  max-width: 358px;
  min-width: 280px;
  margin-top: 0.5rem;
  margin-left: 1.5rem;
}

.reported_container_deleteIcon {
  background: none;
  border: none;
  display: grid;
  align-items: center;
  cursor: pointer;
  position: absolute;
  inset-inline-end: 5px;
  inset-block-start: -10px;
  background-color: var(--secondary-warning-color);
  padding: 0.2rem 0.2rem;
  border-radius: 50%;
}

.reported_containerList {
  padding: 1rem 0.5rem 0.5rem 1.5rem;
}

.reported_containerList .reported_containerItem {
  display: flex;
  position: relative;
}

.reported_containerItem::before {
  background: var(--text-black-60);
  content: "";
  height: 100%;
  left: 12px;
  position: absolute;
  top: 6px;
  width: 2px;
  margin-block: 9px;
}

.reported_containerItem:nth-last-child(3)::before {
  height: 100%;
}

.reported_containerLastItem:nth-last-child(2)::before {
  height: 0%;
}

.reported_containerList .reported_containerItem .timeline_content {
  flex: 1 1 auto;
  padding: 0 0 1rem 1.5rem;
  position: relative;
}

.timeline_icon {
  background-color: var(--text-black-60);
  width: 1rem;
  height: 1rem;
  border-radius: 50%;
  margin: 5px;
  position: absolute;
  position: relative;
  margin-top: 0.5rem;
  flex-shrink: 0;
}

.reporting_level_delete {
  background-color: var(--main_background);
  width: 1rem;
  height: 1rem;
  /* margin: 6px; */

  position: relative;
  left: 1px;
}

.reporting_level_deleteIcon {
  background-color: var(--main_background);
  border: none;
  height: 16px;
  /* padding: 1px; */
}

.timeline_addIcon {
  cursor: pointer;
  display: inline-block;
  width: 20px;
  height: 20px;
  background: url("../../../assets/icons/add_Icon.svg") no-repeat center;
  background-size: contain;
  margin-top: 0.5rem;
  flex-shrink: 0;
  z-index: 0;
  position: relative;
}

.sideicons {
  padding: 0.3rem 1rem;
}

.reported_level_box {
  display: flex;
  flex-direction: column;
  inline-size: 100%;
  gap: 0.8rem;
  align-items: start;
  inline-size: 100%;
  justify-content: start;
}

.reported_level_subBox {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.level_role {
  min-inline-size: 150px;
  background-color: var(--main_background);
  border-radius: 20px;
  box-shadow: var(--extra-shdow-four);
  padding-inline: 1rem;
  padding-block: 0.5rem;
  text-align: center;
  /* color: var(--text-black-60); */
  display: flex;
  align-items: center;
  gap: 0.5rem;
  position: relative;
}

.addbtnlevelpopup {
  background: none;
  border: none;
  display: grid;
  align-items: center;
  cursor: pointer;
}

.reporting_edit {
  cursor: not-allowed;
  pointer-events: none;
  z-index: 0;
  /* filter: brightness(0) saturate(100%) invert(64%) sepia(8%) saturate(21%) hue-rotate(4deg) brightness(92%) contrast(102%); */
  margin-block-start: 0;
}

.transition_div {
  transition: cubic-bezier(0.075, 0.82, 0.165, 1);
  transition-duration: 500ms;
}

.card_title {
  margin-bottom: 0.5rem;
}
