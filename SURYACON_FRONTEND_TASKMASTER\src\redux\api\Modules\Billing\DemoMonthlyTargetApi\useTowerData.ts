import { useState, useEffect } from 'react';
import { TowerApiResponse } from './TowerTypes';

const API_URL = 'http://localhost:3001';

export const useTowerData = (projectId?: string) => {
  const [data, setData] = useState<TowerApiResponse | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  const fetchData = async () => {
    try {
      setLoading(true);
      const response = await fetch(API_URL);
      
      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }
      
      const jsonData = await response.json();
      setData(jsonData);
      setError(null);
    } catch (err) {
      setError('Failed to fetch tower data');
      console.error('Error fetching tower data:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (projectId) {
      fetchData();
    } else {
      setError('No project ID provided');
      setLoading(false);
    }
  }, [projectId]);

  const refetch = () => {
    fetchData();
  };

  return { data, loading, error, refetch };
};