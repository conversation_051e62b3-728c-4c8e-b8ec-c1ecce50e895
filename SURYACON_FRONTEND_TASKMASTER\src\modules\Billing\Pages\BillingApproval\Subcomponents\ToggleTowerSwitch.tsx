import React from "react";
import styles from "../Styles/ToggleTowerSwitch.module.css";

interface ToggleTowerSwitchProps {
  leftLabel: React.ReactNode;
  rightLabel: React.ReactNode;
  onToggle?: (isLeft: boolean) => void;
  width?: string;
  id: string;
  leftbubbleValue?: string;
  rightbubbleValue?: string;
  bubbleTextTagName?: "p";
  bubbletextClassName?: string;
  targetBubbleUncheckedClassName?: string;
  toggleClassName?: string;
}

export const ToggleTowerSwitch: React.FC<ToggleTowerSwitchProps> = ({
  leftLabel,
  rightLabel,
  onToggle,
  width,
  id,
  bubbleTextTagName = "p",
  leftbubbleValue,
  rightbubbleValue,
  bubbletextClassName,
  targetBubbleUncheckedClassName = "targetBubbleUnchecked",
  toggleClassName,
}) => {
  const handleToggle = (event: React.ChangeEvent<HTMLInputElement>) => {
    const isRightSelected = event.target.checked;
    setisleftSelected((prev) => !prev);
    console.log(isRightSelected ? "Right side selected" : "Left side selected");
    onToggle && onToggle(!isRightSelected);
  };
  const [isleftSelected, setisleftSelected] = React.useState(true);
  return (
    <div
      className={`${styles.toggle_main} ${
        toggleClassName ? styles[toggleClassName] : ""
      }`}
    >
      <input
        type="checkbox"
        id={id}
        className={styles.toggleCheckbox}
        onChange={handleToggle}
      />
      <label
        htmlFor={id}
        className={`${styles.toggleContainer} ${
          toggleClassName ? styles[toggleClassName] : ""
        }`}
        style={{ width }}
      >
        <div className="toggle_label">
          {leftLabel}
          {leftbubbleValue && (
            <div
              className={`${
                isleftSelected
                  ? styles.targetBubblechecked
                  : styles[targetBubbleUncheckedClassName] ||
                    targetBubbleUncheckedClassName
              }`}
            >
              {React.createElement(
                bubbleTextTagName,
                {
                  className: bubbletextClassName
                    ? styles?.[bubbletextClassName] || bubbletextClassName
                    : "",
                },
                leftbubbleValue
              )}
            </div>
          )}
        </div>
        <div className="toggle_label">
          {rightLabel}
          {rightbubbleValue && (
            <div
              className={`${
                isleftSelected
                  ? targetBubbleUncheckedClassName
                    ? styles?.[targetBubbleUncheckedClassName] ||
                      targetBubbleUncheckedClassName
                    : ""
                  : styles.targetBubblechecked
              }`}
            >
              {React.createElement(
                bubbleTextTagName,
                {
                  className: bubbletextClassName
                    ? styles?.[bubbletextClassName] || bubbletextClassName
                    : "",
                },
                rightbubbleValue
              )}
            </div>
          )}
        </div>
      </label>
    </div>
  );
};
