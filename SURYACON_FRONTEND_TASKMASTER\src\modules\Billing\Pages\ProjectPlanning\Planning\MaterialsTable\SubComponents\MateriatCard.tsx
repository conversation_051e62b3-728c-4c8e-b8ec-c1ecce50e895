import React from 'react';
import styles from '../Styles/MaterialsTable.module.css';
import Circle from '../../../../../../../components/Reusble/Billing/Circle';

const MateriatCard: React.FC<{ data: any }> = ({ data }) => {
    return (
        <div className={styles.material_card_container}>
            <div className={styles.material_card_container_header}>
                <h4>{data?.subtaskName || "No Subtask Selected"}</h4>
            </div>
            <div className={styles.material_card_container_content}>
                <div className={styles.material_card_container_content_left}>
                    <Circle
                        nameclass="material_card_container_content_box"
                        content={<p className="small_text_p">{data?.unit[0] || "No Unit"}</p>}
                    />
                </div>
                <div className={styles.material_card_container_content_right}>
                    <h4>{data?.name || "No Material Selected"}</h4>
                    <h4>{data?.quantity || 0}</h4>
                </div>
            </div>
        </div>
    );
};

export default MateriatCard;