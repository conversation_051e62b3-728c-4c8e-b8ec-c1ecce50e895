import { FC, useEffect, useState } from "react";
import styles from "../styles/TriggerEvent.module.css";

import { useDispatch, useSelector } from "react-redux";

import { DropDownArrowUpIcon, DropDownCategoryIcon } from "../../../../../assets/icons";
import UnitPopup from "../../../../../components/Reusble/Global/UnitPopup";
import { TEPopupCompProp } from "../../../../../interfaces/Modules/TaskMaster/TaskMasterInterface/TaskMaster";
import { RootState } from "../../../../../redux/store";
import FloatingLabelInput from "../../../../../components/Reusble/Global/FloatingLabel";

const   TEPopupComp: FC<TEPopupCompProp> = ({
  title,
  label,
  error,
  data,
  onSelectCallback,
  mode,
  selectedItemFromTop,
}) => {
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const isOnlyResponse = useSelector((state: RootState) => state.triggerEvent.isOnlyResponse)
  const [searchKey, setSearchKey] = useState<string | null>(null);

  const [selectedItem, setSelectedItem] = useState<{
    id: number | string;
    name: string;
    isFirst?: boolean;
  } | null>({
    id: selectedItemFromTop?._id ?? "",
    name: selectedItemFromTop?.name ?? "",
  });

  const dropdownHandler = () => {
    setSearchKey(null)
    setIsOpen((prev) => !prev);
  };

  const handleSelect = (item: { id: number | string; label: string }) => {
    setSearchKey(null)
      setSelectedItem({
        id: item.id,
        name: item.label,
      });
      onSelectCallback({
        id: item.id,
        name: item.label,
      });
    setIsOpen(false);
    
  };

  useEffect(() => {
    if (selectedItemFromTop) {
      setSelectedItem({
        id: selectedItemFromTop?._id ?? "",
        name: selectedItemFromTop?.name ?? ""
      });
    }
  }, [selectedItemFromTop]);

  console.log("this is selected item", selectedItem)

  return (
    <div
      className={`${styles.mt_popup_component} ${
        error ? `${styles.error}` : ""
      } ${isOnlyResponse ? styles.disable_field : ""}`}
    >
        <FloatingLabelInput
          id="search_key"
          placeholder="Subtasks"
          error={error ?? ""}
          Icon={ isOpen
                    ? DropDownArrowUpIcon
                    : DropDownCategoryIcon}
          iconClick={dropdownHandler}
          value={searchKey != null ? searchKey : selectedItem?.name ?? ""}
          onInputChange={(value) => {
            if(value == ""){
              setIsOpen(false)
              setSearchKey(value)
              return;
            }
            setIsOpen(true)
            setSearchKey(value)
          }}
        />

        {isOpen && (
          <div
            className={`${styles.unit_popup_mt_container} ${
              isOpen ? `${styles.selected}` : "notSelected"
            }`}
          >
            <UnitPopup
              property={"unit_popup_class"}
              data={(searchKey 
                ? data.filter((item) => {
                  const safePattern = searchKey.replace(/[-\/\\^$*+?.()|[\]{}]/g, "\\$&");
                  return new RegExp(safePattern, "i").test(item.name)}) 
                : data
              )?.map((item) => {
                return {
                  id: item.id,
                  label: item.name,
                };
              }) || []}
              onSelect={handleSelect}
              selectedId={selectedItem?.id || null}
            />
          </div>
        )}
      {isOnlyResponse && <div className={styles.action_time_interval_overlay}></div>}
    </div>
  );
};

export default TEPopupComp;
