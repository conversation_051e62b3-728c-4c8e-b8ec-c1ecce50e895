import { ChangedTriggeredDataType } from "../../../../redux/features/TaskMaster/Slices/TriggerEventSlice";
import { ButtonProps } from "../../../../resublecomponents/Button";

export interface MTSubTaskDataProps {
  _id: string;
  location: string;
  floor: number;
  taskDescription: string;
  startDate: string;
  endDate: string;
  area: number;
  Budget: number;
  subtask: string[];
  drawing: string[];
  remarks: string;
  reason: string;
  decline: boolean;
}
export interface MTTaskDataProps {
  _id: string;
  tower: string;
  floor: string[];
  towerDescription: string;
  startDate: string;
  endDate: string;
  area: number;
  Budget: number;
  drawing: string[];
  remarks: string;
  reason: string;
  decline: boolean;
}

export interface MTSubTaskModifiedField<T> {
  value: T;
  className: string;
}

export interface MTSubTaskModifiedDataProps {
  _id: string;
  location: MTSubTaskModifiedField<string>;
  floor: MTSubTaskModifiedField<number>;
  taskDescription: MTSubTaskModifiedField<string>;
  startDate: MTSubTaskModifiedField<string>;
  endDate: MTSubTaskModifiedField<string>;
  area: MTSubTaskModifiedField<number>;
  Budget: MTSubTaskModifiedField<number>;
  subtask: MTSubTaskModifiedField<string[]>;
  drawing: MTSubTaskModifiedField<string[]>;
  remarks: MTSubTaskModifiedField<string>;
  reason: MTSubTaskModifiedField<string>;
  decline: MTSubTaskModifiedField<boolean>;
}
export interface MTTaskModifiedDataProps {
  _id: string;
  location: MTSubTaskModifiedField<string>;
  floor: MTSubTaskModifiedField<string[]>;
  towerDescription: MTSubTaskModifiedField<string>;
  startDate: MTSubTaskModifiedField<string>;
  endDate: MTSubTaskModifiedField<string>;
  area: MTSubTaskModifiedField<number>;
  Budget: MTSubTaskModifiedField<number>;
  drawing: MTSubTaskModifiedField<string[]>;
  remarks: MTSubTaskModifiedField<string>;
  reason: MTSubTaskModifiedField<string>;
  decline: MTSubTaskModifiedField<boolean>;
}

export type MTSubTaskApprovalCardProps = {
  cardData?: MTDataProps;
};

export interface MTSubTaskConfirmationDilalogueProps {
  header: string;
  headerColor: string;
  data?:
    | MTSubTaskModifiedDataProps
    | MTTaskModifiedDataProps
    | ChangedTriggeredDataType;
  inputDecline?: boolean;
  button1Content?: string;
  button1Type?: ButtonProps.type;
  button2Content?: string;
  button2Type?: ButtonProps.type;
  callBack1?: () => void;
  callBack2?: () => void;
  callBackCross: () => void;
}
export interface MTSubTaskConfirmationDeclineRequestProps {
  onClose: () => void;
}

export interface MTTargetCardProps {
  toggleEditFunc?: () => void;
  onInputchange?: (e: any) => void;
  _id: string;
  name: string;
  brand?: string;
  isAllowed?: boolean;
  property?: string;
  edit: boolean;
  type?: string;
  quantity?: number | string;
  onDelete?: () => void;
  towerName: string;
  floorNumber: number;
  taskIndex: number;
  isTower: boolean;
  showDelete?: boolean;
  unit?: string;
}

export type MTCSubCardProps = {
  icon?: React.ComponentType;
  iconBg?: string;
  property?: string;
  keyProp?: string | null;
  valueProp?: string | number | string[] | null;
  displayType?: string;
  valueClassName?: string;
  displayChildType?: string;
};

export type MTTASubCardProp = {
  icon: React.ComponentType;
  property?: string;
  valueClassName?: string;
  keyProp?: string | null;
  valueProp?: string | number | string[] | null;
};
