/*  AUTHOR NAME : CHARVI */
.tasknav_conatiner {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 18rem;

}

.CategoryView_Navlinks {
  display: flex;
  width: fit-content;
  gap: 0.3rem;
  align-items: center;
}

.CategoryView_DesignArrow_Rotate {
  transform: rotate(-90deg);
  display: flex;
  align-items: center;
  width: fit-content;
}

.leftbtn {
  border: 1px solid var(--primary_color);
  width: 5.1rem;
  height: 2rem;
  margin: 0.7rem;
  color: var(--primary_color);
  border-radius: 3.125rem;
  background-color: white;
}

.tasknav_rightbtns {
  display: flex;
  gap: 0.938rem;
  position: relative;
}

.taskdltbtn,
.taskdexportbtn {
  /* width: 7.1rem; */
  height: 2.6rem;
  padding: 1rem 1rem;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
  border-radius: 6.25rem;
  cursor: pointer;
  background-color: var(--main_background);
  box-shadow: 0px 0px 4px 0px #00000066;
  border: none;
  color: var(--text-black-60);
}

.taskaddcategorybtn {
  min-width: 7.1rem;
  height: 2.6rem;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
  border-radius: 6.25rem;
  cursor: pointer;
  background-color: var(--primary_color);
  color: var(--text-white-100);
  border: none;

  padding: 1rem;
}

.locationbtncorner {
  position: fixed;
  right: -3.5rem;
  width: 6.563rem;
  height: 3.25rem;
  text-align: left;
  padding-left: 1rem;
  background-color: var(--main_background);
  box-shadow: 0px 0px 4px 0px #00000066;

  border: none;
  border-radius: 1.75rem;
  cursor: pointer;
  z-index: 2;
}

.location_header {
  position: sticky;
  top: 0;
  z-index: 10;
  margin-right: 0.625rem;
}
.three_toggle_tower_swich {
  padding: 0.2rem;
  border-radius: 5rem;
  box-shadow: 0px 0px 4px 0px #91a1a1bf;

  backdrop-filter: blur(100px);
}
.location_header_left_container {
  display: flex;
  gap: 1rem;
}

.summaryDivData {
  display: flex;
  align-items: center;
}

.summaryDataContent {
  display: flex;
  flex-direction: column;
  background: var(--main_background);
  border-radius: 0.75rem;
  width: 30.8rem;
  min-height: 3rem;
  padding: 1rem;
  white-space: normal;
  margin: 0.6rem;
  line-height: 1.363rem;
  text-align: left;
}

.summaryItem {
  display: flex;
}
.summaryItems {
  display: flex;
  gap: 1rem;
  flex-wrap: "wrap";
}
/* styles for delete project summary end by Rattandeep singh */
