/* .projectPlanning_container {
  width: 100% !important;
  display: flex;
  border-radius: 3rem;
  background-color: yellow;
} */

/* .project_planning_outer_container {
  padding: 0rem;
  display: flex;
  background-color: yellow;
} */

/* .planningTable_container {
  flex: 1;
  min-width: 80.6rem;
  height: 85vh;
  max-height: 77vh;
 
  box-shadow: 0px 0px 3px 0px #00000080;
  background-color: orange;
} */

/* .subtaskcard_container {
  min-height: 50px;
  transition: opacity 0.2s ease-in-out;
}

.subtaskcard_tooltip,
.subtaskcard_tooltip_expand {
  transition: opacity 0.2s ease-in-out;
} */
 /* .estimateBadge {
  width: 15rem;
  height: 40px;
  background: var(--secondary_color);
  color: white;
  border-top-left-radius: 100px;
  border-bottom-left-radius: 100px;
  border-top-right-radius: 24px;
  border-bottom-right-radius: 24px;
  padding: 8px 12px;
  font-weight: 700;
  font-size: 1.15rem;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px #0001;
  position: absolute;
  right: -1.5rem;
  bottom: 1.5rem;
  z-index: 2;
  opacity: 1;
  margin-bottom: -4.5rem;
} */
.estimateBadgeCustom {
  width: fit-content;
  min-width: 9rem;
  height: 40px;
  background: var(--secondary_color);
   color: white;
  border-top-left-radius: 100px;
  border-bottom-left-radius: 100px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  padding: 8px 12px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  position: absolute;
  right: 1.5rem;
  bottom: 1.5rem;
  z-index: 99;
  box-shadow: none;
  letter-spacing: 1px;
  border: none;
  margin-bottom: 0;
  overflow: hidden;
  line-height: 1;
  margin-bottom: -4.5rem;
  margin-right: -3rem;
  
}

.estimateBadgeCustom span {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  width: 100%;
  font-size: 1rem;
  font-weight: 400;
  white-space: nowrap;
}

.planningtable_headereditbtn {
   position: relative;
  display: flex;
  flex-direction: column;
  min-width: 10rem;
  margin-bottom: 2.5rem;
}
.taskcreation_line_container {
  width: 100%;
  height: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 5px;
}

.dottedline_wrapper {
  width: 49%;
  border-bottom: 1px dashed var(--line-color);
}

.redCrossStyle{
    height: 36px;
  width: 36px;
  border-radius: 50%;
  background: var(--main_background);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 5;
  cursor: pointer;
  backdrop-filter: blur(100px);

  box-shadow: 0px 0px 6px 0px #91a1a199;
}
.loading_state {
  min-height: 50px;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
#scroll-container {
  height: 75vh;
  overflow-y: auto;
  position: relative;
  scroll-behavior: smooth;
}

.loader_loading {
  position: absolute;
  top: 63%;
  left: 64%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.loader_loading_image {
  width: 500px;
  height: 500px;
}

@media (min-width: 1201px)  {
  .loader_loading_image {
    width: 500px;
    height: 500px;
  }
}

.skeleton_box {
  display: inline-block;
  background: linear-gradient(90deg, #005968 0%, #9fd7e1 50%, #005968 100%);
  background-size: 200% 100%;
  border-radius: 7px;
  vertical-align: middle;
  margin-bottom: 10px;
  background-position: 100% 0;
  animation: skeleton-wave-rtl 1.5s linear infinite;
}
@keyframes skeleton-wave-rtl {
  0% {
    background-position: 100% 0;
  }
  100% {
    background-position: -100% 0;
  }
}

.planningTableContainer {
  width: 100%;
  border-radius: 36px;
  box-shadow: 0px 0px 3px 0px #00000080;
  height: 76vh;
  display: flex;
  flex-direction: column;
  overflow-y: hidden;
}
@media screen and (max-width: 1200px) {
  .planningTableContainer {
    width: 1200px;
  overflow-y: hidden;
  
  }
}
@media screen and (max-height: 900px) {
  .planningTableContainer {
  overflow-y: hidden;
   
    height: 72vh;
  }
  .target_details_lower_outer_container{
    height: 50vh;
  }
  .subtaskcard_container {
  min-height: 300px !important;
    max-height: 330px !important;
  }
}

.mt_card_input {
  border: none;
  outline: none;
}

.summaryDivData {
  display: flex;
  align-items: center;
}

.summaryDataContent {
  display: flex;
  flex-direction: column;
  background: var(--main_background);
  border-radius: 0.75rem;
  width: 30.8rem;
  /* max-width: 28.5rem; */
  min-height: 3rem;
  padding: 1rem;
  white-space: normal;
  margin: 0.6rem;
  /* gap: 0.2rem; */
  line-height: 1.363rem;
  text-align: left;
}

.summaryDataContent_weightage {
  display: flex;

  background: var(--main_background);
  border-radius: 0.75rem;
  width: 30.8rem;
  /* max-width: 28.5rem; */
  min-height: 3rem;
  padding: 1rem;
  word-break: break-all;
  overflow-wrap: break-word;
  white-space: normal;
  margin: 0.6rem;
  line-height: 1.363rem;
  justify-content: space-between;
  align-items: center;
}

.flexContainer {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  width: 100%;
  max-width: 100%;
}

.flexContainer > .summaryDivData {
  flex: 1;
  min-width: calc(50% - 0.5rem);
  box-sizing: border-box;
}
.materialtable_header {
  min-height: 8.56rem;
  width: 100%;
  background-color: var(--primary_color);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  gap: 1rem;
  border-top-left-radius: 36px;
  border-top-right-radius: 36px;
  position: relative;
  overflow: hidden;
}
.planningtable_header {
  min-height: 8.56rem;
  width: 100%;
  background-color: var(--primary_color);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  gap: 1rem;
  border-top-left-radius: 36px;
  border-top-right-radius: 36px;
   position: relative; /* ADD THIS */
  overflow: hidden; 
}
@media screen and (max-width: 1200px) {
  .planningtable_header {
    width: 1200px;
  }
}
.planningtable_headerUnitsection {
  flex: 4;
  min-width: 20rem;
  display: flex;
  margin-top: 1rem;
  gap: 1rem;
  align-items: center;
}
@media screen and (max-width: 1460px) and (min-width: 1200px) {
  .planningtable_headerUnitsection {
    flex-wrap: wrap;
    justify-content: flex-start;
    height: auto;
    min-height: 6rem;
  }
  
  .planningtable_header_rightdivs {
    margin-bottom: 0.5rem;
    margin-right: 0.5rem;
  }
  .planningtable_header {
    height: auto;
    min-height: 15rem;
  }
}

@media screen and (max-width: 1200px) {
  .planningtable_header {
    width: 1200px;
    height: auto;
    min-height: 7.56rem; /* Return to original height */
  }
  
  .planningtable_headerUnitsection {
    flex-wrap: wrap;
    justify-content: flex-start;
    min-height: initial; 
    height: auto; 
    margin-top: 1rem; 
  }
  
  .planningtable_header_rightdivs {
    margin-bottom: 0.5rem;
    margin-right: 0.5rem;
  }
}
.planningtable_header_inner_container_text_container{
display: flex;
justify-content: space-between;


}

.planningtable_header_inner_container {
  align-self: start;
}
.planningtable_header_inner_container_text {
  color: white;
}
.planning_table_header_material_text {
  color: white;
}
/* .planningtable_headereditbtn {

  display: flex;
  flex-direction: column;
  min-width: 10rem;
  gap: 2.3rem
} */

.planningtable_header_rightdivs {
  height: 3rem;
  display: flex;
  align-items: center;
  gap: 0.8rem;
  background: var(--main_background);
  padding: 0.5rem 1rem;
  border-radius: 12px;
  box-shadow: 0px 0px 3px 0px #91a1a180;
  border: 1px solid
    linear-gradient(
      130.72deg,
      rgba(237, 231, 231, 0.07) -16.06%,
      rgba(251, 251, 251, 0.05) 82.03%
    );
}

.planning_line_container {
  width: 100%;
  width: 100%;

  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 5px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 5px;
  overflow: hidden;
}

.dottedline_wrapper {
  width: 49%;
  border-bottom: 1px dashed var(--line-color);
  overflow: hidden;
}

.groundworksection_container {
  min-height: 9vh;
  width: 90%;
  margin: 1.5rem;
}

.groundworksection_header {
  display: flex;
  gap: 0.6rem;
  align-items: center;
}

.mastersection_container {
  min-height: 10vh;
  width: 90%;
  margin: 1.5rem;
}

.mastersection_rows {
  display: flex;
  gap: 0.8rem;
  min-height: 6vh;
  align-items: center;
}

.cementsection_container,
.plastersection_container,
.craftmenshipsection_container,
.othersection_container {
  min-height: 5vh;
  width: 90%;
  margin: 1rem;
}

.cementsection_header,
.craftmenshipsection_header,
.plastersection_header,
.othersection_header {
  display: flex;
  gap: 0.6rem;
  align-items: center;
}
/* Styles for targetDetails in project planning by rattandeep singh */
.target_details_lower_outer_container {
  justify-content: space-between;
  display: flex;
  scrollbar-width: none;
  /* overflow-x: auto; */
  /* overflow-y: scroll;  */
  max-height: 56vh;
}
@media screen and (max-width: 1200px) {
  .target_details_lower_outer_container {
    width: 1200px;
  }
}

.mt_card_outer_container {
  /* margin-bottom: 1rem;
  margin-top: 2rem; */
  padding: 1.5rem;
}

.edit_pencil_container {
  /* justify-self: end; */
  /* position: absolute; */
  /* top: 5rem;
  right: 3.5rem;
  display: flex;
  width: 2.375rem;
  height: 2.375rem;
  justify-content: center;
  align-items: center;
  background-color: var(--main_background);
  backdrop-filter: blur(100px);
  box-shadow: var(--primary-mt-box-shadow);
  width: 2.375rem;
  height: 2.375rem;
  border-radius: 100%;
  padding: 0.25rem;
  cursor: pointer; */

  /* top: 2rem;
  padding: 0.25rem;
  right: 3.5rem; */

 

  background-color: var(--main_background);
  width: 36px;
  height: 36px;
  border-radius: 100px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0.5rem;
  align-self: end;
/* Move up, adjust value as needed */
  cursor: pointer;
}

.planning_header_badge_container {
  justify-self: end;
  margin-left: 3.5rem;
}

/* Aayush Malviya Subtask Card container start */
.subtaskcard_container_outer {
  overflow: hidden;
  height: 450px;
  width: 260px;
  background-color: var(--main_background);
  backdrop-filter: blur(40px);
  box-shadow: var(--extra-shdow-third);
  border-radius: 25px;
}



.subtaskcard_container {
  height: 445px;
  display: flex;
  align-items: center;
  flex-direction: column;
  overflow: scroll;
  top: 35%;
  right: 2.3%; 
}

.subtaskcard_container::-webkit-scrollbar {
  width: 3px;

}

.subtaskcard_container::-webkit-scrollbar-track {
  background: transparent;
}

.subtaskcard_container::-webkit-scrollbar-thumb {
  border-radius: 10px;
}

.subtaskcard_container:hover::-webkit-scrollbar-thumb {
  background-color: #006d77;
}

@media screen and (max-height: 900px) {
 .subtaskcard_container_outer {
  height: 388px;
}
}

@media screen and (max-height: 880px) {
 .subtaskcard_container_outer {
  height: 310px;
}
}
@media screen and (max-width: 1200px) {
  .subtaskcard_container {
    top: 45%;
    right: 4%;
  }
}

.subtaskcard_parent_container {
  margin-right: 1rem;
  position: absolute;
  /* top: 0px; */
  z-index: 99;
  right: 26px;
}
@media screen and (max-width: 1400px) {
  .subtaskcard_parent_container {
    right: 0;
  }
}
@media screen and (max-width: 1200px) {
  .subtaskcard_parent_container {
    position: static; 
   
  }
}
.subtaskcard_tooltip {
  width: 230px;
  min-height: 50px;
  height: 50px;
  background-color: var(--main_background);
  box-shadow: var(--extra-shdow-four);
  backdrop-filter: blur(40px);
  border-radius: 100px;
  margin: 0.5rem 0rem;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 0 1rem;
  cursor: url("../../../../../../assets/icons/cursor.png"), auto;
  /* transition: border-radius 0.5s ease-in-out; */
  /* animation: 0.3s expandAnimate2 forwards; */
}

.subtaskcard_tooltip h4 {
  color: var(--text-black-60);
}
.subtaskcard_tooltip_expand {
  width: 230px;
  height: 100px;
  background-color: var(--main_background);
  box-shadow: var(--extra-shdow-four);
  backdrop-filter: blur(40px);
  border-radius: 12px;
  border: 1px solid var(--primary_color);
  margin: 0.5rem 0rem;
  cursor: pointer;
  position: relative;
  padding: 0.5rem 1rem;
  animation: 0.2s expandAnimate forwards;
  /* overflow: hidden; */
  /* transition: border-radius 0.5s ease-in-out; */
}

@keyframes expandAnimate {
  0% {
    height: 50px;
    opacity: 0;
  }
  30% {
    height: 50px;
    opacity: 1;
  }
  100% {
    height: 100px;
    opacity: 1;
  }
}
@keyframes expandAnimate2 {
  0% {
    height: 100px;
    border-radius: 0px;
  }
  80% {
    border-radius: 30px;
  }
  100% {
    height: 50px;
    border-radius: 100px;
  }
}

.subtask_tooltip_uppper {
  padding-top: 0.3rem;
  height: 30%;
  width: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.subtask_tooltip_uppper h4 {
  color: var(--text-black-87);
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.subtask_tooltip_bottom {
  height: 60%;
  width: 100%;
  margin-top: 0.8rem;
  display: flex;
  transition: opacity 0.5s;
}

.subtask_tooltip_bottom_left {
  width: 50%;
}

.subtask_tooltip_bottom_right {
  width: 50%;
}

.bottom_left_content {
  display: flex;
  align-items: center;
  gap: 0.3rem;
}

.bottom_left_text p:nth-child(1) {
  color: var(--text-black-87);
}

.bottom_left_text p:nth-child(2) {
  color: var(--text-black-28);
}

.plannig_add_tooltip_subcontainer {
  padding: 0;
}
/* Aayush Malviya Subtask Card container end */
.planningtable_header_material_inner_container {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  align-items: center !important;
}
.mt_cards_container {
  display: flex;
  gap: 2rem;
  max-width: 50rem;
  flex-wrap: wrap;
  margin-top: 1rem;
}
.Subtask_target_badge_containers {
  display: flex;
  gap: 1rem;
  padding: 1.5rem;
  align-items: center;
}
.subtask_card_container {
  height: 70px;
}
.monthly_target_cards {
  overflow-y: scroll;
  max-height: 60vh;
  width: 100%;
}

@media screen and (max-width: 1650px) {
 .mt_cards_container  {
    max-width: 40rem;
  }
}
@media screen and (max-width: 1445px) {
 .mt_cards_container  {
    max-width: 32rem;
  }
}
@media screen and (max-width: 1580px) {
 .mt_cards_container  {
    max-width: 35rem;
  }
}
@media screen and (max-width: 1380px) {
 .mt_cards_container  {
    max-width: 30rem;
  }
}

@media screen and (max-width: 1400px) {
  .monthly_target_cards {
    position: relative;
  }
}
/* Custom scrollbar */
.monthly_target_cards::-webkit-scrollbar {
  width: 4px;
}

.monthly_target_cards::-webkit-scrollbar-track {
  background: transparent;
}

.monthly_target_cards::-webkit-scrollbar-thumb {
  border-radius: 4px;
}
.monthly_target_cards:hover::-webkit-scrollbar-thumb {

   background-color: #006d77;

}




.targetBadge_icon {
  height: 38px;
  width: 38px;
  background-color: var(--main_background);
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 100%;
}

@media (max-width: 1600px) {
  .planningtable_header_rightdivs {
    font-size: 0.75rem; /* Smaller text */
    padding: 0.3rem 0.6rem; /* Tighter padding */
    gap: 0.4rem; /* Less gap */
    height: 2.4rem; /* Shorter height */
  }

  .planningtable_headerUnitsection {
    gap: 0.5rem;
    margin-top: 0.6rem; 
  }

  .Subtask_target_badge_containers {
    gap: 0.5rem;
    padding: 0.5rem;
    align-items: center;
  }
}

/* .monthly_target_cards::-webkit-scrollbar {
  width: 4px;
}

.monthly_target_cards::-webkit-scrollbar-track {
  background-color: transparent;
  border-radius: 10px;
}

.monthly_target_cards::-webkit-scrollbar-thumb {
  background-color: var(--primary_color);
  border-radius: 10px;
} */

/* .target_details_upper_outer_container {
  overflow-y: auto;
  max-height: 50vh; 
} */

.target_details_upper_outer_container::-webkit-scrollbar {
  width: 4px;
}

.target_details_upper_outer_container::-webkit-scrollbar-track {
  background-color: transparent;
  border-radius: 10px;
}

.target_details_upper_outer_container::-webkit-scrollbar-thumb {
  background-color: var(--primary_color);
  border-radius: 10px;
}

