import { useEffect, useRef, useState } from "react";
import { Loader } from "../../../../../../assets/loader";
import {
  useAppDispatch,
  useAppSelector,
} from "../../../../../../redux/hooks/Modules/Reduxhooks/ReduxHooks";
import TMMMNav from "../../../../../../components/Reusble/TMMMNav";
import styles from "../Styles/Manpower.module.css";
import { DeletePopup } from "../../../../../../components/Reusble/Global/DeletePopup";
import { closePopup } from "../../../../../../redux/features/Modules/Reusble/popupSlice";
import {
  initializeDatabase,
  isValidValue,
  pathTableMap,
} from "../../../../../../functions/functions";
import CategoryCard from "../../../../../../components/Reusble/Billing/Masters/CategoryCard/Index";
import { useSelector } from "react-redux";
import { RootState } from "../../../../../../redux/store";
import { resetInputValues } from "../../../../../../redux/features/Modules/Reusble/floatinglabelslice";
import { useDeleteManpowerCategoryByIdMutation } from "../../../../../../redux/api/Modules/Billing/Billingapi";
import { usePouchSearch } from "../../../../../../functions/useLocalSearch";
import { useToast } from "../../../../../../hooks/ToastHook";
import {
  clearFetchedMasters,
  setFetchedMasters,
  setSearchData,
} from "../../../../../../redux/features/Modules/Masters";
const ManpowerCategory = () => {
  const currentOpenPopup = useAppSelector((state) => state.popup.popups);

  //to detect changes in the localdb
  // const detectChanges = useAppSelector((state) => state.backupSlice.isOpen);
  const data = useSelector(
    (state: RootState) => state.masterReduxSlice.fetchedManpower
  );
  // const [manpowerCategoriesData, setManpowerCategoriesData] = useState<any>({});
  const [searchmanpowerCategoriesData, setSearchMachineryCategoriesData] =
    useState<any>([]);
  const [page, setPage] = useState<number>(1);
  const localChange = useAppSelector(
    (state) => state.backupSlice.isLocalChange
  );
  const showToast = useToast();
  const [deleteManpowerCategory] = useDeleteManpowerCategoryByIdMutation();

  const inputValues = useSelector(
    (state: RootState) => state.floatingLabel.inputValues
  );
  const dispatch = useAppDispatch();
const searchedData = useSelector((state: RootState) => state.masterReduxSlice.searchedData);
  // const getDatafromDb = async () => {
  //   const dbName = await initializeDatabase("ManpowerCategory");
  //   const fetchedData = await window.electron.bulkGet({
  //     dbName,
  //     deleted: false,
  //   });

  //   console.log("check for api call tool", fetchedData);

  //   if (fetchedData) {
  //     setManpowerCategoriesData(fetchedData?.docs);
  //   }
  // };

  // useEffect(() => {
  //   getDatafromDb();
  // }, [detectChanges]);

  const [searchLocalKey, setSearchLocalKey] = useState<string>("");
  const searchKey = useAppSelector((state) => state.taskMaster.searchKey);
  //localdb logic

  useEffect(() => {
    if (searchKey) {
      setSearchLocalKey(searchKey);
    } else {
      setSearchLocalKey("");
    }
  }, [searchKey]);

  const getDatafromDb = async (p: any) => {
    const dbName = await initializeDatabase("Manpowercategory");
    const fetchedData = await window.electron.bulkGet({
      dbName,
      page: p,
      deleted: false,
    });

    console.log("check for api call tool", fetchedData);

    if (p === 1) {
      dispatch(
        setFetchedMasters({ data: fetchedData.docs, page: p, type: "manpower" })
      );

      // setManpowerCategoriesData(fetchedData.docs);
    } else {
      const newData = [...data, ...fetchedData.docs];
      dispatch(setFetchedMasters({ data: newData, page: p, type: "manpower" }));
      // setManpowerCategoriesData((prev: any) => [...prev, ...fetchedData.docs]);
    }
  };

  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const target = e.target as HTMLDivElement;

    if (target) {
      const { scrollHeight, clientHeight, scrollTop } = target;

      if (scrollTop + clientHeight >= scrollHeight - 1) {
        console.log("page changed", page);
        setPage((prev) => prev + 1);
      }
    }
  };

  // useEffect(() => {
  //   if (localChange) {
  //     setPage(1);
  //   }
  // }, [detectChanges]);

  useEffect(() => {
    if (page) {
      getDatafromDb(page);
    }
  }, [page]);

  usePouchSearch({
    pathTableMap,
    searchKey: searchLocalKey,
    setData: setSearchData,
    setPage,
  });

  const navRef = useRef<HTMLDivElement>(null);
  const mainContentRef = useRef<HTMLDivElement>(null);
  const [width, setWidth] = useState<null | number>(null);
  const clientFunction = () => {
    const mainContentWidth =
      mainContentRef?.current?.getBoundingClientRect()?.width;
    // console.log('details of card view container in useEffect details',details,);
    // setWidth(details?.width);
    console.log("inner width");
    // if (window.innerWidth < 1200) {
    navRef.current?.style.setProperty("width", `${mainContentWidth}px`);
    // }
  };
  const condition =
    mainContentRef.current &&
    mainContentRef.current.getBoundingClientRect().width < 1200;

  useEffect(() => {
    clientFunction();
    const handleResize = () => clientFunction();
    window.addEventListener("resize", handleResize);
    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, [window.innerWidth, condition]);
  useEffect(() => {
    return () => {
      dispatch(clearFetchedMasters());
    };
  }, []);
  return (
    <>
      {/* <SummaryFields /> */}
      <div ref={navRef}>
        <TMMMNav
          Label={"Category"}
          variant={"manpowerCategory"}
          TargetForm={"AddCategoryForm"}
        />
      </div>
      <div style={{ marginTop: "1.5rem" }}>
        <div
          ref={mainContentRef}
          className={styles.cardview}
          onScroll={(e) => handleScroll(e)}
        >
          <div className={styles.inner_cardview}>
            {data && data?.length > 0 ? (
              (searchedData?.length > 0
                ? searchedData
                : data || []
              )?.map((item: any) => (
                <CategoryCard
                  editData={item}
                  data={{
                    title: item?.name ?? "",
                    _id: item?._id ?? "",
                    path: "manpower-master",
                    total: item?.totalDesignations ?? 0,
                    items: [
                      { title: "Skilled", name: item?.types?.skilled || 0 },
                      { title: "Unskilled", name: item?.types?.unskilled || 0 },
                      { title: "Both", name: item?.types?.both || 0 },
                      { title: "Skills", name: item?.totalSkills || 0 },
                    ],
                  }}
                />
              ))
            ) : (
              <div className={styles.loader_loading}>
                <img
                  src={Loader.suryaconLogo}
                  alt="Loading..."
                  className={styles.loader_loading_image}
                />
              </div>
            )}
          </div>
        </div>
      </div>
      {currentOpenPopup["deleteCategory"] && (
        <DeletePopup
          header="Are you sure you want to delete this Category?"
          height="calc(100% - 9rem)"
          callbackDelete={async () => {
            await deleteManpowerCategory({
              manpowerId: inputValues?._id,
            }).unwrap();

            showToast({
              messageContent: `Manpower Category Deleted Successfully!`,
              type: "success",
            });

            dispatch(closePopup("deleteCategory"));
            dispatch(resetInputValues());
          }}
          onClose={() => {
            dispatch(closePopup("deleteCategory"));
            dispatch(resetInputValues());
          }}
        >
          <div style={{ display: "flex", flexWrap: "wrap" }}>
            {inputValues?.CategoryName &&
              isValidValue(inputValues?.CategoryName) && (
                <div
                  className={styles.summaryDivData}
                  style={{
                    width: "100%",
                  }}
                >
                  <div className={styles.summaryDataContent}>
                    <p
                      style={{ color: "var(--text-black-60)" }}
                      className="p_tag_14px"
                    >
                      Name
                    </p>
                    <h4
                      style={{
                        color: "var(--text-black-87)",
                        marginTop: "0.3rem",
                      }}
                    >
                      {inputValues?.CategoryName}
                    </h4>
                  </div>
                </div>
              )}
            {inputValues?.Description &&
              isValidValue(inputValues?.Description) && (
                <div
                  className={styles.summaryDivData}
                  style={{
                    width: "100%",
                  }}
                >
                  <div className={styles.summaryDataContent}>
                    <div>
                      <p
                        style={{ color: "var(--text-black-60)" }}
                        className="p_tag_14px"
                      >
                        Description
                      </p>
                      <h4
                        style={{
                          color: "var(--text-black-87)",
                          marginTop: "0.3rem",
                        }}
                      >
                        {inputValues?.Description}
                      </h4>
                    </div>
                  </div>
                </div>
              )}
          </div>
        </DeletePopup>
      )}
    </>
  );
};

export default ManpowerCategory;
