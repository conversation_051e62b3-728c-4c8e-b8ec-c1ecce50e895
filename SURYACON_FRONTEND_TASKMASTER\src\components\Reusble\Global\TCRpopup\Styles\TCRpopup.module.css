.tcrpopup_container {
  position: fixed;
  top: 7rem;
  right: 1.5rem;
  transform: translate(0%, 0%);
  background: var(--blur-background);
  /* background-color: var(--main_background); */
  padding: 0.75rem 0.9rem;
  box-shadow: 0px 4px 40px 0px #00000080;
  border-radius: 2.6rem;
  z-index: 9999;
  width: 35rem;
  /* min-height: 85vh; */
  height: calc(100% - 8.5rem);
  animation: slideIn 0.5s ease-out;
  backdrop-filter: blur(150px);

  box-shadow: 0px 4px 40px 0px #00000080;
}

@keyframes slideIn {
  from {
    transform: translate(100%, 0%);
  }

  to {
    transform: translate(0%, 0%);
  }
}

@keyframes slideOut {
  from {
    transform: translate(0%, 0%);
  }

  to {
    transform: translate(100%, 0%);
  }
}

.tcrpopup_container.closing {
  animation: slideOut 0.5s ease-out;
}

.tcrpopup_header {
  color: var(--primary_color);
  display: flex;
  justify-content: center;

  padding: 1.1rem 1.1rem 0.6rem 1.1rem;
  max-width: 32rem;
}

.summaryPage{
  height: calc(100% - 9.5rem);
  /* max-height: calc(100% - 8.5rem); */
  overflow: auto;
}
.tcr_main_content{
  height: calc(100% - 7.5rem);
  max-height: calc(100% - 8.5rem);
  overflow: auto;
}
@media (max-width:1280px) {
.summaryPage{
  height: calc(100% - 6.5rem);
  max-height: calc(100% - 7.5rem);
  overflow: auto;
}
.tcr_main_content{
  height: calc(100% - 6.5rem);
  max-height: calc(100% - 7.5rem);
  overflow: auto;
}
}
.closeButton {
  position: absolute;
  top: 1.5em;
  right: 0.625rem;
  padding: 1rem;
  background: transparent;
  border: none;
  cursor: pointer;
}

.tcrpopup_btngrp {
  position: fixed;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 100%;
  padding: 1.5rem 1rem;
  display: flex;
  justify-content: center;
  gap: 1rem;
  border-radius: 2.6rem;
  /* background-color: var(--main_background); */
  /* backdrop-filter: blur(150px); */
}

.tcrphotopopup_maincontainer {
  /* max-height: 55vh; */
  overflow: auto;
  padding-bottom: 73px;
  margin-top: 1rem;
}

.tcrpopup_row1 {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  padding: 0 0.5rem;
}

.tcrpopup_secondrow_radiosection{
  padding: 0 0.7rem;
}

.tcrpopup_header_attachmentbtn {
  /* border: 1px solid;
  border-image-source: linear-gradient(
    130.72deg,
    rgba(237, 231, 231, 0.07) -16.06%,
    rgba(251, 251, 251, 0.05) 82.03%
  );
  box-shadow: 0px 0px 4px 0px #00000066;
  height: 38px;
  width: 38px;
  border-radius: 50%; */
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  bottom: -0.5rem;
  width: 2.2rem;
  height: 2.2rem;
  position: relative;
  
}

.tcrpopup_descbox {
  flex: 0 0 90%;
}

.tcrpopup_radiobtns {
  display: flex;
  gap: 1.5rem;
  margin-top: 0.5rem;
  margin: 0.5rem 0.8rem 0.8rem 0.8rem;
}

.tcrpopup_btndiv {
  display: flex;
  gap: 0.3rem;
  border: 1px solid #00000047;
  padding: 0.8rem;
  border-radius: 50px;
  color: var(--text-black-60);
}

#changeColor {
  accent-color: var(--primary_color);
}

.tcr_fileNames {
  background-color: var(--primary_background);
  color: var(--text-black-87);
  padding: 0.5rem 0.7rem;
  border-radius: 100px;
  position: relative;
}

.deleteButtondesc {
  height: 25px;
  width: 25px;
  border-radius: 50%;
  background: #f6e6e6;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;

  z-index: 99;
  cursor: pointer;
}

.tcr_fileNames_div {
  /* margin-left: 1rem; */
  display: flex;
  flex-wrap: wrap;
}

.summaryDivData {
  display: flex;
  align-items: center;
}

.summaryDataContent {
  display: flex;
  flex-direction: column;
  background: var(--main_background);
  border-radius: 0.75rem;
  width: 100%;
  /* width: 30.8rem; */
  /* max-width: 28.5rem; */

  min-height: 3rem;
  padding: 1rem;
  white-space: normal;
  margin: 0.6rem;
  /* gap: 0.2rem; */
  line-height: 1.363rem;
  text-align: left;
  background: #ffffff99;
}

.tcrphotopopup_container {
  border: 1px solid #00000047;
  margin: 1rem 0.7rem;
  padding: 1rem;
  border-radius: 16px;
  /* min-height: 16rem;  this is original */
  min-height: 10rem;
  position: relative;
}

.tcrphotopopup_uploadphotocontainer {
  background-color: var(--main_background);
  /* margin: 1rem; */
  border: 2px dashed #b0ccd1;
  height: 9.7rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.uploadWrapper {
  cursor: pointer;
  position: relative;
  width: 100%;
  height: fit-content;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.tcrphotopopup_addphotodiv {
  border: 1px solid var(--primary_color);
  max-width: 5.8rem;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  border-radius: 100px;
  margin-left: 1rem;
}

.previewButtonContainer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.tcrphotopopup_container.expanded {
  flex-direction: column;
  align-items: center;
}

.tcrphotopopup_uploadphotocontainer.expandedUpload {
  height: auto;
}

.tcrphotopopup_uploadphotocontainer img {
  max-width: 100%;
  max-height: 80vh;
}

.imageContainer {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: visible;
}

.uploadedImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.overlayButtons {
  position: absolute;
  top: 8px;
  display: flex;
  gap: 8px;
  z-index: 10;
  width: 100%;
  justify-content: space-between;
}

.previewButton {
  background: #ffffff80;
  display: flex;
  gap: 0.5rem;
  color: var(--text-black-60);
  border: none;
  padding: 5px 10px;
  border-radius: 100px;
  cursor: pointer;
  font-size: 12px;
  margin: 0 1rem;
  align-items: center;
  justify-content: center;
}

.deleteButton {
  height: 25px;
  width: 25px;
  border-radius: 50%;
  background: #f6e6e6;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  right: -0.7rem;
  top: -0.7rem;
  z-index: 99;
  cursor: pointer;
}

.deleteButton2{
  height: 25px;
  width: 25px;
  border-radius: 50%;
  background: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  right: 1.5rem;
  top: 1.7rem;
  z-index: 99;
  cursor: pointer;
}


.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.TCRpopup_popup_tip {
  padding: 8px 12px 8px 12px;
  position: absolute;
  display: none;
  background-color: white;
  border-radius: 12px;

  right: 3rem;
  width: max-content;
  top: 1rem;
  transform: translateY(-0.85rem);
  border-radius: 12px;
}
@media (min-width:1800px) {
  .tcrpopup_header_attachmentbtn{
    bottom: -0.5rem;
  }
  .TCRpopup_popup_tip{
    top: 0.85rem;
  }
}
.tcrpopup_header_attachmentbtn:hover {
  background: white !important;
  width: 2.2rem;
  height: 2.2rem;
  border-radius: 50%;
}
.tcrpopup_header_attachmentbtn:hover .TCRpopup_popup_tip {
  display: block;
  background-color: white !important;
}

.Image_picker_repeat {
  background: #ffffff80;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 100%;
  height: 1.375rem;
  width: 1.375rem;
  position: absolute;

  top: 0.2rem;
  left: 25.75rem;
}
.tcr_fileNames:hover .file_cross_div {
  display: block !important;
}

