//Author <PERSON><PERSON><PERSON><PERSON>
import { createSlice } from "@reduxjs/toolkit";
import { set } from "lodash";

type Brand = {
  _id?: string;
  brand: {
    _id?: string;
    name: string;
  };
  updateId?: string;
  Grade: string[];
  ConversionRates?: {
    _id?: string;
    fromUnit: string;
    toUnit: string;
    rate: number | null;
  }[];
};



interface MastersSliceInterface {
  deleteFormData: Record<any, any>[];
  deleteGradeData: Record<any, any>[];
  imageToggle: boolean;
  deleteToolData: [];
  deleteUnitData: Record<any, any>[];
  formMode: string;
  formToolsData: {
    _id?: string;
    type: string;
    Name: string;
    Photo: { name: string; type: string; file: File } | null;
    Description: string;
    Brands: Brand[];
    Users: { _id: string; name: string }[];
  };
  initialFormToolsData: {
    _id?: string;
    type: string;
    Name: string;
    Photo: { name: string; type: string; file: File } | null;
    Description: string;
    Brands: Brand[];
    Users: { _id: string; name: string }[];
  };
  formManpowerData: {
    _id?: string;
    type: string;
    Name: string;
    Description: string;
    Skills: string[];
  };
  initialFormManpowerData: {
    _id?: string;
    type: string;
    Name: string;
    Description: string;
    Skills: string[];
  };
  formMaterialsData: {
    _id?: string;
    Photo: { name: string; type: string; file: File } | null;
    Name: string;
    Description: string;
    Unit: { _id: string; name: string }[];
    Brands: Brand[];
  };
  initialFormMaterialsData: {
    _id?: string;
    Photo: { name: string; type: string; file: File } | null;
    Name: string;
    Description: string;
    Unit: { _id: string; name: string }[];
    Brands: Brand[];
  };
  formMachineryData: {
    _id?: string;
    Photo: { name: string; type: string; file: File } | null;
    Name: string;
    Description: string;
    Tools: { _id: string; name: string }[];
    Fuel: string;
    Brands: Brand[];
  };
  initialFormMachineryData: {
    _id?: string;
    Photo: { name: string; type: string; file: File } | null;
    Name: string;
    Description: string;
    Tools: { _id: string; name: string }[];
    Fuel: string;
    Brands: Brand[];
  };
  designationFormData: {
    _id?: string;
    designationName:string;
    jobDescription:string;
    requiredExperience:number|null;
    location:string;
    // workingHoursFrom:string;
    // workingHoursTo:string;
    qualifications:string[];
    supervisor:{ _id: string; name: string } | null;
  };

  initialDesignationFormData: {
    _id?: string;
    designationName:string;
    jobDescription:string;
    requiredExperience:number|null;
    location:string;
    qualifications:string[];
    supervisor:{ _id: string; name: string } | null;
    // workingHoursFrom:string;
    // workingHoursTo:string;
  };

  addcategoryTypeFormData: any;

  initialDepartmentFormData: {
    _id?: string;
    departmentName: string;
    departmentHead: { _id: string; name: string } | null;
    description: string;
    email: string;
  };

  departmentFormData: {
    _id?: string;
    departmentName: string;
    departmentHead: { _id: string; name: string } | null;
    description: string;
    email: string;
  };

  mdStateData : any;
}

const initialState: MastersSliceInterface = {
  formMode: "Add",
  imageToggle: false,
  deleteFormData: [],
  deleteGradeData: [],
  deleteToolData: [],
  deleteUnitData: [],
  formToolsData: {
    _id: "",
    type: "",
    Name: "",
    Photo: null,
    Description: "",
    Brands: [
      {
        _id: Date.now().toString(),
        brand: {
          name: "",
        },
        Grade: [],
      },
    ],
    Users: [],
  },
  initialFormToolsData: {
    _id: "",
    type: "",
    Name: "",
    Photo: null,
    Description: "",
    Brands: [
      {
        brand: {
          name: "",
        },
        Grade: [],
      },
    ],
    Users: [],
  },
  formManpowerData: {
    _id: "",
    type: "",
    Name: "",
    Description: "",
    Skills: [],
  },
  initialFormManpowerData: {
    _id: "",
    type: "",
    Name: "",
    Description: "",
    Skills: [],
  },
  formMaterialsData: {
    _id: "",
    Photo: null,
    Name: "",
    Description: "",
    Unit: [],
    Brands: [
      {
        _id: Date.now().toString(),
        brand: {
          name: "",
        },
        Grade: [],
        ConversionRates: [],
      },
    ],
  },
  initialFormMaterialsData: {
    _id: "",
    Photo: null,
    Name: "",
    Description: "",
    Unit: [],
    Brands: [
      {
        brand: {
          name: "",
        },
        Grade: [],
        ConversionRates: [],
      },
    ],
  },
  formMachineryData: {
    _id: "",
    Photo: null,
    Name: "",
    Description: "",
    Tools: [],
    Fuel: "",
    Brands: [
      {
        _id: Date.now().toString(),
        brand: {
          name: "",
        },
        Grade: [],
      },
    ],
  },
  initialFormMachineryData: {
    _id: "",
    Photo: null,
    Name: "",
    Description: "",
    Tools: [],
    Fuel: "",
    Brands: [
      {
        brand: {
          name: "",
        },
        Grade: [],
      },
    ],
  },
  designationFormData:{
    _id:"",
    designationName:"",
    jobDescription:"",
    requiredExperience:null,
    location:"",
    qualifications:[],
    supervisor:null
    // workingHoursTo:"",
    // workingHoursFrom:"",
  },
  initialDesignationFormData:{
    _id:"",
    designationName:"",
    jobDescription:"",
    requiredExperience:null,
    location:"",
    qualifications:[],
    supervisor:null
    // workingHoursFrom:"",
    // workingHoursTo:"",
  },

  addcategoryTypeFormData: {
    data: [],
    primarylabel: "",
    initialSelected: [],
    selectedOption: "",
  },

  initialDepartmentFormData: {
    _id: "",
    departmentName: "",
    departmentHead: null,
    description: "",
    email: "",
  },
  departmentFormData: {
    _id: "",
    departmentName: "",
    departmentHead: null,
    description: "",
    email: "",
  },
  mdStateData : null,
};

const mastersSlice = createSlice({
  name: "mastersSlice",
  initialState,
  reducers: {
    //for all master forms set modes
     setAddCategoryTypeForm(state, action) {
      state.addcategoryTypeFormData = action.payload;
    },
    setFormMode(state, action) {
      state.formMode = action.payload;
    },
    //form tools form
    setFormData(state, action) {
      console.log("payload c", action.payload);
      state.formToolsData = action.payload;
    },
    resetFormData(state) {
      state.formToolsData = initialState.formToolsData;
    },
    setInitialFormData(state, action) {
      state.initialFormToolsData = action.payload;
    },
    resetInitialFormData(state) {
      state.initialFormToolsData = initialState.initialFormToolsData;
    },

    //for manpower forms
    setFormManpowerData(state, action) {
      console.log("manpower", action.payload);
      state.formManpowerData = action.payload;
    },
    resetFormManpowerData(state) {
      state.formManpowerData = initialState.formManpowerData;
    },
    setInitialFormManpowerData(state, action) {
      state.initialFormManpowerData = action.payload;
    },
    resetInitialFormManpowerData(state) {
      state.initialFormManpowerData = initialState.initialFormManpowerData;
    },

    //for manpower forms
    setFormMaterialsData(state, action) {
      state.formMaterialsData = action.payload;
    },
    resetFormMaterialsData(state) {
      state.formMaterialsData = initialState.formMaterialsData;
    },
    setInitialFormMaterialsData(state, action) {
      state.initialFormMaterialsData = action.payload;
    },
    resetInitialFormMaterialsData(state) {
      state.initialFormMaterialsData = initialState.initialFormMaterialsData;
    },

    //for machinery forms
    setFormMachineryData(state, action) {
      state.formMachineryData = action.payload;
    },
    resetFormMachineryData(state) {
      state.formMachineryData = initialState.formMachineryData;
    },
    setInitialFormMachineryData(state, action) {
      state.initialFormMachineryData = action.payload;
    },
    resetInitialFormMachineryData(state) {
      state.initialFormMachineryData = initialState.initialFormMachineryData;
    },

    //for department forms
    setDesignationFormData(state, action) {
      console.log("form data>?>designation form data", action.payload);
      state.designationFormData = action.payload;
    },
    resetDesignationFormData(state){
      console.log("reset designation form data",initialState.designationFormData);
      state.designationFormData=initialState.designationFormData
    },
    
    setInitialDesignationFormData(state, action) {
      state.initialDesignationFormData = action.payload;
    },
    resetInitialDesignationFormData(state) {
      state.initialDesignationFormData =
        initialState.initialDesignationFormData;
    },

    setInitialDepartmentFormData(state, action) {
      state.initialDepartmentFormData = action.payload;
    },

    resetInitialDepartmentFormData(state) {
      state.initialDepartmentFormData = initialState.initialDepartmentFormData;
    },

    setDepartmentFormData(state, action) {
      state.departmentFormData = action.payload;
    },

    resetDepartmentFormData(state) {
      state.departmentFormData = initialState.initialDepartmentFormData;
    },

    setMdStateData(state, action){
      state.mdStateData = action.payload;
    },

    resetMdStateData(state){
      state.mdStateData = initialState.mdStateData;
    },

    setDeletedFormData(state, action) {
      state.deleteFormData = action.payload;
    },
    resetDeleteFormData(state) {
      state.deleteFormData = [];
    },
    setDeletedGradeData(state, action) {
      state.deleteGradeData = action.payload;
    },
    resetDeletedGradeData(state) {
      state.deleteGradeData = [];
    },
    setDeletedToolData(state, action) {
      state.deleteToolData = action.payload;
    },
    resetDeletedToolData(state) {
      state.deleteToolData = [];
    },
    setDeletedUnitData(state, action) {
      state.deleteUnitData = action.payload;
    },
    resetDeleteUnitData(state) {
      state.deleteUnitData = [];
    },

    //for image polling
    setImageToggle(state) {
      state.imageToggle = !state.imageToggle;
    },
  },
});

export const {
  setAddCategoryTypeForm,
  //for tool master
  setFormData,
  resetFormData,
  setInitialFormData,
  resetInitialFormData,

  //to set the mode
  setFormMode,

  //actions for manpower
  setFormManpowerData,
  resetFormManpowerData,
  setInitialFormManpowerData,
  resetInitialFormManpowerData,

  //actions for materials
  setFormMaterialsData,
  resetFormMaterialsData,
  setInitialFormMaterialsData,
  resetInitialFormMaterialsData,

  //actions for machinery
  setFormMachineryData,
  resetFormMachineryData,
  setInitialFormMachineryData,
  resetInitialFormMachineryData,

  //actions for department
  setDesignationFormData,
  resetDesignationFormData,
  setInitialDesignationFormData,
  setInitialDepartmentFormData,
  resetInitialDesignationFormData,
  resetInitialDepartmentFormData,
  setDepartmentFormData,
  resetDepartmentFormData,
  // md state action
  setMdStateData,
  resetMdStateData,

  //action for deleted data
  setDeletedFormData,
  resetDeleteFormData,
  setDeletedGradeData,
  resetDeletedGradeData,
  setDeletedToolData,
  resetDeletedToolData,
  setDeletedUnitData,
  resetDeleteUnitData,

  //for image polling
  setImageToggle,
} = mastersSlice.actions;

export default mastersSlice.reducer;
