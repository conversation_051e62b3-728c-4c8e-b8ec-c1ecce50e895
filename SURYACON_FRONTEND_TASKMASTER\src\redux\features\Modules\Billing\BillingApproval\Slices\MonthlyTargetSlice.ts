import { createSlice, PayloadAction, createSelector } from "@reduxjs/toolkit";
import { TargetGenerationFormState } from "../../../../../../modules/Billing/Pages/BillingApproval/Interface/interface";

// Extended state without cache
interface ExtendedFormState extends TargetGenerationFormState {
  availableTasks: any[];
  availableSubtasks: any[];
  isSwitchingForms: boolean;
  loadingStates: {
    towers: boolean;
    tasks: boolean;
    subtasks: boolean;
  };
}

// Simplified initial state without cache
const initialState: ExtendedFormState = {
  category: "Tower",
  selectedTower: { id: "", name: "" },
  selectedTasks: [],
  activeTaskDetails: null,
  taskDetails: {},
  finalDetails: { selectedTaskIds: [] },
  currentStep: "details",
  isFormValid: false,
  isSubtaskForm: false,
  errors: {},
  availableTasks: [],
  availableSubtasks: [],
  isSwitchingForms: false,
  loadingStates: {
    towers: false,
    tasks: false,
    subtasks: false,
  },
};

// Helper function for task details
const createEmptyTaskDetails = () => ({
  description: "",
  startDate: "",
  endDate: "",
  area: "",
  duration: "",
  drawings: [],
  remarks: "",
  subtasks: [],
});

// Helper functions for task identification
const getTaskId = (task: any): string => task.towerRouteId || task._id || task.id;
const getTaskName = (task: any): string => task.name || task.task_name || task.category || "Unnamed";

const monthlyTargetSlice = createSlice({
  name: "monthlyTargetForm",
  initialState,
  reducers: {
    setCategory: (state, action: PayloadAction<"Tower" | "Non-Tower" | "Miscellaneous">) => {
      if (state.category !== action.payload) {
        state.category = action.payload;
      }
    },

    setSelectedTower: (state, action: PayloadAction<{ id: string; name: string }>) => {
      const { id, name } = action.payload;
      if (state.selectedTower.id !== id) {
        state.selectedTower = { id, name };
        state.availableTasks = [];
      }
    },

    setIsSubtaskForm: (state, action: PayloadAction<boolean>) => {
      state.isSubtaskForm = action.payload;
    },

    // Batch operations for better performance
    batchUpdateTasks: (state, action: PayloadAction<{
      selectedTasks?: any[];
      availableTasks?: any[];
      activeTaskDetails?: string | null;
    }>) => {
      const { selectedTasks, availableTasks, activeTaskDetails } = action.payload;
      
      if (selectedTasks !== undefined) {
        state.selectedTasks = selectedTasks;
      }
      if (availableTasks !== undefined) {
        state.availableTasks = availableTasks;
      }
      if (activeTaskDetails !== undefined) {
        state.activeTaskDetails = activeTaskDetails;
      }
    },

    // Optimized task management with deduplication
    addTasksOptimized: (state, action: PayloadAction<any[]>) => {
      const newTasks = action.payload;
      const existingIds = new Set(state.selectedTasks.map(task => task.id));

      const uniqueNewTasks = newTasks
        .filter(task => !existingIds.has(getTaskId(task)))
        .map(task => ({
          id: getTaskId(task),
          name: getTaskName(task),
          isSelected: true,
        }));

      if (uniqueNewTasks.length > 0) {
        state.selectedTasks.push(...uniqueNewTasks);
      }
    },

    removeTaskOptimized: (state, action: PayloadAction<string>) => {
      const taskId = action.payload;
      const taskIndex = state.selectedTasks.findIndex(task => task.id === taskId);
      
      if (taskIndex !== -1) {
        state.selectedTasks.splice(taskIndex, 1);
        delete state.taskDetails[taskId];
        
        // Remove from final details if present
        const finalIndex = state.finalDetails.selectedTaskIds.indexOf(taskId);
        if (finalIndex !== -1) {
          state.finalDetails.selectedTaskIds.splice(finalIndex, 1);
        }
      }
    },

    // Optimized task details update with shallow comparison
    updateTaskDetailsOptimized: (state, action: PayloadAction<{
      taskId: string;
      details: Partial<any>;
    }>) => {
      const { taskId, details } = action.payload;

      if (!state.taskDetails[taskId]) {
        state.taskDetails[taskId] = createEmptyTaskDetails();
      }

      // Only update if there are actual changes
      const currentDetails = state.taskDetails[taskId];
      const hasChanges = Object.keys(details).some(
        key => currentDetails[key] !== details[key]
      );

      if (hasChanges) {
        state.taskDetails[taskId] = { ...currentDetails, ...details };
      }
    },

    // Loading states
    setLoadingState: (state, action: PayloadAction<{
      type: 'towers' | 'tasks' | 'subtasks';
      loading: boolean;
    }>) => {
      const { type, loading } = action.payload;
      state.loadingStates[type] = loading;
    },

    // Optimized available data setters
    setAvailableTasksOptimized: (state, action: PayloadAction<any[]>) => {
      const newTasks = action.payload || [];
      // Only update if different
      if (JSON.stringify(state.availableTasks) !== JSON.stringify(newTasks)) {
        state.availableTasks = newTasks;
      }
    },

    setAvailableSubtasksOptimized: (state, action: PayloadAction<any[]>) => {
      const newSubtasks = action.payload || [];
      // Only update if different
      if (JSON.stringify(state.availableSubtasks) !== JSON.stringify(newSubtasks)) {
        state.availableSubtasks = newSubtasks;
      }
    },

    // Bulk operations for better performance
    bulkUpdateTaskDetails: (state, action: PayloadAction<Record<string, any>>) => {
      Object.entries(action.payload).forEach(([taskId, details]) => {
        if (!state.taskDetails[taskId]) {
          state.taskDetails[taskId] = createEmptyTaskDetails();
        }
        state.taskDetails[taskId] = { ...state.taskDetails[taskId], ...details };
      });
    },

    // Optimized validation with early exit
    validateFormOptimized: (state) => {
      const errors: Record<string, boolean> = {};
      
      // Early exit if no tower selected
      if (!state.selectedTower.id) {
        errors.tower = true;
        state.errors = errors;
        state.isFormValid = false;
        return;
      }

      // Quick validation
      if (!state.selectedTasks.some(task => task.isSelected)) {
        errors.tasks = true;
      }

      // Validate active task details only if needed
      if (state.activeTaskDetails && state.taskDetails[state.activeTaskDetails]) {
        const taskDetails = state.taskDetails[state.activeTaskDetails];
        if (!taskDetails.description) errors.description = true;
        if (!taskDetails.startDate) errors.startDate = true;
        if (!taskDetails.endDate) errors.endDate = true;
        if (!taskDetails.area) errors.area = true;
      }

      state.errors = errors;
      state.isFormValid = Object.keys(errors).length === 0;
    },

    // Keep existing reducers for compatibility
    setSelectedTasks: (state, action) => {
      state.selectedTasks = action.payload;
    },
    setActiveTaskDetails: (state, action) => {
      state.activeTaskDetails = action.payload;
    },
    setAvailableTasks: (state, action) => {
      state.availableTasks = action.payload ? [...action.payload] : [];
    },
    setAvailableSubtasks: (state, action) => {
      state.availableSubtasks = action.payload;
    },
    addTask: (state, action) => {
      const newTask = action.payload;
      const taskId = getTaskId(newTask);
      const taskExists = state.selectedTasks.some(task => task.id === taskId);
      if (!taskExists) {
        state.selectedTasks.push({
          id: taskId,
          name: getTaskName(newTask),
          isSelected: true,
        });
      }
    },
    addTasks: (state, action) => {
      const newTasks = action.payload;
      const currentTaskIds = state.selectedTasks.map(task => task.id);
      newTasks.forEach(newTask => {
        const taskId = getTaskId(newTask);
        if (!currentTaskIds.includes(taskId)) {
          state.selectedTasks.push({
            id: taskId,
            name: getTaskName(newTask),
            isSelected: true,
          });
        }
      });
    },
    removeTask: (state, action) => {
      const taskId = action.payload;
      state.selectedTasks = state.selectedTasks.filter(task => task.id !== taskId);
      if (state.taskDetails[taskId]) {
        delete state.taskDetails[taskId];
      }
    },
    updateTaskDetails: (state, action) => {
      const { taskId, details } = action.payload;
      if (!state.taskDetails[taskId]) {
        state.taskDetails[taskId] = createEmptyTaskDetails();
      }
      state.taskDetails[taskId] = { ...state.taskDetails[taskId], ...details };
    },
    addDrawing: (state, action) => {
      const { taskId, drawing } = action.payload;
      if (!state.taskDetails[taskId]) {
        state.taskDetails[taskId] = createEmptyTaskDetails();
      }
      state.taskDetails[taskId].drawings.push(drawing);
    },
    removeDrawing: (state, action) => {
      const { taskId, drawingIndex } = action.payload;
      if (state.taskDetails[taskId]) {
        state.taskDetails[taskId].drawings = state.taskDetails[taskId].drawings
          .filter((_, index) => index !== drawingIndex);
      }
    },
    updateFinalDetails: (state, action) => {
      state.finalDetails = action.payload;
    },
    validateForm: (state) => {
      const errors: Record<string, boolean> = {};
      if (!state.selectedTower.id) errors.tower = true;
      if (!state.selectedTasks.some(task => task.isSelected)) errors.tasks = true;
      if (state.activeTaskDetails) {
        const taskDetails = state.taskDetails[state.activeTaskDetails];
        if (taskDetails) {
          if (!taskDetails.description) errors.description = true;
          if (!taskDetails.startDate) errors.startDate = true;
          if (!taskDetails.endDate) errors.endDate = true;
          if (!taskDetails.area) errors.area = true;
        }
      }
      state.errors = errors;
      state.isFormValid = Object.keys(errors).length === 0;
    },
    setCurrentStep: (state, action) => {
      state.currentStep = action.payload;
    },
    setFormSwitching: (state, action) => {
      state.isSwitchingForms = action.payload;
    },
    toggleTaskSelection: (state, action) => {
      const taskId = action.payload;
      const task = state.selectedTasks.find(task => task.id === taskId);
      if (task) {
        task.isSelected = !task.isSelected;
      }
    },
    resetForm: () => initialState,
  },
});

// Memoized selectors for performance
export const selectFormState = (state: any) => state.monthlyTargetForm;
export const selectSelectedTasks = createSelector(
  [selectFormState],
  (formState) => formState.selectedTasks
);
export const selectActiveTaskDetails = createSelector(
  [selectFormState],
  (formState) => formState.activeTaskDetails
);
export const selectTaskDetails = createSelector(
  [selectFormState, selectActiveTaskDetails],
  (formState, activeTaskId) => 
    activeTaskId ? formState.taskDetails[activeTaskId] : null
);
export const selectLoadingStates = createSelector(
  [selectFormState],
  (formState) => formState.loadingStates
);

export const {
  setCategory,
  setSelectedTower,
  setSelectedTasks,
  addTask,
  addTasks,
  addTasksOptimized,
  removeTask,
  removeTaskOptimized,
  toggleTaskSelection,
  setIsSubtaskForm,
  setActiveTaskDetails,
  updateTaskDetails,
  updateTaskDetailsOptimized,
  setAvailableTasks,
  setAvailableSubtasks,
  setAvailableTasksOptimized,
  setAvailableSubtasksOptimized,
  setFormSwitching,
  addDrawing,
  removeDrawing,
  updateFinalDetails,
  setCurrentStep,
  validateForm,
  validateFormOptimized,
  resetForm,
  batchUpdateTasks,
  setLoadingState,
  bulkUpdateTaskDetails,
} = monthlyTargetSlice.actions;

export default monthlyTargetSlice.reducer;