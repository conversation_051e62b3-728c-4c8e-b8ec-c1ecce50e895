import React, { useRef, useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../../../redux/store";

import styles from "./Styles/Floatinglabel.module.css";
import {
  setInputHeight,
  setInputValue,
} from "../../../../redux/features/Modules/Reusble/floatinglabelslice";
import { FloatingLabelInputProps } from "../GlobalInterfaces/GlobalInterface";
import { DeleteIcon } from "../../../../assets/icons";

const FloatingLabelInput: React.FC<FloatingLabelInputProps> = ({
  label,
  id,
  placeholder,
  marginTop = "1.5rem",
  marginBottom = "0",
  isInvalid,
  isDisabled = false,
  error = false,
  itemLabels = [],
  onInputChange,
  Icon,
  iconClick,
  maxlength,
  defaultvalue = "",
  value,
  rows = 1,
  fontSize,
  enterAllowed = true,
  props,
  props2,
  width,
  maxLength,
  handledelete,
  clearInput = false,
  type,
  focusOnInput,
  preventEnter = false,
  backgroundColor,
  onPaste,
  additionalCaseforClippath
}) => {
  const dispatch = useDispatch();
  const inputValue = useSelector(
    (state: RootState) => state.floatingLabel.inputValues[id] || ""
  );
  const storedHeight = useSelector(
    (state: RootState) => state.floatingLabel.inputHeights[id] || "auto"
  );
  const inputRef = useRef<HTMLTextAreaElement>(null);
  const divRef = useRef<HTMLDivElement>(null);
  const labelWidthRef = useRef<number>(0);

  // Adjust height on mount and when inputValue changes
  useEffect(() => {
    const currentValue = value ?? inputValue;
    if (inputRef.current) {
      inputRef.current.style.height = "auto";
      inputRef.current.value = maxLength
        ? getTruncatedText(String(currentValue), maxLength)
        : String(currentValue);
      const newHeight = `${inputRef.current.scrollHeight}px`;
      inputRef.current.style.height = newHeight;
      dispatch(setInputHeight({ [id]: newHeight }));
    }
  }, [value, inputValue, id, dispatch]);

  useEffect(() => {
    const currentValue = value ?? inputValue;
    if (!isBlurred && isFocused) {
      updateClipPath(id, false, currentValue);
    } else {
      updateClipPath(id, true, currentValue);
    }
  });

  // Clear input and reset height if clearInput is true
  useEffect(() => {
    if (clearInput && inputRef.current) {
      inputRef.current.value = "";
      inputRef.current.style.height = "auto";
      dispatch(setInputValue({ [id]: "" }));
      dispatch(setInputHeight({ [id]: "auto" }));
    }
  }, [clearInput, dispatch, id]);

  const handleInputChange = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
    let value = event.target.value; // Extract value from event
    dispatch(setInputValue({ [id]: value })); // Update Redux state

    if (onInputChange) {
      onInputChange(value); // Pass only value to the parent handler
    }

    if (inputRef.current) {
      inputRef.current.style.height = "auto";
      const newHeight = `${inputRef.current.scrollHeight}px`;
      inputRef.current.style.height = newHeight;
      dispatch(setInputHeight({ [id]: newHeight }));
    }
  };

  useEffect(() => {
    if (focusOnInput && inputRef.current) {
      const inputValue = inputRef.current;
      inputValue.focus({ preventScroll: true });
    }
  }, [focusOnInput]);

  const [isBlurred, setIsBlurred] = React.useState(true);
  const [isFocused, setIsFocused] = React.useState(false);
  const [isFocus, setIsFocus] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const handleFocusOrClick = () => {
    if (!isDisabled) {
      updateClipPath(id, false, value || "");
      isBlurred && setIsBlurred(false);
      setIsFocused(true);
      setIsFocus(true);
    }
  };

  const handleBlur = () => {
    if (!isDisabled) {
      updateClipPath(id, true, value || "");
      !isBlurred && setIsBlurred(true);
      setIsFocused(false);

      setIsFocus(false);
    }
  };

  const handleKeyPress = (event: React.KeyboardEvent<HTMLTextAreaElement>) => {
    const inputValue = event.currentTarget.value;

    // Prevent non-numeric characters
    if (type === "number") {
      if (!/[0-9.]/.test(event.key)) {
        event.preventDefault();
      }

      if (inputValue.includes(".")) {
        if (event.key === ".") {
          event.preventDefault();
        }

        let lastDigit = inputValue[inputValue.length - 1];
        if (
          (lastDigit == "1" && event.key >= "2" && event.key <= "9") ||
          (lastDigit > "1" && lastDigit <= "9") ||
          inputValue.split(".")[1].length == 2
        ) {
          event.preventDefault();
        }

        console.log("afterDotIndex : ", inputValue.split("."));
      }

      // Prevent 0 as the first character
      if (inputValue === "" && event.key === "0") {
        event.preventDefault();
      }
    }

    // Prevent "-" if maxlength is set (based on your original condition)
    if (maxlength && event.key === "-") {
      event.preventDefault();
    }

    // Prevent Enter if not allowed
    if (event.key === "Enter" && !enterAllowed) {
      event.preventDefault();
    }
  };

  const getTruncatedText = (text: string, limit: number): string => {
    return text.length > limit ? text.slice(0, limit) + "..." : text;
  };
  const [widthValue, setWidthValue] = React.useState(0);
  function updateClipPath(
    id: string,
    resetBorder: boolean,
    value?: string | number
  ) {
    const inputWrapper = document.querySelector(`#${id}`)?.parentElement;
    if (!inputWrapper) return;

    const label = inputWrapper.querySelector("label");
    const input = inputWrapper.querySelector("textarea");
    // const div = inputWrapper.querySelector(".filediv");

    if (label && input) {
      console.log('label:',label,'value',value,'resetBorder:',resetBorder,'itemLabels>>',itemLabels);
      if (!resetBorder || value) {
        const labelWidth = label.offsetWidth;
        setWidthValue(labelWidth);
        const inputWidth = input.offsetWidth;

        // Calculate clip-path based on label width
        const leftPercentage =
          ((labelWidthRef.current + 15) / inputWidth) * 100;
        input.style.clipPath = `polygon(0 0, ${leftPercentage}% 0, ${leftPercentage}% 2px, 0 2px, 0 100%, 100% 100%, 100% 0, 100% 0, 100% 0, 100% 2px, 100% 0)`;
      } else {
        input.style.clipPath = "none";
      }
    }
  }
  const [open, setOpen] = useState(false);
  useEffect(() => {
    const inputWrapper = document.querySelector(`#${id}`)?.parentElement;
    const labelw = inputWrapper?.querySelector("label");
    if (labelw) {
      // const labelWidth = labelw.offsetWidth;
      // if (labelWidth > 100) {
      labelWidthRef.current = labelw.offsetWidth;
      // } else {
      // labelWidthRef.current = labelw.offsetWidth ;
      // }
    }
  }, [id, isOpen, isFocus]);

  const handleClick = (e: React.MouseEvent<HTMLDivElement>) => {
    // e.stopPropagation();
    setIsOpen(!isOpen);
  };

  useEffect(() => {
    const div = divRef.current;

    const labelWidth = labelWidthRef.current * 1.25;

    setWidthValue(labelWidth);
    const inputWidth = 100;

    // Calculate clip-path based on label width
    let leftPercentage = 0; // Declare leftPercentage outside the block
    if (label === "Drawings" ) {
      leftPercentage = 18;
    }else if(label =="Cover Photo"){
      leftPercentage = 40;
    } else if (div?.offsetWidth) {
      leftPercentage = Math.round(
        ((div.offsetWidth - labelWidth) / div.offsetWidth) * 100
      );
    }
    if (div && leftPercentage > 0) {
      div.style.clipPath = `polygon(0 0, ${leftPercentage}% 0, ${leftPercentage}% 2px, 0 2px, 0 100%, 100% 100%, 100% 0, 100% 0, 100% 0, 100% 2px, 100% 0)`;
    }
  }, [itemLabels, dispatch, id, labelWidthRef, label, divRef]);

  useEffect(() => {
    const textareas = document.querySelectorAll("textarea");
    textareas.forEach((textarea) => {
      if (textarea.value) {
        const length = textarea.value.length;
        textarea.setSelectionRange(length, length);
      }
    });
  }, []);
const additionalClipPath=`polygon(0px 0px, 42.6724% 0px, 42.6724% 2px, 0px 2px, 0px 100%, 100% 100%, 100% 0px, 100% 0px, 100% 0px, 100% 2px, 100% 0px)`
  return (
    <div
      className={`${styles.floatingLabelInput} ${props ? styles[props] : ""}`}
      style={{
        marginBottom: marginBottom ?? "0",
        marginTop: marginTop ?? "1.5rem",
      }}
    >
      {itemLabels && itemLabels?.length > 0 ? (
        <div
          onClick={isDisabled ? iconClick : () => {}}
          className={`${styles.inputText} filediv`}
          ref={divRef}
          style={{
            display: "flex",
            gap: "1rem",
            minWidth: width,
            clipPath: additionalCaseforClippath ? additionalClipPath : "",
            outline: "none",
            backgroundColor: backgroundColor || "", // Add background color functionality for div as well
          }}
        >
          {itemLabels?.map((Component, index) => (
            <>
              <div
                style={{
                  display: "flex",
                  position: "relative",
                  justifyContent: "center",
                  alignItems: "center",
                  
                }}
                className={`${styles.tcr_fileNames} small_text_p_400`}
              >
                <p
                  className="small_text_p_400"
                  style={{ paddingInline: "1rem" }}
                >
                  {Component}
                </p>

                <div
                  className={styles.deleteButtondesc}
                  style={{
                    position: "absolute",
                    right: "-6%",
                    top: "20%",
                    transform: "translateY(-50%)",
                    cursor: "pointer",
                  }}
                  onClick={(e) => {
                    e.stopPropagation();
                    handledelete && handledelete(e, index);
                  }}
                >
                  <DeleteIcon />
                </div>
              </div>
            </>
          ))}
        </div>
      ) : (
        <textarea
          ref={inputRef}
          autoFocus
          className={`${styles.inputText} ${
            id === "search_key" ? styles.search_key : ""
          } ${
            label === "Description" ||
            label === "Solution" ||
            label === "Reference Detail"
              ? styles.fixed_height
              : ""
          } ${isInvalid ? styles.invalid : ""} ${props ? styles[props] : ""} ${
            props2 ? styles[props2] : ""
          }  ${error ? styles.textError : ""}`}
          id={id}
          placeholder={placeholder || " "}
          defaultValue={value ?? inputValue}
          onChange={handleInputChange}
          onKeyPress={handleKeyPress}
          rows={rows}
          onFocus={handleFocusOrClick}
          style={{
            cursor: isDisabled ? "pointer" : "",
            height: label === "Name" ? "53px !important" : "",
            minWidth: width,
            backgroundColor: backgroundColor || "", // Add background color functionality
          }}
          onKeyDown={(e) => {
            if (preventEnter && e.key === "Enter") {
              e.preventDefault();
            }
          }}
          onPaste={onPaste}
          onBlur={handleBlur}
          onClick={isDisabled ? iconClick : () => {}}
          maxLength={maxlength}
          readOnly={isDisabled}
        />
      )}

      <label
        htmlFor={id}
        ref={labelWidthRef}
        style={{ cursor: isDisabled ? "pointer" : "" }}
        className={`${styles.label} ${props ? styles[props] : ""}`}
      >
        {label}
      </label>

      <div className={styles.floatinglabel_icon} onClick={iconClick}>
        {Icon && <Icon />}
      </div>
    </div>
  );
};

export default FloatingLabelInput;
