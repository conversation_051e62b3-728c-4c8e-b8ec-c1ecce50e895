.loader_loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.loader_loading_image {
  width: 500px;
  height: 500px;
}

.add_new_project_cards {
  /* position: relative;
  top: 80px; */
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  margin: 0.7rem 0.7rem 0.7rem 1rem;
}

.add_new_project_container::-webkit-scrollbar {
  width: 3px;
}

.add_new_project_container::-webkit-scrollbar-track {
  border-radius: 10px;
}

.add_new_project_container::-webkit-scrollbar-thumb {
  background: var(--primary-color);
  border-radius: 10px;
}

.add_new_project_header {
  background-color: var(--main_background);
  position: sticky;
  top: 0;
  z-index: 10;
  padding: 0.625rem;
  width: 100%;

}

.add_new_project_container {
  overflow: auto;
  max-height: 86vh;
  min-height: 86vh;
  background-color: var(--main_background);
}


.progress_card_view_container{
  /* overflow-x: scroll; */
  /* background-color: aquamarine; */
  width: 100%;
}




@media (max-width:1800px) {
  .add_new_project_cards {
    grid-template-columns: repeat(3, minmax(25rem, 1fr));
  }
}

@media (max-width:1290px) {
  .add_new_project_cards {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media  (max-width: 1200px) {
  .progress_card_view_container{
    width: fit-content;
  }
  
  .add_new_project_cards {
    grid-template-columns: repeat(3, minmax(25rem, 1fr));
    width: 1780px;
  }
}