/* .addtooltip_container {
    max-width: 1.7rem;
    min-width: 1.7rem;
    height: 0;
    min-height: 2rem;
    display: flex;
    gap: 1rem;
    padding: 0.1rem;
    align-items: center;
} */

.addtooltip_container {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.cover_extra_space > * {
   /* flex-grow: 1; // Allows buttons to take equal space */
}

.addtooltip_sub_container {
  display: flex;
  flex-direction: column;
  gap: 0.2rem;
  position: relative;
  width: fit-content;
  padding: 0.5rem 0.5rem 0.5rem 0.5rem;

}

.addtooltip_header {
  display: flex;
  align-items: center;
  gap: 0.4rem;
  justify-content: space-between;
  width: 100%;
  color: var(--text-black-87);
  max-width: 680px;
}

.addtooltip_header h4 {
  color: var(--text-black-87);
}

.addtooltip_data_container {
  /* flex-direction: column; */
  min-width: 5px;
  width: auto;
  margin-top: 0.5rem;
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  border: 1px solid #00000047;
  border-radius: 24px;
  padding: 1rem;
  max-width: 668px;
  margin-left: 0.5rem;
}
.addtooltip_data_conatiner_column{
  flex-direction: column;
}

.tooltiplusbubble {
  position: relative;
  /* display: flex; */
  align-items: end;
}

/* styles for delete icon in tooltip start by rattandeep singh */
.delete_icon_tooltip {
  position: absolute;
  top: -0.5rem;
  right: 0rem;
  cursor: pointer;
  height: 24px;
  width: 24px;
  background-color: var(--secondary_warning_background);
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 100%;
}
/* styles for delete icon in tooltip end by rattandeep singh */

.width_full{
  width: 100%;
  /* max-width: 690px; */
  padding: 1rem 0rem 0.3rem 1rem;
  /* background-color: red; */
}
.widthfull_1rem{
  width: calc(100% + 1rem);
}

.left_align{
  justify-content: start;
}