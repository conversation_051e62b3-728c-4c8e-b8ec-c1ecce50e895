import React, {
  useEffect,
  useState,
  useRef,
  useLayoutEffect,
  useCallback,
} from "react";
import { useAppSelector } from "../../../../../../redux/hooks/Modules/Reduxhooks/ReduxHooks";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../../../../../redux/store";
import { Loader } from "../../../../../../assets/loader";

import {
  setCategoryLocalDb,
  updatecategoryData,
} from "../../../../../../redux/features/Modules/TaskMaster/Slices/CategorySlice";
import {
  getTime,
  initializeDatabase,
  pathTableMap,
} from "../../../../../../functions/functions";
import {
  closePopup,
  openCategoryPopup,
} from "../../../../../../redux/features/Modules/Reusble/popupSlice";
import { AddCategoryForm } from "../AddCategoryForm/AddcategoryForm";
import { Card } from "../../../../../../components/Reusble/TaskMaster/Card";
import styles from "./CategoryCardView.module.css";
import { CategoryData } from "../../../../../../interfaces/Modules/TaskMaster/TaskMasterInterface/TaskMaster";
import {
  initializeBreadcrumb,
  setNavigate,
} from "../../../../../../redux/features/Modules/Reusble/navigationSlice";
import { DeletePopup } from "../../../../../../components/Reusble/Global/DeletePopup";
import { resetInputValues } from "../../../../../../redux/features/Modules/Reusble/floatinglabelslice";
import { useDeleteTaskCategoryMutation } from "../../../../../../redux/api/Modules/TaskMaster/TaskMasterapi";
import { TaskCategory } from "../../../../../../Backup/TaskMasterBackup";
import { usePouchSearch } from "../../../../../../functions/useLocalSearch";
import { useLocation } from "react-router-dom";
import { useToast } from "../../../../../../hooks/ToastHook";
import {
  clearFetchedMasters,
  setFetchedMasters,
  setSearchData,
} from "../../../../../../redux/features/Modules/Masters";
import { setSearchKey } from "../../../../../../redux/features/Modules/TaskMaster/Slices/TaskMasterSlice";

const categoryFields = [
  {
    label: "CategoryName",
  },
  {
    label: "Description",
  },
];

const CategoryCardView: React.FC = () => {
  const deleted = useAppSelector((state) => state.isDeletedSLice.isDeleted);
  const searchKey = useAppSelector((state) => state.taskMaster.searchKey);
  const [searchLocalKey, setSearchLocalKey] = useState("");
  const [searchCategoriesData, setSearchCategoriesData] = useState([]);
  const showToast = useToast();
  console.log(searchCategoriesData, "searchCategoriesData");
  const [deleteTaskCategory] = useDeleteTaskCategoryMutation();
  const dispatch = useDispatch();
 // const Data = useSelector(
  //   (state: RootState) => state.categoryLocal.categories
  // ) as CategoryData[];
 
  const searchedData = useSelector((state: RootState) => state.masterReduxSlice.searchedData)

  const [page, setPage] = useState(1);
  const categoryData = useSelector(
    (state: RootState) => state.masterReduxSlice.fetchedTaskCat
  );

  console.log("category data view", categoryData);

  const inputValues = useAppSelector(
    (state) => state.floatingLabel.inputValues
  );

  useEffect(() => {
    if (searchKey) {
      setSearchLocalKey(searchKey);
    } else {
      setSearchLocalKey("");
    }
  }, [searchKey]);

  console.log("search dataa", searchLocalKey);

  useEffect(() => {
    dispatch(closePopup("DeleteCatPopUp"));
    dispatch(resetInputValues());
  }, []);

  useEffect(() => {
    dispatch(
      initializeBreadcrumb({
        route: "/category",
        title: "Categories",
      })
    );
    if (deleted) {
      dispatch(
        setNavigate({
          title: "Deleted",
          route: "/category/#",
        })
      );
    }
  }, []);

  console.log("data for category cards", categoryData);

  const currentOpenPopup = useSelector(
    (state: RootState) => state.popup.popups
  );

  const allFetchedCategoriesRef = useRef<any[]>([]);

  const saveData = useCallback(async () => {
    try {
      const dbName = await initializeDatabase("TaskCategory");
      const fetchedData = await window.electron.bulkGet({
        dbName,
        deleted,
        page,
      });
      // const date = await getTime("TaskCategory");

      console.log("fetcheddataaaaaaaa", fetchedData);

      if (page === 1) {
        allFetchedCategoriesRef.current = fetchedData.docs;
        dispatch(
          setFetchedMasters({
            data: allFetchedCategoriesRef.current,
            page: page,
            type: "taskcategory",
          })
        );
      } else {
        const newDocs = fetchedData.docs.filter(
          (doc: any) =>
            !allFetchedCategoriesRef.current.some(
              (existing) => existing._id === doc._id
            )
        );
        allFetchedCategoriesRef.current = [
          ...allFetchedCategoriesRef.current,
          ...newDocs,
        ];
        dispatch(
          setFetchedMasters({
            data: allFetchedCategoriesRef.current,
            page: page,
            type: "taskcategory",
          })
        );
      }
    } catch (error) {
      console.error("Error in saving data", error);
    }
  }, [page, deleted]);

  useEffect(() => {
    return () => {
      dispatch(clearFetchedMasters());
    };
  }, []);

  useEffect(() => {
    dispatch(clearFetchedMasters());
  }, []);

  useEffect(() => {
    saveData();
  }, [page, deleted]);

  const handlePopupClick = (id: string) => {
    dispatch(openCategoryPopup(id));
  };

  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const target = e.target as HTMLDivElement;

    if (target) {
      const { scrollHeight, clientHeight, scrollTop } = target;

      if (scrollTop + clientHeight >= scrollHeight - 1) {
        setPage((prev) => prev + 1);
      }
    }
  };

  useEffect(() => {
    setPage(1);
  }, [deleted]);

  usePouchSearch({
    pathTableMap,
    isDeleted: deleted,
    searchKey: searchLocalKey,
    setData: setSearchData,
    setPage,
    key: "categoryName",
  });
  return (
    <>
      <div
        // ref={containerRef}
        onScroll={(e) => handleScroll(e)}
        className={`${styles.taskContainer_items} scroll`}
        // style={{ scrollBehavior: "smooth" }} // Apply smooth scrolling
      >
        {categoryData?.filter((category) =>
          deleted ? category?.isDeleted : !category?.isDeleted
        ).length > 0 ? (
          (searchedData && searchedData.length > 0
            ? searchedData
            : categoryData || []
          )
            .filter((category) =>
              deleted ? category?.isDeleted : !category?.isDeleted
            )
            .map((data, index) => {
              const categoryId = data?._id;
              if (!categoryId) return null;

              return (
                <React.Fragment key={categoryId}>
                  {currentOpenPopup[categoryId] && (
                    <AddCategoryForm
                      onClose={() => dispatch(closePopup(categoryId))}
                      catId={categoryId}
                    />
                  )}
                  <Card
                    path={`/category/${categoryId}`}
                    category={data}
                    isApprove={true}
                    id={categoryId}
                    Callback={() => handlePopupClick(categoryId)}
                  />
                </React.Fragment>
              );
            })
        ) : (
          <div className={styles.loader_loading}>
            <img
              src={Loader.suryaconLogo}
              alt="Loading..."
              className={styles.loader_loading_image}
            />
          </div>
        )}
      </div>
      {currentOpenPopup["DeleteCatPopUp"] && (
        <DeletePopup
          height="calc(100% - 9rem)"
          //this is callback for delete button
          callbackDelete={async () => {
            const res = await deleteTaskCategory({
              categoryId: inputValues?.catId,
            });

            if (res.error) {
              showToast({
                messageContent: "Oops! Something went wrong!",
                type: "danger",
              });

              return;
            }
            showToast({
              messageContent: `Category Deleted Successfully!`,
              type: "success",
            });
          }}
          //this is callback for cross button
          onClose={() => {
            dispatch(closePopup("DeleteCatPopUp"));
            dispatch(resetInputValues());
          }}
          //this is header for the deletepopup
          header={`Are you sure you want to delete this Category?`}
        >
          {/* here pass the children of the deletepopup */}
          <div
            style={{
              display: "flex",
              flexWrap: "wrap",
              flexDirection: "column",
            }}
          >
            {categoryFields.map(
              (item) =>
                inputValues[item.label] && (
                  <div key={item.label}>
                    <div
                      className={styles.summaryDivData}
                      style={{ width: "100%" }}
                    >
                      <div className={styles.summaryDataContent}>
                        <p
                          className="p_tag_14px_weight"
                          style={{ color: "#444444" }}
                        >
                          {item.label === "CategoryName" ? "Name" : item.label}
                        </p>
                        <h4 style={{ color: "#191919", marginTop: "0.3rem" }}>
                          {inputValues[item.label]}
                        </h4>
                      </div>
                    </div>
                  </div>
                )
            )}
          </div>
        </DeletePopup>
      )}
    </>
  );
};

export default CategoryCardView;
