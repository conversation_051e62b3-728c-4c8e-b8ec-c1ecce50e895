import React, { <PERSON> } from "react";
import styles from "../Styles/Manpower.module.css";
import { isValidValue } from "../../../../../../functions/functions";

interface ManpowerSummaryProps {
  formData: any;
  initialFormData: any;
  formMode: any;
  deletedToolData?: any;
  //   deletedGradeData: Array<Record<number, any[]>>;
  //   deletedFormData: any;
}

const ManpowerSummary: FC<ManpowerSummaryProps> = ({
  formData,
  initialFormData,
  formMode,
  deletedToolData,
  //   deletedGradeData,
  //   deletedFormData,
}) => {
  return (
    <div
      className={`${styles.summary_container}`}
      style={{ display: "flex", flexDirection: "column" }}
    >
      {formData?.type && isValidValue(formData?.type) && (
        <div className={styles.summaryDivData}>
          <div className={styles.summaryDataContent}>
            <p style={{ color: "var(--text-black-60)" }} className="p_tag_14px">
              Type
            </p>
            <h4
              style={{
                color:
                  formMode === "Edit" &&
                  formData?.type?.trim() !== initialFormData?.type?.trim()
                    ? "var(--secondary_color)"
                    : "var(--text-black-87)",
                marginTop: "0.3rem",
              }}
            >
              {formData?.type?.charAt(0).toUpperCase() +
                formData?.type?.slice(1)}
            </h4>
          </div>
        </div>
      )}
      {formData?.Name && isValidValue(formData?.Name) && (
        <div className={styles.summaryDivData}>
          <div className={styles.summaryDataContent}>
            <p style={{ color: "var(--text-black-60)" }} className="p_tag_14px">
              Name
            </p>
            <h4
              style={{
                color:
                  formMode === "Edit" &&
                  formData?.Name?.trim() !== initialFormData?.Name?.trim()
                    ? "var(--secondary_color)"
                    : "var(--text-black-87)",
                marginTop: "0.3rem",
              }}
            >
              {formData?.Name}
            </h4>
          </div>
        </div>
      )}
      {(formData?.Description || initialFormData?.Description?.trim()) &&
        isValidValue(formData?.Description || initialFormData?.Description) && (
          <div className={styles.summaryDivData}>
            <div className={styles.summaryDataContent}>
              <div>
                <p
                  style={{ color: "var(--text-black-60)" }}
                  className="p_tag_14px"
                >
                  Description
                </p>
                <h4
                  style={{
                    color:
                      formMode === "Edit" &&
                      formData?.Description?.trim() !==
                        initialFormData?.Description?.trim()
                        ? "var(--secondary_color)"
                        : "var(--text-black-87)",
                    marginTop: "0.3rem",
                  }}
                >
                  {formData?.Description}
                </h4>
                {formMode === "Edit" &&
                  !formData?.Description?.trim() &&
                  initialFormData?.Description?.trim() && (
                    <h4
                      style={{
                        color: "var(--warning_color)",
                        marginTop: "0.3rem",
                      }}
                    >
                      {initialFormData?.Description}
                    </h4>
                  )}
              </div>
            </div>
          </div>
        )}
      {formData?.Skills &&
        formData?.Skills?.length > 0 &&
        formData?.Skills?.[0]?.trim() && (
          <>
            <div className={styles.summaryDivData}>
              <div className={styles.summaryDataContent}>
                <p
                  style={{ color: "var(--text-black-60)" }}
                  className="p_tag_14px"
                >
                  Skills
                </p>
                <div
                  style={{
                    color: "var(--text-black-87)",
                    display: "flex",
                    marginTop: "0.3rem",
                    gap: "0.3rem 2rem",
                    flexWrap: "wrap",
                  }}
                >
                  {formData?.Skills?.map((item: any, index: number) => (
                    <h4
                      style={{
                        color:
                          formMode === "Edit" &&
                          !initialFormData?.Skills?.includes(item)
                            ? "var(--secondary_color)"
                            : "var(--text-black-87)",
                      }}
                    >
                      {item}
                    </h4>
                  ))}
                  {deletedToolData &&
                    deletedToolData?.map((item: any, index: number) => (
                      <h4
                        style={{
                          color:
                            formMode === "Edit" ? "var(--warning_color)" : "",
                        }}
                      >
                        {item}
                      </h4>
                    ))}
                </div>
              </div>
            </div>
          </>
        )}
    </div>
  );
};

export default ManpowerSummary;
