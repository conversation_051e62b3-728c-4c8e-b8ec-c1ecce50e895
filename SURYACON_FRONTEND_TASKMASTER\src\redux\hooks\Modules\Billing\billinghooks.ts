import {
  useAddProjectMutation,
  useAddTowerLocationMutation,
  useLazyGetAllProjectQuery,
} from "../../../api/Modules/Billing/Billingapi";
import {
  useGetAllSubtaskForTaskRouteForPlanningQuery,
  useGetSubTaskDetailByIdQuery,
  useGetSubTaskOfTaskRouteQuery,
  useGetTowerRoutesQuery,
  useLazyGetSubTaskDetailByIdQuery,
  useUpdateLocationDetailsMutation,
  useSendApprovalByhodMutation,
} from "../../../api/Modules/Billing/ProjectPlanningApi";

export const useAddProjectApi = () => {
  return useAddProjectMutation();
};
export const useGetAllProjectApi = () => {
  return useLazyGetAllProjectQuery();
};
export const useAddTowerLocation = () => {
  return useAddTowerLocationMutation();
};
export const getTowerLocationByObjectId = (id: string) => {
  return useGetTowerRoutesQuery({ projectId: id });
};
export const getLocationTaskSubTask = (locationId: string, taskId: string) => {
  return useGetSubTaskOfTaskRouteQuery({
    locationId: locationId,
    taskId: taskId,
  });
};
export const getLocationTaskSubTaskDetail = (SubTaskId: string) => {
  return useGetSubTaskDetailByIdQuery({ SubTaskId: SubTaskId });
};
export const getAllSubtaskForTaskRouteForPlanning = (
  locationId: string,
  taskId: string
) => {
  return useGetAllSubtaskForTaskRouteForPlanningQuery({ locationId, taskId });
};
export const updateLocationDetails = (data: any) => {
  return useUpdateLocationDetailsMutation;
};
export const useSendApprovalByhod = () => {
  return useSendApprovalByhodMutation();
};