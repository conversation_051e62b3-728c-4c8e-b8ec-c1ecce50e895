import React, { <PERSON> } from "react";
import {
  isValidValue,
  slicedData,
} from "../../../../../../functions/functions";
import styles from "../Styles/Machinery.module.css";

interface MachinerySummaryProps {
  formData: any;
  initialFormData: any;
  formMode: any;
  deletedGradeData: Array<Record<number, any[]>>;
  deletedFormData: any;
  deletedToolData?: any;
}

const MachinerySummary: FC<MachinerySummaryProps> = ({
  formData,
  initialFormData,
  formMode,
  deletedGradeData,
  deletedFormData,
  deletedToolData,
}) => {
  return (
    <div style={{ display: "flex", flexDirection: "column" }}>
      <div style={{ display: "flex", flexWrap: "wrap" }}>
        {formData?.Name && isValidValue(formData?.Name) && (
          <div
            className={styles.summaryDivData}
            style={{ width: formData?.Photo?.name ? "50%" : "100%" }}
          >
            <div className={styles.summaryDataContent}>
              <p
                style={{ color: "var(--text-black-60)" }}
                className="p_tag_14px"
              >
                Name
              </p>
              <h4
                style={{
                  color:
                    formMode === "Edit" &&
                    formData?.Name?.trim() !== initialFormData?.Name?.trim()
                      ? "var(--secondary_color)"
                      : "var(--text-black-87)",
                  marginTop: "0.3rem",
                }}
              >
                {formData?.Name}
              </h4>
            </div>
          </div>
        )}
        {formData?.Photo?.name && isValidValue(formData?.Photo?.name) && (
          <div
            className={styles.summaryDivData}
            style={{ width: formData?.Name ? "50%" : "100%" }}
          >
            <div className={styles.summaryDataContent}>
              <p
                style={{ color: "var(--text-black-60)" }}
                className="p_tag_14px"
              >
                Cover Photo
              </p>
              <h4
                style={{
                  color:
                    formMode === "Edit" &&
                    formData?.Photo?.name.trim() !==
                      initialFormData?.Photo?.name?.trim()
                      ? "var(--secondary_color)"
                      : "var(--text-black-87)",
                  marginTop: "0.3rem",
                }}
              >
                {slicedData(formData?.Photo?.name, 14)}
              </h4>
            </div>
          </div>
        )}
      </div>
      {(formData?.Description ||
        (formMode === "Edit" && initialFormData?.Description?.trim())) &&
        isValidValue(
          formData?.Description ||
            (formMode === "Edit" && initialFormData?.Description)
        ) && (
          <div className={styles.summaryDivData}>
            <div className={styles.summaryDataContent}>
              <div>
                <p
                  style={{ color: "var(--text-black-60)" }}
                  className="p_tag_14px"
                >
                  Description
                </p>
                <h4
                  style={{
                    color:
                      formMode === "Edit" &&
                      formData?.Description?.trim() !==
                        initialFormData?.Description?.trim()
                        ? "var(--secondary_color)"
                        : "var(--text-black-87)",
                    marginTop: "0.3rem",
                  }}
                >
                  {formData?.Description}
                </h4>
                {formMode === "Edit" &&
                  !formData?.Description?.trim() &&
                  initialFormData?.Description?.trim() && (
                    <h4
                      style={{
                        color: "var(--warning_color)",
                        marginTop: "0.3rem",
                      }}
                    >
                      {initialFormData?.Description}
                    </h4>
                  )}
              </div>
            </div>
          </div>
        )}
      {formData?.Brands &&
        formData?.Brands?.length > 0 &&
        formData?.Brands?.[0]?.brand?.name?.trim() && (
          <>
            <h4 style={{ margin: "0.6rem" }}>Brand</h4>
            {formData?.Brands?.map((item: any, index: number) => (
              <div className={styles.summaryDivData}>
                <div
                  className={`${
                    item?.brand?.name || item?.grade?.length
                      ? styles.summaryDataContent
                      : ""
                  }`}
                >
                  {item?.brand?.name?.trim() && (
                    <>
                      <p
                        style={{ color: "var(--text-black-60)" }}
                        className="p_tag_14px"
                      >
                        Name
                      </p>
                      <h4
                        style={{
                          color:
                            formMode === "Edit" &&
                            formData?.Brands?.find(
                              (i: any) => i?._id === item?._id
                            )?.brand?.name?.trim() !==
                              initialFormData?.Brands?.find(
                                (i: any) => i?._id === item?._id
                              )?.brand?.name?.trim()
                              ? "var(--secondary_color)"
                              : "var(--text-black-87)",
                          marginTop: "0.3rem",
                        }}
                      >
                        {item?.brand?.name}
                      </h4>
                    </>
                  )}
                  {item?.Grade?.length > 0 && (
                    <p
                      style={{
                        color: "var(--text-black-60)",
                        marginTop: "0.3rem",
                      }}
                      className="p_tag_14px"
                    >
                      Grades
                    </p>
                  )}
                  {item?.Grade.length > 0 && (
                    <>
                      <div
                        style={{
                          color: "var(--text-black-87)",
                          display: "flex",
                          marginTop: "0.3rem",
                          gap: "0.3rem 2rem",
                          flexWrap: "wrap",
                        }}
                      >
                        {item?.Grade?.map((gradeItem: any, i: number) => (
                          <h4
                            style={{
                              color:
                                formMode === "Edit" &&
                                !initialFormData?.Brands?.find(
                                  (brandItem: any) =>
                                    brandItem?._id === item?._id
                                )?.Grade?.includes(gradeItem)
                                  ? "var(--secondary_color)"
                                  : "var(--text-black-87)",
                            }}
                          >
                            {gradeItem}
                          </h4>
                        ))}
                        {deletedGradeData &&
                          deletedGradeData
                            ?.filter(
                              (gradeItem: any, i: any) => gradeItem[item?._id!]
                            )?.[0]
                            ?.[item?._id!]?.map((grade: any, i: any) => (
                              <h4
                                style={{
                                  color: "var(--warning_color)",
                                }}
                              >
                                {!formData?.Brands?.find(
                                  (i: any) => i?._id === item?._id
                                ).Grade.includes(grade) && grade}
                              </h4>
                            ))}
                      </div>
                    </>
                  )}
                </div>
              </div>
            ))}
            {deletedFormData?.map((item: any, index: any) => (
              <div className={styles.summaryDivData}>
                <div className={styles.summaryDataContent}>
                  <p
                    style={{ color: "var(--text-black-60)" }}
                    className="p_tag_14px"
                  >
                    Name
                  </p>
                  <h4
                    style={{
                      color: "var(--warning_color)",
                      marginTop: "0.3rem",
                    }}
                  >
                    {item?.brand?.name}
                  </h4>
                  {formData?.Brands?.[0]?.Grade &&
                    formData?.Brands?.[0]?.Grade.length > 0 &&
                    formData?.Brands?.[0]?.Grade?.[0] && (
                      <>
                        {" "}
                        <p
                          style={{
                            color: "var(--text-black-60)",
                            marginTop: "0.3rem",
                          }}
                          className="p_tag_14px"
                        >
                          Grades
                        </p>
                        <div
                          style={{
                            display: "flex",
                            marginTop: "0.3rem",
                            gap: "0.3rem 2rem",
                            flexWrap: "wrap",
                          }}
                        >
                          {item?.Grade?.map((item: any, i: any) => (
                            <h4
                              style={{
                                color: "var(--warning_color)",
                              }}
                            >
                              {item}
                            </h4>
                          ))}
                        </div>
                      </>
                    )}
                </div>
              </div>
            ))}
          </>
        )}
      {(formData?.Tools && formData?.Tools?.length > 0) ||
      (deletedToolData?.length > 0 && formMode !== "Add") ? (
        <>
          <div className={styles.summaryDivData}>
            <div className={styles.summaryDataContent}>
              <p
                style={{ color: "var(--text-black-60)" }}
                className="p_tag_14px"
              >
                Tools
              </p>
              <div
                style={{
                  color: "var(--text-black-87)",
                  display: "flex",
                  marginTop: "0.3rem",
                  gap: "0.3rem 2rem",
                  flexWrap: "wrap",
                }}
              >
                {formData?.Tools?.map((item: any, index: number) => (
                  <h4
                    style={{
                      color:
                        formMode === "Edit" &&
                        !initialFormData?.Tools?.find(
                          (toolItem: any) => toolItem._id === item._id
                        )?.name
                          ? "var(--secondary_color)"
                          : "var(--text-black-87)",
                    }}
                  >
                    {item?.name}
                  </h4>
                ))}
                {deletedToolData &&
                  deletedToolData?.map((item: any, index: number) => (
                    <h4
                      style={{
                        color:
                          formMode === "Edit" ? "var(--warning_color)" : "",
                      }}
                    >
                      {item?.name}
                    </h4>
                  ))}
              </div>
            </div>
          </div>
        </>
      ) : (
        ""
      )}

      {formData?.Fuel && isValidValue(formData?.Fuel) && (
        <div className={styles.summaryDivData}>
          <div className={styles.summaryDataContent}>
            <div>
              <p
                style={{ color: "var(--text-black-60)" }}
                className="p_tag_14px"
              >
                Fuel Type
              </p>
              <h4
                style={{
                  color:
                    formMode === "Edit" &&
                    formData?.Fuel?.trim() !== initialFormData?.Fuel?.trim()
                      ? "var(--secondary_color)"
                      : "var(--text-black-87)",
                  marginTop: "0.3rem",
                  textTransform: "capitalize",
                }}
              >
                {formData?.Fuel}
              </h4>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MachinerySummary;
