// Author <PERSON><PERSON><PERSON>
import React, { useEffect, useState, useCallback, useMemo } from "react";
import styles from "../Styles/PlanningTable.module.css";
import { useDispatch, useSelector } from "react-redux";
import { RootState, store } from "../../../../../../../redux/store";
import Circle from "../../../../../../../components/Reusble/Billing/Circle";
import {
  DeleteIcon,
  PercentageIcon,
  SuryconLogo,
} from "../../../../../../../assets/icons";
import { SubtaskCardProps } from "../../../../../../../interfaces/Modules/Billing/ProjectPlanning/ProjectPlanning";
import {
  deleteSubtaskInPlanning,
  selectLocationSubTaskId,
  setallSubtasksData,
  setAllTaskBasicDetails,
  setSubtaskInPlanning,
  setEditMode,
} from "../../../../../../../redux/features/Modules/Billing/ProjectPlanning/Slices/projectPlanningSlice";
import AddToolTip from "../../../../../../../components/Reusble/Global/AddToolTip";
import {
  closePopup,
  openPopup,
} from "../../../../../../../redux/features/Modules/Reusble/popupSlice";
import AddCategoryType from "../../../../../../../components/Reusble/Global/AddCategoryType";
import {
  useGetSubtasksRouteToListQuery,
  useLazyGetSubTaskDetailByIdQuery,
} from "../../../../../../../redux/api/Modules/Billing/ProjectPlanningApi";
import { DeletePopup } from "../../../../../../../components/Reusble/Global/DeletePopup";
import { initializeDatabase } from "../../../../../../../functions/functions";
import { useParams } from "react-router-dom";
import { saveSyncData } from "../../../../../../../Backup/BackupFunctions/BackupFunctions";
import { updateMaterialTable } from "../../../../../../../Backup/BillingBackup";

const SkeletonBox: React.FC<{
  width?: string;
  height?: string;
  className?: string;
}> = ({
  width = "230px",
  height = "100px",
  className = styles.skeleton_box,
}) => <span className={className} style={{ width, height }} />;

const SubtaskCard: React.FC<
  SubtaskCardProps & { expandedSubtaskId?: string }
> = ({ edit, onSubtaskClick, expandedSubtaskId }) => {
  const dispatch = useDispatch();
  const { towerLocationId: objectId } = useParams();
  const [subtasks, setSubtasks] = useState<any[]>([]);
  const [transformedSubtasks, setTransformedSubtasks] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const [deleteItem, setDeleteItem] = useState<any>({});
  const [deleteItemId, setDeleteItemId] = useState<any>("");
  const [isTransitioning, setIsTransitioning] = useState(false);
  const { selectedLocationTaskIdData } = useSelector(
    (state: RootState) => state.projectPlanning
  );
  const selectedLocationTaskId = useSelector(
    (state: RootState) => state.projectPlanning.selectedLocationTaskId
  );
  const { popups } = useSelector((state: RootState) => state.popup);
  const allSubtaskData = useSelector(
    (state: RootState) => state.projectPlanning.allSubtasksdata
  );
  console.log("allSubtaskData", allSubtaskData);
  const allsubtaksBasicDetails = useSelector(
    (state: RootState) => state.projectPlanning.allSubtasksBasicDetails
  );
  const globalEditMode = useSelector(
    (state: RootState) => state.projectPlanning.editMode
  );
  const [taskMasterId, setTaskMasterId] = useState<string | null>(null);
  const isEdit = typeof edit === "boolean" ? edit : globalEditMode;
  const [getsubtasksData] = useLazyGetSubTaskDetailByIdQuery();
  const [deleteItemFullData, setDeleteItemFullData] = useState<any>(null);

  // Fetch subtasks from local DB when selectedLocationTaskId changes
  useEffect(() => {
    let isMounted = true;

    const fetchSubtaskBasicDetails = async () => {
      dispatch(setAllTaskBasicDetails([]));
      setSubtasks([]);

      if (!selectedLocationTaskId) {
        setIsLoading(false);
        return;
      }

      setIsLoading(true);
      setIsTransitioning(true);

      try {
        const dbname = await initializeDatabase("SubTasksBasicDetails");
        const subtaskBasicDetails = await window.electron.getDocumentByParentId(
          {
            categoryId: "task_id",
            dbName: dbname,
            catId: selectedLocationTaskId,
          }
        );

        console.log(subtaskBasicDetails, "Fetched SubTasksBasicDetails");

        if (isMounted) {
          dispatch(setAllTaskBasicDetails(subtaskBasicDetails));
          setSubtasks(subtaskBasicDetails);

          if (subtaskBasicDetails?.length > 0) {
            const taskMasterId =
              subtaskBasicDetails[0]?.subtaskId?.TaskmasterId;
            if (taskMasterId) {
              console.log(taskMasterId, "TaskMasterId set from first subtask");
              setTaskMasterId(taskMasterId);
            } else {
              console.error("TaskMasterId not found in subtaskBasicDetails");
            }
          }
        }
      } catch (error) {
        console.error("Error fetching SubTasksBasicDetails:", error);
        if (isMounted) {
          setSubtasks([]);
          dispatch(setAllTaskBasicDetails([]));
        }
      } finally {
        if (isMounted) {
          setIsLoading(false);
          setIsTransitioning(false);
        }
      }
    };

    fetchSubtaskBasicDetails();
    return () => {
      isMounted = false;
      setSubtasks([]);
      dispatch(setAllTaskBasicDetails([]));
      setIsTransitioning(false);
      setIsLoading(false);
    };
  }, [selectedLocationTaskId, dispatch]);

  // Select first subtask on mount if available
  useEffect(() => {
    if (selectedLocationTaskIdData && subtasks.length > 0) {
      const id = subtasks[0]?._id || null;
      dispatch(selectLocationSubTaskId(id));
      // setSelected(id);
    }
    // eslint-disable-next-line
  }, [selectedLocationTaskIdData, subtasks.length]);

  // useEffect(() => {
  //   if (expandedSubtaskId && expandedSubtaskId !== selected) {
  //     setSelected(expandedSubtaskId);
  //     dispatch(selectLocationSubTaskId(expandedSubtaskId));
  //   }
  // }, [expandedSubtaskId, dispatch]);

  //  useEffect(() => {
  //   if (subtasks && subtasks.length > 0) {
  //     setSelected(subtasks[0]?._id);
  //     dispatch(selectLocationSubTaskId(subtasks[0]?._id));
  //   }
  //   // eslint-disable-next-line
  // }, [subtasks]);

  // Handle subtask selection
  const handleSelect = useCallback(
    (subtaskId: string) => {
      // setSelected(subtaskId);
      dispatch(selectLocationSubTaskId(subtaskId));
      if (onSubtaskClick) onSubtaskClick(subtaskId);
    },
    [dispatch, onSubtaskClick]
  );
  const currentSelectedData = useSelector(
    (state: RootState) => state.projectPlanning.curretSelectedData
  );
  useEffect(() => {
    console.log("taskMasterId changed:", taskMasterId);
    const fetchTaskMasterSubtasks = async () => {
      if (!taskMasterId) return;

      try {
        const dbName = await initializeDatabase("TaskForm");
        const taskMasterSubtasks = await window.electron.getDocumentByParentId({
          categoryId: "_id",
          dbName,
          catId: taskMasterId,
        });
        console.log(taskMasterSubtasks, "taskmaster ka data");

        if (taskMasterSubtasks && taskMasterSubtasks.length > 0) {
          const transformed = taskMasterSubtasks
            .flatMap((subtask: any) =>
              subtask?.Subtaskdetails?.map((detail: any) => ({
                category: detail?.name ?? detail?.subtaskId?.name,
                id: detail?._id ?? detail?.subtaskId?._id,
                Description: detail?.Description,
                Unit: detail?.Unit,
                Tracking: detail?.Tracking,
                subtaskWeighatages: detail?.subtaskWeighatages,
              }))
            )
            .filter((detail: any) => detail);
          setTransformedSubtasks(transformed);
          console.log(transformed, "Transformed TaskMaster Subtasks");
        }
      } catch (error) {
        console.error("Error fetching TaskMaster subtasks:", error);
      }
    };

    fetchTaskMasterSubtasks();
  }, [taskMasterId]);

  const manualSubtaskData = (item: any[]): any => {
    return {
      _id: Date.now().toString(),
      subtaskId: {
        _id: item[0]?.id,
        Description: item[0]?.Description,
        TaskmasterId: taskMasterId,
        Tracking: item[0]?.Tracking,
        Unit: [item[0]?.Unit[0]],
        id: item[0]?.id,
        itemtype: "subtaskmodal",
        name: item[0]?.category,
        subtaskWeighatages: item[0]?.subtaskWeighatages,
      },
      isCompleted: false,
      quantity: 0,
      task_id: selectedLocationTaskId,
      weightage: item[0]?.subtaskWeighatages,
    };
  };

  const handleAddSubtask = useCallback(
    async (item: any[]) => {
      console.log("Selected Subtask for adding:", item[0]);

      try {
        const subtaskToInsert = item;
        if (!subtaskToInsert) {
          throw new Error("No subtask data provided");
        }

        const transformedData = manualSubtaskData(subtaskToInsert);

        const dbname = await initializeDatabase("SubTasksBasicDetails");

        const docsToInsert = Array.isArray(transformedData)
          ? transformedData
          : [transformedData];

        await window.electron.bulkInsert({
          db: dbname,
          docs: docsToInsert,
          time: new Date().toISOString(),
        });

        const dbName = await initializeDatabase("SubTaskForm");
        const subtaskId = item[0]?.id;
        const fetchedData = await window.electron.getDataById({
          dbName,
          id: subtaskId,
        });
        console.log("FetchedData:", fetchedData);
        console.log("ManpowerId:", fetchedData?.ManpowerId);
        console.log("MachinaryId:", fetchedData?.MachinaryId);
        console.log("MaterialId:", fetchedData?.MaterialId);
        console.log("ToolId:", fetchedData?.ToolId);

        if (fetchedData) {
          const subtasklocDetailDb = await initializeDatabase(
            "SubtasklocDetail"
          );

          const now = new Date().toISOString();
          const generateUniqueId = () => {
            return (
              String(Date.now()).slice(0, 10) +
              Math.floor(Math.random() * 1000)
                .toString()
                .padStart(3, "0")
            );
          };
          const transformManpowerData = (arr: any[] = []) => {
            const result: any[] = [];
            arr.forEach((item) => {
              ["skilled", "unskilled"].forEach((type) => {
                result.push({
                  Mastermanpower_id: {
                    Types: [type],
                    itemtype: "manpowerdetailed",
                    name: item.name || "",
                    _id: item._id || "",
                  },
                  quantity: 0,
                  _id: generateUniqueId(),
                  __v: 0,
                  type,
                });
              });
            });
            return result;
          };
          const transformMachineryData = (arr: any[] = []) => {
            const result: any[] = [];
            arr.forEach((item) => {
              (item.BrandDetails || []).forEach((brandDetail: any) => {
                const brand = brandDetail.BrandId?.[0] || {};
                (brandDetail.Specs || []).forEach((spec: any) => {
                  result.push({
                    Mastermachinary_id: {
                      itemtype: "machinaryDesignation",
                      name: item.name || "",
                      _id: item._id || "",
                    },
                    quantity: 0,
                    spec,
                     _id: generateUniqueId(),
                    createdAt: now,
                    updatedAt: now,
                    __v: 0,
                    Masterbrand_id: {
                      Brandname: brand.Brandname || "",
                      _id: brand._id || "",
                      createdAt: now,
                      updatedAt: now,
                    },
                  });
                });
              });
            });
            return result;
          };
          const transformMaterialData = (arr: any[] = []) => {
            const result: any[] = [];
            arr.forEach((item) => {
              (item.BrandDetails || []).forEach((brandDetail: any) => {
                const brand = brandDetail.BrandId?.[0] || {};
                (brandDetail.Specs || item.Specs || []).forEach((spec: any) => {
                  result.push({
                    Mastermaterial_id: {
                      itemtype: "materialdetailed",
                      materialCategoryId: {
                        _id: item.materialCategoryId?._id || "",
                        itemtype: "materialcategory",
                        name: item.materialCategoryId?.name || "",
                      },
                      name: item.name || "",
                      unit: item.unit || [],
                      _id: item._id || "",
                    },
                    quantity: 0,
                    spec,
                    _id: generateUniqueId(),
                    createdAt: now,
                    updatedAt: now,
                    __v: 0,
                    Masterbrand_id: {
                      Brandname: brand.Brandname || "",
                      _id: brand._id || "",
                      createdAt: now,
                      updatedAt: now,
                    },
                  });
                });
              });
            });
            return result;
          };
          const transformToolData = (arr: any[] = []) => {
            const result: any[] = [];
            arr.forEach((tool) => {
              (tool.BrandDetails || []).forEach((brandDetail: any) => {
                const brand = brandDetail.BrandId?.[0] || {};
                (brandDetail.Specs || tool.Specs || []).forEach((spec: any) => {
                  result.push({
                    MasterTool_id: {
                      itemtype: "tooldetailed",
                      name: tool.name || "",
                      _id: tool._id || "",
                    },
                    quantity: 0,
                    spec,
                    createdAt: now,
                    updatedAt: now,
                    __v: 0,
                    _id: generateUniqueId(),
                    Masterbrand_id: {
                      Brandname: brand.Brandname || "",
                      _id: brand._id || "",
                      createdAt: now,
                      updatedAt: now,
                    },
                  });
                });
              });
            });
            return result;
          };

          const subtaskLocDetailDoc = {
            _id: docsToInsert[0]?._id,
            createdAt: now,
            updatedAt: now,
            isCompleted: false,
            quantity: 0,
            weightage: docsToInsert[0]?.weightage || 0,
            subtaskId: docsToInsert[0]?.subtaskId,
            manpowerId: transformManpowerData(fetchedData?.[0]?.ManpowerId),
            machinaryId: transformMachineryData(fetchedData?.[0]?.MachinaryId),
            materialId: transformMaterialData(fetchedData?.[0]?.MaterialId),
            tool_id: transformToolData(fetchedData?.[0]?.ToolId || []),
            taskId: selectedLocationTaskId,
            __v: 0,
          };
          await window.electron.bulkInsert({
            db: subtasklocDetailDb,
            docs: [subtaskLocDetailDoc],
            time: now,
          });

          console.log("data>>>>>>>>>", subtaskLocDetailDoc);
        }

        setSubtasks((prevSubtasks) =>
          Array.isArray(prevSubtasks)
            ? [...prevSubtasks, ...docsToInsert]
            : docsToInsert
        );
        dispatch(
          setAllTaskBasicDetails(
            Array.isArray(allsubtaksBasicDetails)
              ? [...allsubtaksBasicDetails, ...docsToInsert]
              : docsToInsert
          )
        );

        const currentSubtasks =
          objectId && selectedLocationTaskId
            ? (allSubtaskData as any)?.[objectId]?.[selectedLocationTaskId] ||
              []
            : [];

        dispatch(
          setallSubtasksData({
            towerId: objectId,
            taskId: selectedLocationTaskId,
            subtasks: [...currentSubtasks, ...docsToInsert],
          })
        );

        dispatch(setSubtaskInPlanning({ subtask: docsToInsert[0] } as any));
        dispatch(closePopup("AddSubTaskPlanning"));
      } catch (error) {
        console.error(
          "Error adding subtask to SubTasksBasicDetails table:",
          error
        );
      }
    },
    [dispatch, objectId, selectedLocationTaskId, allSubtaskData, setSubtasks]
  );

  const transformedInitialSelected = useMemo(
    () =>
      subtasks?.map((subtask: any) => ({
        name: subtask?.subtaskId?.name,
        _id:
          subtask?.subtaskId?._id?.length === 24
            ? subtask?.subtaskId?._id
            : subtask?.subtaskId?.id,
        Description: subtask?.subtaskId?.Description,
        Unit: subtask?.subtaskId?.Unit,
        Tracking: subtask?.subtaskId?.Tracking,
        subtaskWeighatages: subtask?.subtaskId?.subtaskWeighatages,
        isCompleted: subtask?.isCompleted,
        quantity: subtask?.quantity,
      })) || [],
    [subtasks]
  );
  console.log(transformedInitialSelected, "data btai jrta mujko ");

  // Handle subtask delete
  const handleDeleteSubtask = useCallback(async () => {
    // dispatch(deleteSubtaskInPlanning(deleteItem?._id));
    // here is the functinality to delete the subtask from localdb
    console.log(deleteItem, "this is item to delete the subtaskdfasdf");
    const deleteSubtaskid = deleteItem?._id;
    const dbName = await initializeDatabase("SubtasklocDetail");

    // await window.electron.deleteSubtaskDetailByid({ dbName, id: deleteSubtaskid });
    dispatch(
      deleteSubtaskInPlanning({
        towerId: objectId,
        taskId: selectedLocationTaskId,
        subtaskId: deleteItemId,
      })
    );

    console.log(deleteItemId, "dispatched data");

    dispatch(
      setallSubtasksData(
        (allSubtaskData || []).filter(
          (sub: any) =>
            sub._id !== deleteItemId &&
            sub.subtaskId?._id !== deleteItemId &&
            sub.subtaskId !== deleteItemId
        )
      )
    );

    const updatedToDeleteData = store.getState().projectPlanning.Todeletedata;
    const updatedTaskDataWithToBeDeleted = {
      ...currentSelectedData,
      tobeDeleted: updatedToDeleteData,
    };
    await saveSyncData(
      updatedTaskDataWithToBeDeleted,
      Date.now().toString(),
      "TowerRoutes",
      false,
      dispatch
    );
    dispatch(closePopup("DeleteSubtaskPlanning"));
  }, [
    dispatch,
    deleteItem,
    objectId,
    selectedLocationTaskId,
    allSubtaskData,
    deleteItemId,
  ]);

  const { towerLocationId: tower_Id } = useParams();
  const selectAllSubtasksData = useSelector((state: RootState) =>
    tower_Id && selectedLocationTaskId
      ? state.projectPlanning.allSubtasksdata
      : []
  );
  // Handle delete icon click
  console.log("allSubtaskData", allSubtaskData);
  console.log("selectAllSubtasksData", selectAllSubtasksData);
  const handleDeleteIconClick = useCallback(
    async (item: any, e: React.MouseEvent) => {
      e.stopPropagation();
      setDeleteItem(item);
      console.log(item, "delete item ");
      setDeleteItemId(item?._id);

      // allSubtaskData is an array, so find directly
      let fullData = null;
      if (Array.isArray(allSubtaskData)) {
        fullData = allSubtaskData.find(
          (sub: any) =>
            sub.subtaskId?._id === item?._id ||
            sub.subtaskId === item?._id ||
            sub._id === item?._id
        );
      }
      setDeleteItemFullData(fullData);
      dispatch(openPopup("DeleteSubtaskPlanning"));
    },
    [dispatch, allSubtaskData]
  );

  const getActualId = (id?: string) => id?.split("-").pop() || id;

  useEffect(() => {
    if (expandedSubtaskId) {
      setTimeout(() => {
        const cardElement = document.getElementById(
          `subtask-card-${getActualId(expandedSubtaskId)}`
        );
        const scrollContainer = document.querySelector(
          `.${styles.subtaskcard_container}`
        );

        if (cardElement && scrollContainer) {
          const cardRect = cardElement.getBoundingClientRect();
          const containerRect = scrollContainer.getBoundingClientRect();
          const isCardFullyVisible =
            cardRect.top >= containerRect.top &&
            cardRect.bottom <= containerRect.bottom;

          if (!isCardFullyVisible) {
            let scrollPosition;

            if (cardRect.bottom > containerRect.bottom) {
              scrollPosition =
                cardElement.offsetTop -
                scrollContainer.clientHeight +
                cardRect.height +
                20;
            } else if (cardRect.top < containerRect.top) {
              scrollPosition = cardElement.offsetTop - 20;
            }
            if (scrollPosition !== undefined) {
              scrollContainer.scrollTo({
                top: scrollPosition,
                behavior: "smooth",
              });
            }
          }
        }
      }, 150);
    }
  }, [expandedSubtaskId]);

  console.log(
    transformedSubtasks,
    transformedInitialSelected,
    "non selected data"
  );

  return (
    <div className={styles.subtaskcard_parent_container}>
      <div style={{ minWidth: "50px", position: "relative" }}>
        <AddToolTip
          icon={<SuryconLogo />}
          label="Subtasks"
          onClick={() => dispatch(openPopup("AddSubTaskPlanning"))}
          className={`${styles.task_creation_add_tooltip} ${styles.three_cols}`}
          additionalClass="nobreak_class_tooltip"
          data={[]}
          isEdit={isEdit}
        />
        {popups["AddSubTaskPlanning"] && (
          <AddCategoryType
            title="Add SubTask"
            data={transformedSubtasks}
            initialSelected={transformedInitialSelected}
            label="SubTask"
            singleSelected={true}
            placeholder="Select"
            buttonLabel="Add Category"
            onSelect={handleAddSubtask}
            onClose={() => {
              dispatch(closePopup("AddSubTaskPlanning"));
            }}
          />
        )}
      </div>
      <div className={styles.subtaskcard_container_outer}>
        <div
          className={styles.subtaskcard_container}
          style={{ marginTop: "0.5rem" }}
        >
          {isLoading || isTransitioning || !allsubtaksBasicDetails ? (
            <div className={styles.loading_state}>
              {/* Optional: Add a loading spinner or placeholder here */}
            </div>
          ) : (
            allsubtaksBasicDetails.map((item: any) => {
              // console.log(
              //   "Rendering subtask card:",
              //   item._id,
              //   "Selected:",
              //   selected,
              //   "ExpandedSubtaskId:",
              //   expandedSubtaskId
              // );
              return (
                <div
                  key={item?._id}
                  id={`subtask-card-${item?._id}`}
                  onClick={() => handleSelect(item?._id)}
                  className={
                    expandedSubtaskId === item?._id
                      ? styles.subtaskcard_tooltip_expand
                      : styles.subtaskcard_tooltip
                  }
                >
                  <div
                    className={
                      expandedSubtaskId === item?._id
                        ? styles.subtask_tooltip_uppper
                        : ""
                    }
                    style={{
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "center",
                      width: "100%",
                    }}
                  >
                    <h4>{item?.name ?? item?.subtaskId?.name}</h4>
                    {isEdit && expandedSubtaskId !== item?._id && (
                      <div
                        onClick={(e) => handleDeleteIconClick(item, e)}
                        style={{ paddingLeft: "0.5rem" }}
                        className={styles.mt_target_card_delete}
                      >
                        <DeleteIcon />
                      </div>
                    )}
                  </div>
                  {expandedSubtaskId === item?._id && (
                    <div className={styles.subtask_tooltip_bottom}>
                      <div className={styles.subtask_tooltip_bottom_left}>
                        <div className={styles.bottom_left_content}>
                          <Circle
                            nameclass={"subtaskcircle"}
                            content={item?.Unit ?? item?.subtaskId?.Unit}
                          />
                          <div className={styles.bottom_left_text}>
                            <p className="small_text_p">2000 </p>
                            <p className="very_small_text_p ">Area</p>
                          </div>
                        </div>
                      </div>
                      <div className={styles.subtask_tooltip_bottom_right}>
                        <div className={styles.bottom_left_content}>
                          <Circle
                            nameclass={"subtaskcircle"}
                            icon={<PercentageIcon />}
                          />
                          <div className={styles.bottom_left_text}>
                            <p className="small_text_p">
                              {item?.weightage ?? item?.subtaskWeighatages}
                            </p>
                            <p className="very_small_text_p ">Weightage</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              );
            })
          )}
        </div>
      </div>

      {popups["DeleteSubtaskPlanning"] && (
        <DeletePopup
          header="Are you sure you want to delete this subtask?"
          height="calc(100% - 7.25rem)"
          heightupperlimit="0"
          callbackDelete={async () => {
            dispatch(deleteSubtaskInPlanning(deleteItem?._id));

            dispatch(
              deleteSubtaskInPlanning({
                towerId: objectId,
                taskId: selectedLocationTaskId,
                subtaskId: deleteItem._id,
              })
            );

            console.log(deleteItem, "dispatched data");
            await updateMaterialTable(deleteItem?._id, dispatch);
            // setSubtasks((prev: any) =>
            //   prev?.filter((e: any) => e?._id !== deleteItem?._id)
            // );
            // console.log(deleteItem?._id, "deletesubtakid");
            dispatch(closePopup("DeleteSubtaskPlanning"));
          }}
          onClose={() => {
            dispatch(closePopup("DeleteSubtaskPlanning"));
          }}
        >
          <>
            {console.log("deleteItemFullData", deleteItemFullData)}
            <div className={styles.flexContainer}>
              {(deleteItem?.name || deleteItem?.subtaskId?.name) && (
                <div className={styles.summaryDivData}>
                  <div className={styles.summaryDataContent}>
                    <p
                      style={{ color: "var(--text-black-60)" }}
                      className="p_tag_14px"
                    >
                      Name
                    </p>
                    <h4 style={{ color: "var(--text-black-87)" }}>
                      {deleteItem?.name ?? deleteItem?.subtaskId?.name}
                    </h4>
                  </div>
                </div>
              )}
              {/* Quantity block */}
              {(deleteItem?.quantity || deleteItem?.subtaskId?.quantity) && (
                <div className={styles.summaryDivData}>
                  <div className={styles.summaryDataContent}>
                    <p
                      style={{ color: "var(--text-black-60)" }}
                      className="p_tag_14px"
                    >
                      Quantity
                    </p>
                    <h4 style={{ color: "var(--text-black-87)" }}>
                      {deleteItem?.quantity ?? deleteItem?.subtaskId?.quantity}
                    </h4>
                  </div>
                </div>
              )}
              {/* {(deleteItem?.Unit || deleteItem?.subtaskId?.Unit) && (
                <div className={styles.summaryDivData}>
                  <div className={styles.summaryDataContent}>
                    <p
                      style={{ color: "var(--text-black-60)" }}
                      className="p_tag_14px"
                    >
                      Unit
                    </p>
                    <h4 style={{ color: "var(--text-black-87)" }}>
                      {deleteItem?.Unit ?? deleteItem?.subtaskId?.Unit}
                    </h4>
                  </div>
                </div>
              )} */}
            </div>
            {/* {(deleteItem?.Description ||
              deleteItem?.subtaskId?.Description) && (
              <div className={styles.summaryDivData}>
                <div className={styles.summaryDataContent}>
                  <p
                    style={{ color: "var(--text-black-60)" }}
                    className="p_tag_14px"
                  >
                    Description
                  </p>
                  <h4 style={{ color: "var(--text-black-87)" }}>
                    {deleteItem?.Description ??
                      deleteItem?.subtaskId?.Description}
                  </h4>
                </div>
              </div>
            )} */}
            <div className={styles.flexContainer}>
              {(deleteItem?.subtaskWeighatages || deleteItem?.weightage) && (
                <div className={styles.summaryDivData}>
                  <div className={styles.summaryDataContent_weightage}>
                    <div>
                      <p
                        style={{ color: "var(--text-black-60)" }}
                        className="p_tag_14px"
                      >
                        Weightage (%)
                      </p>
                      <h4 style={{ color: "var(--text-black-87)" }}>
                        {deleteItem?.subtaskWeighatages ??
                          deleteItem?.weightage}
                      </h4>
                    </div>
                  </div>
                </div>
              )}
              {deleteItem?.Tracking && (
                <div className={styles.summaryDivData}>
                  <div className={styles.summaryDataContent}>
                    <p
                      style={{ color: "var(--text-black-60)" }}
                      className="p_tag_14px"
                    >
                      Tracking
                    </p>
                    <h4 style={{ color: "var(--text-black-87)" }}>
                      {deleteItem?.Tracking}
                    </h4>
                  </div>
                </div>
              )}
            </div>
            {deleteItemFullData && (
              <div>
                {Array.isArray(deleteItemFullData.manpowerId) &&
                  deleteItemFullData.manpowerId.length > 0 && (
                    <div className={styles.summaryDivData}>
                      <div className={styles.summaryDataContent}>
                        <p
                          className="p_tag_14px"
                          style={{ color: "var(--text-black-60)" }}
                        >
                          Manpower
                        </p>
                        <ul style={{ paddingTop: "0.5rem", margin: 0 }}>
                          {deleteItemFullData.manpowerId.map(
                            (mp: any, idx: number) => (
                              <li
                                key={idx}
                                style={{
                                  marginBottom: "12px",
                                  listStyle: "none",
                                }}
                              >
                                <div>
                                  <div style={{ fontWeight: 600 }}>
                                    {mp.Mastermanpower_id?.name ||
                                      mp.Mastermanpower_id?._id ||
                                      "N/A"}
                                  </div>
                                  <div
                                    style={{ fontSize: "14px", color: "#555" }}
                                  >
                                    {typeof mp.quantity === "number"
                                      ? mp.quantity
                                      : mp.quantity
                                      ? mp.quantity
                                      : 0}
                                    {mp.type && (
                                      <span
                                        style={{
                                          color: "var(--primary_color)",
                                        }}
                                      >
                                        {` ${mp.type}`}
                                      </span>
                                    )}
                                  </div>
                                </div>
                              </li>
                            )
                          )}
                        </ul>
                      </div>
                    </div>
                  )}

                {/* Material */}
                {Array.isArray(deleteItemFullData.materialId) &&
                  deleteItemFullData.materialId.length > 0 && (
                    <div className={styles.summaryDivData}>
                      <div className={styles.summaryDataContent}>
                        <p
                          className="p_tag_14px"
                          style={{ color: "var(--text-black-60)" }}
                        >
                          Material
                        </p>
                        <ul style={{ paddingTop: "0.5rem", margin: 0 }}>
                          {deleteItemFullData.materialId.map(
                            (mat: any, idx: number) => (
                              <li
                                key={idx}
                                style={{
                                  marginBottom: "12px",
                                  listStyle: "none",
                                }}
                              >
                                <div>
                                  <div style={{ fontWeight: 600 }}>
                                    {mat.Mastermaterial_id?.name ||
                                      mat.Mastermaterial_id?._id ||
                                      "N/A"}
                                  </div>
                                  <div
                                    style={{ fontSize: "14px", color: "#555" }}
                                  >
                                    {mat.quantity}
                                  </div>
                                </div>
                              </li>
                            )
                          )}
                        </ul>
                      </div>
                    </div>
                  )}

                {/* Machinery */}
                {Array.isArray(deleteItemFullData.machinaryId) &&
                  deleteItemFullData.machinaryId.length > 0 && (
                    <div className={styles.summaryDivData}>
                      <div className={styles.summaryDataContent}>
                        <p
                          className="p_tag_14px"
                          style={{ color: "var(--text-black-60)" }}
                        >
                          Machinery
                        </p>
                        <ul style={{ paddingTop: "0.5rem", margin: 0 }}>
                          {deleteItemFullData.machinaryId.map(
                            (mac: any, idx: number) => (
                              <li
                                key={idx}
                                style={{
                                  marginBottom: "12px",
                                  listStyle: "none",
                                }}
                              >
                                <div>
                                  <div style={{ fontWeight: 600 }}>
                                    {mac.Mastermachinary_id?.name ||
                                      mac.Mastermachinary_id?._id ||
                                      "N/A"}
                                  </div>
                                  <div
                                    style={{ fontSize: "14px", color: "#555" }}
                                  >
                                    {mac.quantity}
                                  </div>
                                </div>
                              </li>
                            )
                          )}
                        </ul>
                      </div>
                    </div>
                  )}

                {/* Tools */}
                {Array.isArray(deleteItemFullData.tool_id) &&
                  deleteItemFullData.tool_id.length > 0 && (
                    <div className={styles.summaryDivData}>
                      <div className={styles.summaryDataContent}>
                        <p
                          className="p_tag_14px"
                          style={{ color: "var(--text-black-60)" }}
                        >
                          Tools
                        </p>
                        <ul style={{ paddingTop: "0.5rem", margin: 0 }}>
                          {deleteItemFullData.tool_id.map(
                            (tool: any, idx: number) => (
                              <li
                                key={idx}
                                style={{
                                  marginBottom: "12px",
                                  listStyle: "none",
                                }}
                              >
                                <div>
                                  <div style={{ fontWeight: 600 }}>
                                    {tool.MasterTool_id?.name ||
                                      tool.MasterTool_id?._id ||
                                      "N/A"}
                                  </div>
                                  <div
                                    style={{ fontSize: "14px", color: "#555" }}
                                  >
                                    {tool.quantity}
                                  </div>
                                </div>
                              </li>
                            )
                          )}
                        </ul>
                      </div>
                    </div>
                  )}
              </div>
            )}
          </>
        </DeletePopup>
      )}
    </div>
  );
};

export default SubtaskCard;
