.planningTableContainer {
  width: 100%;
  border-radius: 36px;
  box-shadow: 0px 0px 3px 0px #00000080;
  height: 76vh;
  display: flex;
  flex-direction: column;
}
@media screen and (max-width: 1200px) {
  .planningTableContainer {
    width: 1200px;
  
  }
}
@media screen and (max-height: 900px) {
  .planningTableContainer {
   overflow-y: hidden;
    height: 72vh;
  }
  .target_details_lower_outer_container{
    height: 55vh;
  }
  .material_sub_card_summary{
    min-height: 280px !important;
    max-height: 380px !important;
  }
}




.projectmaterial_container {
  display: flex;
  border-radius: 3rem;
  display: grid;
  grid-template-columns: 18% 82%;
  display: flex;
  border-radius: 3rem;
  display: grid;
  grid-template-columns: 18% 82%;
  position: relative;
 
}

.project_material_outer_container {
  padding: 1rem;
  position: relative;
}
.project_material_outer_container {
  padding: 1rem;
  position: relative;
}
.materialProgress_container {
  flex: 0 0 25%;
  min-width: 15rem;
  padding: 2rem;
}

.taskcreation_line_container {
  width: 100%;
  height: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 5px;
}

.dottedline_wrapper {
  width: 49%;
  border-bottom: 1px dashed var(--line-color);
}
.material_summary_circle {
    height: 1.6rem;
    width: 1.9rem;
    background-color: var(--primary_color);
    color: var(--text-white-100);
}

.materialTable_container {
  flex: 1;
  overflow: hidden;
  min-width: 20rem;
  height: 85vh;
  max-height: 85vh;
  border-radius: 36px;
  box-shadow: 0px 0px 3px 0px #00000080;
}

.materialtable_header {
  min-height: 6.5rem;
  width: 100%;
  background-color: var(--primary_color);
  display: flex;
  justify-content: space-between;
  position: sticky;
  top: 0px;
  align-items: center;
  padding: 1.5rem;
  gap: 1rem;
}

.materialtable_headerUnitsection {
  flex: 4;
  min-width: 20rem;
  display: flex;
  margin-top: 1rem;
  gap: 1rem;
  align-items: center;
}
.materialtable_header_inner_container_text {
  color: white;
}
.materialtable_headereditbtn {
  text-align: end;
  flex: 1;
  min-width: 10rem;
}

.materialtable_header_rightdivs {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  background: var(--main_background);
  padding: 0.5rem 1rem;
  border-radius: 12px;
  box-shadow: 0px 0px 3px 0px #91a1a180;
  border: 1px solid
    linear-gradient(
      130.72deg,
      rgba(237, 231, 231, 0.07) -16.06%,
      rgba(251, 251, 251, 0.05) 82.03%
    );
}

.material_line_container {
  width: 100%;
  width: 100%;

  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 5px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 5px;
  overflow: hidden;
}

.dottedline_wrapper {
  width: 49%;
  border-bottom: 1px dashed var(--line-color);
  overflow: hidden;
}

.groundworksection_container {
  min-height: 9vh;
  width: 90%;
  margin: 1.5rem;
}

.groundworksection_header {
  display: flex;
  gap: 0.6rem;
  align-items: center;
}

.mastersection_container {
  min-height: 10vh;
  width: 90%;
  margin: 1.5rem;
}

.mastersection_rows {
  display: flex;
  gap: 0.8rem;
  min-height: 6vh;
  align-items: center;
}

.cementsection_container,
.plastersection_container,
.craftmenshipsection_container,
.othersection_container {
  min-height: 5vh;
  width: 90%;
  margin: 1rem;
}

.cementsection_header,
.craftmenshipsection_header,
.plastersection_header,
.othersection_header {
  display: flex;
  gap: 0.6rem;
  align-items: center;
}
/* Styles for targetDetails in project material by rattandeep singh */
.target_details_lower_outer_container {
  /* justify-content: space-between;
  display: flex;
  scrollbar-width: none;
  /* overflow-x: auto; */
  /* overflow-y: scroll;  */
   /* overflow: scroll;  */
   position: relative;
   display: grid;
   grid-template-columns: 3fr 1fr;
   max-height: 60vh;
   width: 100%;
}
@media screen and (max-width: 1200px) {
  .target_details_lower_outer_container {
    width: 1200px;
  }
}
#scroll-container{
  height: 75vh;
  overflow-y: auto;
  position: relative;
  scroll-behavior: smooth;
}


.monthly_target_cards {
  overflow: scroll;
  max-height: 62vh;
  grid-column: 1 / -1;
}
/* Custom scrollbar */
.monthly_target_cards::-webkit-scrollbar {
  width: 4px;
}

.monthly_target_cards::-webkit-scrollbar-track {
  background: transparent;
}

.monthly_target_cards::-webkit-scrollbar-thumb {
  border-radius: 4px;
}
.monthly_target_cards:hover::-webkit-scrollbar-thumb {

   background-color: #006d77;

}



/* .material_sub_card_summary{
  height: 70px;
  
} */

.mt_card_outer_container {
  /* margin-bottom: 1rem;
    margin-top: 2rem; */
  padding: 1.5rem;
}
.mt_cards_container {
  display: flex;
  gap: 2rem;
  max-width: 65rem;
  flex-wrap: wrap;
  margin-top: 1rem;
}
.target_badge_container {
  padding: 1.5rem;
}
.edit_pencil_container {
  justify-self: end;
  position: absolute;
  top: 1.5rem;
  right: 1.5rem;
  display: flex;
  min-width: 2.375rem;
  min-height: 2.375rem;
  justify-content: center;
  align-items: center;
  background-color: var(--main_background);
  backdrop-filter: blur(100px);
  box-shadow: var(--primary-mt-box-shadow);
  width: 2.375rem;
  height: 2.375rem;
  border-radius: 100%;
  padding: 0.25rem;
  cursor: pointer;
}
.material_header_badge_container {
  justify-self: end;
}

/* Aayush Malviya Subtask Card container start */

.subtaskcard_container {
  max-height: 500px;
  min-height: 500px;
  width: 260px;
  background-color: var(--main_background);
  backdrop-filter: blur(40px);
  box-shadow: var(--extra-shdow-third);
  border-radius: 25px;
  position: fixed;
  display: flex;
  align-items: center;
  flex-direction: column;
  padding-top: 2rem;
  overflow: scroll;
  top: 35%;
  right: 2.3%;
  z-index: 999;
}

/* Custom scrollbar */
.subtaskcard_container::-webkit-scrollbar {
  width: 4px;
}

.subtaskcard_container::-webkit-scrollbar-track {
  background: transparent;
}

.subtaskcard_container::-webkit-scrollbar-thumb {
  background-color: #006d77;
  border-radius: 4px;
}

.subtaskcard_tooltip {
  width: 230px;
  min-height: 50px;
  height: 50px;
  background-color: var(--main_background);
  box-shadow: var(--extra-shdow-four);
  backdrop-filter: blur(40px);
  border-radius: 100px;
  margin: 0.5rem 0rem;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 0 1rem;
  cursor: url("../../../../../../assets/icons/cursor.png"), auto;
}

.subtaskcard_tooltip h4 {
  color: var(--text-black-60);
}
.subtaskcard_tooltip_expand {
  width: 230px;
  height: 100px;
  background-color: var(--main_background);
  box-shadow: var(--extra-shdow-four);
  backdrop-filter: blur(40px);
  border-radius: 12px;
  border: 1px solid var(--primary_color);
  margin: 0.5rem 0rem;
  cursor: pointer;
  position: relative;
  padding: 0.5rem 1rem;
}

.subtask_tooltip_uppper {
  padding-top: 0.3rem;
  height: 30%;
  width: 100%;
}

.subtask_tooltip_uppper h4 {
  color: var(--text-black-87);
}

.subtask_tooltip_bottom {
  height: 60%;
  width: 100%;
  margin-top: 0.8rem;
  display: flex;
}

.subtask_tooltip_bottom_left {
  width: 50%;
}

.subtask_tooltip_bottom_right {
  width: 50%;
}

.bottom_left_content {
  display: flex;
  align-items: center;
  gap: 0.3rem;
}

.bottom_left_text p:nth-child(1) {
  color: var(--text-black-87);
}

.bottom_left_text p:nth-child(2) {
  color: var(--text-black-28);
}

.material_add_tooltip_subcontainer {
  padding: 0;
}
/* Aayush Malviya Subtask Card container end */
.mt_card_inner_container {
  display: flex;
  max-width: fit-content;
  align-items: center;
}
.material_subtask_budget {
  padding: 0.25rem 0.625rem 0.25rem 0.625rem;
  background-color: var(--extra_color);
  color: var(--secondary_color);
  margin-left: 1rem;
  border-radius: 1rem;
}

/* materialsubcard summary css start by aayush malviya */

.material_subcard_summary_main {
  max-height: 450px;
  min-height: 450px;
  width: 260px;
  background-color: var(--main_background);
  backdrop-filter: blur(40px);
  box-shadow: var(--extra-shdow-third);
  border-radius: 25px;
  position: absolute;
  display: flex;
  grid-column: 2;
  align-items: center;
  flex-direction: column;
  overflow: scroll;
  z-index: 999;
  right: 2.5rem;
  margin: 30px 0px 30px 0px;
}

@media screen and (max-height: 900px) {
  .material_subcard_summary_main {
    max-height: 380px !important;
    min-height: 300px !important;
  }

}
.material_subcard_summary_submain {
  height: 100%;
  width: 100%;
  position: relative;
}

.material_subcard_summary_header {
  width: 100%;
  background-color: var(--primary_background);
  display: flex;
  flex-direction: column;
  padding-left: 3rem;
  padding: 1rem;
  justify-content: center;
}

.material_subcard_summary_content {
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
}
.material_subcard_summary_header p {
  color: var(--text-black-60);
  line-height: 1rem;
}
.material_subcard_summary_header h4 {
  color: var(--text-black-87);
  line-height: 1.3rem;
}
.material_inr_container {
  height: 2.5rem;
  width: 9rem;
  background-color: var(--secondary_background);
  position: absolute;
  right: 0;
  top: 0;
  border-top-left-radius: 100px;
  border-bottom-left-radius: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.2rem;
}
.material_inr_container p {
  color: var(--secondary_color);
}

.material_subcard_content_header {
  width: 100%;
  display: flex;
  gap: 0.5rem;
  height: 10%;
  align-items: center;
  padding-inline: 1rem;
  padding-top: 1rem;
}

.material_subcard_content_header h4 {
  color: var(--text-black-87);
  line-height: 1.3rem;
}

/* .material_summary_circle {
  background-color: red;
} */

/* material card code start here by aayush  */

.material_card_main_scroll {
  height: 100%;
  max-height: 100%;
  width: 100%;
  overflow: scroll;
  position: relative;
  padding: 1rem;
}
.material_card_container {
  height: 7rem;
  width: 100%;
  background: var(--primary-bg-gradient);
  box-shadow: var(--extra-shdow-four);
  backdrop-filter: blur(40px);
  border-radius: 12px;
  margin-bottom: 1rem;
  position: relative;
  overflow: hidden;
}

.material_card_container_header {
  height: 40%;
  width: 100%;
  background-color: var(--primary_background);
  padding: 1rem;
  display: flex;
  align-items: center;
}

.material_card_container_header h4 {
  color: var(--text-black-87);
  line-height: 1.1rem;
}

.material_card_container_content {
  width: 100%;
  display: flex;
  height: 60%;
  padding: 0rem 1rem;
  align-items: center;
  gap: 0.5rem;
}

.material_card_container_content_right h4 {
  color: var(--text-black-60);
  line-height: 1.3rem;
}

.material_card_container_content_right :nth-child(2) {
  line-height: 1.3rem;
  color: var(--text-black-87);
}
/* materialsubcard summary css end by aayush malviya */



.loader_loading {
  position: absolute;
  top: 63%;
  left: 64%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.loader_loading_image {
  width: 500px;
  height: 500px;
}