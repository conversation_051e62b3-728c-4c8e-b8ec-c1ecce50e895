// import {
//   useGetTowerLocatonByObjectIdQuery,
//   useGetSubTaskOfTaskRouteQuery,
//   useGetSubTaskDetailByIdQuery,
// } from "../api/Billing/ProjectPlanningApi";
import {
  useAddTaskCategoryMutation,
  useAddTaskByCategoryIdMutation,
  useGetTaskDetailsByTaskIdQuery,
  useGetTaskBuildingBlocksQuery,
  useUpdateTaskByIdMutation,
  useGetSubTaskRouteByTaskIdQuery,
  useEditTaskCategoryMutation,
  useAddSubTasksByIdMutation,
  useEditTaskMutation,
  useGetTaskBasicsDetailsQuery,
  useLazySyncToBackChangesQuery,
  useUpdateSubTaskMutation,
  useGetsubTaskDetailsQuery,
  useGetTaskCategoriesQuery,
  useGetAllTaskByCategoryIdQuery,
  useDeleteTaskCategoryMutation,
  useDeleteTaskMutation,
  useUpdateBasicSubTaskByIdMutation,
  useLazyGetsubTaskDetailsLazyQuery,
} from "../../../api/Modules/TaskMaster/TaskMasterapi";

// export const useLoginApi = () => useLoginMutation();

export const useAddTaskCategoryApi = () => {
  return useAddTaskCategoryMutation();
};

export const GetTasCkategoryApi = () => {
  return useGetTaskCategoriesQuery("");
};
export const useAddSubTaskByIdApi = () => {
  return useAddSubTasksByIdMutation();
};

export const useupdateSubTaskByid = () => {
  return useUpdateBasicSubTaskByIdMutation();
};
export const useTaskByCategoryIdApi = () => {
  return useAddTaskByCategoryIdMutation();
};

export const GetAllTaskByCategory = ({ id }: { id: string }) => {
  return useGetAllTaskByCategoryIdQuery({
    id,
    time: "", // Ya koi custom time logic
  });
};

export const GetTaskDetailByIdApi = (taskId: string) => {
  return useGetTaskDetailsByTaskIdQuery(taskId);
};

export const GetTaskBuildingBlocks = () => {
  return useGetTaskBuildingBlocksQuery();
};

export const useUpdateTask = (taskId: any) => {
  return useUpdateTaskByIdMutation(taskId);
};
export const useEditTaskCategory = () => {
  return useEditTaskCategoryMutation();
};

export const GetSubTaskRoute = (id: string) => {
  return useGetSubTaskRouteByTaskIdQuery(id);
};
export const useEditTask = () => {
  return useEditTaskMutation();
};
export const GetTaskBasicsDetails = (taskId: string | undefined) => {
  return useGetTaskBasicsDetailsQuery(taskId);
};

export const syncToBackChangeApi = () => {
  return useLazySyncToBackChangesQuery();
};

export const useDeleteCategory = () => {
  return useDeleteTaskCategoryMutation();
};

export const useDeleteTask = () => {
  return useDeleteTaskMutation();
};

export const useUpdateSubtask = () => {
  return useUpdateSubTaskMutation();
};
export const getSubtaskDetailsbyId = (id: string) => {
  return useGetsubTaskDetailsQuery(id);
};
export const getSubtaskDetailsLazy = (id: string) => {
  return useLazyGetsubTaskDetailsLazyQuery(id);
};
