.tableViewProject_outerdiv {
  height: 78vh;
  width: 100%;
  margin: 0.2rem;
  border-radius: 36px;
  overflow: scroll;
  box-shadow: var(--extra-shdow-second);
}

@media (max-width: 1200px) {
  .tableViewProject_outerdiv {
    width: 1780px;
    height: 78vh;
    overflow-x: scroll;

  }
  
}

.tableViewProject_outerheader {
  display: flex;
  justify-content: space-between;
  padding: 1.5rem 1.5rem 0 1.5rem;
  margin-right: 1.8rem;
}

.tableView_exportbtn {
  width: 7.1rem;
  height: 2.6rem;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
  border-radius: 6.25rem;
  cursor: pointer;
  background-color: var(--main_background);
  box-shadow: var(--extra-shadow);
  border: none;
  color: var(--text-black-60);
}

.tableViewProject_tablecontainer {
  overflow-x: auto;
  padding: 1.5rem;
}

.tableViewProject_projectTable {
  width: 100%;

  border-collapse: collapse;
}

.tableViewProject_projectTable th,
.tableViewProject_projectTable td {
  padding: 0.8rem 0.8rem 0.8rem 1rem;
  text-align: left;
}

.tableViewProject_projectTable thead {
  border: 1px solid transparent;
  background-image: linear-gradient(
    130.72deg,
    rgba(237, 231, 231, 0.07) -16.06%,
    rgba(251, 251, 251, 0.05) 82.03%
  );
  background-clip: padding-box;
  box-shadow: var(--extra-shdow-second);
  border-radius: 100px;
}

.tableViewProject_projectTable th {
  color: var(--text-black-87);
}

.tableViewProject_projectTable td {
  color: var(--text-black-87);
}

.tableViewProject_actions {
  display: flex;
  gap: 1.2rem;
}

.projectName,
.projectArea,
.estimatedCost,
.rates {
  display: flex;
  flex-direction: column;
}
.projectIcon {
  border-radius: 100%;
  object-fit: cover;
  overflow: hidden;
}
.projectdiv {
  display: flex;
  gap: 0.8rem;
  align-items: center;
}

.clientName,
.areaUnit,
.estimatedcostCurrency {
  color: var(--text-black-60);
  margin-top: 2px;
}

.rateOfProject {
  color: var(--text-black-60);
  margin-top: 2px;
}

.tableViewProject_projectTable th:nth-child(1),
.tableViewProject_projectTable td:nth-child(1) {
  width: 1rem;
}

.tableViewProject_projectTable th:nth-child(2),
.tableViewProject_projectTable td:nth-child(2) {
  width: 15.563rem;
}

.tableViewProject_projectTable th:nth-child(3),
.tableViewProject_projectTable td:nth-child(3) {
  width: 18rem;
}

.tableViewProject_projectTable th:nth-child(4),
.tableViewProject_projectTable td:nth-child(4) {
  width: 12rem;
}

.tableViewProject_projectTable th:nth-child(5),
.tableViewProject_projectTable td:nth-child(5) {
  width: 12rem;
}

.tableViewProject_projectTable th:nth-child(6),
.tableViewProject_projectTable td:nth-child(6) {
  width: 12rem;
}

.tableViewProject_projectTable th:nth-child(7),
.tableViewProject_projectTable td:nth-child(7) {
  width: 12rem;
}

.tableViewProject_projectTable th:nth-child(8),
.tableViewProject_projectTable td:nth-child(8) {
  width: 12rem;
}

.tableViewProject_projectTable th:nth-child(9),
.tableViewProject_projectTable td:nth-child(9) {
  width: 6rem;
}
.projectcompeleted {
  color: var(--secondary_color);
}
.table_headings {
  color: var(--text-black-60);
}
.table_body {
  overflow: hidden !important;
}
