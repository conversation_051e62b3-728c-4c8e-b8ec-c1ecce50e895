import React from "react";
import { SuryaconLogoSecondary } from "../../icons";

export const LightSvg = () => {
  return (
    <svg
      width="34"
      height="34"
      viewBox="0 0 34 34"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M13.6899 24.0833H20.3099M16.9999 4.25V5.66667M26.0155 7.98439L25.0138 8.98612M29.75 16.9999H28.3333M5.66667 16.9999H4.25M8.98605 8.98612L7.98431 7.98439M11.9913 22.0087C9.22504 19.2425 9.22504 14.7576 11.9913 11.9914C14.7575 9.22515 19.2424 9.22515 22.0086 11.9914C24.7748 14.7576 24.7748 19.2425 22.0086 22.0087L21.2335 22.7838C20.337 23.6803 19.8333 24.8964 19.8333 26.1643V26.9167C19.8333 28.4815 18.5647 29.75 16.9999 29.75C15.4351 29.75 14.1666 28.4815 14.1666 26.9167V26.1643C14.1666 24.8964 13.6629 23.6803 12.7663 22.7838L11.9913 22.0087Z"
        stroke="black"
        strokeOpacity="0.87"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const Notification = () => {
  return (
    <svg
      width="22"
      height="26"
      viewBox="0 0 22 26"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M8.33339 21.2353H2.25922C1.08875 21.2353 0.521039 19.8037 1.37338 19.0015C2.41146 18.0245 3.00006 16.6622 3.00006 15.2367V11.6666C3.00006 7.24835 6.58178 3.66663 11.0001 3.66663M8.33339 21.2353V22.3333C8.33339 23.8061 9.5273 25 11.0001 25C12.4728 25 13.6667 23.8061 13.6667 22.3333V21.2353M8.33339 21.2353H13.6667M13.6667 21.2353H19.7409C20.9114 21.2353 21.4791 19.8037 20.6267 19.0015C19.5887 18.0245 19.0001 16.6622 19.0001 15.2367V11.6666C19.0001 7.24835 15.4183 3.66663 11.0001 3.66663M11.0001 3.66663V1.66663"
        stroke="black"
        strokeOpacity="0.87"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const SerachSvg = () => {
  return (
    <svg
      width="25"
      height="25"
      viewBox="0 0 25 25"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M21.875 21.875L15.625 15.625M17.7083 10.4167C17.7083 14.4437 14.4437 17.7083 10.4167 17.7083C6.38959 17.7083 3.125 14.4437 3.125 10.4167C3.125 6.38959 6.38959 3.125 10.4167 3.125C14.4437 3.125 17.7083 6.38959 17.7083 10.4167Z"
        stroke="black"
        strokeOpacity="0.6"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const TrackingSvg = () => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 22 22" fill="none">
        <path d="M15.2106 15.2135C12.8837 17.5404 9.11109 17.5404 6.78422 15.2135C4.45734 12.8866 4.45734 9.11402 6.78422 6.78715C8.18153 5.38983 10.1002 4.83162 11.9141 5.11251" stroke="#005968" stroke-width="1.5" stroke-linecap="round"/>
        <path d="M10.9987 11.0002L15.536 6.46289C16.4831 5.51581 16.9566 5.04226 16.8532 4.35591C16.7497 3.66956 16.2587 3.40998 15.2765 2.8908C13.9995 2.21573 12.5437 1.8335 10.9987 1.8335C5.93609 1.8335 1.83203 5.93755 1.83203 11.0002C1.83203 16.0628 5.93609 20.1668 10.9987 20.1668C16.0613 20.1668 20.1654 16.0628 20.1654 11.0002C20.1654 9.69647 19.8932 8.45634 19.4026 7.3335" stroke="#005968" stroke-width="1.5" stroke-linecap="round"/>
    </svg>
  )
}

export const UnitSvgs = () => {
  return(
    <svg
    xmlns="http://www.w3.org/2000/svg"
    xmlnsXlink="http://www.w3.org/1999/xlink"
    width={22}
    height={22}
    viewBox="0 0 22 22"
    fill="none"
  >
    <mask
      id="mask0_3992_10011"
      style={{
        maskType: "alpha",
      }}
      maskUnits="userSpaceOnUse"
      x={0}
      y={0}
      width={22}
      height={22}
    >
      <rect
        width={22}
        height={22}
        transform="matrix(-1 0 0 1 22 0)"
        fill="url(#pattern0_3992_10011)"
      />
    </mask>
    <g mask="url(#mask0_3992_10011)">
      <rect width={22} height={22} fill="#005968" />
    </g>
    <defs>
      <pattern
        id="pattern0_3992_10011"
        patternContentUnits="objectBoundingBox"
        width={1}
        height={1}
      >
        <use xlinkHref="#image0_3992_10011" transform="scale(0.00195312)" />
      </pattern>
      <image
        id="image0_3992_10011"
        width={512}
        height={512}
        preserveAspectRatio="none"
        xlinkHref="data:image/png;base64,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"
      />
    </defs>
  </svg>
  )
}

export const SubtaskSvg = () => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="16" viewBox="0 0 20 16" fill="none">
      <path d="M1.11133 3.83221V3.48654C1.11133 2.15021 2.26011 1.06689 3.67719 1.06689H16.3232C17.7403 1.06689 18.8891 2.15021 18.8891 3.48654V3.83221C18.8891 5.16854 17.7403 6.25186 16.3232 6.25186H3.67719C2.26011 6.25186 1.11133 5.16854 1.11133 3.83221Z" stroke="#00596B" stroke-width="1.5"/>
      <path d="M18.8887 12.1287V12.3015C18.8887 13.6379 17.7399 14.7212 16.3228 14.7212H12.6573C11.2402 14.7212 10.0914 13.6379 10.0914 12.3015V12.1287C10.0914 10.7924 11.2402 9.70906 12.6573 9.70906H16.3228C17.7399 9.70906 18.8887 10.7924 18.8887 12.1287Z" stroke="#00596B" stroke-width="1.5"/>
      <path d="M9.91107 12.4754C8.03295 12.4754 7.0939 12.4754 6.34863 12.1982C5.26211 11.7942 4.40205 10.9831 3.97359 9.95852C3.67969 9.25572 3.67969 8.37018 3.67969 6.59908V6.25342" stroke="#00596B" stroke-width="1.5"/>
    </svg>
  )
}

export const SuryaconMainLogo = () => {
  return (
    <svg
    xmlns="http://www.w3.org/2000/svg"
    xmlnsXlink="http://www.w3.org/1999/xlink"
    width={184}
    height={91}
    fill="none"
  >
    <path fill="url(#a)" d="M.113 0h183.549v91H.113z" />
    <defs>
      <pattern
        id="a"
        width={1}
        height={1}
        patternContentUnits="objectBoundingBox"
      >
        <use
          xlinkHref="#b"
          transform="matrix(.00375 0 0 .00746 -.633 -1.045)"
        />
      </pattern>
      <image
        xlinkHref="data:image/png;base64,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"
        id="b"
        width={604}
        height={413}
        preserveAspectRatio="none"
      />
    </defs>
  </svg>
  )
}

interface DesignerDownArrowProps {
  color: string;
}

export const DesignerDownArrow: React.FC<DesignerDownArrowProps> = ({
  color,
}) => {
  return (
    <svg
      className="DesignerDownArrowSvg"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M4.99994 13.1001C5.00077 12.9023 5.06022 12.7092 5.17078 12.5452C5.28135 12.3812 5.43805 12.2537 5.62109 12.1788C5.80412 12.1039 6.00526 12.0849 6.19907 12.1243C6.39288 12.1637 6.57067 12.2596 6.70994 12.4001L11.2899 17.0001C11.3829 17.0938 11.4935 17.1682 11.6154 17.219C11.7372 17.2697 11.8679 17.2959 11.9999 17.2959C12.132 17.2959 12.2627 17.2697 12.3845 17.219C12.5064 17.1682 12.617 17.0938 12.7099 17.0001L17.2899 12.4001C17.3829 12.3063 17.4935 12.2319 17.6154 12.1812C17.7372 12.1304 17.8679 12.1043 17.9999 12.1043C18.132 12.1043 18.2627 12.1304 18.3845 12.1812C18.5064 12.2319 18.617 12.3063 18.7099 12.4001C18.8962 12.5874 19.0007 12.8409 19.0007 13.1051C19.0007 13.3692 18.8962 13.6227 18.7099 13.8101L14.1199 18.4001C13.5574 18.9619 12.7949 19.2774 11.9999 19.2774C11.2049 19.2774 10.4424 18.9619 9.87994 18.4001L5.28994 13.8101C5.19726 13.7166 5.12393 13.6058 5.07417 13.484C5.0244 13.3621 4.99918 13.2317 4.99994 13.1001Z"
        fill={color}
        fillOpacity="0.87"
      />
      <path
        d="M4.99994 6.10006C5.00077 5.90228 5.06022 5.7092 5.17078 5.54521C5.28135 5.38123 5.43805 5.25371 5.62109 5.17879C5.80412 5.10386 6.00526 5.08489 6.19907 5.12428C6.39288 5.16366 6.57067 5.25963 6.70994 5.40006L11.9999 10.6901L17.2899 5.40006C17.3829 5.30633 17.4935 5.23193 17.6154 5.18116C17.7372 5.1304 17.8679 5.10426 17.9999 5.10426C18.132 5.10426 18.2627 5.1304 18.3845 5.18116C18.5064 5.23193 18.617 5.30633 18.7099 5.40006C18.8962 5.58742 19.0007 5.84087 19.0007 6.10506C19.0007 6.36924 18.8962 6.62269 18.7099 6.81006L12.7099 12.8101C12.617 12.9038 12.5064 12.9782 12.3845 13.0289C12.2627 13.0797 12.132 13.1059 11.9999 13.1059C11.8679 13.1059 11.7372 13.0797 11.6154 13.0289C11.4935 12.9782 11.3829 12.9038 11.2899 12.8101L5.28994 6.81006C5.19726 6.71662 5.12393 6.6058 5.07417 6.48396C5.0244 6.36212 4.99918 6.23166 4.99994 6.10006Z"
        fill={color}
        fillOpacity="0.87"
      />
    </svg>
  );
};


export const RateIcon = () => (
  <svg width="12" height="16" viewBox="0 0 12 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M5.16406 2.16667H0.997396C0.776382 2.16667 0.564421 2.07887 0.40814 1.92259C0.25186 1.76631 0.164062 1.55435 0.164062 1.33333C0.164062 1.11232 0.25186 0.900358 0.40814 0.744078C0.564421 0.587797 0.776382 0.5 0.997396 0.5H10.9974C11.2184 0.5 11.4304 0.587797 11.5867 0.744078C11.7429 0.900358 11.8307 1.11232 11.8307 1.33333C11.8307 1.55435 11.7429 1.76631 11.5867 1.92259C11.4304 2.07887 11.2184 2.16667 10.9974 2.16667H8.4974C8.86839 2.65944 9.12462 3.22886 9.2474 3.83333H10.9974C11.2184 3.83333 11.4304 3.92113 11.5867 4.07741C11.7429 4.23369 11.8307 4.44565 11.8307 4.66667C11.8307 4.88768 11.7429 5.09964 11.5867 5.25592C11.4304 5.4122 11.2184 5.5 10.9974 5.5H9.2474C9.055 6.44099 8.54354 7.28667 7.79951 7.89405C7.05548 8.50142 6.12452 8.83322 5.16406 8.83333H4.67573L9.9199 14.0775C10.0717 14.2347 10.1557 14.4452 10.1538 14.6637C10.1519 14.8822 10.0643 15.0912 9.90975 15.2457C9.75524 15.4002 9.54623 15.4878 9.32773 15.4897C9.10923 15.4916 8.89873 15.4076 8.74156 15.2558L2.0749 8.58917C1.95839 8.47262 1.87905 8.32415 1.84691 8.16253C1.81477 8.0009 1.83127 7.83337 1.89433 7.68112C1.95739 7.52887 2.06417 7.39874 2.20118 7.30717C2.33819 7.21559 2.49927 7.1667 2.66406 7.16667H5.16406C5.68113 7.16677 6.18551 7.00654 6.60772 6.70805C7.02993 6.40956 7.34921 5.9875 7.52156 5.5H0.997396C0.776382 5.5 0.564421 5.4122 0.40814 5.25592C0.25186 5.09964 0.164062 4.88768 0.164062 4.66667C0.164062 4.44565 0.25186 4.23369 0.40814 4.07741C0.564421 3.92113 0.776382 3.83333 0.997396 3.83333H7.52156C7.34921 3.34584 7.02993 2.92378 6.60772 2.62529C6.18551 2.3268 5.68113 2.16657 5.16406 2.16667Z"
      fill="#005968"
    />
  </svg>
);

export const DurationIcon = () => (
  <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M9 4.41667V8.43347C9 8.78068 9.19617 9.09809 9.50672 9.25336L11.75 10.375M17.25 9C17.25 4.44365 13.5563 0.75 9 0.75C4.44365 0.75 0.75 4.44365 0.75 9C0.75 13.5563 4.44365 17.25 9 17.25C13.5563 17.25 17.25 13.5563 17.25 9Z"
      stroke="#005968"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);


export const ShutteringIcon = () => (
  <svg width="30" height="18" viewBox="0 0 30 18" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M13.8131 17.481L29 8.36121M10.4687 15.4029L25.5565 6.34274M7.2016 13.4826L22.2894 4.42238M4.1008 11.5649L19.1886 2.50473M1 9.64728L16.0878 0.587088M1.00131 8.34252L16.2511 17.5M4.1008 6.3401L19.3506 15.4976M7.36921 4.42245L22.619 13.5799M10.6376 2.50481L25.8874 11.6623M13.487 0.5L28.7368 9.65748"
      stroke="#00596B"
      strokeWidth="0.9"
      strokeLinecap="round"
    />
  </svg>
);


export const PaymentIcon = () => (
  <svg width="30" height="18" viewBox="0 0 36 28" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect x="3" y="4" width="30" height="20" rx="4" stroke="#005968" strokeWidth="2.5" fill="none"/>
    <rect x="3" y="9" width="30" height="2.5" fill="#005968"/>
    <rect x="25" y="18" width="6" height="2.5" rx="1.25" fill="#005968"/>
  </svg>
);