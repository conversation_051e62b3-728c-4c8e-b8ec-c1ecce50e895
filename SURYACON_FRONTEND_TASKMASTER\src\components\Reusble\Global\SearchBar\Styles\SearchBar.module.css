/*  AUTHOR NAME : CHARVI */
.searchBar {
  display: flex;
  justify-content: center;
  gap: 0.2rem;
  padding: 1rem 1.5rem 1rem 1.5rem;
  border-radius: 3.125rem;
  max-width: 30rem;
  transition: width 0.3s ease;

  box-shadow: 0px 0px 4px 0px #91a1a1bf;

  transition: width 0.3s ease, box-shadow 0.4s ease;
}

.searchBar:hover {
  box-shadow: 0px 4px 8px #ffffffbf, 0px 2px 8px 0px #00000040 inset;
  /* box-shadow: 0px 2px 8px 0px #00000040 inset; */
}

.searchBar input {
  background: none;
  border: none;
  outline: none;
  width: 100%;
  /* padding-top: 0.2rem; */
  padding-left: 0.5rem;
}

.searchBar input::placeholder {
  color: var(--text-black-60);
  font-size: 1rem;
}

.searchBar span {
  display: flex;
  align-items: center;
}

/* styles for search bar in table view start by rattandeep singh */
.tableview_search_placeholder::placeholder {
  color: #00000047 !important;
}

.tableview_searchbar {
  height: 2.25rem !important;
  width: 12.375rem !important;
  padding: 0 1rem !important;
}

/* styles for search bar in table view end by rattandeep singh */
