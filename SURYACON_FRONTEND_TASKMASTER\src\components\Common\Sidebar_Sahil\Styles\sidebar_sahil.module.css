.sidebar_container {
  width: 210px;
  height: 100%;
  background-color: #ffffff;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.cp_selector {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  margin-bottom: 16px;
  cursor: pointer;
  font-weight: 500;
}

.menu_items_container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.sidebar_item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 10px 16px;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.sidebar_item:hover {
  background-color: #f5f5f5;
}

.sidebar_item.selected {
  background-color: #006D77;
  color: white;
}

.sidebar_item .icon {
  font-size: 20px;
}

.others_section {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding-top: 16px;
}

.section_title {
  font-weight: 500;
  font-size: 0.875rem;
  padding: 8px 16px;
  margin-bottom: 4px;
}