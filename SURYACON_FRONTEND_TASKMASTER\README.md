# SURYACON_FRONTEND_TASKMASTER_MAIN
ONLY FOR PROJECT LEAD
# SURYACON_FRONTEND_TASKMASTER
SURYACON PROJECT FRONTEND TASKMASTER
<!-- dmg-license install -->


<!-- 
local db table track for version with role like first me check krunga ki jo role backend se aaya h uska  
 koi already version exist krta hai ya to krta hai ya nhi krega 
 if krega then --check for version exist=== or new version equal h no issue no download not equal hai means download krna pdega 

 --then jb download krenge tb jo hmare local resources h delete kr denge and new add kr denge 
 --after download hm db me bhi changes krenge and jo bhi data save h erase krke new version save kr denge 
 --if net off hua ya user exist hua then koi version save hi nhi hoga emptyr rhega next time fir se download krega vo delete wala to koi issue nhi hoga 

--download after log in also setup in app tsx if already log in here user 

 -->





