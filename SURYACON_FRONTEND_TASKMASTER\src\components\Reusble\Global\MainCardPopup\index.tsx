import styles from "./Styles/CardDotPopup.module.css";
import { YellowEditPencil, DeleteIcon } from "../../../../assets/icons";
import { useAppDispatch } from "../../../../redux/hooks/Modules/Reduxhooks/ReduxHooks";
import {
  clearPopups,
  closePopup,
  openPopup,
} from "../../../../redux/features/Modules/Reusble/popupSlice";
import {
  setDepartmentFormData,
  setDesignationFormData,
  setFormData,
  setFormMachineryData,
  setFormManpowerData,
  setFormMaterialsData,
  setFormMode,
  setInitialDepartmentFormData,
  setInitialDesignationFormData,
  setInitialFormData,
  setInitialFormMachineryData,
  setInitialFormManpowerData,
  setInitialFormMaterialsData,
} from "../../../../redux/features/Modules/TaskMaster/Slices/MastersSlice";
import { useCallback, useEffect, useRef, useState } from "react";
import { setInputValue } from "../../../../redux/features/Modules/Reusble/floatinglabelslice";
import { getFileName } from "../../../../functions/functions";
import { log } from "console";
import { set } from "lodash";

interface cardDotPopup {
  setIsPopupOpen: React.Dispatch<React.SetStateAction<boolean>>;
  editData?: any;
  varient?: string;
}

const CardDotPopup: React.FC<cardDotPopup> = ({
  setIsPopupOpen,
  varient,
  editData,
}) => {
  const dispatch = useAppDispatch();

  console.log("editing data", editData);

  const cardRef = useRef<HTMLDivElement>(null);
  const popup = useRef<HTMLDivElement>(null);

  const handleClickOutside = useCallback(
    (event: MouseEvent) => {
      if (
        cardRef.current &&
        !cardRef.current.contains(event.target as Node) &&
        popup.current &&
        !popup.current.contains(event.target as Node)
      ) {
        setIsPopupOpen && setIsPopupOpen(false);
      }
    },
    [dispatch]
  );

  useEffect(() => {
    document.addEventListener("mousedown", (e) => handleClickOutside(e));
    return () => {
      document.removeEventListener("mousedown", (e) => handleClickOutside(e));
    };
  }, [handleClickOutside]);

  const handleEdit = () => {
    if (varient === "tools") {
      dispatch(
        setInitialFormData({
          _id: editData?._id ?? "",
          type: editData?.type ?? "",
          Photo: editData?.images?.[0]
            ? {
                name: getFileName(editData?.images?.[0]),
                file: editData?.images?.[0],
              }
            : null,
          Name: editData?.name ?? "",
          Description: editData?.Description ?? "",
          Brands: editData?.BrandDetails
            ? editData?.BrandDetails?.map((item: any) => ({
                _id: item?.BrandId?.[0]?._id,
                brand: {
                  name: item?.BrandId?.[0]?.Brandname,
                  _id: item?.BrandId?.[0]?._id,
                },
                Grade: item?.Specs,
                updateId: item?.BrandId?.[0]?._id,
              }))
            : [
                {
                  name: "",
                  Grade: [],
                },
              ],
          Users: editData?.DesignationId
            ? editData?.DesignationId?.map((item: any) => ({
                _id: item?._id,
                name: item?.name,
              }))
            : [],
        })
      );
      dispatch(
        setFormData({
          _id: editData?._id ?? "",
          type: editData?.type ?? "",
          Photo: editData?.images?.[0]
            ? {
                name: getFileName(editData?.images?.[0]),
                file: editData?.images?.[0],
              }
            : null,
          Name: editData?.name ?? "",
          Description: editData?.Description ?? "",
          Brands: editData?.BrandDetails
            ? editData?.BrandDetails?.map((item: any) => ({
                _id: item?.BrandId?.[0]?._id,
                brand: {
                  name: item?.BrandId?.[0]?.Brandname,
                  _id: item?.BrandId?.[0]?._id,
                },
                Grade: item?.Specs,
                updateId: item?.BrandId?.[0]?._id,
              }))
            : [
                {
                  name: "",
                  Grade: [],
                },
              ],
          Users: editData?.DesignationId
            ? editData?.DesignationId?.map((item: any) => ({
                _id: item?._id,
                name: item?.name,
              }))
            : [],
        })
      );
      dispatch(setFormMode("Edit"));
      setIsPopupOpen && setIsPopupOpen(false);
      setTimeout(() => {
        dispatch(openPopup("AddToolsForm"));
      }, 100);
    }
    if (varient === "material") {
      dispatch(
        setInitialFormMaterialsData({
          _id: editData?._id ?? "",
          Name: editData?.name ?? "",
          Photo: editData?.images?.[0]
            ? {
                name: getFileName(editData?.images?.[0]),
                file: editData?.images?.[0],
              }
            : null,
          Description: editData?.Description ?? "",
          Brands: editData?.BrandDetails
            ? editData?.BrandDetails?.map((item: any) => ({
                _id: item?.BrandId?.[0]?._id,
                brand: {
                  name: item?.BrandId?.[0]?.Brandname,
                  _id: item?.BrandId?.[0]?._id,
                },
                Grade: item?.Specs,
                ConversionRates: item?.conversionRate?.map((rate: any) => {
                  return {
                    fromUnit: rate?.unit1,
                    toUnit: rate?.unit2,
                    rate: rate?.value,
                    _id: rate?._id,
                  };
                }),
              }))
            : [
                {
                  name: "",
                  Grade: [],
                },
              ],
          Unit:
            editData?.unit?.map((item: string, index: number) => ({
              _id: index + 1,
              name: item,
            })) ?? [],
        })
      );
      dispatch(
        setFormMaterialsData({
          _id: editData?._id ?? "",
          Name: editData?.name ?? "",
          Photo: editData?.images?.[0]
            ? {
                name: getFileName(editData?.images?.[0]),
                file: editData?.images?.[0],
              }
            : null,
          Description: editData?.Description ?? "",
          Brands: editData?.BrandDetails
            ? editData?.BrandDetails?.map((item: any) => ({
                _id: item?.BrandId?.[0]?._id,
                brand: {
                  name: item?.BrandId?.[0]?.Brandname,
                  _id: item?.BrandId?.[0]?._id,
                },
                Grade: item?.Specs,
                ConversionRates: item?.conversionRate?.map((rate: any) => {
                  return {
                    fromUnit: rate?.unit1,
                    toUnit: rate?.unit2,
                    rate: rate?.value,
                    _id: rate?._id,
                  };
                }),
              }))
            : [
                {
                  name: "",
                  Grade: [],
                },
              ],
          Unit:
            editData?.unit?.map((item: string, index: number) => ({
              _id: index + 1,
              name: item,
            })) ?? [],
        })
      );
      dispatch(setFormMode("Edit"));
      setIsPopupOpen && setIsPopupOpen(false);
      setTimeout(() => {
        dispatch(openPopup("AddMaterialsForm"));
      }, 100);
    }
    if (varient === "manpower") {
      dispatch(
        setFormManpowerData({
          _id: editData?._id ?? "",
          type: editData?.Types?.[0] ?? "",
          Name: editData?.name ?? "",
          Description: editData?.Description ?? "",
          Skills: editData?.skills ?? [],
        })
      );
      dispatch(
        setInitialFormManpowerData({
          _id: editData?._id ?? "",
          type: editData?.Types?.[0] ?? "",
          Name: editData?.name ?? "",
          Description: editData?.Description ?? "",
          Skills: editData?.skills ?? [],
        })
      );
      dispatch(setFormMode("Edit"));
      setIsPopupOpen && setIsPopupOpen(false);
      setTimeout(() => {
        dispatch(openPopup("AddManpowerForm"));
      }, 100);
    }
    if (varient === "machinery") {
      dispatch(
        setInitialFormMachineryData({
          _id: editData?._id ?? "",
          Name: editData?.name ?? "",
          Photo: editData?.images?.[0]
            ? {
                name: getFileName(editData?.images?.[0]),
                file: editData?.images?.[0],
              }
            : null,
          Description: editData?.Description ?? "",
          Brands: editData?.BrandDetails
            ? editData?.BrandDetails?.map((item: any) => ({
                _id: item?.BrandId?.[0]?._id,
                brand: {
                  name: item?.BrandId?.[0]?.Brandname,
                  _id: item?.BrandId?.[0]?._id,
                },
                Grade: item?.Specs,
              }))
            : [
                {
                  name: "",
                  Grade: [],
                },
              ],
          Tools: editData?.tools ?? [],
          Fuel: editData?.Fueltype,
        })
      );
      dispatch(
        setFormMachineryData({
          _id: editData?._id ?? "",
          Name: editData?.name ?? "",
          Photo: editData?.images?.[0]
            ? {
                name: getFileName(editData?.images?.[0]),
                file: editData?.images?.[0],
              }
            : null,
          Description: editData?.Description ?? "",
          Brands: editData?.BrandDetails
            ? editData?.BrandDetails?.map((item: any) => ({
                _id: item?.BrandId?.[0]?._id,
                brand: {
                  name: item?.BrandId?.[0]?.Brandname,
                  _id: item?.BrandId?.[0]?._id,
                },
                Grade: item?.Specs,
              }))
            : [
                {
                  name: "",
                  Grade: [],
                },
              ],
          Tools: editData?.tools ?? [],
          Fuel: editData?.Fueltype,
        })
      );
      dispatch(setFormMode("Edit"));
      setIsPopupOpen && setIsPopupOpen(false);
      setTimeout(() => {
        dispatch(openPopup("AddMachineryForm"));
      }, 100);
    }
    if (varient === "category") {
      dispatch(
        setInputValue({
          _id: editData?._id ?? "",
          CategoryName: editData?.name ?? "",
          Description: editData?.Description ?? "",
        })
      );
      setIsPopupOpen && setIsPopupOpen(false);
      dispatch(setFormMode("Edit"));
      setTimeout(() => {
        dispatch(openPopup("AddCategoryForm"));
      }, 100);
    }

    if (varient === "Desigination") {
      dispatch(setDesignationFormData(editData));
      dispatch(setInitialDesignationFormData(editData));
      setIsPopupOpen && setIsPopupOpen(false);
      dispatch(setFormMode("Edit"));
      setTimeout(() => {
        dispatch(openPopup("AddDesignationForm"));
      }, 100);
    }

    if (varient === "Department") {
      console.log("editData in department", editData);
      dispatch(setInitialDepartmentFormData(editData));
      dispatch(setDepartmentFormData(editData));
      setIsPopupOpen && setIsPopupOpen(false);
      dispatch(setFormMode("Edit"));
      setTimeout(() => {
        dispatch(openPopup("AddDepartmentForm"));
      }, 100);
    }
  };

  const handleDelete = () => {
    if (varient === "tools") {
      dispatch(
        setFormData({
          _id: editData?._id ?? "",
          type: editData?.type ?? "",
          Photo: editData?.images?.[0]
            ? {
                name: getFileName(editData?.images?.[0]),
                file: editData?.images?.[0],
              }
            : null,
          Name: editData?.name ?? "",
          Description: editData?.Description ?? "",
          Brands: editData?.BrandDetails
            ? editData?.BrandDetails?.map((item: any) => ({
                _id: item?.BrandId?.[0]?._id,
                brand: {
                  name: item?.BrandId?.[0]?.Brandname,
                  _id: item?.BrandId?.[0]?._id,
                },
                Grade: item?.Specs,
              }))
            : [
                {
                  name: "",
                  Grade: [],
                },
              ],
          Users: editData?.DesignationId
            ? editData?.DesignationId?.map((item: any) => ({
                _id: item?._id,
                name: item?.name,
              }))
            : [],
        })
      );
      setIsPopupOpen && setIsPopupOpen(false);
      setTimeout(() => {
        dispatch(openPopup("deleteTool"));
      }, 100);
    }
    if (varient === "material") {
      dispatch(
        setFormMaterialsData({
          _id: editData?._id ?? "",
          Name: editData?.name ?? "",
          Photo: editData?.images?.[0]
            ? {
                name: getFileName(editData?.images?.[0]),
                file: editData?.images?.[0],
              }
            : null,
          Description: editData?.Description ?? "",
          Brands: editData?.BrandDetails
            ? editData?.BrandDetails?.map((item: any) => ({
                _id: item?.BrandId?.[0]?._id,
                brand: {
                  name: item?.BrandId?.[0]?.Brandname,
                  _id: item?.BrandId?.[0]?._id,
                },
                Grade: item?.Specs,
                ConversionRates: item?.conversionRate?.map((rate: any) => {
                  return {
                    fromUnit: rate?.unit1,
                    toUnit: rate?.unit2,
                    rate: rate?.value,
                    _id: rate?._id,
                  };
                }),
              }))
            : [
                {
                  name: "",
                  Grade: [],
                },
              ],
          Unit:
            editData?.unit?.map((item: string, index: number) => ({
              _id: index + 1,
              name: item,
            })) ?? [],
        })
      );
      setIsPopupOpen && setIsPopupOpen(false);
      setTimeout(() => {
        dispatch(openPopup("deleteMaterial"));
      }, 100);
    }
    if (varient === "manpower") {
      dispatch(
        setFormManpowerData({
          _id: editData?._id ?? "",
          type: editData?.Types?.[0] ?? "",
          Name: editData?.name ?? "",
          Description: editData?.Description ?? "",
          Skills: editData?.skills ?? [],
        })
      );
      setIsPopupOpen && setIsPopupOpen(false);
      setTimeout(() => {
        dispatch(openPopup("deleteManpower"));
      }, 100);
    }
    if (varient === "machinery") {
      dispatch(
        setFormMachineryData({
          _id: editData?._id ?? "",
          Name: editData?.name ?? "",
          Photo: editData?.images?.[0]
            ? {
                name: getFileName(editData?.images?.[0]),
                file: editData?.images?.[0],
              }
            : null,
          Description: editData?.Description ?? "",
          Brands: editData?.BrandDetails
            ? editData?.BrandDetails?.map((item: any) => ({
                _id: item?.BrandId?.[0]?._id,
                brand: {
                  name: item?.BrandId?.[0]?.Brandname,
                  _id: item?.BrandId?.[0]?._id,
                },
                Grade: item?.Specs,
              }))
            : [
                {
                  name: "",
                  Grade: [],
                },
              ],
          Tools: editData?.tools ?? [],
          Fuel: editData?.Fueltype,
        })
      );
      setIsPopupOpen && setIsPopupOpen(false);
      setTimeout(() => {
        dispatch(openPopup("deleteMachine"));
      }, 100);
    }
    if (varient === "category") {
      dispatch(
        setInputValue({
          _id: editData?._id ?? "",
          CategoryName: editData?.name ?? "",
          Description: editData?.Description ?? "",
        })
      );
      setIsPopupOpen && setIsPopupOpen(false);
      setTimeout(() => {
        dispatch(openPopup("deleteCategory"));
      }, 100);
    }

    if (varient === "Desigination") {
      dispatch(setDesignationFormData(editData));
      setIsPopupOpen && setIsPopupOpen(false);
      dispatch(setFormMode("Deleted"));
      setTimeout(() => {
        dispatch(openPopup("AddDesignationForm"));
      }, 100);
    }

    if (varient === "Department") {
      dispatch(setDepartmentFormData(editData));
      setIsPopupOpen && setIsPopupOpen(false);
      dispatch(setFormMode("Deleted"));
      setTimeout(() => {
        dispatch(openPopup("AddDepartmentForm"));
      }, 100);
    }
  };

  // const cardRef = useRef<HTMLDivElement>(null);
  // const popup = useRef<HTMLDivElement>(null);

  const [isHovered, setIsHovered] = useState<{
    id: number | null;
    hoverState: boolean;
  }>({
    id: null,
    hoverState: false,
  });

  console.log("isHovered", isHovered);

  const handleMouseEnter = (id: number) => {
    setIsHovered({ id, hoverState: true });
    console.log("mouse enter with id", id);
  };

  const handleMouseLeave = (id: number) => {
    setIsHovered((prev) => ({
      ...prev,
      hoverState: false,
      id: id,
    }));
  };

  return (
    <div className={styles.cat_popup} ref={cardRef}>
      {/* <div onClick={() => {}} className={styles.cat_popup_view}>
        <div className={styles.cat_popup_viewicon}>
          <ViewIcon />
        </div>
        <h4>View</h4>
      </div> */}
      <div
        ref={popup}
        onClick={(e) => {
          e.stopPropagation();
          dispatch(clearPopups());
          handleEdit();
        }}
        onMouseEnter={() => handleMouseEnter(1)}
        onMouseLeave={() => handleMouseLeave(1)}
        className={`${styles.cat_popup_edit} ${
          isHovered?.id === 1
            ? isHovered.hoverState
              ? styles.edit_hovered
              : styles.edit_notHovered
            : ""
        }`}
      >
        <div className={styles.cat_popup_transition_div_edit}></div>
        <div className={styles.cat_popup_editicon}>
          <YellowEditPencil />
        </div>
        <h4>Edit</h4>
      </div>
      <div
        onClick={(e) => {
          e.stopPropagation();
          dispatch(clearPopups());
          handleDelete();
        }}
        onMouseEnter={() => handleMouseEnter(2)}
        onMouseLeave={() => handleMouseLeave(2)}
        // className={styles.cat_popup_dlt}
        className={`${styles.cat_popup_dlt} ${
          isHovered?.id === 2
            ? isHovered.hoverState
              ? styles.dlt_hovered
              : styles.dlt_notHovered
            : ""
        }`}
      >
        <div className={styles.cat_popup_transition_div_delete}></div>
        <div className={styles.cat_popup_dlticon}>
          <DeleteIcon />
        </div>
        <h4>Delete</h4>
      </div>
    </div>
  );
};

export default CardDotPopup;
