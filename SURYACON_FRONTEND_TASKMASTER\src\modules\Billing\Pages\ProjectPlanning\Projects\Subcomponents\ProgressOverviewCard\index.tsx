import React from "react";
import styles from "./styles/ProgressOverviewCard.module.css";
import { POCProps } from "../../../../../../../interfaces/Modules/Billing/ProjectPlanning/ProjectPlanning";

const ProgressOverviewCard: React.FC<POCProps> = ({
  keyname,
  value,
  color,
  subtextColor = "black",
}) => {
  return (
    <div className={`${styles.progress_overview_card_outer_container}`}>
      <div className={`${styles.progress_overview_card_inner_container}`}>
        <div className={`${styles.progress_overview_card_key_value_section}`}>
          <p
            className={`${styles.progress_overview_key_text} small_text_p`}
            style={{ color: color }}
          >
            {keyname}
          </p>
          <p
            className={` ${styles.progress_overview_value_text} p`}
            style={{ color: subtextColor }}
          >
            {value}
          </p>
        </div>
      </div>
    </div>
  );
};

export default ProgressOverviewCard;
