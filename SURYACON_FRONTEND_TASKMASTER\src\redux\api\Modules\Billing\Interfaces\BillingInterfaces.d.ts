import { JSX } from "react/jsx-runtime";

export interface ApiResponse<T> {
  success?: boolean;
  message: string;
  data: {
    response: T;
  };
}

export interface GetTowerResponse {
  success: boolean;
  data: Task[];
}
export interface Task {
  name: string;
  id: string;
  progressPercentage?: number;
}
export interface TransformedTask {
  name: string;
  _id: string;
  progressPercentage?: number;
}

interface TaskId {
  raft: string;
  column: string;
  slab: string;
  mivan: string;
  _id: string;
}

interface Result {
  Rate: number;
}

export interface TowerDataProp {
  _id: string;
  category: string;
  name: string;
  location_drawing: string[];
  project_id: string;
  area: number;
  number_of_floors: number;
  number_of_basements: number;
  location_duration: number;
  structure_type: string;
  conventionals: string[];
  mivan: string[];
  remarks: string;
  TaskId: TaskId;
  __v: number;
  isDeleted: boolean;
  updatedAt: string;
  result: Result[];
  combineRoutes: string[];
}

interface GetSubTaskResponse {
  success: boolean;
  data: {
    response: SubTaskDataProp[];
  };
}

interface SubTaskDataProp {
  _id: string;
  unit: string[];
  subtaskWeighatages: number;
  itemtype: string;
  name: string;
}

interface Subtask {
  _id: string;
  Unit: string[];
  subtaskWeighatages: number;
  itemtype: string;
  name: string;
}

interface TaskDetails {
  _id: string;
  Unit: string;
  Quantity: number;
  itemtype: string;
  name: string;
  Description: string;
}

interface FinalData {
  finalData: Subtask[];
  details: TaskDetails;
}
export interface taskDetailandFinalData {
  _id: string;
  Unit: string;
  Quantity: number;
  itemtype: string;
  name: string;
  Description: string;
  finalData: Subtask[];
  details: TaskDetails;
}

export interface ApiResponse<T> {
  success?: boolean;
  message: string;
  data: {
    response: T;
  };
}

export interface AddProjectRequest {
  name: string;
  photo: string[];
  Address: string;
  project_type: string;
  estimate_budget: number;
  project_area: number;
  rate_type: string;
  project_duration: number;
  project_drawing: string;
  project_status: string;
  clientName: string;
  ClientPhoneNumber: string;
  Remarks: string;
  startDate: string;
}
export interface ResponseProjectData extends AddProjectRequest {
  _id: string;
}
export interface Responses {
  data: {
    length: number;
    map(
      arg0: (designationItem: any, index: any) => JSX.Element
    ): import("react").ReactNode;
    responseData: ResponseProjectData[];
    date: string;
  };
}
export interface AddTowerLocation {
  category: string;
  name: string;
  location_drawing: string[];
  project_id: string;
  area: number;
  number_of_floors: number;
  number_of_basements: number;
  location_duration: number;
  structure_type: string;
  conventionals: string[];
  mivan: string[];
  remarks: string;
}
