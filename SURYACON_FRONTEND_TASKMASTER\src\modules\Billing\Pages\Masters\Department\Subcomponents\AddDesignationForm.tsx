import React, { FC, memo, useCallback, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useParams } from "react-router-dom";
import Datafield from "../../../../../../components/Reusble/Billing/Masters/Datafield";
import FloatingLabelInput from "../../../../../../components/Reusble/Global/FloatingLabel";
import RadioBtns from "../../../../../../components/Reusble/Global/RadioBtns";
import { AddDesignationFormProps } from "../../../../../../interfaces/Modules/Billing/DepartmentInterfaces/DepartmentInterfaces";
import {
  useAddDepartmentDesignationMutation,
  useDeleteDesiginationByIdMutation,
  useUpdateDesiginationMutation,
} from "../../../../../../redux/api/Modules/Billing/Billingapi";
import { setToast } from "../../../../../../redux/features/Modules/Reusble/ToastSlice";
import {
  resetDesignationFormData,
  resetInitialDesignationFormData,
  setDesignationFormData,
} from "../../../../../../redux/features/Modules/TaskMaster/Slices/MastersSlice";
import { RootState } from "../../../../../../redux/store";
import DynamicGradeInput from "../../Subcomponents/DynamicGradeInput";
import styles from "./../Styles/Department.module.css";
import AddFormWrapper from "./AddFormWrapper";
import FormChildTemplate from "./FormChildTemplate";

// Form Error InitialState
const initialErrorsState = {
  designationName: {
    error: false,
    message: "",
  },
  jobDescription: {
    error: false,
    message: "",
  },
  requiredExperience: {
    error: false,
    message: "",
  },
  location: {
    error: false,
    message: "",
  },
  qualifications: {
    error: false,
    message: "",
  },
  supervisor: {
    error: false,
    message: "",
  },
};

// AddDesignationForm component handles Add/Edit/Delete/Reason modes for Designation data.
// It manages form state, validation, API calls, and renders the form UI and summary views.
const AddDesignationForm: FC<AddDesignationFormProps> = ({
  isClosing = false,
  setIsClosing,
  handleClose,
}) => {
  // console.log("AddDesignationForm rendered");
  const [isSummary, setIsSummary] = useState(false);
  const [isDiscard, setIsDiscard] = useState(false);
  const [errors, setErrors] = React.useState(initialErrorsState); // Error State with message
  const dispatch = useDispatch();
  const formMode = useSelector((state: RootState) => state.masterForm.formMode); // form Mode -> ADD, EDIT, DELETED, REASON
  // Get current designation form data from Redux store
  const formData = useSelector(
    (state: RootState) => state.masterForm.designationFormData
  );
  // Get initial designation form data from Redux store (used for comparison/reset)
  const initialFormData = useSelector(
    (state: RootState) => state.masterForm.initialDesignationFormData
  );
  const { departmentId } = useParams();
  const [updateDesigination] = useUpdateDesiginationMutation(); // updating a designation
  const [deleteDesiginationById] = useDeleteDesiginationByIdMutation(); // deleting a designation by ID
  const [addDepartmentDesignation] = useAddDepartmentDesignationMutation(); // adding a department designation

  // console.log("Updated Form Data:", formData);

  // Updates Redux store with new field value and clears error for that field if set.
  const handleInputChange = (field: string, value: any) => {
    dispatch(
      setDesignationFormData({
        ...formData,
        [field]: value,
      })
    );

    // If there was an error for this field, clear it on change
    if (errors[field].error) {
      setErrors((prevErrors) => ({
        ...prevErrors,
        [field]: {
          error: false,
          message: "",
        },
      }));
    }
    console.log("Value: designation formData: ", formData);
  };

  // Returns true if all main fields in the designation form are empty, otherwise false.
  const isEmpty = (data: any) => {
    if (
      data?.designationName.trim() === "" &&
      data?.jobDescription.trim() === "" &&
      (data?.requiredExperience === "" || data?.requiredExperience === null) &&
      data?.location === "" &&
      data?.qualifications.length === 0
    ) {
      return true;
    } else {
      return false;
    }
  };

  // Resets form state, errors, and Redux data to their initial values.
  const setInitialDesignationState = () => {
    if (isSummary) setIsSummary(false);
    if (isDiscard) setIsDiscard(false);
    if (errors) setErrors(initialErrorsState);
    if (!isEmpty(formData)) dispatch(resetDesignationFormData());
    if (formMode === "Edit" && !isEmpty(initialFormData))
      dispatch(resetInitialDesignationFormData());
  };

  // Checks if two arrays are different by comparing their sorted values (by name if object, or value if primitive).
  const isArrayDifferent = (arr1: any[], arr2: any[]) => {
    if (arr1.length !== arr2.length) return true;

    const sortedArr1 = [...arr1].sort();
    const sortedArr2 = [...arr2].sort();

    // Compare each item by name (if object) or by value (if primitive)
    return sortedArr1.some((item, index) => {
      const item1 = typeof item === "object" ? item.name : item;
      const item2 =
        typeof sortedArr2[index] === "object"
          ? sortedArr2[index].name
          : sortedArr2[index];
      return item1 !== item2;
    });
  };

  // Checks if the form data has changed compared to the initial data (for Add/Edit mode).
  const hasFormChanged = () => {
    if (formMode === "Add") {
      return (
        formData.designationName.trim() !== "" ||
        formData.jobDescription.trim() !== "" ||
        formData.requiredExperience !== null ||
        formData.location.trim() !== "" ||
        formData.qualifications.length > 0 ||
        formData.supervisor !== null
      );
    } else {
      return (
        initialFormData.jobDescription.trim() !==
          formData.jobDescription.trim() ||
        initialFormData.designationName.trim() !==
          formData.designationName.trim() ||
        initialFormData.requiredExperience != formData.requiredExperience ||
        initialFormData.location.trim() !== formData.location.trim() ||
        isArrayDifferent(
          initialFormData.qualifications,
          formData.qualifications
        ) ||
        JSON.stringify(initialFormData.supervisor) !==
          JSON.stringify(formData.supervisor)
      );
    }
  };
  console.log("initialformdata:", initialFormData, "mainformdata:", formData);

  // Handles closing the form and resets state after a short delay.
  const onClose = () => {
    handleClose("AddDesignationForm");
    setTimeout(setInitialDesignationState, 400);
  };

  // Handles form submission for Add, Edit, and Delete modes, dispatches API calls and shows toast notifications.
  const handleSubmit = async () => {
    const formattedData = {
      _id: formData?._id,
      name: formData?.designationName,
      Description: formData?.jobDescription,
      Experience: Number(formData?.requiredExperience) || 0,
      location: formData?.location,
      RequiredQualification: formData?.qualifications || [],
      DepartmentId: departmentId,
      ReporterId: formData?.supervisor?._id || null,
    };

    // shows toast notifications based on result.
    const showToast = (
      message: string,
      type: "success" | "info" | "danger"
    ) => {
      dispatch(setToast({ isOpen: true, messageContent: message, type }));
    };

    try {
      // check if any update in form edit state and edit Api
      if (formMode === "Edit") {
        if (!hasFormChanged()) {
          showToast("No changes made to the designation", "info");
          setIsSummary(false);
          return;
        }
        const response = await updateDesigination(formattedData).unwrap();
        if (response?.success) {
          onClose();
          showToast(
            response?.data || "Designation updated successfully",
            "success"
          );
        }
      } else if (formMode === "Deleted") {
        // Delete API
        const response = await deleteDesiginationById(
          formattedData._id
        ).unwrap();
        if (response?.success) {
          onClose();
          showToast(
            response?.data || "Designation deleted successfully",
            "success"
          );
        }
      } else {
        // Add NEW API
        const response = await addDepartmentDesignation(formattedData).unwrap();
        if (response?.success) {
          onClose();
          showToast(
            response?.data || "Designation added successfully",
            "success"
          );
        }
      }
    } catch (error) {
      console.error("Submit error:", error);
      showToast(error?.data?.message, "danger");
    }
  };

  // Validates form fields and sets error state, shows warning toast if any required field is empty.
  const validateError = useCallback(() => {
    const newErrors = { ...errors };
    let hasErrors = false;

    Object.entries(formData ?? {}).forEach(([key, value]) => {
      if (key === "_id") return;
      if (key === "supervisor") return;
      if (value === undefined) return;

      let isEmpty = false;
      if (Array.isArray(value)) {
        isEmpty = !value || value.length <= 0;
      } else if (typeof value === "object") {
        isEmpty = !value || Object.keys(value).length < 0;
      } else {
        isEmpty = !value || value.toString().trim() === "";
      }

      newErrors[key] = {
        error: isEmpty,
        message: isEmpty ? `${key} is required.` : "",
      };

      if (isEmpty) {
        hasErrors = true;
        // Double dispatch is likely a mistake, but left as in original code.
        dispatch(
          setToast({
            isOpen: true,
            messageContent: "Please fill the required fields",
            type: "warning",
          })
        );
      }
    });

    setErrors(newErrors);

    return hasErrors;
  }, [formData, errors]);

  // Determines if a field was updated, discarded, or unchanged compared to initial data (for Edit mode).
  const formUpdatedField = React.useCallback(
    (
      field: string,
      value: string = ""
    ): "default" | "updated" | "discard" | undefined => {
      if (formMode !== "Edit") return "default";

      const key = field as keyof typeof formData;
      const currentValue = formData?.[key];
      const originalValue = initialFormData?.[key];

      // Handle Arrays
      if (Array.isArray(originalValue)) {
        const currentArray: string[] = Array.isArray(currentValue)
          ? currentValue.map((item: { _id?: string; name: string } | string) =>
              typeof item === "object" ? item?.name : item
            )
          : [];

        const originalArray: string[] = originalValue.map(
          (item: { _id?: string; name: string } | string) =>
            typeof item === "object" ? item?.name : item
        );

        const isRemoved =
          !currentArray.includes(value) && originalArray.includes(value);
        const isAdded =
          currentArray.includes(value) && !originalArray.includes(value);

        if (isRemoved) return "discard";
        if (isAdded) return "updated";
        return "default";
      }

      // Handle Objects with _id
      if (
        typeof currentValue === "object" &&
        currentValue !== null &&
        "_id" in currentValue
      ) {
        const currentId = currentValue?._id;
        const originalId = originalValue?._id;

        if (!currentId && originalId) return "discard"; // removed
        if (currentId && currentId !== originalId) return "updated"; // changed
        return "default";
      }

      // Handle Primitives (string, number, etc.)
      const current = String(currentValue ?? "").trim();
      const original = String(originalValue ?? "").trim();
      console.log("check Update:", current, original);

      if (original && !current) return "discard"; // was removed
      if (current && current != original) return "updated"; // changed
      return "default";
    },
    [formMode, formData, initialFormData]
  );

  // console.log("Value: designation formData: ", formData);

  return (
    <div className={`${styles.add_designation_form} }`}>
      <AddFormWrapper
        key="designation-form"
        label="Designation"
        isOpen={!isClosing}
        headingLabel={
          formMode === "Add" ? "Add Designation" : "Edit Designation"
        }
        validateIsEmpty={() => isEmpty(formData)}
        onClose={onClose}
        isSummary={isSummary}
        isDiscard={isDiscard}
        setSummary={setIsSummary}
        setDiscard={setIsDiscard}
        isRightButtonVisible={true}
        isDeleted={formMode === "Deleted"}
        isEdited={formMode === "Edit"}
        validateError={validateError}
        btn1Label="Cancel"
        btn1Handler={() => {}}
        btn2Handler={
          isSummary || formMode === "Deleted" ? handleSubmit : () => {}
        }
        validateFormUpdated={hasFormChanged}
      >
        {/* Renders the designation form fields for Add/Edit mode, including Designation Name, Job Description, Required Experience, Location, Qualifications, and Supervisor. */}
        {!isSummary &&
          !isDiscard &&
          (formMode === "Add" || formMode === "Edit") && (
            <div>
              <FloatingLabelInput
                id="designationName"
                type="text"
                label={"Designation Name"}
                value={formData?.designationName || ""}
                onInputChange={(value) =>
                  handleInputChange("designationName", value)
                }
                focusOnInput={true}
                error={errors.designationName.error}
                props="one_line"
              />
              <FloatingLabelInput
                id="jobDescription"
                type="text"
                label={"Job Description"}
                value={formData?.jobDescription || ""}
                onInputChange={(value) =>
                  handleInputChange("jobDescription", value)
                }
                error={errors.jobDescription.error}
                props="description_prop2"
              />
              <div style={{ position: "relative" }}>
                <FloatingLabelInput
                  id="requiredExperience"
                  label={"Required Experience"}
                  value={formData?.requiredExperience || ""}
                  type="number"
                  onPaste={(e) => {
                    // console.log("Event : ", e.clipboardData.getData("text"));
                    const pastedValue = e.clipboardData.getData("text");
                    if (!/^\d+(\.\d+)?$/.test(pastedValue)) {
                      e.preventDefault();
                    }
                  }}
                  onInputChange={(value) => {
                    console.log("Value: form", value);
                    handleInputChange("requiredExperience", value);
                  }}
                  // maxLength={3}
                  maxlength={5}
                  error={errors.requiredExperience.error}
                  focusOnInput={false}
                  props="one_line"
                />

                <p className={`${styles.input_tag} small_text_p`}>
                  {(formData?.requiredExperience ?? 0) > 1 ? "years" : "year"}
                </p>
              </div>

              <div
                className={`${styles.radiobtn_wrapper} ${
                  errors.location.error && styles.error
                }`}
              >
                <h4 className={styles.radio_btn_label}>Location</h4>
                <RadioBtns
                  id="location12"
                  name="designation-location"
                  selectedValue={formData?.location}
                  onValueChange={(value: string) => {
                    // console.log("Selected Location:", value);
                    // setSelectedLocation(value);
                    handleInputChange("location", value);
                  }}
                  options={[
                    { value: "headoffice", label: "Head office" },
                    { value: "site", label: "Site" },
                  ]}
                  error={errors.location.error}
                />
              </div>
              <div className={styles.datafield_field_section}>
                <DynamicGradeInput
                  placeholder="Qualification"
                  label="Qualifications"
                  variant="qualifications"
                  needBold={true}
                  needBoldColor="var(--text-black-60)"
                  initialData={formData?.qualifications}
                  labelHeader="Qualifications"
                  onGradesUpdate={(value: string[]) => {
                    if (errors["qualifications"].error) {
                      setErrors((prevErrors) => ({
                        ...prevErrors,
                        ["qualifications"]: {
                          error: false,
                          message: "",
                        },
                      }));
                    }
                    dispatch(
                      setDesignationFormData({
                        ...formData,
                        qualifications: value,
                      })
                    );
                  }}
                  callbackDelete={(index: number) => {
                    const updatedQualifications =
                      formData?.qualifications.filter(
                        (item: any, i) => i !== index
                      );
                    dispatch(
                      setDesignationFormData({
                        ...formData,
                        qualifications: updatedQualifications,
                      })
                    );
                  }}
                  error={errors.qualifications.error}
                />
                <Datafield
                  heading="Supervisor"
                  selectedValues={
                    formData?.supervisor && [formData?.supervisor]
                  }
                  needBold={true}
                  error={errors.supervisor.error}
                  callbackDelete={() => {
                    dispatch(
                      setDesignationFormData({
                        ...formData,
                        supervisor: null,
                      })
                    );
                  }}
                />
              </div>
            </div>
          )}

        {/* Renders summary/discard/deleted view using FormChildTemplate with designation fields. */}
        {formMode !== "Reason" &&
          (isDiscard || isSummary || formMode === "Deleted") && (
            <FormChildTemplate
              formData={formData}
              formUpdatedField={formUpdatedField}
              formDataLabels={
                formMode === "Reason"
                  ? {
                      reason: "Reason",
                    }
                  : {
                      designationName: "Designation Name",
                      jobDescription: "Job Description",
                      requiredExperience: "Required Experience",
                      location: "Location",
                      qualifications: "Qualifications",
                      supervisor: "Supervisor",
                    }
              }
              initialFormData={initialFormData}
            />
          )}

        {/* Renders reason view using FormChildTemplate with only the reason field. */}
        {formMode === "Reason" && (
          <FormChildTemplate
            formData={formData || {}}
            formDataLabels={{
              reason: "Reason",
            }}
          />
        )}
      </AddFormWrapper>
    </div>
  );
};

export default memo(AddDesignationForm);
