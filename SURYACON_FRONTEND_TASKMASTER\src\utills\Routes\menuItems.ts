import { useSelector } from "react-redux";
import {
  // SummarySVG,
  // BillSvg,
  // AnalyticsSvg,
  // ReportSvg,
  // BenefitsSvg,
  // SettingsSvg,
  // SupportSvg,
  DashboardSvg,
} from "../../assets/SidebarAssets/SVGs/index";
import { RootState } from "../../redux/store";

interface SubRoute {
  route: string;
  label: string;
  icon?: React.ElementType;
}

interface MenuItem {
  id: number;
  slug?: string;
  label: string;
  icon?: React.ElementType;
  subRoute: SubRoute[];
  otherSubRoute: SubRoute[];
}

export const mainMenuItems: MenuItem[] = [
  {
    id: 1,
    slug: "dashboard",
    label: "Dashboard",
    icon: DashboardSvg,
    subRoute: [{ route: "/tools-master", label: "Tools" }],
    otherSubRoute: [],
  },
  {
    id: 2,
    slug: "billing",
    label: "Billings",
    icon: DashboardSvg,
    subRoute: [{ route: "/bil", label: "Tools" }],
    otherSubRoute: [],
  },
  {
    id: 3,
    slug: "vendors",
    label: "Vendors",
    icon: DashboardSvg,
    subRoute: [],
    otherSubRoute: [],
  },
  {
    id: 4,
    slug: "items",
    label: "Items",
    icon: DashboardSvg,
    subRoute: [],
    otherSubRoute: [],
  },
  {
    id: 5,
    slug: "rmcs",
    label: "RMC's",
    icon: DashboardSvg,
    subRoute: [],
    otherSubRoute: [],
  },
  {
    id: 6,
    slug: "tools-master",
    label: "Tools",
    icon: DashboardSvg,
    subRoute: [],
    otherSubRoute: [],
  },
  {
    id: 7,
    slug: "materials-master",
    label: "Materials",
    icon: DashboardSvg,
    subRoute: [],
    otherSubRoute: [],
  },
  {
    id: 8,
    slug: "manpower-master",
    label: "Manpower",
    icon: DashboardSvg,
    subRoute: [],
    otherSubRoute: [],
  },
  {
    id: 9,
    slug: "machinery-master",
    label: "Machinery",
    icon: DashboardSvg,
    subRoute: [],
    otherSubRoute: [],
  },

  // {
  //   id: 10,
  //   slug: "billing",
  //   label: "Billing",
  //   icon: DashboardSvg,
  //   subRoute: [
  //     { route: "/billing/location", label: "Planning" },
  //     { route: "/monthly-target", label: "Monthly Target" },
  //   ],
  //   otherSubRoute: [],
  // },
  {
    id: 11,
    slug: "category",
    label: "Task Master",
    icon: DashboardSvg,
    subRoute: [],
    otherSubRoute: [],
  },
  {
    id: 12,
    slug: "department",
    label: "Department",
    icon: DashboardSvg,
    subRoute: [
      {route : "/department/:departmentId", label : "Designation"}
    ],
    otherSubRoute: [],
  },
  // {
  //   id: 3,
  //   slug: "project-manager/single-Kanban-view",
  //   label: "Project Manager",
  //   // icon: DashboardSvg,
  //   subRoute: [
  //     { route: "/category", label: "Task" },
  //     { route: "/monthly-target", label: "monthly target" },
  //     { route: "/billing", label: "billing" },
  //     { route: "/projectmanager", label: "projectmanager" },
  //   ],
  //   otherSubRoute: [],
  // },
];
