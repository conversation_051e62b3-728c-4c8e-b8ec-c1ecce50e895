import { useState, useEffect } from "react";

import styles from "../Styles/TCRpopup.module.css";
import {
  AddCategoryIcon,
  BackIcon,
  CloudUpload,
  DeleteIcon,
  PreviewIcon,
  ReverseArrow,
} from "../../../../../assets/icons";
import FloatingLabelInput from "../../FloatingLabel";
import { useToast } from "../../../../../hooks/ToastHook";
import {
  getFileName,
  getOnlineURl,
  isBase64,
} from "../../../../../functions/functions";
import { image_url } from "../../../../../config/urls";
import { useAppDispatch } from "../../../../../redux/hooks/Modules/Reduxhooks/ReduxHooks";

interface PhotoSection {
  id: number;
  photo: string | null;
  referenceDetail: string;
}

interface TcrPhotoCheckboxPageProps {
  isEdit?: boolean;
  photoUploadSections: PhotoSection[];
  setPhotoErrors: React.Dispatch<
    React.SetStateAction<{ id: number; error: string }[]>
  >;
  photoErrors: { id: number; error: string }[];
  setPhotoUploadSections: React.Dispatch<React.SetStateAction<PhotoSection[]>>;
}

function TcrPhotoCheckboxPage({
  isEdit = false,
  photoErrors,
  setPhotoErrors,
  photoUploadSections,
  setPhotoUploadSections,
}: TcrPhotoCheckboxPageProps) {
  const [expandedSectionId, setExpandedSectionId] = useState<number | null>(
    null
  );
  const [imageUrls, setImageUrls] = useState<{ [key: string]: string }>({});
  const dispatch = useAppDispatch();
  const showToast = useToast();
  console.log(photoErrors, "photoerrors");
  useEffect(() => {
    if (photoUploadSections.length === 0) {
      setPhotoUploadSections([{ id: 1, photo: null, referenceDetail: "" }]);
    }
  }, [photoUploadSections]);
  console.log("photodetails>>>tcphoto", photoUploadSections);
  // useEffect(() => {
  //   localStorage.setItem(
  //     "photoUploadSections",
  //     JSON.stringify(photoUploadSections)
  //   );
  // }, [photoUploadSections]);

  const handleAddSection = () => {
    setPhotoUploadSections((prevSections) => [
      ...prevSections,
      {
        id: prevSections[prevSections.length - 1]?.id + 1,
        photo: null,
        referenceDetail: "",
      },
    ]);
  };

  const handleFileChange = (
    event: React.ChangeEvent<HTMLInputElement>,
    id: number
  ) => {
    const fileInput = event.target;
    const file = fileInput.files ? fileInput.files[0] : null;

    if (file) {
      const validTypes = ["image/jpeg", "image/jpg", "image/png"];
      if (validTypes.includes(file.type)) {
        const reader = new FileReader();
        reader.onload = () => {
          const base64Image = reader.result as string;
          setPhotoUploadSections((prevSections) =>
            prevSections.map((section) =>
              section.id === id
                ? {
                    ...section,
                    photo: base64Image,
                    fileName: file.name,
                  }
                : section
            )
          );
          setImageUrls((prev) => ({
            ...prev,
            [id]: base64Image,
          }));

          fileInput.value = "";
        };
        reader.readAsDataURL(file);
      } else {
        alert("Please upload a valid image (JPEG, JPG, PNG).");
      }
    }
  };

  console.log(photoUploadSections, "photoUploadSections");
  const handleReferenceDetailChange = (value: string, id: number) => {
    setPhotoUploadSections((prevSections) =>
      prevSections.map((section) =>
        section.id === id ? { ...section, referenceDetail: value } : section
      )
    );
  };

  const handleDeletePhoto = (id: number) => {
    setPhotoUploadSections((prevSections) =>
      prevSections.filter((section) => section.id !== id)
    );
  };

  const handledeletePhotoOnly = (id: number) => {
    setPhotoUploadSections((prevSections) => {
      const updatedSections = prevSections.map((section) =>
        section.id === id
          ? { ...section, photo: null, fileName: null }
          : section
      );
      return updatedSections;
    });
  };
  useEffect(() => {
    const fetchImages = async () => {
      for (const section of photoUploadSections) {
        const key = section.id;

        if (!imageUrls[key] && section.photo && !isBase64(section.photo)) {
          const url = await getOnlineURl(section.photo, dispatch);
          setImageUrls((prev) => ({ ...prev, [key]: url }));
        } else if (!imageUrls[key] && isBase64(section.photo!)) {
          setImageUrls((prev) => ({ ...prev, [key]: section.photo }));
        }
      }
    };
    console.log(fetchImages, "This is fetched image");
    fetchImages();
  }, [photoUploadSections, imageUrls, dispatch]);
  return (
    <div className={styles.tcrphotopopup_maincontainer}>
      {photoUploadSections.map((section) => (
        <div
          key={section.id}
          className={`${styles.tcrphotopopup_container} ${
            expandedSectionId === section.id ? styles.expanded : ""
          }`}
          style={
            photoErrors?.some((e) => e.id === section?.id && e.error === "both")
              ? { border: "1px solid red" }
              : undefined
          }
        >
          <div
            className={styles.deleteButton}
            onClick={() => handleDeletePhoto(section.id)}
          >
            <DeleteIcon />
          </div>
          {(section.photo !== null ||
            section.photo == null ||
            section?.referenceDetail !== "") && (
            <div
              className={styles.deleteButton2}
              onClick={() => handledeletePhotoOnly(section.id)}
            >
              <DeleteIcon />
            </div>
          )}
          <div
            className={`${styles.tcrphotopopup_uploadphotocontainer} ${
              expandedSectionId === section.id ? styles.expandedUpload : ""
            }`}
            style={
              photoErrors?.some(
                (e) => e.id === section?.id && e.error === "photo"
              )
                ? { border: "1px solid red" }
                : undefined
            }
          >
            <div className={styles.uploadWrapper}>
              {section.photo ? (
                <div
                  className={styles.imageContainer}
                  // style={
                  //   photoErrors?.some((e) => e.id === section?.id && e.error !=="both")
                  //     ? { border: "1px solid red" }
                  //     : undefined
                  // }
                >
                  {imageUrls[section.id] && (
                    <div className={styles.imageContainer}>
                      <img
                        src={imageUrls[section.id]}
                        alt="Uploaded"
                        className={styles.uploadedImage}
                      />
                    </div>
                  )}
                  <div className={styles.overlayButtons}>
                    <div
                      className={styles.previewButton}
                      onClick={() =>
                        setExpandedSectionId(
                          expandedSectionId === section.id ? null : section.id
                        )
                      }
                    >
                      {expandedSectionId === section.id ? (
                        <>
                          <BackIcon /> <p className="p_tag_14px">Back</p>
                        </>
                      ) : (
                        <>
                          <PreviewIcon /> <p className="p_tag_14px">Preview</p>
                        </>
                      )}
                    </div>
                    <div
                      className={styles.Image_picker_repeat}
                      style={{ cursor: "pointer" }}
                      onClick={() =>
                        document
                          .getElementById(`fileInput-${section.id}`)
                          ?.click()
                      }
                    >
                      <ReverseArrow />
                    </div>
                  </div>
                </div>
              ) : (
                <div
                  onClick={() =>
                    document.getElementById(`fileInput-${section.id}`)?.click()
                  }
                  className={styles.emptyState}
                >
                  <CloudUpload />
                  <h4 style={{ color: "var(--text-black-60)" }}>
                    Upload a reference photo
                  </h4>
                  <p
                    className="p_tag_14px"
                    style={{ color: "var(--text-black-28)" }}
                  >
                    Supports: png, jpeg
                  </p>
                </div>
              )}
              <input
                id={`fileInput-${section.id}`}
                type="file"
                accept=".jpeg,.jpg,.png"
                style={{ display: "none" }}
                onChange={(event) => handleFileChange(event, section.id)}
              />
            </div>
          </div>
          <FloatingLabelInput
            label="Reference Detail"
            id={`ReferenceDetail-${section.id}`}
            placeholder="Reference Detail"
            value={section.referenceDetail}
            props="description_prop"
            isInvalid={photoErrors?.some(
              (e) => e.id === section.id && e.error === "referencedetails"
            )}
            onInputChange={(value) => {
              setPhotoErrors && setPhotoErrors([]);
              handleReferenceDetailChange(value, section.id);
            }}
          />
        </div>
      ))}
      <div
        className={styles.tcrphotopopup_addphotodiv}
        onClick={() => {
          if (!photoUploadSections[photoUploadSections.length - 1].photo) {
            showToast({
              messageContent: "Please upload last reference photo!",
              type: "warning",
            });
          }
          photoUploadSections[photoUploadSections.length - 1]?.photo &&
            handleAddSection();
          // handleAddSection()
        }}
        style={{ cursor: "pointer" }}
      >
        Photo <AddCategoryIcon />
      </div>
    </div>
  );
}

export default TcrPhotoCheckboxPage;
