import React, { useState, useEffect, useRef } from "react";
import {
  Outlet,
  useParams,
  useLocation,
  useNavigate,
  redirectDocument,
} from "react-router-dom";
import styles from "./Styles/MainPage.module.css";
import Sidebar from "./Subcomponents/sidebar";
import MainMenu from "../../../../components/Common/Sidebar/SubComponents/MainMenu";
import LocationHeader from "../ProjectPlanning/Locations/Subcomponents/LocationHeader";
import MonthlyTargetHeader from "../BillingApproval/Subcomponents/MonthlyTargetHeader";
import MTcreationHeader from "../BillingApproval/Subcomponents/MTcreationHeader";
import NavigationComponent from "../../../../components/Reusble/Global/navigationComponents/commonHeaderComponent";
import PlanningTableHeader from "../ProjectPlanning/Planning/SendAprovalHeader/PlanningTableHeader";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../../../redux/store";
import { ToggleTowerSwitch } from "../BillingApproval/Subcomponents/ToggleTowerSwitch";
import TargetBadge from "../../../../components/Reusble/Global/TargetBadge/TargetBadge";
import { SendIcon, Uploadicon } from "../../../../assets/icons";
import { initializeDatabase } from "../../../../functions/functions";
import { setSelectedTowerLocationName } from "../../../../redux/features/Modules/Billing/ProjectPlanning/Slices/ProjectSlice";

// Map of route keys to display names for breadcrumb
const routeDisplayNames: Record<string, string> = {
  summary: "Summary",
  location: "Planning",
  "monthly-target": "Monthly Target",
  "billing-approval": "Billing Approval",
  settings: "Settings",
  support: "Support",
  masters: "Masters",
  "actual-work": "Actual Work",
  inventory: "Inventory",
  "petty-contractors": "Petty Contractors",
  "site-billing": "Site Billing",
};

// Component to render the appropriate header based on route
const RouteHeader = ({ route }: { route: string }) => {
  const location = useLocation();
  const { towerLocationId } = useParams<{
    projectId: string;
    towerLocationId: string;
  }>();


  const isPlanningTablePage =
    towerLocationId &&
    location.pathname.includes(`/location/${towerLocationId}`);

  switch (route) {
    case "location":
      if (isPlanningTablePage) {
        return <PlanningTableHeader />;
      }
      return <LocationHeader />;
    case "monthly-target":
      return <MonthlyTargetHeader />;
    case "inventory":
      return <MTcreationHeader />;
    default:
      return null;
  }
};

const BillingMainPage: React.FC = () => {
  const { projectId, towerLocationId } = useParams<{
    projectId: string;
    towerLocationId: string;
  }>();
  const [currentRoute, setCurrentRoute] = useState<string>("");
  const [projectName, setProjectName] = useState<string>("");
  const location = useLocation();
  const navigate = useNavigate();
  const mainContentRef = useRef<HTMLDivElement>(null);

const towerLocationName = useSelector(
  (state: RootState) => state.projectLocalDb.selectedTowerLocationName
);


console.log("Breadcrumb towerLocationName:", towerLocationName, "towerLocationId:", towerLocationId);


  useEffect(() => {
    // Scroll to top when the route changes
    if (mainContentRef.current) {
      mainContentRef.current.scrollTop = 0;
    }
  }, [location.pathname]);

  // Get the project name from Redux store if available
  const selectOpenedProject = useSelector(
    (state: RootState) => state.projectLocalDb?.openedProject
  );



  // Fetch project name based on projectId
  useEffect(() => {
    if (projectId) {
      // Use the project name from Redux if available, otherwise use placeholder
      if (selectOpenedProject) {
        setProjectName(selectOpenedProject);
      } else {
        setProjectName(`Project ${projectId}`);
      }
    }
  }, [projectId, selectOpenedProject]);

  // Extract current route from URL path
  useEffect(() => {
    const pathParts = location.pathname.split("/");
    // The route we want will be after the projectId in the URL
    const currentPathIndex =
      pathParts.findIndex((part) => part === projectId) + 1;
    if (currentPathIndex < pathParts.length) {
      setCurrentRoute(pathParts[currentPathIndex]);
    }
  }, [location, projectId]);

  // This function will be passed to the Sidebar component
  const handleRouteChange = (route: string) => {
    setCurrentRoute(route);
  };

  // Get the display name for the current route
  const currentPageName = routeDisplayNames[currentRoute] || currentRoute;

  // Create navigation route array for breadcrumb
  const navigationRoutes = (() => {
    const baseRoutes = [
      { route: "/billing", title: "Projects" },
      { route: `/billing/main/${projectId}`, title: projectName },
    ];


    if (towerLocationId && location.pathname.includes(`/location/${towerLocationId}`)) {
      return [
        ...baseRoutes,
        { route: `/billing/main/${projectId}/location`, title: "Planning" },
        {
          route: `/billing/main/${projectId}/location/${towerLocationId}`,
          title:  towerLocationName,
        },
      ];
    }

    // If just on the planning page (not inside a tower)
    if (location.pathname.includes("/location")) {
      return [
        ...baseRoutes,
        { route: `/billing/main/${projectId}/location`, title: "Planning" },
      ];
    }

    // Default: other pages
    return [...baseRoutes, { route: "#", title: currentPageName }];
  })();

  // Handle navigation from breadcrumb
  const handleOutsideNavigation = (title: string, route: string) => {
    if (route !== "#") {
      navigate(route);
    }
  };

  return (
    <div className={styles.page_container}>
      {/* Header section with Navigation Component */}
      <div className={styles.header_container}>
        <div className={styles.navigation_container}>
          <MainMenu />
          <NavigationComponent
            route={navigationRoutes}
            handleOutsideNavigation={handleOutsideNavigation}
          />
        </div>
        <RouteHeader route={currentRoute} />
      </div>

      {/* Content section with sidebar on left and main content with Outlet */}
      <div className={styles.content_wrapper}>
        <div className={styles.sidebar_container}>
          <Sidebar onRouteChange={handleRouteChange} />
        </div>
        <div ref={mainContentRef} className={styles.main_content}>
          <Outlet />
        </div>
      </div>
    </div>
  );
};

export default BillingMainPage;
