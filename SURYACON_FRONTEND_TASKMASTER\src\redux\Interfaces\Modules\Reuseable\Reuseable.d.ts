// interface for backupSlice.ts
export interface BackupInterface {
  isOpen: boolean;
  isLocalChange:boolean;
}
// interface for deletedslice.ts
export interface isDeleted {
  isDeleted: boolean;
  isDeletedNext: boolean;
}
//   interface for floatinglabelslice.ts
export interface FloatingLabelState {
  inputValues: Record<string, string | number>;
  inputHeights: Record<string, string>;
  selectedUnit: string;
}
// interface for navigationslice.ts
export interface NavigationInterface {
  navigateArray: Array<{ route: string; title: string; isSubtask?: boolean }>;
  title: string
  removeNavigationKey: boolean,
}
// interface for popupslice.ts
export interface PopupSliceState {
  popups: { [key: string]: boolean };
  openDocData: [];
  categoryPopup: string | null;
  approvalFormStep: "approve" | "decline" | "initial" | "reason";

}
// interfaces for sidebarslice.ts
export interface SubRoute {
  route: string;
  label: string;
  icon?: React.ElementType;
}
interface SideBarState {
  label: String;
  subRoutes: SubRoute[];
  currentActiveSubRouteIndex: Number;
  currentActiveRotueIndex: Number;
}
// interface for toastslice.ts
export interface ToastMessageInterface {
  isOpen: boolean;
  messageContent: string;
  type: string;
}

export interface PopupSliceState {
  popups: { [key: string]: boolean };
  openDocData: [];
  categoryPopup: string | null;
  approvalFormStep: "approve" | "decline" | "initial" | "reason";

}