import { useParams } from "react-router-dom";
import {
  useAddTaskByCategoryIdMutation,
  useDeleteTaskMutation,
  useEditTaskMutation,
} from "../../../../../../redux/api/Modules/TaskMaster/TaskMasterapi";
import { useEffect, useRef, useState, useCallback } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../../../../../redux/store";
import {
  closePopup,
  togglePopup,
} from "../../../../../../redux/features/Modules/Reusble/popupSlice";
import {
  resetInputValues,
  setInputValue,
} from "../../../../../../redux/features/Modules/Reusble/floatinglabelslice";
import { useToast } from "../../../../../../hooks/ToastHook";
import {
  generateSummaryData,
  isValidValue,
  isValidValue2,
} from "../../../../../../functions/functions";
import styles from "./AddTask.module.css";
import FloatingLabelInput from "../../../../../../components/Reusble/Global/FloatingLabel";
import UnitPopup from "../../../../../../components/Reusble/Global/UnitPopup";
import Button from "../../../../../../components/Reusble/Global/Button";
import {
  CloseIcon,
  DropDownArrowUpIcon,
  DropDownCategoryIcon,
} from "../../../../../../assets/icons";
import SummaryPage from "../../../../../../components/Reusble/Global/SummaryPage/SummaryPage";
import { DeletePopup } from "../../../../../../components/Reusble/Global/DeletePopup";
import { useAppSelector } from "../../../../../../redux/hooks/Modules/Reduxhooks/ReduxHooks";
import {
  resetNavigate,
  resetTaskHeader,
  resetTaskName,
} from "../../../../../../redux/features/Modules/Reusble/navigationSlice";
import { updateTaskData } from "../../../../../../redux/features/Modules/TaskMaster/Slices/TaskMasterSlice";

interface TaskFormProps {
  onClose: () => void;
  mode: "add" | "edit";
  isHeader?: boolean;
  updateApi?: (data: any) => void;
  isTaskPage?: boolean;
  isEdit?: boolean;
  onSubmit?: (data: any) => Promise<void>;
}

interface FormState {
  TaskName: string;
  Description: string;
  Quantity: number | null;
  Unit: string;
}

const unitData: Array<{ id: number; label: string }> = [
  { id: 1, label: "Bag" },
  { id: 2, label: "Box" },
  { id: 3, label: "Cft" },
  { id: 4, label: "Cum" },
  { id: 5, label: "Feet" },
  { id: 6, label: "Kgs" },
  { id: 7, label: "Length" },
  { id: 8, label: "Ltrs" },
  { id: 9, label: "Month" },
  { id: 10, label: "Mtr" },
  { id: 11, label: "Nos" },
  { id: 12, label: "Pair" },
  { id: 13, label: "Pkts" },
  { id: 14, label: "Rft" },
  { id: 15, label: "Roll" },
  { id: 16, label: "Set" },
  { id: 17, label: "Sqft" },
  { id: 18, label: "Sqmt" },
  { id: 19, label: "YDS" },
];

export function TaskForm({
  onClose,
  mode,
  isEdit = false,
  isTaskPage = false,
  isHeader = false,
  updateApi,
}: TaskFormProps) {
  const [AddTaskByCategory] = useAddTaskByCategoryIdMutation();
  const { catId } = useParams<{ catId: string }>();
  const [selectedUnitId, setSelectedUnitId] = useState<number | null>(null);
  const [isSummaryPage, setIsSummaryPage] = useState(false);
  const [hasQuantityAndUnit, sethasQuantityAndUnit] = useState<boolean>(false);
  const [wasTrue, setWasTrue] = useState(false);
  const [discard, setDiscard] = useState<boolean>(false);
  const [editTask] = useEditTaskMutation();
  const [summaryData, setSummaryData] = useState<
    Array<{ label: string; value: string; isChanged: boolean }>
  >([]);

  const TaskData = useSelector(
    (state: RootState) =>
      state.taskForm.currentSubtaskData || {
        _id: "",
        name: "",
        Unit: "",
        description: "",
        subtaskWeighatages: 0,
        Tracking: "",
        MaterialId: [],
        ToolId: [],
        MachinaryId: [],
        ManpowerId: [],
        AdminId: [],
        AssigneeId: [],
        Reporter: [],
        Subtaskdetails: [],
        MethodId: {
          work_instruction_id: [],
          task_closing_requirement: [],
          Controlplan: [],
          Failuremode: [],
        },
      }
  );
  const showToast = useToast();
  const handleFormClick = (e: React.MouseEvent) => {
    e.stopPropagation(); // This will prevent the click event from bubbling up to the parent component
  };

  const [errors, setErrors] = useState<{ [key: string]: boolean }>({
    TaskName: false,
  });
  const dispatch = useDispatch();
  const inputValues = useSelector(
    (state: RootState) => state.floatingLabel.inputValues
  );

  const isUnitPopUpVisible = useSelector(
    (state: RootState) => state.popup.popups["unitPopup"]
  );
  const [formState, setFormState] = useState<FormState>({
    TaskName: inputValues?.TaskName ?? "",
    Description: inputValues?.Description ?? "",
    Quantity: Number(inputValues.Quantity),
    Unit: unitData.find((unit) => unit.label === inputValues.Unit)?.label || "",
  });

  const toggleUnitPopUp = (e: React.MouseEvent) => {
    e.stopPropagation();
    dispatch(togglePopup("unitPopup"));
  };

  const handleUnitSelect = (
    unit: { id: number; label: string } & { id: number }
  ) => {
    dispatch(setInputValue({ Unit: unit.label }));
    setSelectedUnitId(unit.id);
    clearUnitError();
    dispatch(closePopup("unitPopup"));
  };

  const handleAction = async () => {
    const isTaskNameEmpty =
      !inputValues.TaskName || inputValues.TaskName.trim() === "";
    const isUnitEmpty = selectedUnitId === null;
    console.log(summaryData, "this is summary daata");
    const hasQuantityAndUnit = Boolean(
      (inputValues?.Quantity || inputValues.Quantity === 0 || "0") &&
        inputValues?.Unit
    );
    sethasQuantityAndUnit(hasQuantityAndUnit);

    setErrors({
      TaskName: isTaskNameEmpty,
      Unit: isUnitEmpty,
    });

    if (isTaskNameEmpty || isUnitEmpty) {
      showToast({
        messageContent: "Enter Required Fields!",
        type: "warning",
      });

      return;
    }

    if (isNaN(inputValues?.Quantity) && inputValues?.Quantity !== undefined) {
      setErrors({ ...errors, Quantity: true });

      showToast({
        messageContent: "Enter Numeric Value!",
        type: "warning",
      });
      return;
    }

    setErrors({ TaskName: false, Unit: false, Quantity: false });
    setIsSummaryPage(true);
  };

  const clearUnitError = () => {
    if (errors.Unit) {
      setErrors((prev) => ({ ...prev, Unit: false }));
    }
  };

  useEffect(() => {
    if (mode === "edit" && inputValues.Unit) {
      setSelectedUnitId(
        unitData.find((unit) => unit.label === inputValues.Unit)?.id || null
      );
    }
  }, [dispatch, mode]);

  // useEffect(() => {
  //   setFormState();
  // }, [mode,inputValues]);

  console.log("has changes on outside click>>form state", formState);

  useEffect(() => {
    const fields = [
      { label: "TaskName", value: formState?.TaskName },
      { label: "Description", value: formState?.Description },
      { label: "Quantity", value: formState?.Quantity },
      { label: "Unit", value: formState?.Unit },
    ];
    const summary = generateSummaryData(inputValues, fields, null, mode);

    setSummaryData(summary);
  }, [inputValues, formState]);

  const [isClosing, setIsClosing] = useState<boolean>(false);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);

  const handleFormClose = useCallback(() => {
    setIsClosing(true);
    setTimeout(() => {
      onClose();
      dispatch(resetInputValues());
    }, 400);
  }, [onClose, dispatch]);

  const submitHandler = async () => {
    if (isSubmitting) return; // Prevent double submit
    setIsSubmitting(true);
    try {
      const taskData = {
        Taskname: inputValues.TaskName,
        CategoryId: catId || "",
        Unit: inputValues.Unit,
        Description: inputValues.Description,
        Quantity: Number(inputValues.Quantity) || "",
      };

      if (mode === "edit") {
        const noChanges =
          (typeof inputValues?.TaskName === "string"
            ? inputValues?.TaskName.trim()
            : inputValues?.TaskName) === formState?.TaskName &&
          (typeof inputValues?.Description === "string"
            ? inputValues?.Description.trim()
            : inputValues?.Description) === formState?.Description &&
          Number(inputValues?.Quantity) === Number(formState?.Quantity) &&
          inputValues?.Unit === formState?.Unit;
        if (noChanges) {
          showToast({
            messageContent: "There were no changes!",
            type: "info",
          });

          setIsSummaryPage(false);
          setIsSubmitting(false);
          return;
        }
      }

      if (mode === "edit") {
        if (isHeader) {
          await updateApi?.({
            name: inputValues?.TaskName?.toString(),
            Description: inputValues?.Description?.toString(),
            Unit: inputValues?.Unit,
            Quantity: Number(inputValues?.Quantity),
          });
          showToast({
            messageContent: "Task updated successfully!",
            type: "success",
          });
          dispatch(
            resetTaskHeader({ title: inputValues?.TaskName.toString() })
          );
          dispatch(
            updateTaskData({
              ...TaskData,
              name: inputValues?.TaskName?.toString(),
              Description: inputValues?.Description?.toString(),
              Unit: inputValues?.Unit,
              subtaskWeighatages: Number(inputValues?.Quantity),
            })
          );
          
          handleFormClose();
          return;
        }
        const res = await editTask({
          taskId: inputValues?.tskId,
          name: inputValues?.TaskName?.toString(),
          Description: inputValues?.Description?.toString(),
          Unit: inputValues?.Unit,
          Quantity: Number(inputValues?.Quantity),
        }).unwrap();
        if (res?.success) {
          showToast({
            messageContent: "Task updated successfully!",
            type: "success",
          });
          handleFormClose();
        } else {
          showToast({
            messageContent: res?.message || "Update failed.",
            type: "danger",
          });
          setIsSubmitting(false);
          return;
        }
      } else {
        // Prevent unnecessary API call in add mode if all fields are empty
        const allEmpty =
          !inputValues.TaskName &&
          !inputValues.Description &&
          (!inputValues.Quantity || inputValues.Quantity === "") &&
          !inputValues.Unit;
        if (allEmpty) {
          showToast({
            messageContent: "Please fill in the required fields.",
            type: "warning",
          });
          setIsSubmitting(false);
          return;
        }
        const res = await AddTaskByCategory(taskData).unwrap();
        if (res?.success) {
          showToast({
            messageContent: "Task added successfully!",
            type: "success",
          });
          handleFormClose();
        } else {
          showToast({
            messageContent: res?.message || "Add failed.",
            type: "danger",
          });
          setIsSubmitting(false);
          return;
        }
      }
    } catch (error: any) {
      setIsSubmitting(false);
      console.error("Submission Error:", error);
      showToast({
        messageContent:
          error?.data?.message || error?.message || "Submission failed.",
        type: "danger",
      });
    }
  };

  const clearError = () => {
    if (errors.TaskName) {
      setErrors((prev) => ({ ...prev, TaskName: false }));
    }

    if (errors.Quantity) {
      setErrors((prev) => ({ ...prev, Quantity: false }));
    }
  };

  const hasFormChanges = (where?: string) => {
    console.log("has changes on outside click", where, inputValues, formState);
    if (mode === "edit") {
      return (
        inputValues?.TaskName?.trim() !== formState?.TaskName?.trim() ||
        inputValues?.Description?.trim() !== formState?.Description?.trim() ||
        Number(inputValues?.Quantity) != Number(formState?.Quantity) ||
        inputValues?.Unit !== formState?.Unit
      );
      
    } else {
      return (
        inputValues?.TaskName?.trim() ||
        inputValues?.Description?.trim() ||
        Number(inputValues?.Quantity) ||
        inputValues?.Unit
      );
    }
  };
  const handleClose = useCallback(() => {
    const hasChanges = hasFormChanges("clossse");

    console.log("has changes on outside click>>", hasChanges);

    const hasQuantityAndUnit = Boolean(
      (inputValues?.Quantity || inputValues.Quantity === 0 || "0") &&
        inputValues?.Unit
    );
    sethasQuantityAndUnit(hasQuantityAndUnit);
    if (hasChanges) {
      setDiscard(true);
      return;
    }
    handleFormClose();
  }, [hasFormChanges, handleFormClose]);

  const isEmpty = (value: any) => {
    console.log("outisde click tools inside if>>val:", value);
    return !Object.values(value).some((val) => {
      // console.log('outisde click tools inside if>>val:', val)
      return val !== undefined && val !== null && val !== "";
    });
  };

  const taskFormRef = useRef<HTMLDivElement | null>(null);
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      console.log("outisde click tools", inputValues);
      if (formRef.current && !formRef.current.contains(event.target as Node)) {
        console.log("outisde click tools inside if");
        const isEmp = isEmpty(inputValues);
        console.log("outisde click tools inside if >>isEmp:", isEmp);
        if (isEmp) {
          // setIsClosing(true);
          // setTimeout(onClose, 400);

          console.log("outisde click tools inside inner if");

          handleClose();
          return;
        }

        const hasChanges = hasFormChanges("outttside");
        console.log("has changes on outside click", hasChanges);
        if (!hasChanges || (!hasChanges && !isSummaryPage)) {
          handleClose();
        }
      }
    };

    document.addEventListener("mousedown", handleClickOutside);

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [inputValues, dispatch, handleClose]);

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if(isClosing) return;
    if (e.key === "Enter" && e.shiftKey) return;

    if (e.key === "Enter") {
      e.preventDefault();
      e.stopPropagation();
      if (!isSummaryPage && !discard) {
        handleAction();
      }
      const hasQuantityAndUnit = Boolean(
        (inputValues?.Quantity || inputValues.Quantity === 0 || "0") &&
          inputValues?.Unit
      );
      console.log("has both quantity and unit", hasQuantityAndUnit);
      sethasQuantityAndUnit(hasQuantityAndUnit);
      if (isSummaryPage) {
        submitHandler();
      } else if (discard) {
        setIsClosing(true);
        setTimeout(() => {
          if (isEdit) {
            dispatch(closePopup("TaskEdit"));
            dispatch(resetInputValues());
            return;
          }
          dispatch(closePopup("AddTaskForm"));
          dispatch(resetInputValues());
        }, 400);
      }
    }

    if (e.key === "Escape") {
      e.preventDefault();
      e.stopPropagation();
      if (isSummaryPage) {
        setIsSummaryPage(false);
      } else if (discard) {
        setDiscard(false);
      } else {
        handleClose();
      }
    }
  };
  const formRef = useRef(null);
  useEffect(() => {
    console.log(isSummaryPage, "summarypageee");
    if (isSummaryPage || discard) {
      if (formRef.current) {
        formRef.current.focus();
      }
    }
  }, [isSummaryPage, discard]);
  console.log(isSummaryPage, "this is mode");

  console.log("valuesss?????", inputValues, formState, summaryData);
  return (
    <>
      <div
        tabIndex={0}
        ref={formRef}
        className={`${styles.addTaskForm_container} ${
          isClosing ? styles.closing : ""
        }`}
        onClick={handleFormClick}
        onKeyDown={handleKeyDown}
      >
        <div
          className={styles.addTaskForm_header}
          style={{ color: discard ? "var(--warning_color)" : "" }}
        >
          <button
            className={styles.closeButton}
            onClick={() => {
              if (!hasFormChanges() && isSummaryPage) {
                handleClose();
                return;
              }
              if (isSummaryPage) {
                setWasTrue(true);
                setDiscard(true);
                setIsSummaryPage(false);
                return;
              }
              if (discard && !wasTrue) {
                setDiscard(false);
                return;
              }
              if (discard && wasTrue) {
                setDiscard(false);
                setWasTrue(false);
                setIsSummaryPage(true);
                return;
              }

              handleClose();
            }}
          >
            <CloseIcon />
          </button>
          <h3 className={styles.addTaskForm_header_text}>
            {isSummaryPage
              ? `Are you sure you want to ${
                  mode === "edit" ? "update" : "add"
                } this task?`
              : discard
              ? "Are you sure you want to discard these changes?"
              : `${mode === "edit" ? "Edit" : "Add"} Task`}
          </h3>
        </div>

        <div className={styles.addTaskForm_datainputs}>
          {isSummaryPage ? (
            <div>
              <SummaryPage
                hasQuantityAndUnit={hasQuantityAndUnit}
                summaryData={summaryData}
              />
            </div>
          ) : discard ? (
            <div
              style={{
                display: "flex",
                flexWrap: "wrap",
                flexDirection: "row",
              }}
            >
              {summaryData.map((item) => {
                console.log("item?>", item, hasQuantityAndUnit);
                return (
                  <div
                    key={item.label}
                    style={{
                      width:
                        // hasQuantityAndUnit &&
                        (item.label === "Unit" || item.label === "Quantity") &&
                        isValidValue2(item?.value)
                          ? "50%"
                          : "100%",
                    }}
                    // className={`${
                    //   (inputValues?.Unit && inputValues.Quantity)
                    //     ? styles.flexContainer
                    //     : ""
                    // }`}
                  >
                    {isValidValue2(item?.value) && (
                      <div className={styles.summaryDivData}>
                        <div className={styles.summaryDataContent}>
                          <p
                            className="p_tag_14px_weight"
                            style={{ color: "#444444" }}
                          >
                            {item?.label === "CategoryName" ||
                            item?.label === "TaskName"
                              ? "Name"
                              : item.label}
                          </p>
                          <h4
                            style={{
                              color: item?.isChanged
                                ? "var(--secondary_color)"
                                : "#191919",
                              marginTop: "0.3rem",
                            }}
                          >
                            {item.value}
                          </h4>
                        </div>
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          ) : (
            <>
              <div className={styles.addtaskform_f2_inputs}>
                <FloatingLabelInput
                  label="Name"
                  id="TaskName"
                  focusOnInput={true}
                  placeholder="Name"
                  isInvalid={errors.TaskName}
                  onInputChange={clearError}
                  props="one_line"
                />
                <FloatingLabelInput
                  label="Description"
                  id="Description"
                  placeholder="Description"
                  isInvalid={false}
                  onInputChange={() => {}}
                  props="description_prop"
                />
              </div>
              <div className={styles.addtaskform_qtyinputs}>
                <FloatingLabelInput
                  width="14.75rem"
                  label="Quantity"
                  id="Quantity"
                  maxlength={9}
                  placeholder="Quantity"
                  value={inputValues?.Quantity}
                  isInvalid={errors.Quantity}
                  preventEnter={true}
                  props="one_line"
                  type="number"
                  onInputChange={(value) => {
                    if (value && isNaN(value)) {
                      setErrors((prev) => ({ ...prev, Quantity: true }));
                    } else {
                      clearError();
                    }
                  }}
                />
                <FloatingLabelInput
                  width="14.75rem"
                  label="Unit"
                  id="Unit"
                  placeholder="Unit"
                  isDisabled={true}
                  isInvalid={errors.Unit}
                  onInputChange={() => {}}
                  // onInputChange={clearUnitError}
                  Icon={
                    isUnitPopUpVisible
                      ? DropDownArrowUpIcon
                      : DropDownCategoryIcon
                  }
                  iconClick={toggleUnitPopUp}
                  value={
                    selectedUnitId === null
                      ? ""
                      : unitData.find((unit) => unit.id === selectedUnitId)
                          ?.label
                  }
                />
                {isUnitPopUpVisible && (
                  <div
                    style={
                      {
                        // position: "absolute",
                        // left: "53%",
                        // top: "96%",
                        // zIndex: 10,
                      }
                    }
                    className={`${styles.unitPopup} ${
                      isUnitPopUpVisible ? "" : styles.hidden
                    }`}
                  >
                    <UnitPopup
                      data={unitData}
                      onSelect={(unit) => {
                        handleUnitSelect(unit as { id: number; label: string });
                      }}
                      selectedId={selectedUnitId}
                    />
                  </div>
                )}
              </div>
            </>
          )}
        </div>

        <div className={styles.addtaskform_btngrp}>
          {isSummaryPage ? (
            <>
              <Button
                type="Cancel"
                Content="Back"
                Callback={() => setIsSummaryPage(false)}
              />
              <Button
                type="Next"
                Content={"Submit"}
                Callback={submitHandler}
                disabled={isSubmitting}
              />
            </>
          ) : discard ? (
            <>
              <Button
                type="Cancel"
                Content="No"
                Callback={() => {
                  if (discard && wasTrue) {
                    setDiscard(false);
                    setIsSummaryPage(true);
                    setWasTrue(false);
                    return;
                  }
                  setDiscard(false);
                }}
              />
              <Button
                type="Approve"
                Content="Yes"
                Callback={() => {
                  setIsClosing(true);
                  setTimeout(() => {
                    if (isEdit) {
                      dispatch(closePopup("TaskEdit"));
                      dispatch(resetInputValues());
                      return;
                    }
                    dispatch(closePopup("AddTaskForm"));
                    dispatch(resetInputValues());
                  }, 400);
                }}
              />
            </>
          ) : (
            <>
              <Button type="Cancel" Content="Cancel" Callback={handleClose} />
              <Button
                type="Next"
                Content={mode === "edit" ? "Update" : "Add"}
                Callback={handleAction}
              />
            </>
          )}
        </div>
      </div>
    </>
  );
}
