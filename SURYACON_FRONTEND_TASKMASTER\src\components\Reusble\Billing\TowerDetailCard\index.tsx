import { useC<PERSON>back, useEffect, useRef, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";

import styles from "./Styles/TowerDetailCard.module.css";
import { TowerCardItems } from "./Subcomponents/TowerCardItems";
import {
  DeleteIcon,
  DurationIcon,
  FloorsIcon,
  MivanProject,
  SuryconLogo,
  Twodots,
  YellowEditPencil,
} from "../../../../assets/icons";
import { useDispatch } from "react-redux";
import { setselectedLocationId } from "../../../../redux/features/Modules/Billing/ProjectPlanning/Slices/projectPlanningSlice";
import { slicedData } from "../../../../functions/functions";
import { openPopup } from "../../../../redux/features/Modules/Reusble/popupSlice";
import {
  setSelectedTowerLocationData,
  setTowerLocationFormData,
  setSelectedTowerLocationName,
} from "../../../../redux/features/Modules/Billing/ProjectPlanning/Slices/ProjectSlice";
import { TowerLocationData } from "../../../../modules/Billing/Pages/ProjectPlanning/Locations/Subcomponents/AddLocation/Interfaces/Interface";
import { useToast } from "../../../../hooks/ToastHook";

export interface TowerLocationInterface {
  _id?: string;
  name: string;
  category: string;
  structure_type: string;
  project_id: string;
  area: number;
  number_of_basements: number;
  location_duration: number;
  remarks: string;
  isCompleted: number;
  isDeleted: boolean;
  isApprovesBy: string;
  isDeniedBy: string;
  isOpenBy: string;
  createdAt: string;
  updatedAt: string;
  DeniedComment: string;

  number_of_floors?: number;

  TaskId: {
    column: string;
    mivan: string;
    raft: string;
    slab: string;
  };
  location_drawing: string[];
  conventionals: any[];
  mivan: any[];
}
export interface towerCardprops {
  data: TowerLocationInterface;
}
const TowerDetailCard: React.FC<towerCardprops> = ({ data }) => {
  console.log(data, "thisisid");
  const navigate = useNavigate();
  const { projectId } = useParams();
  console.log(projectId, "this is project id");
  const [isCatPopupVisible, setIsCatPopupVisible] = useState(false);
  const dispatch = useDispatch();

 const handleCardClick = (
  event: React.MouseEvent<HTMLDivElement, MouseEvent>
) => {
  if (!(event.target as HTMLElement).closest(`.${styles.cat_popup}`)) {
    if (data?._id === undefined) return;
    dispatch(setselectedLocationId(data?._id));
    dispatch(setSelectedTowerLocationName(data?.name));
      console.log("dispatching");

      // Updated navigation path - removed redundant projectId
    navigate(`/billing/main/${projectId}/location/${data?._id}`);
  }
};
const showToast = useToast();
  const handleDotsClick = (
    event: React.MouseEvent<HTMLDivElement, MouseEvent>
  ) => {
    event.stopPropagation();
    if (!navigator.onLine) {
      showToast({
        messageContent: "Oops! no internet connection!",
        type: "danger",
      });
      return;
    }
    setIsCatPopupVisible(!isCatPopupVisible);
  };

  const [isHovered, setIsHovered] = useState<{
    id: number | null;
    hoverState: boolean;
  }>({
    id: null,
    hoverState: false,
  });

  console.log("isHovered", isHovered.id);

  const handleMouseEnter = (id: number) => {
    setIsHovered({ id, hoverState: true });
  };

  const handleMouseLeave = (id: number) => {
    setIsHovered((prev) => ({
      ...prev,
      hoverState: false,
      id: id,
    }));
  };

  const cardRef = useRef<HTMLDivElement>(null);
  const popup = useRef<HTMLDivElement>(null);

  const handleClickOutside = useCallback(
    (event: MouseEvent) => {
      if (
        cardRef.current &&
        !cardRef.current.contains(event.target as Node) &&
        popup.current &&
        !popup.current.contains(event.target as Node)
      ) {
        setIsCatPopupVisible && setIsCatPopupVisible(false);
      }
    },
    [dispatch]
  );

  useEffect(() => {
    document.addEventListener("mousedown", (e) => handleClickOutside(e));
    return () => {
      document.removeEventListener("mousedown", (e) => handleClickOutside(e));
    };
  }, [handleClickOutside]);

  return (
    <div
      ref={cardRef}
      className={styles.towercard_outer_container}
      onClick={handleCardClick}
    >
      <div className={styles.towercard_inner_container}>
        <div className={styles.towercard_top_container}>
          <div className={`${styles.progress_card_top_left_container}`}>
            <h2
              className={`${styles.progress_card_title}`}
              style={{ whiteSpace: "nowrap" }}
            >
              {slicedData(data?.name, 20)}
            </h2>
            <div className={`${styles.project_sft_card}`}>
              <h4>{data?.structure_type}</h4>
            </div>
          </div>

          <div className={styles.tower_card_topright_container}>
            <div className={styles.tower_card_percentage_card}>
              <p>{Math.round(data?.isCompleted ? data?.isCompleted : 0)}%</p>
            </div>
            <div className={styles.dots_container} onClick={handleDotsClick}>
              <Twodots />
              {isCatPopupVisible && (
                <div
                  className={styles.cat_popup}
                  onClick={(e) => e.stopPropagation()}
                  ref={popup}
                >
                  <div
                    onClick={() => {
                      console.log(data, "clicking on button");
                      dispatch(setSelectedTowerLocationData(data as any));
                      dispatch(setTowerLocationFormData(data as any));
                      dispatch(openPopup("AddLocationForm"));
                       setIsCatPopupVisible(false); 
                    }}
                    onMouseEnter={() => handleMouseEnter(1)}
                    onMouseLeave={() => handleMouseLeave(1)}
                    className={`${styles.cat_popup_edit} ${
                      isHovered?.id === 1
                        ? isHovered.hoverState
                          ? styles.edit_hovered
                          : styles.edit_notHovered
                        : ""
                    }`}
                  >
                    <div className={styles.cat_popup_transition_div_edit}></div>
                    <div className={styles.cat_popup_editicon}>
                      <YellowEditPencil />
                    </div>
                    <h4>Edit</h4>
                  </div>
                  <div
                    onClick={() => {
                      console.log(data, "clicking on button");

                      dispatch(setTowerLocationFormData(data as any));
                      dispatch(openPopup("DeleteLocationForm"));
                      setIsCatPopupVisible(false);
                    }}
                    onMouseEnter={() => handleMouseEnter(2)}
                    onMouseLeave={() => handleMouseLeave(2)}
                    className={`${styles.cat_popup_dlt} ${
                      isHovered?.id === 2
                        ? isHovered.hoverState
                          ? styles.dlt_hovered
                          : styles.dlt_notHovered
                        : ""
                    }`}
                  >
                    <div
                      className={styles.cat_popup_transition_div_delete}
                    ></div>
                    <div className={styles.cat_popup_dlticon}>
                      <DeleteIcon />
                    </div>
                    <h4>Delete</h4>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        <div className={styles.towercard_lower_container}>
          <TowerCardItems
            key={"Area"}
            title={"Area"}
            num={data?.area}
            titleColor="var(--primary_color)"
          />
          {data?.number_of_floors && (
            <TowerCardItems
              key={"Floors"}
              title={"Floors"}
              num={data?.number_of_floors ? data?.number_of_floors : 0}
              titleColor="var(--primary_color)"
            />
          )}

          <TowerCardItems
            key={"Basements"}
            title={"Basements"}
            num={data?.number_of_basements}
            titleColor="var(--primary_color)"
          />
          <TowerCardItems
            key={"Duration"}
            title={"Duration"}
            num={data?.location_duration}
            titleColor="var(--primary_color)"
          />
          {data?.number_of_floors && (
            <TowerCardItems
              key={"Conventional"}
              title={"Conventional"}
              num={data?.conventionals?.length}
              titleColor="var(--primary_color)"
            />
          )}
          {data?.number_of_floors && (
            <TowerCardItems
              key={"Mivan"}
              title={"Mivan"}
              num={data?.mivan?.length}
              titleColor="var(--primary_color)"
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default TowerDetailCard;
