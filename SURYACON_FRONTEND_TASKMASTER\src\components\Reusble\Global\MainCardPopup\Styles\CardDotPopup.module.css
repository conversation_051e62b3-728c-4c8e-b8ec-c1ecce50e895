.cat_popup {
    position: absolute;
    top: 100%;
    right: 0;
    z-index: 0;
    background-color: var(--white-50-background);
    /* width: 7rem; */
    /* height: 6.2rem; */
    border: 1px solid;
    border-radius: 20px;
    border-image-source: linear-gradient(130.72deg,
            rgba(237, 231, 231, 0.07) -22.43%,
            rgba(251, 251, 251, 0.05) 75.66%);
    backdrop-filter: blur(150px);

    box-shadow: 0px 4px 20px 0px #00000033;
    display: flex;
    flex-direction: column;
    row-gap: 0.25rem;
    padding: 1rem;
}

.cat_popup_delete {
    position: absolute;
    top: 100%;
    right: 0;
    z-index: 0;
    background-color: var(--main_background);
    width: 7rem;
    height: 6rem;
    border: 1px solid;
    border-radius: 20px;
    border-image-source: linear-gradient(130.72deg,
            rgba(237, 231, 231, 0.07) -22.43%,
            rgba(251, 251, 251, 0.05) 75.66%);
    backdrop-filter: blur(150px);
    box-shadow: 0px 4px 20px 0px #00000033;
}

.cat_popup_view,
.cat_popup_edit,
.cat_popup_dlt {
    display: flex;
    /* padding: 0.6rem 0.7rem; */
    gap: 0.4rem;
    color: var(--text-black-60);
    align-items: center;
    background-color: var(--main_background);
    padding: 0.25rem;
    padding-right: .75rem;
    border-radius: 50px;
    transition: background-color 0.2s ease-in-out;
    /* background-color: red; */
}

.cat_popup_edit>h4{
  position: relative;
  z-index: 2;
}
@keyframes animateIn {
  0% {
    width: 0rem;
    height: 0rem;
    left: 1.5rem;
  }
  20% {
    width: 2.25rem;
    height: 1.7rem;
    left: 1.3rem;
  }
  100% {
    width: calc(100% - 2.2rem);
    height: 2rem;
    left: 1.1rem;
  }
  }
  
  @keyframes animateOut {
  0% {
    width: calc(100% - 2.2rem);
    height: 2rem;
    left: 1.1rem;
  }
  40% {
    width: 2.25rem;
    height: 2rem;
    left: 1.3rem;
  }
  100% {
    width: 0rem;
    height: 0rem;
    left: 1.5rem;
  }
  } 
  
  .cat_popup_transition_div_edit {
  position: absolute;
  height: 0rem;
  width: 0rem;
  border-radius: 100px;
  z-index: 1;
  background-color: var(--extra_color);
  transition: all 0.3s ease-in-out;
  }
  
  .cat_popup_edit.edit_hovered .cat_popup_transition_div_edit {
    animation: animateIn 0.3s ease-in-out forwards !important;
  }
  
  .cat_popup_edit.edit_notHovered .cat_popup_transition_div_edit {
    animation: animateOut 0.3s ease-in-out forwards !important;
  }

.cat_popup_viewicon {
    background: var(--primary_background);
    width: 28px;
    height: 28px;
    border-radius: 29px;
    display: flex;
    justify-content: center;
    align-items: center;
}



.cat_popup_editicon {
    background: #fff6d9;
    width: 28px;
    height: 28px;
    border-radius: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    z-index: 2;
}

.cat_popup_dlt>h4 {
    position: relative;
    z-index: 2;
}

.cat_popup_transition_div_delete {
    position: absolute;
    height: 0rem;
    width: 0rem;
    border-radius: 100px;
    z-index: 1;
    background-color:  #f6e6e6;
    transition: all 0.3s ease-in-out;
    
  }

  .cat_popup_dlt.dlt_hovered .cat_popup_transition_div_delete {
    animation: animateIn 0.3s ease-in-out forwards !important;
  }
  
  .cat_popup_dlt.dlt_notHovered .cat_popup_transition_div_delete {
    animation: animateOut 0.3s ease-out forwards !important;
  }  

.cat_popup_dlticon {
    background: #f6e6e6;
    width: 28px;
    height: 28px;
    border-radius: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    z-index: 2;
}



.cat_popup_dlt.dlt_default  .cat_popup_transition_div_delete {
  background-color: transparent; /* Example styling */
  transition: all 0.3s ease-in-out;
} 