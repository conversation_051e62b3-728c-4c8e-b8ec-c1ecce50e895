import React, { useState, useEffect, useRef } from "react";
import styles from "./Styles/DynamicGradeInput.module.css";
import { AddCategoryIcon, DeleteIcon } from "../../../../../../assets/icons";
interface DynamicGradeInputProps {
  label: string;
  error?: boolean;
  formData?: any; //will update in future
  callbackDelete?: (index: number) => void;
  initialData: string[];
  onGradesUpdate: (grades: string[]) => void;
  formMode?: string;
  variant?: "default" | "grade" | "qualifications";
  brandId?: string;
  placeholder?: string;
  needBold?:boolean;
  needBoldColor?:string;
  labelHeader?:string
}

const DynamicGradeInput: React.FC<DynamicGradeInputProps> = ({
  label,
  error = false,
  callbackDelete,
  initialData,
  placeholder = "Grade",
  onGradesUpdate,
  variant = "default",
  needBold=false,
  labelHeader='',
  needBoldColor=''
}) => {
  const [gradeInputs, setGradeInputs] = useState<string[]>(
    initialData && initialData?.length > 0 ? initialData : []
  );

  console.log("Grade Inputs:", gradeInputs);
  const [lastIndex, setSelectedLastIndex] = useState<number | null>(null);

  const textareaRefs = useRef<(HTMLTextAreaElement | null)[]>([]);

  const handleInputChange = (index: number, value: string) => {
    const updatedInputs = [...gradeInputs];
    updatedInputs[index] = value;
    setGradeInputs(updatedInputs);
    onGradesUpdate && onGradesUpdate(updatedInputs);
  };

  const addNewInput = () => {
    if (!gradeInputs[gradeInputs?.length - 1] && gradeInputs?.length > 0) {
      return;
    }
    setGradeInputs((prev) => [...prev, ""]);
    setTimeout(() => {
      textareaRefs.current[gradeInputs?.length]?.focus();
    }, 0);
    setSelectedLastIndex(gradeInputs.length - 1);
  };

  useEffect(() => {
    gradeInputs.forEach((_, index) => {
      const textarea = textareaRefs.current[index];
      if (textarea) {
        textarea.style.width = "4rem"; // Reset to minimum width
        textarea.style.width = `${textarea.scrollWidth}px`;
      }
    });
  }, [gradeInputs]);
  useEffect(() => {
    setSelectedLastIndex(gradeInputs.length);
  }, []);

  useEffect(() => {
    if (initialData) {
      setGradeInputs(initialData);
    }
  }, [initialData]);

  const handleDelete = (index: number) => {
    callbackDelete && callbackDelete(index);
  };

  return (
    <div
      className={styles.datafield_box}
      style={{
        border: error ? "1px solid var(--warning_color) !important" : "",
      }}
      onClick={addNewInput}
    >
      {labelHeader && gradeInputs.length > 0 && <h4 style={{left:'0',marginBottom:'0.5rem'}}>{labelHeader}</h4>}
      <div className={styles.datafield_header}>
        <div className={styles.tooltip_container}>
          {needBold ? (
            <h4 style={{color:needBoldColor}}>{gradeInputs.length > 0 ? "" : label}</h4>
          ) : (
            <>{gradeInputs.length > 0 ? "" : label}</>
          )}
          
        </div>
        
        {gradeInputs?.length <= 0 && (
          <div className={styles.datafield_addIcon}>
            <AddCategoryIcon />
          </div>
        )}
      </div>

      <div className={styles.tooltip_container}>
        {gradeInputs.map((value, index) => (
          <div className={styles.tooltip_box} style={{ position: "relative" }}>
            <div key={index} className={styles.gradeinputbox_container}>
              <textarea
                ref={(el) => (textareaRefs.current[index] = el)}
                className={styles.gradeinputbox}
                value={value}
                onClick={(e) => e.stopPropagation()}
                onChange={(e) => {
                  e.stopPropagation();
                  handleInputChange(index, e.target.value);
                }}
                onKeyDown={(e) => {
                  if (e.key === "Enter") {
                    e.preventDefault();
                    setSelectedLastIndex(index);
                    addNewInput();
                  }
                }}
                rows={1}
                placeholder={placeholder}
                readOnly={(variant == "grade" || variant == "qualifications") && index <= lastIndex!}
              />
            </div>
            <div
              className={styles.delete_icon_tooltip}
              onClick={(e) => {
                e.stopPropagation();
                handleDelete(index);
              }}
            >
              <DeleteIcon />
            </div>
          </div>
        ))}
        {gradeInputs?.length > 0 && (
          <div className={styles.datafield_addIcon}>
            <AddCategoryIcon />
          </div>
        )}
      </div>
    </div>
  );
};

export default DynamicGradeInput;
