import { useEffect, useState } from "react";
import styles from "./styles/otpVerfication.module.css";
import Button from "../../../components/Reusble/Global/Button";


const OtpVerification = () => {
  const [otp, setOtp] = useState<string[]>(["", "", "", ""]);
  const [error, setError] = useState("");
  const [timeLeft, setTimeLeft] = useState(30);
  const [canResend, setCanResend] = useState(false);

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    index: number
  ) => {
    const value = e.target.value;

    if (/^[0-9]$/.test(value) || value === "") {
      const newOtp = [...otp];
      newOtp[index] = value;
      setOtp(newOtp);

      if (value && index < otp.length - 1) {
        document.getElementById(`otp-input-${index + 1}`)?.focus();
      }
    }
  };

  const handleKeyDown = (
    e: React.KeyboardEvent<HTMLInputElement>,
    index: number
  ) => {
    if (e.key === "Backspace" && otp[index] === "") {
      if (index > 0) {
        document.getElementById(`otp-input-${index - 1}`)?.focus();
      }
    }
  };

  const handleSubmit = () => {
    if (otp.join("") === "") {
      setError("Please enter the OTP");
      return;
    }
  };

  useEffect(() => {
    if (timeLeft > 0) {
      const timer = setInterval(() => {
        setTimeLeft((prevTime) => prevTime - 1);
      }, 1000);

      return () => clearInterval(timer);
    } else {
      setCanResend(true);
    }
  }, [timeLeft]);

  const handleResend = () => {
    if (canResend) {
      setTimeLeft(30);
      setCanResend(false);
      console.log("OTP Resent");
    }
  };

  return (
    <>
      <div className={`${styles.OtpVerification_heading}`}>
        <h2>OTP Verification</h2>
        <h3>
          We've sent a One-Time Password (OTP) to your registered mobile number
          ending in XXXXXX9897. Please enter the OTP below to reset your
          password.
        </h3>
      </div>
      <div>
        <div className={`${styles.OtpVerification_error_message}`}>
          {error !== "" && <p className="small_text_p">{error}</p>}
        </div>
        <div className={`${styles.OtpVerification_otp_input_container}`}>
          {otp?.map((value, index) => (
            <div key={index} className={`${styles.OtpVerification_otp_input}`}>
              <input
                id={`otp-input-${index}`}
                type="text"
                value={value}
                onChange={(e) => handleInputChange(e, index)}
                onKeyDown={(e) => handleKeyDown(e, index)}
                maxLength={1}
                autoFocus={index === 0}
                placeholder={value === "" ? "-" : ""}
              />
            </div>
          ))}
        </div>
        <div className={styles.OtpVerification_otp_utilities}>
          <div className={styles.OtpVerification_otp_resend_button}>
            <p className="small_text_p">Haven’t received an OTP?</p>
            <p
              className={`small_text_p`}
              onClick={handleResend}
              style={{
                cursor: canResend ? "pointer" : "not-allowed",
                color: canResend ? "var(--secondary_color)" : "grey",
              }}
            >
              Resend
            </p>
          </div>
          <div>
            <p className="small_text_p">{timeLeft}s</p>
          </div>
        </div>
      </div>
      <div className={`${styles.continue_button}`}>
        <Button
          type="Accept"
          Content="Continue"
          width="65%"
          Callback={handleSubmit}
        />
      </div>
    </>
  );
};

export default OtpVerification;
