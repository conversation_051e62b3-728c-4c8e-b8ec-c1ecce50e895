// interfaces for AddCategoryType
export type TaskDataType = {
  id: string;
  category: string;
  unit?: string;
};

export type AddTaskPropType = {
  modelname?: string;
  isStepForm?: boolean;
  data: TaskDataType[];
  title?: string;
  isUnit?: boolean;
  label?: string;
  isColorChange?: boolean;
  placeholder?: string;
  buttonLabel?: string;
  onSelect:
    | ((
        categories: TaskDataType[],
        label: string,
        parents: { category: string }[]
      ) => void)
    | ((categories: string[]) => void);
  onClose: () => void;
  initialSelected?: requiredthings[];
  singleSelected?: boolean;
  brandType?: boolean;
  grade?: boolean;
  showAddIcon?: boolean;
  primaryLabel?: string;
  primaryLabel2?: string;
  textWidth?:string;
};

import { ReactNode } from "react";
//   interface for addtooltip
import { SubRoute } from "../../../redux/features/sidebarSlice";
import {
  controlPlan,
  failureMode,
  requiredthings,
  SubtaskRoute,
} from "../../../redux/features/TaskMaster/Slices/TaskMasterSlice";
s;
export interface AddToolTipProps {
  label: string;
  onClick?: (event: any) => void;
  style?: React.CSSProperties;
  className?: string;
  className2?: string;
  additionalClass?: string;
  setReporterAdded?: React.Dispatch<React.SetStateAction<boolean>>;
  reporterAdded?: boolean;
  isReporter?: boolean;
  data?: requiredthings[] | string[] | controlPlan[] | failureMode[];
  icon?: React.ReactNode;
  isPllaning?: boolean;
  isActive?: boolean;
  onTooltipClick?: (item: any) => void;
  isEdit?: boolean;
  handleDelete?: (item: any) => void;
}

export type propDataType = {
  id: string;
};
// interface for button
export interface ButtonProps {
  type:
    | "Accept"
    | "Approve"
    | "Decline"
    | "Navigate"
    | "Navigate2"
    | "Normal"
    | "Next"
    | "Cancel"
    | "Disable"
    | "Delete"
    | "EditRequest"
    | "Reason"; 

  Content: string;
  width?: string;
  property?: string;
  height?: string;
  Callback?: (
    e: React.MouseEvent<HTMLDivElement> | React.KeyboardEvent<HTMLDivElement>
  ) => void;
}
//   Interface for delete popup
interface DeletePopupProps {
  onClose: () => void;
  children?: React.ReactNode;
  callbackDelete: () => Promise<void>;
  header: string;
  width?: string;
  height?: string;
  heightupperlimit?: string;
  isOpen?: boolean;
}
interface DeletePopupProps {
  onClose: () => void;
  children?: React.ReactNode;
  callbackDelete: () => any;
  header: string;
  width?: string;
  isOpen?: boolean;
}

// inteface for dialog box
export interface DialogboxProps {
  isOpen?: boolean;
  customClass?: string;
  children: React.ReactNode;
}
// interface for FloatingLabelInputProps

interface FloatingLabelInputProps {
  label?: string;
  itemLabels?: string[];
  marginBottom?: string;
  marginTop?: string;
  id: string;
  isDisabled?: boolean;
  error?: boolean | string;
  placeholder?: string;
  width?: string;
  defaultvalue?: string;
  enterAllowed?: boolean;
  isInvalid?: boolean;
  handledelete?: (
    e: MouseEvent<HTMLDivElement, MouseEvent>,
    index: number
  ) => void | undefined;
  maxlength?: number;
  onInputChange?: (value: string | number) => void | undefined;
  Icon?: React.ElementType;
  iconClick?: (e: React.MouseEvent) => void;
  value?: string | number | any;
  rows?: number;
  clearInput?: boolean;
  fontSize?: string | number;
  props?: string;
  props2?: string;
  maxLength?: number;
  type?: string;
  focusOnInput?: boolean;
  preventEnter?: boolean;
  backgroundColor?: string;
  onPaste?: (e: React.ClipboardEvent<HTMLInputElement>) => void;
  additionalCaseforClippath?: boolean;
}
// inteface for navigationcomponent
type NavigationProps = {
  route: {
    route: string;
    title: string;
  }[];
  handleOutsideNavigation?: (title: string, route: string) => void;
};
// inteface for radiobutton

interface RadioGroupProps {
  id?: string;
  options: { value: string; label: string }[];
  errors?: boolean;
  name?: string;
  selectedValue: String;
  onValueChange: (value: string) => void;
  error?: boolean;
  customStyles?: { radioGroup?: string; radioButton?: string };
}
// inteface for search bar

interface SearchBarProps {
  height?: string;
  placeholder?: string;
  isTypeForm?: boolean;
  placeholderClassName?: string;
  className?: string;
  debounceDelay?: number;
  value?: string;
  onChange?: (value: string) => void; // ADD THIS
}
// interface for summarypage

interface SummaryPageProps {
  summaryData: Array<{ label: string; value: string; isChanged: boolean }>;
  hasQuantityAndUnit?: boolean;
}
// interface for summarypopup
export interface SummaryPopupProps {
  header: string;
  callbackBack: () => void;
  callbackCross: () => void;
  callbackApprove: () => void;
  children: React.ReactNode;
}
// inteface for target badge
export interface TargetBadgeProps {
  id?: string;
  maxlength?: number;
  edit?: boolean;
  value?: string;
  input?: boolean;
  order?: boolean;
  handleValuechange?: (e) => void;
  frontIcon?: React.ReactNode;
  outerContainerClassName?: string | string[];
  bubbleValue?: string;
  bubbleTextTagName?: keyof JSX.IntrinsicElements;
  bubbletextClassName?: string | string[];
  valueTextTagName?: keyof JSX.IntrinsicElements;
  secondValueTextTagName?: keyof JSX.IntrinsicElements;
  secondvalue?: string;
  secondValueTextClassName?: string | string[];
  valueTextClassName?: string | string[];
  bubbleClassname?: string | string[];
  active?: boolean;
  bubbleBackgroundColor?: string;
  backgroundColor?: string;
  icon?: React.ReactNode;
  isArray?: boolean;
  onClick?: () => void;
  disabled?: boolean;
}
//interfaces for tcrpopup
interface WIopupProps {
  onCancel: () => void;
  onSubmit: (data: {
    id?: string;
    description: string;
    file: { name: string; type: string } | null;
    category: string;
    photoDetails: PhotoSection[];
  }) => void;
  startWithPhotoCheckboxPage?: boolean;
  isEdit?: boolean;
  initialData?: {
    id?: string;
    description: string;
    file: { name: string; type: string } | null;
    category: string;
    photoDetails: PhotoSection[];
  };
}

export interface PhotoSection {
  id: number;
  photo: string | null;
  referenceDetail: string;
}
// interface for tooltip start
interface TooltipProps {
  content: string;
  id: string;
  index?: number;
  label?: string;
  className?: string;
  handleClick?: (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => void;
  quantity?: string;
  selectedCategory?: string;
  onCategoryChange?: (category: string) => void;
  isEdit?: boolean;
}
// interface for tooltip end

// interface for unitpopup
type DataType = {
  id: number | string;
  label: string;
};

type UnitPopupPropType = {
  data: DataType[];
  width?: string;
  height?: string;
  left?: string;
  right?: string;
  top?: string;
  alignment?: string;
  onSelect: (item: DataType | any) => void;
  selectedId: number | string | null;
  property?: string;
};
// interface for value popup
interface ValuePopupProps {
  onClose: () => void;
  onSubmit?: (category: string, qty: string) => void; // Pass data to parent
}
