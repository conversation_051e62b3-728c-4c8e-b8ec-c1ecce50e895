/* Montserrat */
@font-face {
  font-family: 'Montserrat';
  src: url('/fonts/montserrat/Montserrat-VariableFont_wght.ttf') format('truetype');
  font-weight: 100 900;
  font-style: normal;
}

@font-face {
  font-family: 'Montserrat';
  src: url('/fonts/montserrat/Montserrat-Italic-VariableFont_wght.ttf') format('truetype');
  font-weight: 100 900;
  font-style: italic;
}

/* Nunito */
@font-face {
  font-family: 'Nunito';
  src: url('/fonts/nunito/Nunito-VariableFont_wght.ttf') format('truetype');
  font-weight: 200 1000;
  font-style: normal;
}

@font-face {
  font-family: 'Nunito';
  src: url('/fonts/nunito/Nunito-Italic-VariableFont_wght.ttf') format('truetype');
  font-weight: 200 1000;
  font-style: italic;
}


:root {
  /* main  colors use for div and backgrounds */
  --primary_color: #005968;
  /* --primary_color:white; */
  --primary_background: #f0f6f6;
  --primary_progress: #d5dcdd;
  --secondary_color: #e8b000;
  --extra_color: #fff6d9;
  --warning_color: #a80000;
  --main_background: #ffffff;
  /* --main_background:	#141414; */
  --secondary_background: #fff6d9;
  --white-50-background: #ffffff80;
  --white-70-background: #ffffffb3;
  --secondary_warning_background: #f6e6e6;
  --violet-color: #9328d5;
  --violet_background: #f7e9ff;


  /* White text colors */
  --text-white-100: #ffffff;
  --text-white-87: #dedede;
  --text-white-60: #999999;
  --text-white-28: #474747;
  /* Black text colors */
  --text-black-87: #212121;
  --text-black-60: #666666;
  --text-black-28: #b8b8b8;
  --secondary-warning-color: #f6e6e6;
  --border-primary: #00000026;
  --border-secondary: #00000099;
  /* //box shadow root varaibles */
  --primary-shadow: 0 0 3px #808080;
  --secondary-box-shadow: 0 0 4px #80acb5;
  --pop-up-card-shadow: 0 0px 40px #808080;
  --extra-shadow: 0px 0px 4px 0px #00000066;
  --extra-shdow-second: 0px 0px 3px 0px #91a1a180;
  --extra-shdow-third: 0px 0px 8px 0px #91a1a180;
  --extra-shdow-four: 0px 0px 4px 0px #91a1a180;
  --extra-shadow-five: 0px 4px 40px 0px #00000080;

  --extra-shdow-six: 0px 0px 8px 0px #91a1a1cc;
  --line-color: #b0ccd1;
  --extra-box-shdow-seven: 0px 4px 8px 0px #ffffff,
    0px 2px 8px 0px #91a1a1cc inset;
  --extra-shdow-eight: 0px 2px 8px 0px #91a1a1cc;
  --extra-shadow-nine: 0px 16px 40px 0px rgba(0, 0, 0, 0.2);

  --extra-gray-28: #00000047;
  /* Blur background */
  /* backdrop-filter: blur(100px)

box-shadow: 0px 0px 6px 0px #91A1A199; */
  --primary-mt-box-shadow: 0px 0px 6px 0px #91a1a199;
  --blur-background: #f0f6f61a;

  --primary-bg-gradient: linear-gradient(130.72deg,
      rgba(237, 231, 231, 0.07) -16.06%,
      rgba(251, 251, 251, 0.05) 82.03%);
}

h1 {
  /* use this for 36px  */

  font-size: 2.25rem;
  font-weight: 500;
}

h2 {
  /* use this font size for  27px  */

  font-size: 1.6rem;
  font-weight: 500;
}

h3 {
  /* use this for 21px  */
  font-size: 1.3rem;
  font-weight: 500;
}

/* use this for 21px  */
.p_tag_21px {
  font-size: 1.3rem;
  font-weight: 300;
}

h4 {
  /* use this for semi bold 16px  */
  font-size: 1rem;
  font-weight: 500;
}

p {
  /* use this for regular 16px  */
  font-size: 1rem;
  font-weight: 300;
}

.input_16px {
  /* use this for regular 16px  */
  font-size: 1rem;
  font-weight: 300;
}

.p_tag_14px {
  /* use tjos fpr 14px  */
  font-size: 0.85rem;
  font-weight: 500;
}

.p_tag_14px_weight {
  /* use tjos fpr 14px  */
  font-size: 0.85rem;
  font-weight: 300;
}

.p_tag_48px {
  /* use tjos fpr 14px  */
  font-size: 3rem;
  font-weight: 300;
}

.small_text_p {
  /* use this for 12px semi bold */

  font-size: 0.75rem !important;
  font-weight: 500;
}

.small_text_p_400 {
  /* /use this for 12px  400  regular */

  font-size: 0.75rem !important;
  font-weight: 300;
}

.very_small_text_p {
  /* for font  9 px */
  font-size: 0.6rem;
  font-weight: 500;
}

.h4_18px {
  /* for font  18 px */
  font-size: 1.125rem;
  font-weight: 500;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: "Nunito", serif;
}

html,
body {
  height: 100%;
  width: 100%;
  user-select: none;
}

body ::selection {
  background-color: var(--primary_color) !important;
  color: #fff !important;
}

.svgDimension {
  width: 1.2rem;
  height: 1.2rem;
  width: 1.2rem;
  height: 1.2rem;
}

.not_Compatible {
  display: none;
}

ul {
  list-style-type: none;
  overflow: hidden;
}

.DesignerDownArrowSvg {
  width: 1.5rem;
  height: 1.5rem;
}

/* // ---------------------------------- Author: Armaan Singh -------------------------------- */
/* ----------------------------------------Main Menu Css starts */

.main_menu_parent {
  width: 90%;
  position: relative;
}

.main_menu {
  width: 100%;
  display: flex;
  align-items: center;
  flex-direction: column;
  padding: 1rem 1rem 2rem 1rem;
  border-radius: 24px;
  border: 1px solid;
  border-image-source: linear-gradient(130.72deg,
      rgba(237, 231, 231, 0.07) -16.06%,
      rgba(251, 251, 251, 0.05) 82.03%);
  box-shadow: var(--primary-shadow);
  font-weight: 600;
  z-index: 10;
  top: 1rem;
  backdrop-filter: blur(40px);
  height: 1.1rem;
  overflow-y: hidden;
  background: var(--blur-background);
  font-weight: 400;
  transition: all 0.5s ease;
  position: absolute;
}

.main_menu_open {
  z-index: 20;
  height: 80vh;
  border-radius: 24px;
  color: var(--text-black-87);
}

.main_menu_button {
  width: 90%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: var(--text-black-87);
  font-weight: 600;
  cursor: pointer;
  transform: translateY(-0.4rem);
}

.main_menu_list {
  width: 100%;
}

.main_menu_list ul {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 0.4rem;
  align-items: center;
  margin-top: 0.6rem;
}

.main_menu_list li {
  width: 100%;
  padding: 0.6rem 1rem;
}

.main_menu_list_child {
  width: 90%;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 1rem;
}

.main_menu_list_child_selected {
  color: var(--primary_color);
}

.main_menu_icon {
  display: flex;
  align-items: center;
}

.main_menu_arrow {
  transform: translateY(0.2rem);
}

.cursor_pointer {
  cursor: pointer;
}

/* ----------------------------------------Main Menu Css Ends ---------------------------------------------------------------------- */

/* ----------------------------------------Main Menu Css Ends ---------------------------------------------------------------------- */

/* ---------------------------------------------Sidebar Css Start ------------------------------------------------------------------------------------------------- */

/* .sidebar {
  width: 13%;
  height: 100vh;
  margin-right: 1rem;
} */

.sidebar_parent {
  overflow-x: hidden;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  align-items: center;
  gap: 1rem;
  z-index: 0;
}

.sidebar_menu_parent {
  z-index: 0;
  height: 100vh;
  width: 100%;
  align-self: center;
  font-size: 1rem;
  font-weight: 600;
  margin-top: 4rem;
}

.sidebar_open {
  display: block !important;
}

.sidebar_menu_child {
  display: none;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
}

.sidebar_menu_child_heading {
  display: flex;
  justify-content: space-between;
  width: 86%;
  padding: 1rem;
  color: var(--primary_color);
  font-weight: 600;
}

.summery_menu {
  width: 90%;
  transition: all 0.5s ease;
  overflow: scroll;
  display: flex;
  flex-direction: column;
  gap: 0.1rem;
}

.summery_menu_close {
  height: 0rem;
}

.summery_menu_open {
  height: 40rem;
}

.summery_menu li {
  width: 100%;
  border-radius: 50px;
  cursor: pointer;
  margin-bottom: 0.3rem;
  padding: 0.5rem 0rem;
}

.summery_menu_list {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 1rem;
  padding-left: 1.5rem;
}

.summery_menu_list_selected {
  border-radius: 50px;
  background-color: var(--primary_color) !important;
  color: var(--text-white-100);
}

.summery_menu-icons {
  width: 10%;
  transform: translateY(0.15rem);
}

.summery_menu li:hover {
  background-color: var(--primary_background);
}

/* ---------------------------------------------Sidebar Css Ends ------------------------------------------------------------------------------------------------- */

/* -----------------------------------------------Topbar CSS Start */

.topbar {
  gap: 1rem;
  color: var(--primary_color);
}

.topbar_parent {
  gap: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  align-content: baseline;
  margin-top: 1rem;
  width: 100%;
  padding-left: 3.3rem;
}

.topbar_heading {
  /* width: 20rem; */
  font-weight: 600;
  font-size: 2.25rem;
}

.topbar_search {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  box-shadow: var(--primary-shadow);
  padding: 0.8rem 1rem;
  border-radius: 50px;
}

.topbar_search input {
  font-size: 1rem;
  border: none;
  outline: none;
}

.topbar_search input:focus {
  border: none;
  outline: none;
}

.topbar_search input::placeholder {
  transform: translateX(1.7rem);
}

.topbar_search span {
  display: flex;
  align-items: center;
}

.topbar_utilities {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.topbar_Hr_details {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  border: 1px solid;
  border-image-source: linear-gradient(130.72deg,
      rgba(237, 231, 231, 0.07) -16.06%,
      rgba(251, 251, 251, 0.05) 82.03%);
  box-shadow: var(--primary-shadow);
  padding: 0.4rem;
  border-radius: 50px;
  font-size: 1rem;
  font-weight: 500;
}

.topbar_Hr_details_image img {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  display: flex;
  justify-content: center;
}

.topbar_Hr_details_dropdown {
  display: flex;
  justify-content: center;
}

.topbar_Hr_details_name {
  color: var(--text-black-87);
}

@media screen and (max-width: 1200px) {
  .topbar_Hr_details_image img {
    width: 2.5rem;
    height: 2.5rem;
  }
}

/* -----------------------------------------------Topbar CSS Ends */

/* --------------------------------------------------Media Quesries Starts --------------------------- */
@media only screen and (max-width: 1800px) {
  h1 {
    /* use this for 36px  */
    font-size: 2.025rem;
  }

  h2 {
    /* use this font size for  27px  */
    font-size: 1.44rem;
  }

  h3 {
    /* use this for 21px  */
    font-size: 1.17rem;
  }

  h4 {
    /* use this for semi bold 16px  */
    font-size: 1rem;
  }

  p {
    /* use this for regular 16px  */
    font-size: 1rem;
  }

  .p_tag_14px {
    /* use tjos fpr 14px  */
    font-size: 0.765rem;
  }

  .small_text_p {
    /* use this for 12px semi bold */
    font-size: 0.675rem !important;
  }

  .small_text_p_400 {
    /* /use this for 12px  400  regular */
    font-size: 0.675rem !important;
  }

  .very_small_text_p {
    /* for font  9 px */
    font-size: 0.54rem;
  }
}

@media only screen and (max-width: 1280px) {
  h1 {
    /* use this for 36px  */
    font-size: 1.8rem;
  }

  h2 {
    /* use this font size for  27px  */
    font-size: 1.28rem;
  }

  h3 {
    /* use this for 21px  */
    font-size: 1.04rem;
  }

  h4 {
    /* use this for semi bold 16px  */
    font-size: 1rem;
  }

  p {
    /* use this for regular 16px  */
    font-size: 1rem;
  }

  .p_tag_14px {
    /* use this for 14px  */
    font-size: 0.68rem;
  }

  .small_text_p {
    /* use this for 12px semi bold */
    font-size: 0.6rem !important;
  }

  .small_text_p_400 {
    /* use this for 12px  400  regular */
    font-size: 0.6rem !important;
    font-weight: 300;
  }

  .very_small_text_p {
    /* for font  9 px */
    font-size: 0.48rem;
  }
}

@media only screen and (max-width: 1024px) {
  .layout_container {
    overflow: hidden;
    display: none !important;
  }

  .not_Compatible {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    background-color: var(--main_background);
    color: var(--text-black-87);
    font-size: 2rem;
    font-weight: 600;
    text-align: center;
    padding: 1rem;
  }
}

@media screen and (max-width: 1536px) {
  .p_tag_21px {
    font-size: 1.1rem;
    font-weight: 300;
  }

  .sidebar_parent img {
    width: 10rem;
  }

  .main_menu {
    padding: 1rem;
  }

  .main_menu_open {
    padding: 1rem;
  }

  .main_menu-responsive_open {
    margin-top: 1rem;
  }

  .main_menu_button {
    transform: translateY(-0.9rem);
  }

  .main_menu_list_child {
    font-size: 0.8rem;
  }

  .svgDimension {
    width: 1rem;
    height: 1rem;
  }

  .summery_menu_list {
    font-size: 0.8rem;
  }

  .summery_menu_open {
    height: 17rem;
  }

  .topbar_heading {
    font-size: 2rem;
  }

  .topbar_search {
    padding: 0.6rem 1rem;
  }

  .topbar_search input {
    font-size: 0.8rem;
  }

  .topbar_search input::placeholder {
    transform: translateX(1.3rem);
  }

  .topbar_search span {
    width: 1rem;
    height: 1rem;
  }

  .topbar_Hr_details {
    width: 100%;
    font-size: 0.8rem;
    padding: 0.6rem;
  }

  .topbar_Hr_details_image img {
    width: 2rem;
    height: 2rem;
  }

  /* .DesignerDownArrowSvg {
    width: 1.1rem;
    height: 1.1rem;
  } */
}

@media screen and (max-width: 1280px) {
  .sidebar_parent img {
    width: 8rem;
  }

  .main_menu_button {
    font-size: 0.8rem;
  }

  .summery_menu_list {
    font-size: 0.6rem;
  }
}

@media only screen and (max-width: 1024px) {
  .not_Compatible {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    background-color: var(--main_background);
    color: var(--text-black-28);
    font-size: 2rem;
    font-weight: 600;
    text-align: center;
    padding: 1rem;
  }
}

@media (max-height: 900px) {
  .p_tag_48px {
    /* use tjos fpr 14px  */
    font-size: 2.4rem;
    font-weight: 300;
  }
}

@media (max-height: 820px) {
  .p_tag_48px {
    /* use tjos fpr 14px  */
    font-size: 2.2rem;
    font-weight: 300;
  }
}

/* --------------------------------------------------Media Quesries Ends ---------------------------------/* --------------------------------------------------Media Quesries Ends ---------------------------------*/

/* // ---------------------------------- Author: Armaan Singh -------------------------------- */

/* scrollbar properties */
::-webkit-scrollbar {
  width: 3px;
}

::-webkit-scrollbar-track {
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: var(--primary-color);
  border-radius: 10px;
} 