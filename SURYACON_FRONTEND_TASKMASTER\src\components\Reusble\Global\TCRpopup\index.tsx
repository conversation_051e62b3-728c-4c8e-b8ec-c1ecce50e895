import styles from "./Styles/TCRpopup.module.css";

import { useDispatch, useSelector } from "react-redux";
import { useToast } from "../../../../hooks/ToastHook";

import TCRpopupSummarypage from "./Subcomponents/TcrSummaryPage";
import TCRpopupsphotopage from "./Subcomponents/TcrPhotoCheckboxPage";

import TcrSummaryPage from "./Subcomponents/TcrSummaryPage";
import TcrPhotoCheckboxPage from "./Subcomponents/TcrPhotoCheckboxPage";
import { useEffect, useRef, useState } from "react";
import {
  resetInputValues,
  setInputValue,
} from "../../../../redux/features/Modules/Reusble/floatinglabelslice";
import {
  Attachment,
  AttachmentIcon,
  AudioIcon,
  CloseIcon,
  DeleteIcon,
  ImageIcon,
  RedCross,
  ReverseArrow,
  VideoIcon,
} from "../../../../assets/icons";
import FloatingLabelInput from "../FloatingLabel";
import Button from "../Button";
import RadioBtns from "../RadioBtns";
import { RootState } from "../../../../redux/store";
import { PhotoSection, WIopupProps } from "../GlobalInterfaces/GlobalInterface";
import { settaskChangeAPiFlag } from "../../../../redux/features/Modules/TaskMaster/Slices/TaskMasterSlice";
import { InitialStateProp } from "./../../../../redux/Interfaces/Modules/Billing/ProjectPlanning/ProjectPlanning.d";
import DiscardPopup from "../DiscardPopup";

function TCRpopup({
  onCancel,
  onSubmit,
  startWithPhotoCheckboxPage,

  isEdit = false,
  initialData,
}: WIopupProps) {
  const [photoUploadSections, setPhotoUploadSections] = useState<
    PhotoSection[]
  >(initialData?.photoDetails || []);
  const contentRef = useRef<HTMLDivElement | null>(null);
  const [isSummaryPage, setIsSummaryPage] = useState<boolean>(false);
  const [isClosing, setIsClosing] = useState<boolean>(false);
  const [error, setError] = useState<boolean>(false);
  const [description, setDescription] = useState(
    initialData?.description || ""
  );
  const [selectedCategory, setSelectedCategory] = useState<string>(
    initialData?.category || ""
  );
  const [file, setFile] = useState<{ name: string; type: string } | null>(
    initialData?.file || null
  );

  const options = [
    { value: "photo", label: "Photo" },
    { value: "checkbox", label: "Checkbox" },
  ];

  const inputValues = useSelector(
    (state: RootState) => state.floatingLabel.inputValues
  );
  const dispatch = useDispatch();
  const showToast = useToast();
  type photoErrors = {
    id: number;
    error: string;
  }[];
  const [categoryError, setCategoryError] = useState<boolean>(false);
  const [photoErrors, setPhotoErrors] = useState<photoErrors>([]);

  useEffect(() => {
    if (startWithPhotoCheckboxPage) {
      setSelectedCategory("photo");
    }
  }, [startWithPhotoCheckboxPage]);

  const handleAddOrUpdate = () => {
    // Check if description is empty or if category is not selected
    const isDescriptionValid = description.trim() !== "";
    const isCategoryValid = selectedCategory !== "";

    // Validate photo sections
    const photoErrors = photoUploadSections.reduce((errors, section) => {
      const { id, photo, referenceDetail } = section;
      const trimmedReferenceDetail = referenceDetail.trim();
      const isFirstSection =
        photoUploadSections.length === 1 || id === photoUploadSections[0].id;

      //this is original
      if (!trimmedReferenceDetail && !photo && isFirstSection) {
        errors.push({ id, error: "both" });
      } else if (!photo) {
        if (isFirstSection || trimmedReferenceDetail) {
          errors.push({ id, error: "photo" });
        }
      } else if (!trimmedReferenceDetail) {
        if (isFirstSection || photo) {
          errors.push({ id, error: "referencedetails" });
        }
      }

      // if (!photo && isFirstSection) {

      // }      errors.push({ id, error: "both" });
      // } else if (!photo) {
      //   if (isFirstSection) {
      //     errors.push({ id, error: "photo" });
      //   }

      return errors;
    }, [] as { id: number; error: string }[]);
    console.log(
      "isDescriptionValid",
      isDescriptionValid,
      "isCategoryValid",
      isCategoryValid
    );
    if (!isDescriptionValid || !isCategoryValid || photoErrors.length > 0) {
      // Set errors based on validation checks
      setError(!isDescriptionValid); // Set description error if invalid
      setCategoryError(!isCategoryValid); // Set category error if invalid
      // Check for duplicate IDs in photoErrors

      const uniquePhotoErrors = photoErrors.reduce((acc, current) => {
        const x = acc.find((item) => item.id === current.id);
        if (!x) {
          return acc.concat([current]);
        } else {
          return acc;
        }
      }, [] as { id: number; error: string }[]);

      setPhotoErrors(uniquePhotoErrors); // Set photo section errors if invalid
      showToast({
        messageContent: "Enter Required Fields!",
        type: "error",
      });

      return;
    }

    // Reset errors if everything is valid
    setError(false);
    setCategoryError(false);
    setPhotoErrors([]);

    // Move to summary page if valid
    setIsSummaryPage(true);
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files && files.length > 0) {
      const selectedFile = files[0];
      setFile({
        name: selectedFile.name,
        type: selectedFile.name.split(".").pop()?.toLowerCase() || "",
      });
    }
  };
  useEffect(() => {
    if (!initialData) {
      dispatch(resetInputValues());
    }
  }, [initialData]);
  const handleSubmit = () => {
    console.log(photoUploadSections, "yoyo");
    const data = {
      id: initialData?.id,
      description: description || "",
      file: file,
      category: selectedCategory,
      photoDetails: photoUploadSections.filter(
        (section) => section?.photo !== null && section?.referenceDetail !== ""
      ) as PhotoSection[],
    };

    onSubmit(data);
    dispatch(settaskChangeAPiFlag(true));
    handleClose();
  };

  const handleDeleteFile = () => {
    setFile(null);
  };

  // useEffect(() => {
  //   if (initialData) {
  //     dispatch(setInputValue({ description: initialData.description }));
  //   }
  // }, []);

  const handleValueChange = (value: string) => {
    if (value === "checkbox") {
      if (
        photoUploadSections.length > 1 ||
        (photoUploadSections.length === 1 &&
          (photoUploadSections[0].photo !== null ||
            photoUploadSections[0].referenceDetail.trim() !== ""))
      ) {
        showToast({
          messageContent: "Delete all the photo sections to add checkbox!",
          type: "error",
        });
        return;
      }
      setPhotoUploadSections([]);
    } else if (value === "photo") {
      setPhotoUploadSections([{ id: 1, photo: null, referenceDetail: "" }]);
    }

    setSelectedCategory(value);
    setPhotoErrors([]);
    setCategoryError(false);
  };
  const [showDiscardConfirmation, setShowDiscardConfirmation] =
    useState<boolean>(false);

  const hasChanges = () => {
    if (!initialData) {
      return (
        (description?.trim() || "") !== "" || // Safely handle undefined or null
        selectedCategory !== "" ||
        file !== null ||
        photoUploadSections.length > 0
      );
    }

    return (
      (description || "") !== initialData.description || // Handle undefined or null
      selectedCategory !== initialData.category ||
      file?.name !== initialData.file?.name ||
      JSON.stringify(photoUploadSections) !==
        JSON.stringify(initialData.photoDetails)
    );
  };

  const handleDiscard = () => {
    if (hasChanges()) {
      setShowDiscardConfirmation(true);
    } else {
      handleClose();
    }
  };

  const handleClose = () => {
    setIsClosing(true);
    setTimeout(() => {
      onCancel();
    }, 400);
  };

  const handleDiscardConfirmation = (confirm: boolean) => {
    if (confirm) {
      handleClose();
    } else {
      setShowDiscardConfirmation(false);
    }
  };
  useEffect(() => {
    initialData?.photoDetails == photoUploadSections;
  }, [photoUploadSections]);
  console.log("photodetails>>>main", photoUploadSections);
  // console.log('photodetails>>>dis', showDiscardConfirmation)
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if(isClosing) return;
    if (e.key === "Enter" && e.shiftKey) return;

    if (e.key === "Enter") {
      e.preventDefault();
      e.stopPropagation();
      if (!isSummaryPage && !showDiscardConfirmation) {
        handleAddOrUpdate();
      }
      if (isSummaryPage) {
        handleSubmit();
      }
      if (showDiscardConfirmation) {
        handleDiscardConfirmation(true);
      }
    }

    if (e.key === "Escape") {
      e.preventDefault();
      e.stopPropagation();
      if (!isSummaryPage && !showDiscardConfirmation) {
        handleDiscard();
      }
      if (isSummaryPage) {
        setIsSummaryPage(false);
      }
    }
  };

  const formRef = useRef(null);
  useEffect(() => {
    if (isSummaryPage || showDiscardConfirmation) {
      if (formRef.current) {
        formRef.current.focus();
      }
    }
  }, [isSummaryPage, showDiscardConfirmation]);

  const isEmpty = (value: any, file: any) => {
    console.log("outisde click tools inside if>>val:>", value, file);
    return (
      !Object.values(value).some((val) => {
        // console.log('outisde click tools inside if>>val:', val)
        return val !== undefined && val !== null && val !== "";
      }) && file === null
    );
  };
  const isEmpty2 = (data: any) => {
    console.log("outisde click tools inside if >>isEmp:::>>val:", data);
    if (
      (data?.photo === null && data?.referenceDetail === "") ||
      data === undefined
    ) {
      return true;
    } else {
      return false;
    }
  };
  console.log(isSummaryPage, " this is summary page");
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      console.log("outisde click tools", inputValues);
      if (formRef.current && !formRef.current.contains(event.target as Node)) {
        console.log("outisde click tools inside if");
        const isEmp = isEmpty(inputValues, file);
        const isEmp2 = isEmpty2(photoUploadSections?.[0]);
        console.log("outisde click tools inside if >>isEmp:", isEmp);
        if (isEmp && isEmp2) {
          // setIsClosing(true);
          // setTimeout(onClose, 400);

          console.log("outisde click tools inside inner if");

          handleClose();
        }
        if (!hasChanges() && !showDiscardConfirmation && !isSummaryPage) {
          handleClose();
        }
      }
    };

    document.addEventListener("mousedown", handleClickOutside);

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [inputValues, dispatch, file, photoUploadSections]);
  const [height, setHeight] = useState(0);
  const headerRef = useRef<HTMLDivElement>(null);
  useEffect(() => {
    const height = headerRef.current?.getBoundingClientRect().height;
    console.log("height>> of h3", height);
    if (height) {
      setHeight(Math.round(height));
    }
  }, [height, isSummaryPage]);

  useEffect(() => {
    requestAnimationFrame(() => {
    if (contentRef.current) {
      contentRef.current.scrollTop = 0;
    }
  });
}, [isSummaryPage, showDiscardConfirmation, selectedCategory, photoUploadSections.length]);
  return (
    <div
      className={`${styles.tcrpopup_container} ${
        isClosing ? styles.closing : ""
      }`}
      ref={formRef}
      onKeyDown={handleKeyDown}
      tabIndex={0}
    >
      <div ref={headerRef} className={styles.tcrpopup_header}>
        <h3
          style={{
            textAlign: "center",
            color: showDiscardConfirmation ? "var(--warning_color)" : "inherit",
          }}
        >
          {showDiscardConfirmation
            ? "Are you sure you want to discard these changes?"
            : isSummaryPage
            ? initialData
              ? "Are you sure you want to update this Task Closing Requirement?"
              : "Are you sure you want to add these Task Closing Requirements?"
            : initialData
            ? "Edit Task Closing Requirement"
            : "Add Task Closing Requirement"}
        </h3>
        <button
          className={styles.closeButton}
          onClick={
            showDiscardConfirmation
              ? () => setShowDiscardConfirmation(false)
              : handleDiscard
          }
        >
          <CloseIcon />
        </button>
      </div>

      {showDiscardConfirmation ? (
        <TcrSummaryPage
          description={
            description ? String(description) : "No description provided"
          }
          file={file}
          summary={true}
          selectedAction={selectedCategory}
          photoDetails={
            photoUploadSections?.filter(
              (section) =>
                section?.photo !== null && section?.referenceDetail !== ""
            ) as PhotoSection[]
          }
          height={height}
          onBack={() => setShowDiscardConfirmation(false)}
          onSubmit={() => handleDiscardConfirmation(true)}
          initialdata={initialData}
          startWithPhotoCheckboxPage={startWithPhotoCheckboxPage}
        />
      ) : isSummaryPage ? (
        <TcrSummaryPage
          height={height}
          description={
            description ? String(description) : "No description provided"
          }
          file={file}
          summary={true}
          selectedAction={selectedCategory}
          photoDetails={
            photoUploadSections?.filter(
              (section) =>
                section?.photo !== null && section?.referenceDetail !== ""
            ) as PhotoSection[]
          }
          onBack={() => setIsSummaryPage(false)}
          onSubmit={handleSubmit}
          initialdata={initialData}
          startWithPhotoCheckboxPage={startWithPhotoCheckboxPage}
        />
      ) : startWithPhotoCheckboxPage ? (
        <TcrPhotoCheckboxPage
          photoUploadSections={photoUploadSections}
          setPhotoUploadSections={setPhotoUploadSections}
          photoErrors={photoErrors}
          isEdit={isEdit}
        />
      ) : (
        <div className={styles.tcr_main_content} ref={contentRef}>
          <div className={styles.tcrpopup_row1}>
            <div className={styles.tcrpopup_descbox}>
              <FloatingLabelInput
                label="Description"
                id="description"
                focusOnInput={true}
                placeholder="Description"
                isInvalid={error}
                props="description_prop"
                value={description || ""}
                onInputChange={(value: string) => {
                  setDescription(value);
                  dispatch(setInputValue({ description: value }));
                  setError(false);
                }}
              />
            </div>
            <div
              className={
                !file
                  ? styles.tcrpopup_header_attachmentbtn
                  : styles.tcrpopup_header_attachmentbtn
              }
              style={{ cursor: "pointer" }}
              onClick={() => document.getElementById("fileInput")?.click()}
            >
              {file ? <ReverseArrow /> : <Attachment />}
              <input
                id="fileInput"
                type="file"
                style={{ display: "none" }}
                accept="audio/*,video/*,image/*"
                onChange={handleFileChange}
              />
              {/* {!file && ( */}
              <div className={styles.TCRpopup_popup_tip}>
                <p className="p_tag_14px">Image, Video, Audio (Any One)</p>
              </div>
              {/* )} */}
            </div>
          </div>
          {file && (
            <div className={styles.tcr_fileNames_div}>
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  margin: "0.5rem",
                  position: "relative",
                }}
                className={`${styles.tcr_fileNames} small_text_p_400`}
              >
                {file.type === "jpg" ||
                file.type === "jpeg" ||
                file.type === "png" ? (
                  <ImageIcon />
                ) : null}
                {file.type === "mp4" ? <VideoIcon /> : null}
                {file.type === "mp3" ? <AudioIcon /> : null}

                <p
                  style={{ marginLeft: "0.5rem" }}
                  className="small_text_p_400"
                >
                  {file.name}
                </p>
                <div
                  className={styles.file_cross_div}
                  style={{
                    display: "none",
                    cursor: "pointer",
                    marginLeft: "0.5rem",
                  }}
                  onClick={handleDeleteFile}
                >
                  <RedCross height="8px" width="8px" />
                </div>
                <div
                  className={styles.deleteButtondesc}
                  style={{
                    position: "absolute",
                    right: "-6%",
                    top: "5%",
                    transform: "translateY(-50%)",
                    cursor: "pointer",
                    display: "none",
                  }}
                  onClick={handleDeleteFile}
                >
                  <DeleteIcon />
                </div>
              </div>
            </div>
          )}
          <div className={styles.tcrpopup_secondrow}>
            <div className={styles.tcrpopup_secondrow_radiosection}>
              <p>Action:</p>

              <RadioBtns
                options={options}
                selectedValue={selectedCategory}
                onValueChange={handleValueChange}
                error={categoryError}
              />
            </div>
            {selectedCategory === "photo" && (
              <TcrPhotoCheckboxPage
                photoUploadSections={photoUploadSections}
                setPhotoUploadSections={setPhotoUploadSections}
                photoErrors={photoErrors}
                setPhotoErrors={setPhotoErrors}
                isEdit={initialData ? true : false}
              />
            )}
          </div>
        </div>
      )}
      <div className={styles.tcrpopup_btngrp}>
        {showDiscardConfirmation ? (
          <>
            <Button
              type="Cancel"
              Content="No"
              Callback={() => handleDiscardConfirmation(false)}
            />
            <Button
              type="Next"
              Content="Yes"
              Callback={() => handleDiscardConfirmation(true)}
            />
          </>
        ) : isSummaryPage ? (
          <>
            <Button
              type="Cancel"
              Content="Back"
              Callback={() => setIsSummaryPage(false)}
            />
            <Button type="Next" Content="Submit" Callback={handleSubmit} />
          </>
        ) : (
          <>
            <Button type="Cancel" Content="Cancel" Callback={handleDiscard} />
            <Button
              type="Next"
              Content={initialData ? "Update" : "Add"}
              Callback={() => {
                handleAddOrUpdate();
              }}
            />
          </>
        )}
      </div>
    </div>
  );
}

export default TCRpopup;
