/* Base skeleton box styles */
.skeleton_box {
  display: inline-block;
  background: linear-gradient(90deg, #005968 0%, #9fd7e1 50%, #005968 100%);
  background-size: 200% 100%;
  border-radius: 7px;
  vertical-align: middle;
  margin-bottom: 10px;
  background-position: 100% 0;
  animation: skeleton-wave-rtl 1.5s linear infinite;
}

/* Light version for smaller elements */
.skeleton_box_light {
  display: inline-block;
  background: linear-gradient(90deg, #e0e0e0 0%, #f5f5f5 50%, #e0e0e0 100%);
  background-size: 200% 100%;
  border-radius: 7px;
  vertical-align: middle;
  margin-bottom: 10px;
  background-position: 100% 0;
  animation: skeleton-wave-rtl 1.5s linear infinite;
}

/* Animation for the skeleton boxes */
@keyframes skeleton-wave-rtl {
  0% {
    background-position: 100% 0;
  }
  100% {
    background-position: -100% 0;
  }
}

/* Fixed box container */
.fixed_box {
  display: inline-block;
  vertical-align: middle;
  overflow: hidden;
  text-align: left;
}

/* Title text style */
.title_text {
  line-height: 32px;
  /* font-size: 2rem; */
  /* font-weight: 600; */
}

/* Description text style */
.description_text {
  line-height: 18px;
}

/* Content text style */
.content_text {
  line-height: 18px;
}

@keyframes shimmer-gradient {
  0% {
    background-position: 0% 0;
  }
  50% {
    background-position: 100% 0;
  }
  100% {
    background-position: 0% 0;
  }
}

.loader_container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.loader_image {
  width: 300px;
  height: 300px;
}

.taskcreation_container {
  height: 80vh;
  max-height: 75vh;
  overflow-x: hidden;
  width: 96%;
  position: relative;
  background-color: var(--main_background);
  /* ask aryan regarding this shadow */
  box-shadow: 0px 0px 3px 0px #00000080;
  border-radius: 36px;
  -ms-overflow-style: none;
  /* IE and Edge */
  scrollbar-width: none;
  display: flex;
  flex-direction: column;
  /* Firefox */
}

.taskcreation_form_container {
  /* padding-block: 5px;
  padding-block-end: 15px; */
  max-height: 60.5vh;
  /* Inherit from parent but don't stretch */
  overflow-x: hidden;
  scroll-behavior: smooth;
  flex: 1 1 auto;
  min-height: 0;
  max-height: none;
  overflow-y: auto;
}

/* Style for the scrollbar itself */
.taskcreation_form_container::-webkit-scrollbar {
  width: 4px;
  /* Width of the scrollbar */
}

/* Style for the scrollbar track (the background area) */
.taskcreation_form_container::-webkit-scrollbar-track {
  background-color: transparent;
  /* Light grey track */
  border-radius: 10px;
}

/* Style for the scrollbar thumb (the draggable part) */
.taskcreation_form_container::-webkit-scrollbar-thumb {
  background-color: var(--primary_color);
  /* Darker thumb */
  border-radius: 10px;
}

@media screen and (max-width: 1480px) {
  .taskcreation_container {
    height: 80vh;
    max-height: 76vh;
    overflow-y: auto;
    overflow-x: hidden;
    width: 96%;
    position: relative;
    background-color: var(--main_background);
    /* ask aryan regarding this shadow */
    box-shadow: 0px 0px 3px 0px #00000080;
    border-radius: 36px;
    -ms-overflow-style: none;
    /* IE and Edge */
    scrollbar-width: none;
    /* Firefox */
  }
}
@media screen and (max-height: 850px) {
  .taskcreation_container {
    max-height: 71vh;
  }
  .taskcreation_form_container {
    max-height: 52.7vh;
  }
}

.task_creation_header {
  flex: 0 0 auto;
  min-height: 140px;
  width: 100%;
  background-color: var(--primary_color);
  display: flex;
  justify-content: space-between;
  padding: 1rem;
  position: sticky;
  top: 0px;
  z-index: 1;
}

.taskcreation_editableState {
  margin: 0.5rem;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
}

.redcrossiconstyle {
  height: 36px;
  width: 36px;
  border-radius: 50%;
  background: var(--main_background);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 5;
  cursor: pointer;
  backdrop-filter: blur(100px);

  box-shadow: 0px 0px 6px 0px #91a1a199;
}

.taskcreation_dustbinicon {
  height: 36px;
  width: 36px;
  border-radius: 50%;
  background: #f6e6e6;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(100px);

  box-shadow: 0px 0px 6px 0px #91a1a199;
  z-index: 5;
  cursor: pointer;
}

.task_creation_titleandDesc {
  flex: 4;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 1rem;
}

.taskeditpencil {
  background-color: var(--main_background);
  width: 36px;
  height: 36px;
  border-radius: 100px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0.5rem;
  cursor: pointer;
}

.task_creation_right {
  flex: 2;
  width: 100%;
  display: flex;
  align-items: center;
  margin-top: 1rem;
  gap: 1.5rem;
  justify-content: center;
}

.task_creation_tooltip {
  height: 3.3rem;
  padding: 0rem 0.6rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 0.5rem;
  border-radius: 12px;
  background-color: var(--main_background);
}

.task_creation_tooltip_icon {
  min-width: 36px;
  height: 36px;
  background-color: var(--primary_background);
  border-radius: 8px;
  color: var(--primary_color);
  font-weight: 700;
  font-size: 12px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0.3rem;
}

.task_creation_header h2 {
  color: var(--text-white-100);
  line-height: 2.25rem;
}

.task_creation_header p {
  color: var(--text-white-87);
}

.task_creation_tooltip p {
  color: var(--text-black-87);
  line-height: 1rem;
}

.task_creation_master_container,
.task_creation_designation_container,
.task_creation_automation_container {
  min-height: 15vh;
  width: 99%;
  margin: 0.5rem;
}

.taskcreation_column {
  width: 100%;
  padding-left: 0.7rem;
}

.task_creation_master_row {
  display: flex;
  width: 100%;
  gap: 16px;
  flex-wrap: wrap;
  justify-content: space-between;
}

.task_creation_master_row_master {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  width: 100%;
  gap: 16px;
  flex-wrap: wrap;
  justify-content: space-between;
}

.task_creation_master_row_subtask {
  display: flex;
  width: 100%;
  gap: 16px;
  flex-wrap: wrap;
  justify-content: space-between;
}

.task_creation_master_row > * {
  flex: 0 0 32%;
}

.taskcreation_line_container {
  width: 100%;
  height: 0.5rem;
  display: flex;
  align-items: center;
  margin-block: 2rem;
  justify-content: space-between;
  gap: 5px;
}

.dottedline_wrapper {
  width: 49%;
  border-bottom: 1px dashed var(--line-color);
}

.task_creation_designation_header {
  display: flex;
  gap: 1rem;
  align-items: center;
  margin-left: 0.5rem;
}

.task_creation_designation_header h3 {
  color: var(--text-black-87);
}

.editTask_container {
  position: fixed;
  top: 48%;
  right: 0;
  transform: translate(-5%, -43%);
  background: var(--blur-background);
  padding: 1.25rem;
  backdrop-filter: blur(150px);
  box-shadow: 0px 4px 40px 0px #00000080;
  border-radius: 0.5rem;
  z-index: 6;
  width: 35rem;
  min-height: 85vh;
  border-radius: 2.6rem;
  animation: slideIn 0.5s ease-out;
}

.taskCreation_reporter_popup {
  position: absolute;
  inset-inline: 0;
  inset-block-start: 40px;
  background-color: var(--main_background);
  inline-size: 350px;
}

@keyframes slideIn {
  from {
    transform: translate(100%, -43%);
  }

  to {
    transform: translate(-5%, -43%);
  }
}

@keyframes slideOut {
  from {
    transform: translate(-5%, -43%);
  }

  to {
    transform: translate(100%, -43%);
  }
}

.editTask_container.closing {
  animation: slideOut 0.5s ease-out;
}

.editTaskform_header {
  color: #004350;

  display: flex;
  justify-content: center;
  padding: 0.6rem;
}

.closeButton {
  position: absolute;
  top: 0.625rem;
  right: 0.625rem;
  padding: 1rem;
  background: transparent;
  border: none;
  cursor: pointer;
}

.editTask_qtyinputs {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  position: relative;
}

.editTask_datainputs {
  height: 65vh;
  overflow-y: auto;
}

.editTask_btngrp {
  display: flex;
  justify-content: center;
  gap: 1rem;
  padding-top: 3rem;
}

/*Styles for subtask sidebar start by rattandeep singh  */
.subtask_outer_contaienr {
  display: flex;
  padding-left: 1rem;
  padding-right: 1rem;
  margin-top: 2rem;
  gap: 2.4rem;
}

.subtask_sidebar_contaienr {
  width: 13.5rem;
}

.subtask_sidebar_task_detail_container {
  cursor: pointer;
  padding: 0.5rem 1rem;
  display: flex;
  width: 13.5rem;
  align-items: center;
  border-radius: 100px;
  gap: 0.5rem;
}
.subtask_sidebar_task_detail_container p {
  font-weight: 500;
}

.subtask_sidebar_subtasks_lists_container {
  margin-top: 2rem;
  height: 42.5rem;
  overflow: hidden;
}

.subtask_sidebar_subtasks_lists_container_scroll {
  overflow-y: scroll;
  overflow-x: hidden;
  height: 40.8rem;
  padding: 0rem 0.4rem 1.6rem 1.4rem;
  margin-left: -1.2rem;
}

.subtask_sidebar_subtasks_lists_container_scroll::-webkit-scrollbar {
  width: 4px;
  /* Width of the scrollbar */
}

/* Style for the scrollbar track (the background area) */
.subtask_sidebar_subtasks_lists_container_scroll::-webkit-scrollbar-track {
  background-color: transparent;
  /* Light grey track */
  border-radius: 10px;
}

/* Style for the scrollbar thumb (the draggable part) */
.subtask_sidebar_subtasks_lists_container_scroll::-webkit-scrollbar-thumb {
  background-color: var(--primary_color);
  /* Darker thumb */
  border-radius: 10px;
}

.subtask_sidebar_subtask_container {
  cursor: pointer;
  display: flex;
  gap: 0.125rem;
  margin-top: 0.125rem;
}

.subtask_sidebar_subtask_right_container {
  align-self: end;
  display: flex;
  position: relative;
  gap: 0.125rem;
  top: 1.1875rem;
}

.subtask_sidebar_subtask_dotted_container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.125rem;
}

.subtask_sidebar_subtask_dots {
  border-radius: 10px;
  width: 0.0625rem;
  height: 0.375rem;
  background-color: var(--text-black-28);
}

.subtask_sidebar_subtask_right_dots_container {
  display: flex;
  align-items: center;
  gap: 0.125rem;
}

.subtask_sidebar_subtask_right_dots {
  width: 0.375rem;
  height: 0.0625rem;
  background-color: var(--text-black-28);
}

.active_subtaskRoute {
  background-color: var(--primary_color);
  color: var(--main_background) !important;
  height: 2.375rem;
  min-width: -webkit-fill-available;
  max-width: 8.6rem;
  display: flex;
  text-align: center;
  justify-content: center;
  padding: 0 0.5rem;
  align-items: center;
  border-radius: 100px;
}

.unactive_subtaskRoute {
  background-color: var(--main_background);
  color: var(--text-black-87);
  height: 2.375rem;
  width: 8.8125rem;
  display: flex;
  align-items: center;
  border-radius: 100px;
}

/*Styles for subtask sidebar end by rattandeep singh  */

/* this is from subtask and needed to be changed in future */
/*  AUTHOR NAME: CHARVI */
.subtaskcreation_container {
  height: 75vh;
  max-height: 75vh;
  overflow-y: auto;
  overflow-x: hidden;
  width: 96%;
  position: relative;
  background-color: var(--main_background);
  /* ask aryan regarding this shadow */
  box-shadow: 0px 0px 3px 0px #00000080;
  border-radius: 36px;
  -ms-overflow-style: none;
  /* IE and Edge */
  scrollbar-width: none;
  /* Firefox */
}

.subtask_confirmation_subcard {
  padding: 1rem;
  border-radius: 0.75rem;
  margin-top: 0.5rem;
  background-color: #ffffff99;
}

.subtask_creation_master_container,
.subtask_creation_designation_container,
.subtask_creation_automation_container,
.subtask_creation_method_container {
  min-height: 10vh;
  width: 99%;
  margin: 1rem;
}

.subtask_action_discard_time_interval {
  margin-top: 0.5rem;
  display: flex;
  justify-content: space-between;
}

.subtask_creation_master_row {
  display: flex;
  width: 100%;
  gap: 16px;
  flex-wrap: wrap;
  justify-content: space-between;
}

.subtask_creation_required_row {
  display: flex;
  width: 100%;
  gap: 16px;
  flex-wrap: wrap;
  justify-content: space-between;
}

.subtask_creation_required_row > * {
  flex: 0 0 40%;
}

.subtask_creation_master_row > * {
  flex: 0 0 32%;
}

.subtask_creation_method_row {
  display: flex;
  width: 95%;
  gap: 16px;
  flex-wrap: nowrap;
  justify-content: space-between;
}

.subtask_creation_method_row > * {
  flex: 0 0 50%;
  /* Each child takes 50% of the row's width */
  box-sizing: border-box;
  /* Include padding and borders in the width */
}

.subtaskcreation_line_container {
  width: 100%;
  height: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 5px;
}

.dottedline_wrapper {
  width: 49%;
  border-bottom: 1px dashed var(--line-color);
}

.subtask_creation_designation_header {
  display: flex;
  gap: 1rem;
  align-items: center;
  margin-left: 0.5rem;
}

.time_unit {
  border-radius: 3.5625rem;
  background-color: var(--primary_background);
  padding: 0.25rem 0.5rem;
  color: var(--primary_color);
}

.subtask_creation_designation_header h3 {
  color: var(--text-black-87);
}

.subtask_method_popup_container {
  /* min-width: 30.688rem; */
  margin-bottom: 0.5rem;
  border-radius: 24px;
  margin-left: 1.5rem;
  max-width: 668px;
  border: 1px solid #00000047;
}

.subtask_method_popup_header {
  padding: 1rem;
  display: flex;
  flex-wrap: wrap;
  align-items: start;
  gap: 1rem;
  min-width: 0;
  overflow-wrap: break-word;
  /* word-break: break-word; */
}

.subtask_method_popup_header_descdiv {
  background-color: red;
  box-shadow: 0px 0px 4px 0px #91a1a180;

  backdrop-filter: blur(40px);
  border: 1px solid #005968;
  min-width: 8rem;
  border-radius: 100px;
  padding: 0.8rem;
  text-align: center;
  word-wrap: break-word;

  overflow-wrap: break-word;

  white-space: normal;
}

.subtask_method_popup_row {
  display: flex;
  width: 100%;
  gap: 16px;
  flex-wrap: wrap;
  padding-left: 0.7rem;
}

.subtask_method_popup_row > div {
  flex: 1;
  min-width: 0;
  position: relative;
}

.control_plan_container {
  max-width: 16rem;
  width: auto;
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  border: 1px solid #00000047;
  border-radius: 24px;
  padding: 0.6rem 0.5rem;
}

.addtooltip_popup {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  background: #fff;
  border: 1px solid #ddd;
  box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
  z-index: 10;
  /* Ensure it's above other content */
  padding: 10px;
  border-radius: 4px;
}

.tcr_container {
  max-width: 15rem;
  width: auto;
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  border: 1px solid #00000047;
  border-radius: 24px;
}

.subtask_tcr_popup_headerPhotoupload > input {
  outline: none;
  border: 1px solid #00000047;
  box-shadow: 0px 0px 4px 0px #91a1a180;
  border-radius: 100px;
  min-width: 10rem;
  max-width: 10rem;
  padding: 0.7rem;
  text-align: center;

  color: var(--text-black-60);
}

.subtask_tcr_popup_headerPhotoupload {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  flex-wrap: wrap;
}

.subtask_fmea_popup_container {
  max-width: 31rem;
  width: auto;
  display: flex;
  /* Use flexbox */
  justify-content: space-between;

  align-items: center;

  /* Add spacing between items */
  border: 1px solid #00000047;
  border-radius: 24px;
}

.fmea_desc_container,
.fmea_solution_container {
  flex: 0.9;
  display: flex;
  justify-content: center;
  align-items: center;
}

.fmea_severity_counter {
  flex: 0 1 auto;
  /* Allow it to shrink and grow automatically */
  display: flex;
  gap: 0.1rem;
  justify-content: center;
  align-items: center;
  backdrop-filter: blur(40px);
  border: 1px solid #00596b;
  padding: 0.5rem;
  border-radius: 1.5rem;
  height: 3rem;
  box-shadow: 0px 0px 8px 0px #91a1a180;
  min-width: 6rem;
  /* Minimum width to prevent it from shrinking too much */
  width: auto;
  /* Adjust width based on content */
  transition: width 0.3s ease, padding 0.3s ease;
  /* Smooth transition for resizing */
}

.fmea_severity_counter h4 {
  margin: 0;
  /* Remove margin to keep the text centered */
  color: var(--text-black-60);
}

.fmea_severity_counter h4 {
  margin: 0;
  /* Remove any default margin to make sure it fits well */
  color: var(--text-black-60);
}

.severnity_minus,
.severnity_plus {
  width: 28px;
  height: 28px;
  border-radius: 100px;
  background-color: var(--primary_color);
  display: flex;
  align-items: center;
  justify-content: center;
}

.subtask_creation_header {
  min-height: 140px;
  width: 100%;
  background-color: var(--primary_color);
  display: flex;
  justify-content: space-between;
  padding: 1rem;
  position: sticky;
  top: 0px;
  z-index: 4;
  color: var(--text-white-100);
}

.subtask_creation_titleandDesc {
  flex: 4;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 1rem;
}

.subtask_creation_right {
  flex: 2;
  width: 100%;
  display: flex;
  align-items: center;
  margin-top: 1rem;
  gap: 1.5rem;
  justify-content: center;
}

.subtask_creation_tooltip {
  height: 3.3rem;
  padding: 0rem 0.6rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 0.5rem;
  border-radius: 12px;
  background-color: var(--main_background);
}

.subtask_creation_tooltip_icon {
  width: 36px;
  height: 36px;
  background-color: var(--primary_background);
  border-radius: 8px;
  color: var(--primary_color);
  font-weight: 700;
  font-size: 12px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.taskeditpencil {
  background-color: var(--main_background);
  width: 36px;
  height: 36px;
  border-radius: 100px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0.5rem;
  cursor: pointer;
}

.fmea_data_container {
  min-width: 5px;
  width: auto;
  max-width: 42.2rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  border: 1px solid #00000047;
  border-radius: 24px;
  padding: 1rem;
  margin-top: 0rem;
  margin-left: 1rem;
}

.fmea_data_container + .fmea_data_container {
  margin-top: 1rem;
}

.tcr_data_container {
  min-width: 5px;
  width: auto;
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  border: 1px solid #00000047;
  border-radius: 24px;
  padding: 1rem;
  margin-left: 1rem;
  gap: 1rem;
  align-items: baseline;
}

.tcr_desc_details {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.tcr_photoname {
  background-color: var(--primary_background);
  border-radius: 100px;
  padding: 0.4rem;
  min-width: 2rem;
  text-align: center;
  width: fit-content;
}

.fmea_tooltip {
  min-height: 2.5rem;
  min-width: 15rem;
  cursor: pointer;
  backdrop-filter: blur(40px);
  border-radius: 25px;
  display: flex;
  align-items: center;
  /* justify-content: center; */
  padding: 1rem;
  box-shadow: 0px 0px 4px 0px #91a1a180;
  border: 1px solid var(--primary_color);
  backdrop-filter: blur(40px);
}

.tcr_tooltip {
  min-height: 2.5rem;
  min-width: 6rem;
  cursor: pointer;
  backdrop-filter: blur(40px);
  border-radius: 20px;
  display: flex;
  align-items: center;

  padding: 0.6rem;
  box-shadow: 0px 0px 4px 0px #91a1a180;
  border: 1px solid var(--primary_color);
  backdrop-filter: blur(40px);
  /* color: var(--text-black-60); */
  justify-content: center;
}

.tcr_tooltip_photo {
  padding: 0.6rem 1.5rem;
  box-shadow: 0px 0px 4px 0px #91a1a180;
  border: 1px solid var(--primary_color);
  border-radius: 100px;
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.fmea_tooltip p {
  color: #000000;
  line-height: 1.1rem;
  color: var(--text-black-60);
}

.tcr_photoadd_container {
  position: fixed;
  top: 48%;
  right: 0;
  transform: translate(-5%, -43%);
  background: var(--blur-background);
  /* background-color: var(--main_background); */
  padding: 1.25rem;
  box-shadow: 0px 4px 40px 0px #00000080;
  border-radius: 2.6rem;
  z-index: 9999;
  width: 35rem;
  min-height: 85vh;
  animation: slideIn 0.5s ease-out;
  backdrop-filter: blur(150px);

  box-shadow: 0px 4px 40px 0px #00000080;
}

@keyframes slideIn {
  from {
    transform: translate(100%, -43%);
  }

  to {
    transform: translate(-5%, -43%);
  }
}

.fmea_tooltip_description,
.fmea_tooltip_Solution {
  font-size: 16px;
  font-weight: 600;
  color: var(--primary_color);
}

.fmea_tooltip_solution {
  min-height: 2.5rem;
  min-width: 15rem;
  cursor: pointer;
  backdrop-filter: blur(40px);
  border-radius: 25px;
  display: flex;
  justify-content: space-between;
  box-shadow: 0px 0px 4px 0px #91a1a180;
  border: 1px solid var(--primary_color);
  backdrop-filter: blur(40px);
  clip-path: inset(0px round 25px);
}

.fmea_tooltip_solution_container {
  flex: 1 0 70%;

  border-radius: 25px 0px 0px 25px;
  padding: 1rem;
}

.fmea_tooltip_severity_container {
  flex-grow: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: var(--primary_color);
  color: var(--text-white-100);
  flex-direction: column;
  overflow: hidden;
  /* border-radius: 0px 25px 25px 0px; */
}

/* styles for delete icon in subtask header */
.header_delete_icon {
  background-color: var(--main_background);
  width: 36px;
  height: 36px;
  border-radius: 100px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0.5rem;
  cursor: pointer;
  position: absolute;
  bottom: 1rem;
  right: 1rem;
}

/* styles for delete subtask popups start by rattandeep singh */

.summaryDivData {
  display: flex;
  align-items: center;
  width: 100%;
  flex: 1;
  min-width: 150px;
}

.summaryDataContent {
  display: flex;
  flex-direction: column;
  background: var(--main_background);
  border-radius: 0.75rem;
  width: 30.8rem;
  width: 100%;

  /* width: 30.8rem; */
  width: 100%;
  /* max-width: 28.5rem; */
  min-height: 3rem;
  padding: 1rem;
  /* word-break: break-all; */
  overflow-wrap: break-word;
  white-space: normal;
  margin: 0.6rem;
  /* gap: 0.2rem; */
  line-height: 1.363rem;
  text-align: left;
}

.summaryDataContent_weightage {
  display: flex;

  background: var(--main_background);
  border-radius: 0.75rem;
  width: 30.8rem;
  /* max-width: 28.5rem; */
  min-height: 3rem;
  padding: 1rem;
  /* word-break: break-all; */
  overflow-wrap: break-word;
  white-space: normal;
  margin: 0.6rem;

  line-height: 1.363rem;
  justify-content: space-between;
  align-items: center;
}

.flexContainer {
  display: flex;
  flex-wrap: wrap;
  /* gap: 1rem; */
  width: 100%;
  max-width: 100%;
}

.flexContainer > .p_tag_14px {
  flex: 1;
  min-width: calc(50% - 0.5rem);
  box-sizing: border-box;
}

/* styles for delete subtask popups end by rattandeep singh */
.category {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 90%;
  padding: 0.6rem 1rem;
  margin: 0.5rem;
  border-radius: 1.5rem;
  background: var(--white-70-background);
  box-shadow: var(--extra-shadow-four);
  position: relative;
  left: 50%;
  transform: translateX(-53%);
  cursor: pointer;
  list-style-type: none;
  color: var(--text-black-60);
  border: 1px solid;
  border-image-source: var(--primary-bg-gradient);
  backdrop-filter: blur(100px);
}

.delete_icon_tooltip {
  position: absolute;
  top: -0.5rem;
  right: 0rem;
  cursor: pointer;
  height: 24px;
  width: 24px;
  background-color: var(--secondary_warning_background);
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 100%;
}

@media (max-width: 1200px) {
  .taskcreation_container {
    min-width: 1400px;
  }

  .subtask_outer_contaienr {
    width: fit-content;
  }
}
@media (max-height: 900px) {
  .subtask_sidebar_subtasks_lists_container {
    margin-top: 2rem;
    height: 34.5rem;
    overflow: hidden;
  }

  .subtask_sidebar_subtasks_lists_container_scroll {
    overflow-y: scroll;
    height: 31.3rem;
    margin-bottom: 1rem;
  }
}

.task_version_history_popup {
  position: absolute;
  inset-inline-end: 0;
  inset-block-start: 10%;
  inset-block-end: 0;
  z-index: 1000;
}

/* Base skeleton box styles */
.skeleton_box {
  display: inline-block;
  background: linear-gradient(90deg, #005968 0%, #9fd7e1 50%, #005968 100%);
  background-size: 200% 100%;
  border-radius: 7px;
  vertical-align: middle;
  margin-bottom: 10px;
  background-position: 100% 0;
  animation: skeleton-wave-rtl 1.5s linear infinite;
}

/* Light version for smaller elements */
/* .skeleton_box_light {
  display: inline-block;
  background: linear-gradient(90deg, #e0e0e0 0%, #f5f5f5 50%, #e0e0e0 100%);
  background-size: 200% 100%;
  border-radius: 7px;
  vertical-align: middle;
  margin-bottom: 10px;
  background-position: 100% 0;
  animation: skeleton-wave-rtl 1.5s linear infinite;
} */

/* Animation for the skeleton boxes */
@keyframes skeleton-wave-rtl {
  0% {
    background-position: 100% 0;
  }
  100% {
    background-position: -100% 0;
  }
}

/* Fixed box container */
.fixed_box {
  display: inline-block;
  vertical-align: middle;
  overflow: hidden;
  text-align: left;
}

/* Title text style */
.title_text {
  line-height: 32px;
  /* font-size: 2rem; */
  /* font-weight: 600; */
}

/* Description text style */
.description_text {
  line-height: 18px;
}

/* Content text style */
.content_text {
  line-height: 18px;
}

@keyframes shimmer-gradient {
  0% {
    background-position: 0% 0;
  }
  50% {
    background-position: 100% 0;
  }
  100% {
    background-position: 0% 0;
  }
}

.loader_container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.loader_image {
  width: 300px;
  height: 300px;
}

.glowGreen, .glowRed {
  height: 9px;
  width: 9px;
  border-radius: 50%;
  margin-left: 8px;
  box-shadow: 0 0 6px rgba(0, 0, 0, 0.2);
}

.glowGreen {
  background-color: #4CAF50;
  border: 2px solid #4CAF50;
}

.glowRed {
  background-color: #f44336;
  border: 2px solid #f44336;
}
