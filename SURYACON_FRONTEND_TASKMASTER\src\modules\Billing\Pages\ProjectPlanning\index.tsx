import React, { useEffect } from "react";
import { Outlet, useNavigate } from "react-router-dom";
import TaskHeader from "../../../TaskMaster/Pages/TaskForm/Subcomponents/TaskHeader/TaskHeader";

const ProjectPlanning: React.FC = () => {
  // useEffect(() => {
  //   navigate("/billing/location/project");
  // }, []);
  return (
    <>
      {/* <TaskHeader /> */}
      <Outlet />
    </>
  );
};

export default ProjectPlanning;
