import { useState, useEffect } from "react";

import styles from "../Styles/WorkInstructions.module.css";
import {
  AddCategoryIcon,
  BackIcon,
  CloudUpload,
  DeleteIcon,
  PreviewIcon,
  ReverseArrow,
} from "../../../../../assets/icons";
import FloatingLabelInput from "../../../Global/FloatingLabel";
import {
  compressImage,
  getFileName,
  getOnlineURl,
  isBase64,
  reencodeImage,
} from "../../../../../functions/functions";
// import { compressImage } from "../../../../../functions/functions";
import { useAppSelector } from "../../../../../redux/hooks/Modules/Reduxhooks/ReduxHooks";
import { image_url, url } from "../../../../../config/urls";
import { useDispatch } from "react-redux";
import { useToast } from "../../../../../hooks/ToastHook";

interface PhotoSection {
  id: number;
  photo: string | null;
  referenceDetail: string;
}

interface WorkinstructionsphotopageProps {
  setSelectedCategory: React.Dispatch<React.SetStateAction<string>>;
  isEdit?: boolean;
  photoUploadSections: PhotoSection[];
  setPhotoErrors?: React.Dispatch<
    React.SetStateAction<{ id: number; error: string }[]>
  >;
  photoErrors: { id: number; error: string }[];
  setPhotoUploadSections: React.Dispatch<React.SetStateAction<PhotoSection[]>>;
}

function Workinstructionsphotopage({
  isEdit = false,
  setSelectedCategory,
  photoErrors,
  setPhotoErrors,
  photoUploadSections,
  setPhotoUploadSections,
}: WorkinstructionsphotopageProps) {
  const [expandedSectionId, setExpandedSectionId] = useState<number | null>(
    null
  );
  const [imageUrls, setImageUrls] = useState<{ [key: string]: string }>({});
  const dispatch = useDispatch();
  const showToast = useToast();
  const imageLocalPath = useAppSelector((state) => state.localImageSlice.src);
  // useEffect(() => {
  //   localStorage.setItem(
  //     "photoUploadSections",
  //     JSON.stringify(photoUploadSections)
  //   );
  // }, []);
  console.log("photoUploadSections", photoUploadSections);
  //remove depenency bcz re renderirng from usseffect photoUploadSections
  const handleAddSection = () => {
    setPhotoUploadSections((prevSections) => [
      ...prevSections,
      {
        id: prevSections[prevSections.length - 1]?.id + 1,
        photo: null,
        referenceDetail: "",
      },
    ]);
    // setPhotoUploadSections((prevSections) =>
    //   prevSections.map((section) =>
    //     section.id === id ? { ...section, referenceDetail: value } : section
    //   )
    // );
  };

  const handleFileChange = async (
    event: React.ChangeEvent<HTMLInputElement>,
    id: number
  ) => {
    const file = event.target.files ? event.target.files[0] : null;
    const fileInput = event.target;
    if (file) {
      const validTypes = ["image/jpeg", "image/jpg", "image/png", "image/webp"];
      const compressedFile = await compressImage(file, 0.3);

      if (validTypes.includes(compressedFile.type)) {
        const base64Image = await reencodeImage(compressedFile);

        setPhotoUploadSections((prevSections) =>
          prevSections.map((section) =>
            section.id === id
              ? {
                  ...section,
                  photo: base64Image,
                  fileName: compressedFile.name,
                }
              : section
          )
        );
        setImageUrls((prev) => ({
          ...prev,
          [id]: base64Image,
        }));

        fileInput.value = "";
      } else {
        console.warn("Invalid file type or size:", compressedFile);
      }
    }
  };

  const handleReferenceDetailChange = (value: string, id: number) => {
    setPhotoUploadSections((prevSections) =>
      prevSections.map((section) =>
        section.id === id ? { ...section, referenceDetail: value } : section
      )
    );
  };

  console.log("photo upload>>>>>>>>>>>>>", photoUploadSections);
  const handleDeletePhoto = (id: number) => {
    setPhotoUploadSections((prevSections) => {
      if (prevSections.length === 1) {
        return [{ id: 1, photo: null, referenceDetail: "" }];
      }
      const updatedSections = prevSections.filter(
        (section) => section.id !== id
      );
      if (updatedSections.length === 0) {
        setSelectedCategory("");
      }
      return updatedSections;
    });
  };

  const handledeletePhotoOnly = (id: number) => {
    setPhotoUploadSections((prevSections) => {
      const updatedSections = prevSections.map((section) =>
        section.id === id
          ? { ...section, photo: null, fileName: null }
          : section
      );
      return updatedSections;
    });
  };
  useEffect(() => {
    const fetchImages = async () => {
      for (const section of photoUploadSections) {
        const key = section.id;

        if (!imageUrls[key] && section.photo && !isBase64(section.photo)) {
          const url = await getOnlineURl(section.photo, dispatch);
          setImageUrls((prev) => ({ ...prev, [key]: url }));
        } else if (!imageUrls[key] && isBase64(section.photo!)) {
          setImageUrls((prev) => ({ ...prev, [key]: section.photo }));
        }
      }
    };

    fetchImages();
  }, [photoUploadSections, imageUrls, dispatch]);

  return (
    <div className={styles.tcrphotopopup_maincontainer}>
      {photoUploadSections.map((section) => (
        <div
          key={section.id}
          className={`${styles.tcrphotopopup_container} ${
            expandedSectionId === section.id ? styles.expanded : ""
          }`}
          style={
            photoErrors?.some((e) => e.id === section?.id && e.error === "both")
              ? { border: "1px solid red" }
              : undefined
          }
        >
          {" "}
          {(section.photo !== null ||
            section.photo == null ||
            section?.referenceDetail !== "") && (
            <div
              className={styles.deleteButton}
              onClick={() => handleDeletePhoto(section.id)}
            >
              <DeleteIcon />
            </div>
          )}
          {(section.photo !== null ||
            section.photo == null ||
            section?.referenceDetail !== "") && (
            <div
              className={styles.deleteButton2}
              onClick={() => handledeletePhotoOnly(section.id)}
            >
              <DeleteIcon />
            </div>
          )}
          <div
            className={`${styles.tcrphotopopup_uploadphotocontainer} ${
              expandedSectionId === section.id ? styles.expandedUpload : ""
            }`}
            style={
              photoErrors?.some(
                (e) => e.id === section?.id && e.error === "photo"
              )
                ? { border: "1px solid red" }
                : undefined
            }
          >
            <div className={styles.uploadWrapper}>
              {section.photo ? (
                <div
                  className={styles.imageContainer}
                  // style={
                  //   photoErrors?.some((e) => e.id === section?.id && e.error !=="both")
                  //     ? { border: "1px solid red" }
                  //     : undefined
                  // }
                >
                  {imageUrls[section.id] && (
                    <div className={styles.imageContainer}>
                      <img
                        src={imageUrls[section.id]}
                        alt="Uploaded"
                        className={styles.uploadedImage}
                      />
                    </div>
                  )}
                  <div className={styles.overlayButtons}>
                    <div
                      className={styles.previewButton}
                      onClick={() =>
                        setExpandedSectionId(
                          expandedSectionId === section.id ? null : section.id
                        )
                      }
                    >
                      {expandedSectionId === section.id ? (
                        <>
                          <BackIcon /> <p className="p_tag_14px">Back</p>
                        </>
                      ) : (
                        <>
                          <PreviewIcon /> <p className="p_tag_14px">Preview</p>
                        </>
                      )}
                    </div>
                    <div
                      className={styles.Image_picker_repeat}
                      style={{ cursor: "pointer" }}
                      onClick={() =>
                        document
                          .getElementById(`fileInput-${section.id}`)
                          ?.click()
                      }
                    >
                      <ReverseArrow />
                    </div>
                  </div>
                </div>
              ) : (
                <div
                  onClick={() =>
                    document.getElementById(`fileInput-${section.id}`)?.click()
                  }
                  className={styles.emptyState}
                >
                  <CloudUpload />
                  <h4 style={{ color: "var(--text-black-60)" }}>
                    Upload a reference photo
                  </h4>
                  <p
                    className="p_tag_14px"
                    style={{ color: "var(--text-black-28)" }}
                  >
                    Supports: png, jpeg
                  </p>
                </div>
              )}
              <input
                id={`fileInput-${section.id}`}
                type="file"
                accept=".jpeg,.jpg,.png"
                style={{ display: "none" }}
                onChange={(event) => {
                  setPhotoErrors && setPhotoErrors([]);
                  handleFileChange(event, section.id);
                }}
              />
            </div>
          </div>
          <FloatingLabelInput
            label="Reference Detail"
            id={`ReferenceDetail-${section.id}`}
            placeholder="Reference Detail"
            value={section.referenceDetail}
            props="description_prop"
            isInvalid={photoErrors?.some(
              (e) => e.id === section.id && e.error === "referencedetails"
            )}
            onInputChange={(value: any) => {
              setPhotoErrors && setPhotoErrors([]);
              handleReferenceDetailChange(value, section.id);
            }}
          />
        </div>
      ))}
      <div
        className={styles.tcrphotopopup_addphotodiv}
        onClick={() => {
          if (!photoUploadSections[photoUploadSections.length - 1].photo) {
            showToast({
              messageContent: "Please upload last reference photo ",
              type: "warning",
            });
          }
          photoUploadSections[photoUploadSections.length - 1]?.photo &&
            handleAddSection();
        }}
        style={{ cursor: "pointer" }}
      >
        Photo <AddCategoryIcon />
      </div>
    </div>
  );
}

export default Workinstructionsphotopage;
