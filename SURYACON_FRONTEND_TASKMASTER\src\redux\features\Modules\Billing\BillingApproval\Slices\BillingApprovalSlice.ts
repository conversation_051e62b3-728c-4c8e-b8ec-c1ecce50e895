import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { formatDateMT } from "../../../../../../functions/functions";
import {
  MTSubTaskDataProps,
  MTSubTaskModifiedDataProps,
  MTTaskDataProps,
  MTTaskModifiedDataProps,
} from "../../../../../../interfaces/Modules/Billing/BillingApproval/BillingApproval";

// Remove the static data and replace it with more flexible interfaces for selected data

export function modifiedMTTaskDataFormatter(
  data: MTTaskDataProps
): MTTaskModifiedDataProps {
  return {
    _id: data._id,
    Tower: { value: data.tower, className: "w-50" },
    Floor: { value: data.floor, className: "w-50" },
    "Tower Description": {
      value: data.towerDescription,
      className: "w-100",
    },
    "Start Date": {
      value: formatDateMT(data.startDate),
      className: "w-50",
    },
    "End Date": { value: formatDateMT(data.endDate), className: "w-50" },
    Area: { value: data.area, className: "w-50" },
    Budget: { value: data.Budget, className: "w-50" },
    Drawings: { value: data.drawing, className: "w-100" },
    Remarks: { value: data.remarks || [], className: "w-100" },
    Reason: { value: data.reason, className: "w-100" },
    Decline: { value: data.decline, className: "w-50" },
  };
}

export function modifiedMTSubTaskDataFormatter(
  data: MTSubTaskDataProps
): MTSubTaskModifiedDataProps {
  return {
    _id: data._id,
    Location: { value: data.location, className: "w-50" },
    Floor: { value: data.floor, className: "w-50" },
    "Task Description": { value: data.taskDescription, className: "w-100" },
    // "Start Date": { value: formatDateMT(data.startDate), className: "w-50" },
    // "End Date": { value: formatDateMT(data.endDate), className: "w-50" },
    Area: { value: data.area, className: "w-50" },
    Budget: { value: data.Budget, className: "w-50" },
    Subtasks: { value: data.subtask, className: "w-100" },
    Drawings: { value: data.drawing, className: "w-100" },
    Remarks: { value: data.remarks || [], className: "w-100" },
    Reason: { value: data.reason, className: "w-100" },
    Decline: { value: data.decline, className: "w-50" },
  };
}

interface MonthlyTargetState {
  CurrentEditcardId: string | null;
  isApproveDialogOpen: boolean;
  isDeclineDialogOpen: boolean;
  isSubTaskApprovalFormOpen: boolean;
  isTaskApprovalFormOpen: boolean;
  isSummaryDialogueOpen: boolean;
  selectedTaskId: string | null;
  selectedTower: string | null;
  selectedFloor: number | null;
  monthlyTargetSubTaskData: MTSubTaskDataProps | null;
  modifiedMonthlyTargetSubTaskData: MTSubTaskModifiedDataProps | null;
  monthlyTargetTaskData: MTTaskDataProps | null;
  modifiedMonthlyTargetTaskData: MTTaskModifiedDataProps | null;
}

const initialState: MonthlyTargetState = {
  CurrentEditcardId: null,
  isApproveDialogOpen: false,
  isDeclineDialogOpen: false,
  isSubTaskApprovalFormOpen: true,
  isTaskApprovalFormOpen: false,
  isSummaryDialogueOpen: false,
  selectedTaskId: null,
  selectedTower: null,
  selectedFloor: null,
  monthlyTargetSubTaskData: null,
  modifiedMonthlyTargetSubTaskData: null,
  monthlyTargetTaskData: null,
  modifiedMonthlyTargetTaskData: null,
};

const monthlyTargetSlice = createSlice({
  name: "monthlyTarget",
  initialState,
  reducers: {
    openSubTaskApproveDialog: (state, action: PayloadAction<string>) => {
      state.isApproveDialogOpen = true;
      state.isTaskApprovalFormOpen = false;
      state.isSummaryDialogueOpen = false;
      state.isDeclineDialogOpen = false;
      state.isSubTaskApprovalFormOpen = false;
      state.selectedTaskId = action.payload;
    },
    openTaskApproveDialog: (state, action: PayloadAction<string>) => {
      state.isApproveDialogOpen = false;
      state.isSummaryDialogueOpen = false;
      state.isDeclineDialogOpen = false;
      state.isSubTaskApprovalFormOpen = false;
      state.isTaskApprovalFormOpen = true;
      state.selectedTaskId = action.payload;
    },
    openDeclineDialog: (state, action: PayloadAction<string>) => {
      state.isDeclineDialogOpen = true;
      state.isApproveDialogOpen = false;
      state.isTaskApprovalFormOpen = false;
      state.isSummaryDialogueOpen = false;
      state.isSubTaskApprovalFormOpen = false;
      state.selectedTaskId = action.payload;
    },
    openSummaryDialog: (state, action: PayloadAction<string>) => {
      state.isApproveDialogOpen = false;
      state.isSummaryDialogueOpen = true;
      state.isTaskApprovalFormOpen = false;
      state.isDeclineDialogOpen = false;
      state.isSubTaskApprovalFormOpen = false;
      state.selectedTaskId = action.payload;
    },
    closeDialogs: (state) => {
      state.isApproveDialogOpen = false;
      state.isDeclineDialogOpen = false;
      state.isTaskApprovalFormOpen = false;
      state.isSubTaskApprovalFormOpen = true;
      state.isSummaryDialogueOpen = false;
      state.selectedTaskId = null;
    },
    setSelectedTowerAndFloor: (state, action: PayloadAction<{ tower: string; floor: number }>) => {
      state.selectedTower = action.payload.tower;
      state.selectedFloor = action.payload.floor;
    },
    setMonthlyTargetSubTaskData: (state, action: PayloadAction<MTSubTaskDataProps>) => {
      state.monthlyTargetSubTaskData = action.payload;
      state.modifiedMonthlyTargetSubTaskData = modifiedMTSubTaskDataFormatter(action.payload);
    },
    setMonthlyTargetTaskData: (state, action: PayloadAction<MTTaskDataProps>) => {
      state.monthlyTargetTaskData = action.payload;
      state.modifiedMonthlyTargetTaskData = modifiedMTTaskDataFormatter(action.payload);
    },
    setCurrentCardId: (state, action: PayloadAction<string>) => {
      state.CurrentEditcardId = action.payload;
    },
  },
});

export const {
  openSubTaskApproveDialog,
  openTaskApproveDialog,
  setCurrentCardId,
  openDeclineDialog,
  closeDialogs,
  openSummaryDialog,
  setSelectedTowerAndFloor,
  setMonthlyTargetSubTaskData,
  setMonthlyTargetTaskData,
} = monthlyTargetSlice.actions;

export default monthlyTargetSlice.reducer;