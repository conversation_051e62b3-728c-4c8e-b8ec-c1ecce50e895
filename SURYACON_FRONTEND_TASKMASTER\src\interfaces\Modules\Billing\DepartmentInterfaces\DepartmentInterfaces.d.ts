export interface IDepartMentformData {
  _id?: string;
  departmentName: string;
  departmentHead: { _id: string; name: string } | null;
  description: string;
  email: string;
}

// Designation Interface

export interface AddDesignationFormProps {
  isClosing?: boolean;
  setIsClosing?: React.Dispatch<React.SetStateAction<boolean>>;
  handleClose: (targetForm: string) => void;
}

// Department Card Interface
export interface IDepartmentCardProps {
  data: any;
  cardHandler: (...args: any[]) => any;
  type: "Department" | "Desigination";
  isSoftDeleted : boolean;
}

// FormChild Template
export interface FormChildTemplateProps {
  formData: Record<string, any>;
  formDataLabels: Record<string, string>;
  formUpdatedField?: (
    key: string,
    value?: string
  ) => "default" | "updated" | "discard" | undefined;
  initialFormData?: Record<string, any>;
  formValueListStyle?:React.CSSProperties;
}
