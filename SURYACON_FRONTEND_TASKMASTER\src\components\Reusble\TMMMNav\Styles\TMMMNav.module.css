/*  AUTHOR NAME : CHARVI */
.tmmmnav_conatiner {
    display: flex;
    margin-top: 1rem !important;
    justify-content: space-between;
    align-items: center;
    height: 2.6rem;
    padding-left: 1rem;
    padding-right: 0.5rem !important;
  }
  
  .CategoryView_Navlinks {
    display: flex;
    width: fit-content;
    gap: 0.3rem;
    align-items: center;
  }
  
  .CategoryView_DesignArrow_Rotate {
    transform: rotate(-90deg);
    display: flex;
    align-items: center;
    width: fit-content;
  }
  
  .summaryDivData {
    display: flex;
    align-items: center;
  }
  
  .summaryDataContent {
    display: flex;
    flex-direction: column;
    background: #ffffff99;
    border-radius: 0.75rem;
    width: 30.8rem;
    /* max-width: 28.5rem; */
    min-height: 3.188rem;
    padding: 1rem;
    white-space: normal;
    margin: 0.6rem;
    gap: 0.2rem;
    line-height: 1.363rem;
    text-align: left;
  }
  
  .leftbtn {
    border: 1px solid var(--primary_color);
    width: 5.1rem;
    height: 2rem;
    margin: 0.7rem;
    color: var(--primary_color);
    border-radius: 3.125rem;
    background-color: white;
  }
  
  .tmmmnav_rightbtns {
    display: flex;
    width: 25%;
    justify-content: flex-end;
    gap: 0.938rem;
    position: relative;
  }

  .tmmmnav_center{
    display: flex;
    width: 44.8%;
    justify-content: center;
  }
  
  .tmmmdltbtn,
  .tmmmdexportbtn {
    width: 7.1rem;
    height: 2.6rem;
    padding: 1rem 1rem;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;
    border-radius: 6.25rem;
    cursor: pointer;
    background-color: var(--main_background);
    box-shadow: 0px 0px 4px 0px #00000066;
    border: none;
    color: var(--text-black-60);
  }
  
  .tmmmaddcategorybtn {
    /* min-width: 7.1rem; */
    height: 2.6rem;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;
    border-radius: 6.25rem;
    cursor: pointer;
    background-color: var(--primary_color);
    color: var(--text-white-100);
    border: none;
  
    padding: 1rem;
  }
  
  .tmmm_header {
    position: sticky;
    top: 0;
    z-index: 1;
    margin-right: 0.625rem;
    left: 0;
  }
  
  .tmmmnav_left {
    display: flex;
    width: 37.8%;
    gap: 1rem;
  }
  