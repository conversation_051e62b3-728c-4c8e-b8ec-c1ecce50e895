import { useState } from "react";
import { currentSubtaskData } from "./../../../../Interfaces/Modules/TaskMaster/TaskMasterInterface.d";
import {
  ActionReducerMapBuilder,
  createSlice,
  PayloadAction,
} from "@reduxjs/toolkit";
import { stat } from "fs";
import {
  SubtaskRoute,
  TaskMasterState,
} from "../../../../Interfaces/Modules/TaskMaster/TaskMasterInterface";

const initialState: TaskMasterState = {
  isChange: false,
  isApiCall: false,
  searchKey: "",
  typeSearchKey: "",
  SubTasksRoutes: [],
  currentSubTaskRoute: "",
  currentSubtaskData: {
    _id: "",
    name: "",
    Unit: "",
    Description: "",
    subtaskWeighatages: 0,
    Tracking: "",
    MaterialId: [],
    TaskmasterId: {},
    ToolId: [],
    MachinaryId: [],
    ManpowerId: [],
    AdminId: [],
    ReporterId: {
      Reporter: [],
    },
    AssigneeId: [],
    AutoId: {
      TriggerAction: {
        ActionName: {
          id: "",
          name: "",
        },
        ActionTime: "",
      },
      TriggerResponse: {
        _id: "",
        name: "",
        isFirst: false,
      },
      ResponseTime: "",
    },
    MethodId: {
      work_instruction_id: [],
      task_closing_requirement: [],
      Controlplan: [],
      Failuremode: [],
    },
  },
};

const TaskMasterSlice = createSlice({
  name: "taskMaster",
  initialState,
  reducers: {
    setSubTaskRoute: (state, action: PayloadAction<SubtaskRoute[]>) => {
      state.SubTasksRoutes = action.payload;
    },
    addSubTaskRoute: (state, action: PayloadAction<SubtaskRoute>) => {
      state.SubTasksRoutes.push(action.payload);
    },
    setCurrentSubTaskRoute: (state, action: PayloadAction<string>) => {
      state.currentSubTaskRoute = action.payload;
    },
    setcurrentSubtaskData: (
      state,
      action: PayloadAction<currentSubtaskData>
    ) => {
      console.log("action.payload", action.payload);
      state.currentSubtaskData = action.payload;

      console.log(state.currentSubtaskData, "action.payload 3");
    },
    setSearchKey: (state, action: PayloadAction<string>) => {
      state.searchKey = action.payload;
    },
    setTypeSearchKey: (state, action: PayloadAction<string>) => {
      state.typeSearchKey = action.payload;
    },
    updateSubtaskData: (state, action: PayloadAction<currentSubtaskData>) => {
      console.log("update to ho raha he bhai", action.payload);
      state.currentSubtaskData = action.payload;
    },
    setIsChangeSubtask: (state, action: PayloadAction<void>) => {
      state.isChange = true;
    },
    resetCurrentSubtaskData: (state) => {
      state.currentSubtaskData = initialState.currentSubtaskData;
    },
  },
});

// task redux setup by aayush for add ,delete and update task data here ..............

// task initial state
const TaskinitialState: TaskMasterState = {
  SubTasksRoutes: [],
  currentSubTaskRoute: "",
  navigateToTask: false,
  navigateToTaskView: false,
  currentSubtaskData: {
    _id: "",
    name: "",
    Unit: "",
    Description: "",
    subtaskWeighatages: 0,
    Tracking: "",
    DepartmentId: [],
    DesignationId: [],
    MaterialId: [],
    ToolId: [],
    MachinaryId: [],
    ManpowerId: [],
    Adminid: [],
    ReporterId: [
      {
        _id: "",
        Reporter: [],
      },
    ],
    TaskmasterId: {},
    AssigneeId: [],
    Subtaskdetails: [],
    // AutoId: {
    //   TriggerAction: {
    //     ActionName: "delayed",
    //     ActionTime: 10,
    //   },
    //   _id: "67c17a844069d288508501a9",
    //   TriggerResponse: {
    //     _id: "67b3319fe8f04cbb591e9c5c",
    //     itemtype: "subtaskmodal",
    //     name: "setting out site",
    //   },
    //   ResponseTime: 6,
    // },
    MethodId: {
      work_instruction_id: [],
      task_closing_requirement: [],
      Controlplan: [],
    },
  },
};

//task initial task data slice
const TaskDataSlice = createSlice({
  name: "taskslice",
  initialState: TaskinitialState,
  reducers: {
    setcurrentTaskData: (state, action: PayloadAction<currentSubtaskData>) => {
      state.currentSubtaskData = action.payload;
    },
    updateTaskData: (state, action: PayloadAction<currentSubtaskData>) => {
      state.currentSubtaskData = action.payload;
    },
    resetTaskData: (state, action: PayloadAction<void>) => {
      return initialState;
    },
    setNavigateToTask: (state, action: PayloadAction<boolean>) => {
      console.log("action.payload >>>>>>>>>>", action.payload);
      state.navigateToTask = action.payload;
    },
    setNavigateToTaskView: (state, action: PayloadAction<boolean>) => {
      console.log("action.payload >>>>>>>>>>123123", action.payload);
      state.navigateToTaskView = action.payload;
    },
  },
});

// Method Id For Api Call Here By Aayush

const MethodIdCurrentTask = {
  MethodId: {
    work_instruction_id: [
      {
        photoref: {
          photos: [
            {
              photo: "",
              details: "",
            },
          ],
        },
        file: {
          name: "",
          type: "",
        },
        _id: "",
        Description: "",
        optionselected: "",
        materialId: [],
        manpowerId: [],
        toolsId: [],
        machinaryId: [],
      },
    ],
    task_closing_requirement: [],
    Controlplan: [],
  },
};

const MethodCurrentTaskSlice = createSlice({
  name: "taskslice",
  initialState: MethodIdCurrentTask,
  reducers: {
    setcurrentTaskMethodMethod: (state, action: PayloadAction<any>) => {
      state.MethodId = action.payload;
    },
    updateTaskData: (state, action: PayloadAction<any>) => {
      state.MethodId = action.payload;
    },
  },
});

const isEditTaskState = {
  isEdit: false,
  istaskApiCall: false,
  issubtaskApiCall: false,
};

const isEditTaskSlice = createSlice({
  name: "isEditTask",
  initialState: isEditTaskState,
  reducers: {
    setIsEditTask: (state, action: PayloadAction<boolean>) => {
      state.isEdit = action.payload;
    },
    settaskChangeAPiFlag: (state, action: PayloadAction<boolean>) => {
      state.istaskApiCall = action.payload;
    },
    setChangeAPiFlag: (state, action: PayloadAction<boolean>) => {
      console.log(action.payload, "action payload here >>>>>>>>>>>>");
      state.issubtaskApiCall = action.payload;
    },
  },
});

export const {
  setSubTaskRoute,
  addSubTaskRoute,
  setSearchKey,
  setTypeSearchKey,
  setCurrentSubTaskRoute,
  setcurrentSubtaskData,
  updateSubtaskData,
  setIsChangeSubtask,
  resetCurrentSubtaskData,
} = TaskMasterSlice.actions;

export const {
  setcurrentTaskData,
  updateTaskData,
  resetTaskData,
  setNavigateToTask,
  setNavigateToTaskView,
} = TaskDataSlice.actions;

export const { setIsEditTask, settaskChangeAPiFlag, setChangeAPiFlag } =
  isEditTaskSlice.actions;
export const subtaskReducer = TaskMasterSlice.reducer;
export const taskFormReducer = TaskDataSlice.reducer;
export const MethodCurrentTaskReducer = MethodCurrentTaskSlice.reducer;
export const isEditTaskReducer = isEditTaskSlice.reducer;
