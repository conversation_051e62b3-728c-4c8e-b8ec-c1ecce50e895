import React, { useCallback, useEffect, useRef, useState } from "react";
import styles from "./Styles/ProgressCard.module.css";
import ProgressOverviewCard from "../ProgressOverviewCard";
import { useToast } from "../../../../../../../hooks/ToastHook";

import { useNavigate } from "react-router-dom";
import {
  DeleteIcon,
  Twodots,
  YellowEditPencil,
} from "../../../../../../../assets/icons";
import { useDispatch } from "react-redux";
import {
  setopenedProject,
  setSelectedProjectIddata,
} from "../../../../../../../redux/features/Modules/Billing/ProjectPlanning/Slices/ProjectSlice";
import { openPopup, closePopup} from "../../../../../../../redux/features/Modules/Reusble/popupSlice";
import { ProjectData } from "../../AddProjectForm/Interfaces/interface";
import {
  extractDateParts,
  getFileName,
  isBase64,
  slicedData,
} from "../../../../../../../functions/functions";
import {
  setSetProjectRate,
  setselectedprojectestimate,
} from "../../../../../../../redux/features/Modules/Billing/ProjectPlanning/Slices/projectPlanningSlice";
import { image_url } from "../../../../../../../config/urls";
import { useAppSelector } from "../../../../../../../redux/hooks/Modules/Reduxhooks/ReduxHooks";

const ProgressCard: React.FC<ProjectData> = (data) => {
  const navigate = useNavigate();
  const [isCatPopupVisible, setIsCatPopupVisible] = useState(false);
  const [actualImageSrc, setActualImageSrc] = useState("/SuryaconLogo.png");
  const imagePoll = useAppSelector((state) => state.masterForm.imageToggle);
  console.log(getFileName(data.photo as string), "data for progress card");
  const handleCardClick = (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
    if (!(e.target as HTMLElement).closest(`.${styles.cat_popup}`)) {
      navigate(`/billing/main/${data?._id}`);
      dispatch(setopenedProject(data?.name));
      dispatch(setSetProjectRate(data?.rate));
      dispatch(setselectedprojectestimate(data?.estimate_budget));
      console.log(data, "this is data in project");
    }
  };
  console.log(data, "this is progress card data");
  const showToast = useToast();
  const handleDotsClick = (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
    e.stopPropagation();
    
    if (!navigator.onLine) {
      showToast({
        messageContent: "Oops! no internet connection!",
        type: "danger",
      });
      return;
    }
    
    setIsCatPopupVisible(!isCatPopupVisible);
  };

  // function to show sliced data end by rtn
  // function to convert iso date in reaquired format to show
  const [isHovered, setIsHovered] = useState<{
    id: number | null;
    hoverState: boolean;
  }>({
    id: null,
    hoverState: false,
  });

  console.log("isHovered", isHovered.id);

  const handleMouseEnter = (id: number) => {
    setIsHovered({ id, hoverState: true });
  };

  const handleMouseLeave = (id: number) => {
    setIsHovered((prev) => ({
      ...prev,
      hoverState: false,
      id: id,
    }));
  };

  useEffect(() => {
    if (!data?.photo) {
      setActualImageSrc("/SuryaconLogo.png");
      return;
    }

    const imageUrl = `${image_url}/images/${getFileName(data?.photo)}`;
    const img = new Image();

    img.src = imageUrl;

    img.onload = () => {
      console.log("Image is now available:", imageUrl);
      setActualImageSrc(imageUrl); // Set the actual image
    };

    img.onerror = () => {
      console.log("Image still not available.");
      setActualImageSrc("/SuryaconLogo.png");
    };
  }, [imagePoll, data?.photo]);

  const dispatch = useDispatch();
  const cardRef = useRef<HTMLDivElement>(null);
  const popup = useRef<HTMLDivElement>(null);

  const handleClickOutside = useCallback(
    (event: MouseEvent) => {
      if (
        cardRef.current &&
        !cardRef.current.contains(event.target as Node) &&
        popup.current &&
        !popup.current.contains(event.target as Node)
      ) {
        setIsCatPopupVisible && setIsCatPopupVisible(false);
      }
    },
    [dispatch]
  );

  useEffect(() => {
    document.addEventListener("mousedown", (e) => handleClickOutside(e));
    return () => {
      document.removeEventListener("mousedown", (e) => handleClickOutside(e));
    };
  }, [handleClickOutside]);
  return (
    <div ref={cardRef}>
      <div className={styles.completed_card}>
        <div
          className={`${styles.progress_card_outer_container} `}
          style={{}}
          onClick={handleCardClick}
        >
          <div className={`${styles.progress_card_inner_container}`}>
            <div className={`${styles.progress_card_top_container}`}>
              <div className={`${styles.progress_card_top_left_container}`}>
                <h2
                  className={`${styles.progress_card_title}`}
                  style={{ whiteSpace: "nowrap" }}
                >
                  {slicedData(data?.name, 20)}
                </h2>
                <div className={`${styles.project_sft_card}`}>
                  <h4>{data?.rate_type}</h4>
                </div>
              </div>
              <div
                className={`${styles.progress_card_topright_outer_container}`}
              >
                <div className={`${styles.progress_card_topright_container}`}>
                  <p className={`${styles.progress_card_percentage_card}`}>
                    {Math.round(Number(data?.project_completes))}%
                  </p>
                  <div
                    className={styles.dots_container}
                    onClick={handleDotsClick}
                  >
                    <Twodots />
                    {isCatPopupVisible && (
                      <div ref={popup} className={styles.cat_popup}>
                        <div
                          onClick={() => {
                            console.log(data, "this is add project data11111");
                            dispatch(closePopup("AddProjectForm"));

                            setTimeout(() => {
                              dispatch(setSelectedProjectIddata(data));
                              dispatch(openPopup("AddProjectForm"));
                            },);
                          }}
                          onMouseEnter={() => handleMouseEnter(1)}
                          onMouseLeave={() => handleMouseLeave(1)}
                          className={`${styles.cat_popup_edit} ${
                            isHovered?.id === 1
                              ? isHovered.hoverState
                                ? styles.edit_hovered
                                : styles.edit_notHovered
                              : ""
                          }`}
                        >
                          <div
                            className={styles.cat_popup_transition_div_edit}
                          ></div>
                          <div className={styles.cat_popup_editicon}>
                            <YellowEditPencil />
                          </div>
                          <h4>Edit</h4>
                        </div>
                        <div
                          onClick={() => {
                            console.log(data, "thisisprogressdata");
                            dispatch(setSelectedProjectIddata(data));
                            dispatch(openPopup("deleteproject"));
                          }}
                          onMouseEnter={() => handleMouseEnter(2)}
                          onMouseLeave={() => handleMouseLeave(2)}
                          className={`${styles.cat_popup_dlt} ${
                            isHovered?.id === 2
                              ? isHovered.hoverState
                                ? styles.dlt_hovered
                                : styles.dlt_notHovered
                              : ""
                          }`}
                        >
                          <div
                            className={styles.cat_popup_transition_div_delete}
                          ></div>
                          <div className={styles.cat_popup_dlticon}>
                            <DeleteIcon />
                          </div>
                          <h4>Delete</h4>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
            <div className={`${styles.progress_card_lower_container}`}>
              <div className={`${styles.progress_card_image_container}`}>
                <img
                  loading="lazy"
                  src={actualImageSrc}
                  alt={getFileName(data.photo as string) as string}
                />
              </div>
              <div
                className={`${styles.progress_card_overviewcards_container}`}
              >
                <ProgressOverviewCard
                  keyname={"Client name"}
                  value={slicedData(data?.clientName, 12)}
                />
                <ProgressOverviewCard
                  keyname={"Approx. Area"}
                  value={String(data?.project_area)}
                />
                <ProgressOverviewCard
                  keyname={"Start date"}
                  value={((date) =>
                    `${date?.day} ${date?.monthName} ${date?.year}`)(
                    extractDateParts(data?.project_start_date)
                  )}
                />
                <ProgressOverviewCard
                  keyname={"Duration"}
                  value={`${String(data?.project_duration)} Months`}
                />
                <ProgressOverviewCard
                  keyname={"Estimated Budget"}
                  value={String(data?.estimate_budget)}
                  color="var(--primary_color)"
                />
                <ProgressOverviewCard
                  keyname={"Project Completed"}
                  value={"250,00,000,000"}
                  color="var(--secondary_color)"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProgressCard;
