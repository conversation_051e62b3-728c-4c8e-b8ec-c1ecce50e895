import { useEffect, useRef, useState } from "react";
import styles from "../Styles/Manpower.module.css";
import { CloseIcon } from "../../../../../../assets/icons";
import Button from "../../../../../../components/Reusble/Global/Button";
import FloatingLabelInput from "../../../../../../components/Reusble/Global/FloatingLabel";
import RadioBtns from "../../../../../../components/Reusble/Global/RadioBtns";
import { useSelector } from "react-redux";
import { RootState } from "../../../../../../redux/store";
import {
  resetDeletedToolData,
  setDeletedToolData,
  setFormManpowerData,
} from "../../../../../../redux/features/Modules/TaskMaster/Slices/MastersSlice";
import { useAppDispatch } from "../../../../../../redux/hooks/Modules/Reduxhooks/ReduxHooks";

import DynamicGradeInput from "../../Subcomponents/DynamicGradeInput";
import { isValidValue } from "../../../../../../functions/functions";
import {
  useAddManpowerDesignationMutation,
  useUpdateManpowerDesignationMutation,
} from "../../../../../../redux/api/Modules/Billing/Billingapi";
import { useParams } from "react-router-dom";
import ManpowerDiscard from "./ManpowerDiscard";
import ManpowerSummary from "./ManpowerSummary";
import { taskFormReducer } from "./../../../../../../redux/features/Modules/TaskMaster/Slices/TaskMasterSlice";
import { setIsLocalChange } from "../../../../../../redux/features/Modules/Reusble/backupSlice";
import { useToast } from "../../../../../../hooks/ToastHook";

const AddManpowerForm: React.FC<{
  isClosing?: boolean;
  setIsClosing?: React.Dispatch<React.SetStateAction<boolean>>;
  handleClose: (targetForm: string) => void;
}> = ({ isClosing = false, handleClose }) => {
  const formData = useSelector(
    (state: RootState) => state.masterForm.formManpowerData
  );
  const dispatch = useAppDispatch();
  const initialFormData = useSelector(
    (state: RootState) => state.masterForm.initialFormManpowerData
  );
  const formMode = useSelector((state: RootState) => state.masterForm.formMode);
  const [errors, setErrors] = useState<{
    Type: boolean;
    Name: boolean;
    Skills: boolean;
  }>({
    Type: false,
    Name: false,
    Skills: false,
  });
  const contentRef = useRef<HTMLDivElement>(null);
  const [selectedOption, setSelectedOption] = useState<string>(
    formData?.type ?? ""
  );
  const deletedToolData = useSelector(
    (state: RootState) => state.masterForm.deleteToolData
  );
  const [discard, setDiscard] = useState(false);
  const [showSummary, setShowSummary] = useState(false);
  const [wasTrue, setWasTrue] = useState(false);
  const formRef = useRef(null);
  const showToast = useToast();
  //params
  const { manpowerCategoryId } = useParams();

  const options = [
    { value: "skilled", label: "Skilled" },
    { value: "unskilled", label: "Unskilled" },
    { value: "both", label: "Both" },
  ];

  //api to add material
  const [addManpowerDesignation] = useAddManpowerDesignationMutation();

  //update api
  const [updateManpowerDesignation] = useUpdateManpowerDesignationMutation();

  // Handle Input Change
  const handleInputChange = (id: string, value: string) => {
    dispatch(
      setFormManpowerData({
        ...formData,
        [id]: value,
      })
    );
  };

  // console.log("form data", formData);

  // Next Button Click
  const handleNext = () => {
    // console.log("input values", formData);
    if (showSummary) {
      // console.log("Form Submitted: ", formData);
    } else {
      if (
        !selectedOption ||
        !formData?.Name?.trim() ||
        (formData?.type !== "unskilled" && !formData?.Skills?.[0]?.trim())
      ) {
        setErrors((prevErrors) => ({
          ...prevErrors,
          Type: !selectedOption || prevErrors.Type,
          Name: !formData?.Name?.trim() || prevErrors.Name,
          Skills:
            formData?.type !== "unskilled"
              ? !formData?.Skills?.[0]?.trim() || prevErrors.Skills
              : false,
        }));

        showToast({
          messageContent: "Enter Required Fields!",
          type: "warning",
        });

        return;
      }
      setShowSummary(true);
    }
  };

  const handleBack = () => {
    setDiscard(false);
    setShowSummary(false);
  };

  const areArraysDifferent = (arr1: {}[], arr2: {}[]) => {
    if (!arr1 || !arr2) return true;
    if (arr1.length !== arr2.length) return true;

    return arr1.some(
      (item, index) => JSON.stringify(item) !== JSON.stringify(arr2[index])
    );
  };

  const hasFormChanged = () => {
    if (formMode === "Add") {
      return (
        formData?.Name?.trim() ||
        formData?.Description?.trim() ||
        formData?.type ||
        formData?.Skills?.[0]?.trim()
      );
    } else {
      return (
        formData?.Name?.trim() !== initialFormData?.Name?.trim() ||
        formData?.Description?.trim() !==
          initialFormData?.Description?.trim() ||
        formData?.type !== initialFormData?.type ||
        areArraysDifferent(formData?.Skills, initialFormData?.Skills)
      );
    }
  };
  const handleCancel = () => {
    const hasChanged = hasFormChanged();

    if (hasChanged) {
      setDiscard(true);
      return;
    }

    handleClose("AddManpowerForm");
  };

  //for submission api call
  const handleSubmit = async () => {
    try {
      if (formMode === "Edit") {
        const changes = hasFormChanged();

        if (!changes) {
          setShowSummary(false);
          showToast({
            messageContent: "There were no changes!",
            type: "warning",
          });
          return;
        }
      }

      //formatted the data into the form acceptable by backend so that in future minimum changes are required
      const formatedData = {
        ...(formMode === "Edit" && formData?._id
          ? { manpowerDesignationId: formData._id }
          : {}),
        name: formData?.Name,
        Description: formData?.Description,
        Types: formData?.type?.toLowerCase(),
        ...(formMode === "Add"
          ? { manpowerCategoryId: manpowerCategoryId }
          : {}),
        skills: formData?.Skills,
      };

      if (formMode === "Add") {
        const response = await addManpowerDesignation(formatedData).unwrap();
        showToast({
          messageContent: "Manpower added successfully!",
          type: "success",
        });
      } else {
        //update api call
        const response = await updateManpowerDesignation(formatedData).unwrap();
        showToast({
          messageContent: "Manpower updated successfully!",
          type: "success",
        });
      }
      handleClose("AddManpowerForm");
      dispatch(setIsLocalChange(true));
    } catch (error) {
      console.error("error", error);
      showToast({
        messageContent:
          (error as { data?: { message?: string } })?.data?.message ||
          "Oops! Something went wrong",
        type: "danger",
      });
    }
  };
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if(isClosing) return;
    if (e.key === "Enter" && e.shiftKey) return;

    if (e.key === "Enter") {
      e.preventDefault();
      e.stopPropagation();
      if (!showSummary && !discard) {
        handleNext();
      }
      if (showSummary) {
        handleSubmit();
        dispatch(resetDeletedToolData());
      }
      if (discard) {
        handleClose("AddManpowerForm");
        dispatch(resetDeletedToolData());
      }
    }

    if (e.key === "Escape") {
      e.preventDefault();
      e.stopPropagation();
      if (!showSummary && !discard) {
        handleCancel();
      }
      if (showSummary) {
        handleBack();
      }
      if (discard) {
        if (discard && wasTrue) {
          setDiscard(false);
          setShowSummary(true);
          setWasTrue(false);
          return;
        }
        setDiscard(false);
      }
    }
  };
  useEffect(() => {
    if (showSummary || discard) {
      if (formRef.current) {
        formRef.current.focus();
      }
    }
  }, [showSummary, discard]);

  console.log("manpower form ::", formData);

  const [formEmpty, setFormEmpty] = useState(true);

  const isEmpty = (data): boolean => {
    if (
      data?.Name === "" &&
      data?.Description === "" &&
      data?.Skills?.length === 0 &&
      data?.type === ""
    ) {
      console.log("issempty>>: true");
      return true;
    } else {
      console.log("issempty>>: false");
      return false;
    }
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (formRef.current && !formRef.current.contains(event.target as Node)) {
        const isEmp = isEmpty(formData);
        setFormEmpty(isEmp);
        if (isEmp) {
          handleClose("AddManpowerForm");
          return;
        }

        if (!hasFormChanged() && !discard) {
          handleClose("AddManpowerForm");
        }
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [formData, dispatch]);

  useEffect(() => {
    requestAnimationFrame(() => {
      if (contentRef.current) {
        contentRef.current.scrollTop = 0;
      }
    });
  }, [showSummary, discard]);

  return (
    <div
      className={`${styles.addmanpowerform_container} ${
        isClosing ? styles.closing : ""
      }`}
      tabIndex={0}
      onKeyDown={handleKeyDown}
      ref={formRef}
    >
      <div
        className={styles.addmanpowerform_header}
        style={{ color: discard ? "var(--warning_color)" : "" }}
      >
        <h3>
          {showSummary
            ? `Are you sure you want to ${
                formMode === "Add" ? "add" : "update"
              } this Manpower?`
            : discard
            ? "Are you sure you want to discard these changes?"
            : formMode === "Add"
            ? "Add Manpower"
            : "Edit Manpower"}
        </h3>
        <button
          onClick={() => {
            if (!hasFormChanged() && showSummary) {
              handleClose("AddManpowerForm");
              return;
            }
            if (showSummary) {
              setDiscard(true);
              setWasTrue(true);
              setShowSummary(false);
              return;
            }
            if (discard && !wasTrue) {
              setDiscard(false);
              return;
            }
            if (discard && wasTrue) {
              setDiscard(false);
              setWasTrue(false);
              setShowSummary(true);
              return;
            }

            handleCancel();
          }}
          className={styles.closeButton}
        >
          <CloseIcon />
        </button>
      </div>
      {showSummary ? (
        <ManpowerSummary
          formData={formData}
          initialFormData={initialFormData}
          formMode={formMode}
          // deletedFormData={deletedFormData}
          // deletedGradeData={deletedGradeData}
          deletedToolData={deletedToolData}
        />
      ) : discard ? (
        // <ManpowerDiscard
        //   formData={formData}
        //   initialFormData={initialFormData}
        //   formMode={formMode}
        //   // deletedFormData={deletedFormData}
        //   // deletedGradeData={deletedGradeData}
        // />
        <ManpowerSummary
          formData={formData}
          initialFormData={initialFormData}
          formMode={formMode}
          // deletedFormData={deletedFormData}
          // deletedGradeData={deletedGradeData}
          deletedToolData={deletedToolData}
        />
      ) : (
        <div className={styles.addmanpowerform_datainputs} ref={contentRef}>
          <h4>Type</h4>
          <RadioBtns
            options={options}
            selectedValue={selectedOption}
            onValueChange={(value) => {
              if (value === "unskilled" && formData?.Skills?.length > 0) {
                showToast({
                  messageContent: "Please Delete All Skills!",
                  type: "warning",
                });
                return;
              }
              setSelectedOption(value);
              setErrors({ ...errors, Type: false });
              handleInputChange("type", value);
            }}
            errors={errors?.Type}
          />

          <FloatingLabelInput
            label="Name"
            focusOnInput={true}
            id="name"
            error={errors?.Name}
            props="one_line"
            placeholder="Name"
            value={formData?.Name}
            onInputChange={(value: any) => {
              handleInputChange("Name", value);
              setErrors({ ...errors, Name: false });
            }}
          />

          <FloatingLabelInput
            label="Description"
            id="Description"
            placeholder="Description"
            props="description_prop"
            value={formData?.Description}
            onInputChange={(value: any) => {
              handleInputChange("Description", value);
            }}
          />
          {formData?.type !== "unskilled" && (
            <>
              <h4 style={{ marginTop: "1.5rem" }}>Skills</h4>
              <div style={{ paddingTop: "0.5rem" }}>
                {/* <GradeInputbox /> */}
                <DynamicGradeInput
                  label="Add"
                  placeholder="Skill"
                  error={errors?.Skills}
                  callbackDelete={(deleteIndex) => {
                    const deletedGradeValue = formData?.Skills[deleteIndex];
                    const deletedGrade =
                      formData?.Skills?.includes(deletedGradeValue) &&
                      initialFormData?.Skills?.includes(deletedGradeValue);

                    if (deletedGrade && formMode !== "Add")
                      dispatch(
                        setDeletedToolData([
                          ...deletedToolData,
                          deletedGradeValue,
                        ])
                      );
                    dispatch(
                      setFormManpowerData({
                        ...formData,
                        Skills: Array.isArray(formData?.Skills)
                          ? formData?.Skills.filter((_, i) => i !== deleteIndex) // Remove Skill at `index`
                          : [],
                      })
                    );
                  }}
                  initialData={formData?.Skills}
                  onGradesUpdate={(grades) => {
                    setErrors({ ...errors, Skills: false });
                    dispatch(
                      setFormManpowerData({
                        ...formData,
                        Skills: [...grades],
                      })
                    );
                  }}
                />
              </div>
            </>
          )}
        </div>
      )}
      <div className={styles.addmanpowerform_btngroup}>
        {showSummary ? (
          <>
            <Button type="Cancel" Content="Back" Callback={handleBack} />
            <Button
              type="Next"
              Content="Submit"
              Callback={() => {
                handleSubmit();
                dispatch(resetDeletedToolData());
              }}
            />
          </>
        ) : discard ? (
          <>
            <Button
              type="Cancel"
              Content="No"
              Callback={() => {
                if (discard && wasTrue) {
                  setDiscard(false);
                  setShowSummary(true);
                  setWasTrue(false);
                  return;
                }
                setDiscard(false);
              }}
            />
            <Button
              type="Next"
              Content="Yes"
              Callback={() => {
                handleClose("AddManpowerForm");
                dispatch(resetDeletedToolData());
              }}
            />
          </>
        ) : (
          <>
            <Button type="Cancel" Content="Cancel" Callback={handleCancel} />
            <Button
              type="Next"
              Content={formMode === "Add" ? "Add" : "Update"}
              Callback={handleNext}
            />
          </>
        )}
      </div>
    </div>
  );
};

export default AddManpowerForm;
