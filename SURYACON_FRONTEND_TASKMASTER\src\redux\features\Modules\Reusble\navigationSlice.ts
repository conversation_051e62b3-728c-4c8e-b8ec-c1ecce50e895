import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { NavigationInterface } from "../../../Interfaces/Modules/Reuseable/Reuseable";

const loadFromLocalStorage = (): Array<{ route: string; title: string }> => {
  const storedState = localStorage.getItem("navigateArray");
  return storedState ? JSON.parse(storedState) : [];
};

const initialState: NavigationInterface = {
  navigateArray: loadFromLocalStorage(),
  title: "Tools",
  removeNavigationKey: false,
};

const navigationSlice = createSlice({
  name: "navigation",
  initialState,
  reducers: {
    setNavigate(
      state,
      action: PayloadAction<{
        route: string;
        title: string;
        isSubtask?: boolean;
      }>
    ) {
      const { title } = action.payload;
      const navIndex = state.navigateArray.findIndex(
        (item) => item.title === title
      );

      // If title already exists in the navigation array, do nothing
      if (navIndex !== -1) return;

      // Remove any existing '/subtask' route if present
      if (state.navigateArray.some((item) => item.route.includes("/subtask"))) {
        state.navigateArray.pop();
      }

      state.navigateArray.push(action.payload);

      // updating localStorage when navigation array has changed
      const updatedNavigateArray = state.navigateArray;
      localStorage.setItem(
        "navigateArray",
        JSON.stringify(updatedNavigateArray)
      );
    },

    //to go back to the layout on which we clicked
    resetNavigate(state, action: PayloadAction<{ title: string }>) {
      const navIndex = state.navigateArray.findIndex(
        (item) => item.title === action.payload.title
      );
      if (navIndex !== -1) {
        state.navigateArray = state.navigateArray.slice(0, navIndex + 1);
      }

      localStorage.setItem(
        "navigateArray",
        JSON.stringify(state.navigateArray)
      );
    },

    //this is only for when we move from subtask to task from the breadcrumb in taskmaster
    removeNavigate(state) {
      state.navigateArray.pop();

      localStorage.setItem(
        "navigateArray",
        JSON.stringify(state.navigateArray)
      );
    },
    spliceNavigate(state) {
      state.navigateArray.splice(1);

      localStorage.setItem(
        "navigateArray",
        JSON.stringify(state.navigateArray)
      );
    },

    //this to completely empty the navigation array
    emptyNavigate(state) {
      state.navigateArray = [];
      localStorage.setItem(
        "navigateArray",
        JSON.stringify(state.navigateArray)
      );
    },

    //this is to initialize the navigation array ( like whenever parent changes)
    initializeBreadcrumb(
      state,
      action: PayloadAction<{ title: string; route: string }>
    ) {
      state.navigateArray = [action.payload];
      localStorage.setItem(
        "navigateArray",
        JSON.stringify(state.navigateArray)
      );
    },

    //this is for when we update the task it should also be updated inside the breadcrumb
    resetTaskName(state, action: PayloadAction<{ title: string }>) {
      state.title = action.payload.title;

      localStorage.setItem(
        "navigateArray",
        JSON.stringify(state.navigateArray)
      );
    },
    resetTaskHeader(state, action: PayloadAction<{ title: string }>) {
      // state..title = action.payload.title
      console.log(action.payload, "action");
      state.navigateArray[state.navigateArray.length - 1].title =
        action.payload.title;
    },
    backToPreviousTask(state) {
      if (state.navigateArray.length > 1) {
        state.navigateArray.pop();
        localStorage.setItem(
          "navigateArray",
          JSON.stringify(state.navigateArray)
        );
      }
    },

    setRemoveNavigationKey(state, action: PayloadAction<boolean>) {
      state.removeNavigationKey = action.payload;
    },
  },
});

export const {
  setNavigate,
  resetNavigate,
  removeNavigate,
  emptyNavigate,
  resetTaskName,
  resetTaskHeader,
  initializeBreadcrumb,
  backToPreviousTask,
  spliceNavigate,
  setRemoveNavigationKey
} = navigationSlice.actions;
export default navigationSlice.reducer;
