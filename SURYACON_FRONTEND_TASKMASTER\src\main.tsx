import React, { StrictMode, useEffect, useState } from "react";
import { createRoot } from "react-dom/client";
import { HashRouter } from "react-router-dom";
import App from "./App";
import "./index.css";
import { Provider } from "react-redux";
import { store } from "./redux/store";
import { SocketProvider } from "./SocketProvider";
import { AuthProvider } from "./AuthProvider";
import styled from "styled-components";

// 1. ZoomWrapper styled-component
const ZoomWrapper = styled.div<{ scale: number }>`
  transform: scale(${(props) => props.scale});
  transform-origin: top left;
  width: ${(props) => 100 / props.scale}vw;
  height: ${(props) => 100 / props.scale}vh;
  overflow: hidden;
  background-color: #fff;
`;

// 2. Scale logic based on screen width
const getScale = (width: number): number => {
  if (width < 600) return 0.7;
  if (width < 1800) return 0.85;
  return 1;
};

// 3. ScaledApp component that wraps your App with zoom logic
const ScaledApp = () => {
  const [scale, setScale] = useState(() => {
    const initial = getScale(window.innerWidth);
    console.log("Initial scale:", initial);
    return initial;
  });

  useEffect(() => {
    const handleResize = () => {
      const newScale = getScale(window.innerWidth);
      console.log("Resized, new scale:", newScale);
      setScale(newScale);
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  console.log("Rendering ScaledApp with scale:", scale);

  return (
    <ZoomWrapper scale={scale}>
      <App />
    </ZoomWrapper>
  );
};

// 4. Render everything
createRoot(document.getElementById("root")!).render(
  <Provider store={store}>
    <StrictMode>
      <HashRouter>
        <AuthProvider>
          <SocketProvider>
          <App/>
          </SocketProvider>
        </AuthProvider>
      </HashRouter>
    </StrictMode>
  </Provider>
);
