import React, { useEffect, useRef, useState } from "react";
import styles from "../../Styles/SubtaskCreationForm.module.css";
import { useDispatch, useSelector } from "react-redux";
import {
  changeActiveState,
  setAllSubRoutes,
  setIsOnlyResponse,
  setTriggerFormData,
  setTriggerMode,
} from "../../../../../../redux/features/Modules/TaskMaster/Slices/TriggerEventSlice";
import {
  closePopup,
  openPopup,
} from "../../../../../../redux/features/Modules/Reusble/popupSlice";
import { SuryconLogo } from "../../../../../../assets/icons";
import TriggerTip from "../../../../../../components/Reusble/TaskMaster/TriggerTip";
import { RootState } from "../../../../../../redux/store";

import { setInputValue } from "../../../../../../redux/features/Modules/Reusble/floatinglabelslice";
import { useGetSubtaskForTriggerEventQuery } from "../../../../../../redux/api/Modules/TaskMaster/TaskMasterapi";
import { DeletePopup } from "../../../../../../components/Reusble/Global/DeletePopup";
import {
  setChangeAPiFlag,
  updateSubtaskData,
} from "../../../../../../redux/features/Modules/TaskMaster/Slices/TaskMasterSlice";
import { saveSyncData } from "../../../../../../Backup/BackupFunctions/BackupFunctions";
import { useToast } from "../../../../../../hooks/ToastHook";

const SubTaskCreationAutomation: React.FC<{ isEdit: boolean }> = ({
  isEdit,
}) => {
  const [triggerData, setTriggerData] = useState<any>({});
  const [deleteItem, setDeleteItem] = useState<any>({});
  const { popups } = useSelector((state: RootState) => state.popup);
  //this is for the trigger component
  const subTaskData = useSelector(
    (state: RootState) => state.taskMaster.currentSubtaskData
  );

  const triggerApiData = useSelector(
    (state: RootState) => state.taskMaster.currentSubtaskData?.AutoId
  );

  const taskData = useSelector(
    (state: RootState) => state.taskForm.currentSubtaskData
  );

  const {
    data: triggerSubtasks,
    refetch: refetchTriggerSubtasks,
  } = useGetSubtaskForTriggerEventQuery({ TaskId: taskData?._id });

  useEffect(() => {
    refetchTriggerSubtasks();
    dispatch(setAllSubRoutes(triggerSubtasks));
  }, [triggerSubtasks, taskData?._id, subTaskData?._id]);

  useEffect(() => {
    const initialTriggerData = {
      Trigger: [subTaskData?.AutoId?.TriggerResponse],
      Action: [subTaskData?.AutoId?.TriggerAction?.ActionName],
      ResponseTime: [
        subTaskData?.AutoId?.ResponseTime
          ? {
              id: 1,
              name: String(subTaskData?.AutoId?.ResponseTime),
            }
          : null,
      ],
    };
    setTriggerData(initialTriggerData);
  }, [subTaskData]);

  const dispatch = useDispatch();

  const handleToggleDropdown = () => {
    dispatch(openPopup("TEForm"));
  };

  const IsActive = useSelector(
    (state: RootState) => state.triggerEvent.IsActive
  );

  console.log("isActive", IsActive);

  const categoryConfig = {
    Trigger: {
      label: "Trigger Response",
      title: "Trigger",
      isActive: true,
    },
    Action: {
      label: "Action",
      title: "Action",
      isActive: IsActive,
    },
    ResponseTime: {
      label: "Response Time",
      title: "Response Time",
      isActive: IsActive,
    },
  } as const;

  console.log("isActive 2", categoryConfig);

  const showToast = useToast();

  return (
    <>
      <div className={styles.subtask_creation_automation_container}>

        <div className={`${styles.subtask_creation_designation_header}`}>
          <SuryconLogo />
          <h3>Trigger Event</h3>
        </div>
        <div className={styles.subtask_creation_master_row}>
          {(
            Object.keys(categoryConfig) as Array<keyof typeof categoryConfig>
          ).map((category) => (
            <div
              key={category}
              style={{
                minWidth: "50px",
                position: "relative",
              }}
            >
              {
                <>
                  <TriggerTip
                    label={categoryConfig[category].label}
                    handleDelete={() => {
                      setDeleteItem({
                        triggerResponse:
                          subTaskData?.AutoId?.TriggerResponse?.name,
                        triggerAction:
                          subTaskData?.AutoId?.TriggerAction?.ActionName?.name,
                        responseTime: subTaskData?.AutoId?.ResponseTime,
                        timeInterval:
                          subTaskData?.AutoId?.TriggerAction?.ActionTime,
                      });
                      dispatch(openPopup("DeleteTriggerEvent"));
                    }}
                    isEdit={isEdit}
                    // className="width_100"
                    // className2="width_100"
                    onClick={() => {
                      if (category === "Action") {
                        if (!triggerData["Trigger"][0]) {
                          showToast({
                               messageContent:  "Kindly fill Trigger Response first!",
                              type: "warning",
                          });
                      
                          return;
                        }
                      }
                      if (category === "ResponseTime") {
                        if (
                          !triggerData["Trigger"][0] &&
                          !triggerData["Action"][0]
                        ) {
                          showToast({
                            messageContent: "Kindly fill Trigger Response first!",
                            type: "warning",
                          });
                        
                          return;
                        }
                        dispatch(
                          setTriggerFormData({
                            TriggerAction: {
                              ActionName: triggerData["Action"][0],
                              ActionTime: Number(
                                triggerApiData?.TriggerAction?.ActionTime
                              ),
                            },
                            TriggerResponse: triggerData["Trigger"][0],
                            ResponseTime: Number(triggerApiData?.ResponseTime),
                          })
                        );
                        dispatch(
                          setInputValue({
                            trigger_event_add_start_after_time: String(
                              triggerApiData?.TriggerAction?.ActionTime
                            ),
                          })
                        );
                        dispatch(
                          setInputValue({
                            trigger_event_add_response_time: String(
                              triggerApiData?.ResponseTime
                            ),
                          })
                        );
                        dispatch(setIsOnlyResponse(true));
                      }
                      handleToggleDropdown();
                      dispatch(setTriggerMode("add"));
                    }}
                    data={triggerData[category] ?? []}
                    activeTip={category}
                    isActive={categoryConfig[category].isActive}
                  />
                </>
              }
            </div>
          ))}
        </div>
      </div>
      {popups["DeleteTriggerEvent"] && (
        <DeletePopup
         height="calc(100% - 8.5rem)"
          heightupperlimit="0rem"
          header="Are you sure you want to delete this Trigger Response?"
          callbackDelete={async () => {
            dispatch(
              updateSubtaskData({
                ...subTaskData,
                AutoId: {
                  TriggerAction: {
                    ActionName: null,
                    ActionTime: "",
                  },
                  TriggerResponse: null,
                  ResponseTime: "",
                },
              })
            );
            saveSyncData(
              {
                ...subTaskData,
                AutoId: {
                  TriggerAction: {
                    ActionName: null,
                    ActionTime: "",
                  },
                  TriggerResponse: null,
                  ResponseTime: "",
                },
              },
              "time",
              "SubTaskForm"
            );
            dispatch(setChangeAPiFlag(true));
            dispatch(changeActiveState({ active: true }));
            showToast({              messageContent: "Trigger Event Deleted Successfully!",
              type: "danger",
            });
            dispatch(closePopup("DeleteTriggerEvent"));
          }}
          onClose={() => {
            dispatch(closePopup("DeleteTriggerEvent"));
          }}
        >
          <>
            <div className={styles.flexContainer}>
              {deleteItem?.triggerResponse && (
                <div className={styles.summaryDivData}>
                  <div className={styles.summaryDataContent}>
                    <p
                      style={{ color: "var(--text-black-60)" }}
                      className="p_tag_14px"
                    >
                      Trigger Response
                    </p>
                    <h4
                      style={{
                        color: "var(--text-black-87)",
                        marginTop: "0.3rem",
                      }}
                    >
                      {deleteItem?.triggerResponse}
                    </h4>
                  </div>
                </div>
              )}
            </div>
            {deleteItem?.triggerAction && (
              <div className={styles.summaryDivData}>
                <div className={styles.summaryDataContent}>
                  <p
                    style={{ color: "var(--text-black-60)" }}
                    className="p_tag_14px"
                  >
                    Action
                  </p>
                  <h4
                    style={{
                      color: "var(--text-black-87)",
                      marginTop: "0.3rem",
                    }}
                  >
                    {deleteItem?.triggerAction}
                  </h4>
                </div>
              </div>
            )}
            <div className={styles.flexContainer}>
              {deleteItem?.timeInterval !== 0 &&
                deleteItem?.triggerResponse !== "This is a First Subtask" && (
                  <div className={styles.summaryDivData}>
                    <div className={styles.summaryDataContent_weightage}>
                      <div>
                        <p
                          style={{ color: "var(--text-black-60)" }}
                          className="p_tag_14px"
                        >
                          Hours
                        </p>
                        <h4
                          style={{
                            color: "var(--text-black-87)",
                            marginTop: "0.3rem",
                          }}
                        >
                          {deleteItem?.timeInterval}
                        </h4>
                      </div>
                    </div>
                  </div>
                )}
              {deleteItem?.responseTime !== 0 &&
                deleteItem?.triggerResponse !== "This is a First Subtask" && (
                  <div className={styles.summaryDivData}>
                    <div className={styles.summaryDataContent}>
                      <p
                        style={{ color: "var(--text-black-60)" }}
                        className="p_tag_14px"
                      >
                        Response Time
                      </p>
                      <h4
                        style={{
                          color: "var(--text-black-87)",
                          marginTop: "0.3rem",
                        }}
                      >
                        {deleteItem?.responseTime} Hours
                      </h4>
                    </div>
                  </div>
                )}
            </div>
          </>
        </DeletePopup>
      )}
    </>
  );
};

export default SubTaskCreationAutomation;
