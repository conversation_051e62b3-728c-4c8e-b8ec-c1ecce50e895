import React, { useState, useEffect } from "react";
import styles from "../Styles/ToggleTowerSwitchThree.module.css";

interface ThreeToggleTowerSwichProps {
  leftLabel: React.ReactNode;
  rightLabel: React.ReactNode;
  centerLabel?: React.ReactNode; // New optional center label
  onToggle?: (position: "left" | "center" | "right") => void; // Updated to support three positions
  width?: string;
  id: string;
  leftBubbleValue?: string;
  centerBubbleValue?: string; // New optional center bubble value
  rightBubbleValue?: string;
  bubbleTextTagName?: keyof JSX.IntrinsicElements;
  bubbleTextClassName?: string;
  targetBubbleUncheckedClassName?: string;
  defaultSelectedPosition?: "left" | "center" | "right"; // New prop for default selected position
}

export const ThreeToggleTowerSwitch: React.FC<ThreeToggleTowerSwichProps> = ({
  leftLabel,
  centerLabel,
  rightLabel,
  onToggle,
  width,
  id,
  bubbleTextTagName = "p",
  leftBubbleValue,
  centerBubbleValue,
  rightBubbleValue,
  bubbleTextClassName,
  targetBubbleUncheckedClassName = styles.targetBubbleUnchecked,
  defaultSelectedPosition = "left", // Default to "left" if not provided
}) => {
  const [selectedPosition, setSelectedPosition] = useState<
    "left" | "center" | "right"
  >(defaultSelectedPosition);

  useEffect(() => {
    setSelectedPosition(defaultSelectedPosition); // Update state if prop changes
  }, [defaultSelectedPosition]);

  const handleToggle = (position: "left" | "center" | "right") => {
    setSelectedPosition(position);
    onToggle?.(position);
  };

  const BubbleText = bubbleTextTagName;

  const getContainerClassName = () => {
    const baseClass = styles.toggleContainer;
    if (!centerLabel) return baseClass;
    return `${baseClass} ${styles.threeColumns}`;
  };

  const getSliderClassName = () => {
    const baseClass = styles.slider;
    if (selectedPosition === "center" && centerLabel) {
      return `${baseClass} ${styles.sliderCenter}`;
    } else if (selectedPosition === "right") {
      return `${baseClass} ${styles.sliderRight}`;
    }
    return baseClass;
  };

  return (
    <div className={styles.toggle_main}>
      <div className={getContainerClassName()} style={{ width }}>
        <div className={getSliderClassName()} />

        <div
          className={`${styles.toggle_label} ${
            selectedPosition === "left" ? styles.selected : ""
          }`}
          onClick={() => handleToggle("left")}
          role="button"
          tabIndex={0}
        >
          {leftLabel}
          {leftBubbleValue && (
            <div
              className={
                selectedPosition === "left"
                  ? styles.targetBubblechecked
                  : targetBubbleUncheckedClassName
              }
            >
              <BubbleText className={bubbleTextClassName}>
                {leftBubbleValue}
              </BubbleText>
            </div>
          )}
        </div>

        {centerLabel && (
          <div
            className={`${styles.toggle_label} ${
              selectedPosition === "center" ? styles.selected : ""
            }`}
            onClick={() => handleToggle("center")}
            role="button"
            tabIndex={0}
          >
            {centerLabel}
            {centerBubbleValue && (
              <div
                className={
                  selectedPosition === "center"
                    ? styles.targetBubblechecked
                    : targetBubbleUncheckedClassName
                }
              >
                <BubbleText className={bubbleTextClassName}>
                  {centerBubbleValue}
                </BubbleText>
              </div>
            )}
          </div>
        )}

        {/* <div
          className={`${styles.toggle_label} ${
            selectedPosition === "right" ? styles.selected : ""
          }`}
          onClick={() => handleToggle("right")}
          role="button"
          tabIndex={0}
        >
          {rightLabel}
          {rightBubbleValue && (
            <div
              className={
                selectedPosition === "right"
                  ? styles.targetBubblechecked
                  : targetBubbleUncheckedClassName
              }
            >
              <BubbleText className={bubbleTextClassName}>
                {rightBubbleValue}
              </BubbleText>
            </div>
          )}
        </div> */}
        
      </div>
    </div>
  );
};
