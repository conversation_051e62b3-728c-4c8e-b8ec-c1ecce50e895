// ---------------------------------- Author: <PERSON><PERSON> --------------------------------
import { useEffect, useState } from "react";
import ToastMessage from "./SubComponents/ToastMessage";
import Styles from "./Styles/ToastContainer.module.css";
import { ToastContainerProps, ToastMessageItemProps } from "../CommonInterface/CommonInterface";



const ToastContainer: React.FC<ToastContainerProps> = ({
  ToastMessageItems,
}) => {
  const [viewAbleQueue, setViewAbleQueue] = useState<ToastMessageItemProps[]>(
    []
  );
  const [currentIndex, setCurrentIndex] = useState<number>(0);

  useEffect(() => {
    if (currentIndex < ToastMessageItems.length) {
      setViewAbleQueue((prevQueue) => [
        ...prevQueue,
        ToastMessageItems[currentIndex],
      ]);

      const timer = setTimeout(() => {
        setCurrentIndex((prevIndex) => prevIndex + 1);
      }, 2000);

      return () => clearTimeout(timer);
    }
  }, [currentIndex, ToastMessageItems]);

  return (
    <div style={{ position: "relative" }}>
      <div className={`${Styles.toast_container}`}>
        {viewAbleQueue.map((toast, index) => (
          <ToastMessage
            key={index}
            message={toast.message}
            heading={toast.heading}
            icon={toast.icon}
            type={toast.type}
          />
        ))}
      </div>
    </div>
  );
};

export default ToastContainer;
