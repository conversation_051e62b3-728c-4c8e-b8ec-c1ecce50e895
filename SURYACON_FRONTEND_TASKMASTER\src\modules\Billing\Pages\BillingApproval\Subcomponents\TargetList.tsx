// Author: <PERSON><PERSON>,<PERSON><PERSON>
import React, { useEffect, useState, useTransition } from "react";
import styles from "../styles/BillingApproval.module.css";
import { Rupee } from "../../../../../assets/icons";
import { ToggleTowerSwitch } from "./ToggleTowerSwitch";
import TargetListCard from "./TargetListCard";

import { useTowerData } from "../../../../../redux/api/Modules/Billing/DemoMonthlyTargetApi/useTowerData";

interface TargetListProps {
  selectedTower: string | null;
  setSelectedTower: (tower: string | null) => void;
  isTowerSelected: boolean;
  setIsTowerSelected: (isTower: boolean) => void;
  projectId: string; // Add projectId to props
}

const TargetList: React.FC<TargetListProps> = ({
  selectedTower,
  setSelectedTower,
  isTowerSelected,
  setIsTowerSelected,
  projectId // This prop should be passed from the parent component
}) => {
  // Use the projectId in the useTowerData hook
  const { data, loading, error } = useTowerData(projectId);
  const [, startTransition] = useTransition();
  const [displayBudget, setDisplayBudget] = useState(0);
  
  // Calculate total counts and budget
  const towerCount = data ? Object.keys(data.Towers).length : 0;
  const nonTowerCount = data ? Object.keys(data.NonTowers).length : 0;
  const totalCount = towerCount + nonTowerCount;
  
  // Get all towers or non-towers based on selection
  const getStructures = () => {
    if (!data) return [];
    
    const structures = isTowerSelected ? data.Towers : data.NonTowers;
    return Object.entries(structures).map(([name, details]) => ({
      name,
      budget: details.Details.Budget,
      floors: details.Details.Floors,
      tasks: details.FloorDetails.reduce((acc, floor) => acc + floor.Tasks.length, 0)
    }));
  };
  
  // Update display budget whenever relevant state changes
  useEffect(() => {
    if (!data) return;
    
    try {
      if (selectedTower) {
        const structures = isTowerSelected ? data.Towers : data.NonTowers;
        if (structures[selectedTower]) {
          setDisplayBudget(structures[selectedTower].Details.Budget || 0);
        }
      } else {
        const structures = isTowerSelected ? data.Towers : data.NonTowers;
        if (Object.keys(structures).length > 0) {
          const firstStructure = Object.values(structures)[0];
          setDisplayBudget(firstStructure ? firstStructure.Details.Budget : 0);
        } else {
          setDisplayBudget(0);
        }
      }
    } catch (error) {
      console.log("Error calculating budget:", error);
    }
  }, [data, selectedTower, isTowerSelected]);
  
  // Set default selected tower on initial load or when switching between tower/non-tower
  useEffect(() => {
    if (data) {
      startTransition(() => {
        const structures = isTowerSelected ? data.Towers : data.NonTowers;
        const structureNames = Object.keys(structures);
        
        if (structureNames.length > 0) {
          // Only update if the current selection doesn't exist in the new structure type
          if (!selectedTower || !structureNames.includes(selectedTower)) {
            setSelectedTower(structureNames[0]);
          }
        } else {
          setSelectedTower(null);
        }
      });
    }
  }, [data, isTowerSelected, setSelectedTower, startTransition]);

  const handleOngoingCompletedToggle = () => {
    startTransition(() => {
      setIsTowerSelected(!isTowerSelected);
    });
  };
  
  const structures = getStructures();
  
  if (loading) {
    return <div className={styles.targetList_main}>Loading tower data...</div>;
  }
  
  if (error) {
    return <div className={styles.targetList_main}>Error loading tower data</div>;
  }

  if (!selectedTower) {
    return <div className={styles.targetList_main}>Loading...</div>;
  }

  return (
    <div className={`${styles.targetList_main}`}>
      <div className={`${styles.targetList}`}>
        <div className={`${styles.targetList_top}`}>
          <div className={`${styles.targetListHeader}`}>
            <div className={`${styles.targetListHeading}`}>
              <div>
                <h4 className={`${styles.targetListHeadingText}`}>Target List</h4>
              </div>
              <div className={`${styles.targetListBubble}`}>
                <p className={``}>{totalCount}</p>
              </div>
            </div>
            <div className={`${styles.targetListtextBubble}`}>
              <Rupee color="#FFFFFF" width={12} height={12} />
              <p className={`small_text_p`}>{displayBudget.toLocaleString()}</p>
            </div>
          </div>
          <div>
            <ToggleTowerSwitch
              leftLabel={<p className="">Tower</p>}
              leftbubbleValue={towerCount.toString()}
              bubbletextClassName="small_text_p"
              bubbleTextTagName="p"
              rightbubbleValue={nonTowerCount.toString()}
              toggleClassName="target_list_padding_helper"
              rightLabel={<p className="">Non Tower</p>}
              onToggle={handleOngoingCompletedToggle}
              width="20rem"
              id="toggle-ongoing-completed1"
            />
          </div>
        </div>

        <div className={`${styles.targetListCards}`}>
          {structures.map((structure, index) => (
            <TargetListCard 
              key={index}
              name={structure.name}
              budget={structure.budget}
              floors={structure.floors}
              tasks={structure.tasks}
              isSelected={selectedTower === structure.name}
              onClick={() => setSelectedTower(structure.name)}
            />
          ))}
          
          {structures.length === 0 && (
            <div className={styles.noData}>
              <p>No {isTowerSelected ? 'towers' : 'non-towers'} available</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default TargetList;
