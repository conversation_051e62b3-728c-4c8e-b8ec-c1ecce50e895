import React from "react";
import styles from "./Styles/MenuItem.module.css";
interface MenuitemProps {
  SvgElement?: React.ElementType;
  label: String;
  isSelected: boolean;
  onClick: () => void;
  itemKey: any;
}
const Menuitem: React.FC<MenuitemProps> = ({
  SvgElement,
  label,
  isSelected,
  onClick,
  itemKey,
}) => {
  return (
    <>
      <div
        key={itemKey}
        onClick={onClick}
        className={`${styles.menu_item_outer_container}`}
      >
        {/* <SvgElement
          color={isSelected ? "var(--primary_color)" : "var(--text-black-87)"}
        /> */}
        <h3>{label && label}</h3>
      </div>
    </>
  );
};

export default Menuitem;
