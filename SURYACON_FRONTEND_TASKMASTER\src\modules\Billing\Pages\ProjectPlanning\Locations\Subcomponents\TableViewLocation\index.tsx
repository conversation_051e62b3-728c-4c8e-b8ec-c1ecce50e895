import { useState, useEffect } from "react";

import styles from "./Styles/TableViewLocation.module.css";
import SearchBar from "../../../../../../../components/Reusble/Global/SearchBar";
import { useSelector } from "react-redux";
import { TowerLocationInterface } from "../../../../../../../components/Reusble/Billing/TowerDetailCard";
import { RootState } from "../../../../../../../redux/store";

const TableViewLocations: React.FC = () => {
  const allTowerLocations = useSelector(
    (state: RootState) => state?.projectLocalDb?.towerLocations
  ) as TowerLocationInterface[];
  const [search, setSearch] = useState("");
  const [debouncedSearch, setDebouncedSearch] = useState(search);

  // useEffect(() => {
  //   const timer = setTimeout(() => {
  //     setDebouncedSearch(search);
  //   }, 500);

  //   return () => clearTimeout(timer);
  // }, [search]);

  return (
    <div className={styles.tableViewProject_outerdiv}>
      <div className={styles.tableViewProject_tablecontainer}>
        <table className={styles.tableViewProject_projectTable}>
          <thead>
            <tr>
              <th>
                <input type="checkbox" />
              </th>
              <th>
                <SearchBar
                  className="tableview_searchbar"
                  placeholder="Search"
                  onChange={(e) => setSearch(e.target.value)}
                  placeholderClassName="tableview_search_placeholder"
                />
              </th>
              <th>
                <h4 className={styles.table_headings}>Structure Type</h4>
              </th>
              <th>
                <h4 className={styles.table_headings}>Area</h4>
              </th>
              <th>
                <h4 className={styles.table_headings}>Floors</h4>
              </th>
              <th>
                <h4 className={styles.table_headings}>Conventional</h4>
              </th>
              <th>
                <h4 className={styles.table_headings}>Basements</h4>
              </th>
              <th>
                <h4 className={styles.table_headings}>Milan</h4>
              </th>
              <th>
                <h4 className={styles.table_headings}>Duration</h4>
              </th>
              <th>
                <h4 className={styles.table_headings}>Progress</h4>
              </th>
            </tr>
          </thead>
          <tbody className={styles.tableview_location_head}>
            {allTowerLocations.map((item, index) => (
              <tr key={index}>
                <td>
                  <input type="checkbox" />
                </td>
                <td>
                  <div className={styles.projectdiv}>
                    <div className={styles.projectName}>
                      <p>{item.name}</p>
                    </div>
                  </div>
                </td>
                <td>
                  <div className={styles.projectArea}>
                    <p>{item.clientName}</p>
                  </div>
                </td>
                <td>
                  <div className={styles.projectArea}>
                    <p>{item.startDate}</p>
                  </div>
                </td>
                <td>
                  <div className={styles.estimatedCost}>
                    <p>{item.estimatedCost}</p>
                  </div>
                </td>
                <td>
                  <div className={styles.estimatedCost}>
                    <p className={styles.projectcompeleted}>
                      {item.projectCompleted}
                    </p>
                  </div>
                </td>
                <td>
                  <div className={styles.projectArea}>
                    <p>{item.area}</p>
                  </div>
                </td>
                <td>
                  <div className={styles.rates}>
                    <p>{item.rate}</p>
                  </div>
                </td>
                <td>
                  <div className={styles.projectArea}>
                    <p>{item.duration}</p>
                  </div>
                </td>
                <td>
                  <div className={styles.projectArea}>
                    <p>{item.progress}</p>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default TableViewLocations;
