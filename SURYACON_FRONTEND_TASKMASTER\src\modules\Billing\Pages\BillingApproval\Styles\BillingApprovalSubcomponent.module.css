/*👨‍🎓Author: <PERSON><PERSON><PERSON><PERSON>*/

/* 🍁 Monthly target approval card css from here */
.mt_task_approval_card {
  background: #f0f6f61a;
  box-shadow: 0px 0px 4px 0px #00000066;
  width: 18.625rem;
  border-radius: 0.75rem;
  margin: 2.4rem;
  overflow: hidden;
  position: fixed;
  right: 1%;
  top: 30.5%;
  background-color: var(--main_background);
  z-index: 20;
}

@media (max-width: 1400px) {
  .mt_task_approval_card {
    position: static;
    margin: 1rem auto;
  }
}

.mt_task_approval_card_top {
  display: flex;
  justify-content: space-between;
  padding: 0.5rem;
  background-color: var(--primary_background);
}

.mt_task_approval_card_top_left {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.mt_task_approval_card_top_left_text {
  color: #005968;
}
.mt_task_approval_card_top_left_text2 {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 1.8rem;
  height: 1.8rem;
  max-width: 2.2rem;
  max-height: 2.2rem;
  color: var(--text-white-100);
  border-radius: 50%;
  background-color: var(--primary_color);
}

.mt_task_approval_card_top_right {
  display: flex;
  align-items: center;
}

.expand_button {
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
  color: rgba(0, 89, 104, 1);
  transition: opacity 0.3s ease;
}

.expand_button:hover {
  opacity: 0.8;
}

.expand_icon_wrapper {
  width: 40px;
  height: 40px;
  border-radius: 100px;
  border-width: 1px;
  padding: 10px;
  gap: 10px;
  background: rgba(240, 246, 246, 1);
  border: 1px solid;
  border-image-source: linear-gradient(130.72deg, rgba(237, 231, 231, 0.07) -16.06%, rgba(251, 251, 251, 0.05) 82.03%);
  backdrop-filter: blur(40px);
  box-shadow: 0px 0px 4px 0px rgba(145, 161, 161, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
}

.expanded_icon {
  transform: rotate(180deg);
  transition: transform 0.5s ease;
}

.mt_task_approval_card_bottom {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  max-height: 0;
  overflow: hidden;
  opacity: 0;
  transition: all 0.5s ease;
  padding: 0 1rem;
}

.mt_task_approval_card_bottom.expanded {
  max-height: 1000px;
  opacity: 1;
  padding: 1rem;
}

.mt_task_approval_card_bottom.collapsed {
  max-height: 0;
  padding-top: 0;
  padding-bottom: 0;
  opacity: 0;
}

.mt_task_approval_card_bottom_buttons {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.5rem;
}



@media (max-height: 900px) {
  .mt_task_approval_card {
    top: 34.5vh;
  }
}

@media (max-height: 800px) {
  .mt_task_approval_card {
    top: 38.5vh;
  }
  .mt_task_approval_card_bottom {
    height: auto;
    max-height: 33vh;
  }
}

/* 🍁 Task approval subcard css from here*/

.mt_task_approval_subcard {
  display: flex;
  column-gap: 1rem;
}

.mt_task_approval_subcard_right {
}

.mt_task_approval_subcard_left {
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  min-width: 2.75rem;
  height: 2.75rem;
  background-color: var(--primary_background);
}

.mt_task_approval_subcard_left.secondaryBg {
  background-color: var(--secondary_background);
}

.mt_task_approval_subcard_right_text1 {
  color: var(--text-black-87);
  max-width: 13rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.mt_task_approval_subcard_right_text2 {
  color: var(--text-black-28);
}

.mt_task_approval_subcard_right_text1.decline_reason_text {
  color: var(--warning_color);
}

/* 🍁 Monthly target Confirmation Dialogue Form css from here */



/*👨‍🎓Author: Abhishel Raj*/
.mt_confirmation_dialog_form_container {
  position: fixed;
  top: 7rem;
  right: 1.5rem;
  transform: translate(0%, 0%);
  background: var(--blur-background);
  padding: 1.25rem;
  backdrop-filter: blur(60px);
  -webkit-backdrop-filter: blur(150px); /* Added for Safari support */
  box-shadow: var(--extra-shadow-five);
  border-radius: 2.6rem;
  z-index: 10;
  width: 32rem;
  height: calc(100% - 8.5rem);
  animation: slideIn 0.5s ease-out;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

@keyframes slideIn {
  from {
    translate: 100% 0%;
  }

  to {
    translate: 0% 0%;
  }
}




.mt_confirmation_dialog_form_top {
  position: relative;
}

.mt_confirmation_dialog_form_top_text {
  margin: auto;
  text-align: center;
  color: var(--primary_color);
  margin-bottom: 1.5rem;
  position: relative;
  max-width: 95%;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 1.04rem;
  /* white-space: nowrap; */
}

.mt_confirmation_cross_icon {
  position: absolute;
  top: 0.25rem;
  right: 0.25rem;
  cursor: pointer;
}

.mt_confirmation_dialog_form_main {
  display: flex;
  flex-wrap: wrap;
  row-gap: 1rem;
  column-gap: 1.5rem;
  max-height: 18vh;
  overflow: auto;
}

.mt_confirmation_dialog_form_first_main {
  display: flex;
  /* flex-direction: column; */
  flex-wrap: wrap;
  row-gap: 1rem;
  /* row-gap:1rem ; */
  column-gap: 1.5rem;
  max-height: 60vh;
  overflow: auto;
}

.mt_confirmation_dialog_form_first_row {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
}

.mt_confirmation_item {
  padding: 1rem;
  border-radius: 0.75rem;
  /* width: 100%; */
  background-color: #ffffff99;
}

.mt_confirmation_item.w-100 {
  width: 100%;
}
.mt_confirmation_item.w-50 {
  width: 47.75%;
}

.mt_task_confirmation_subcard_left_text2 {
  color: var(--text-black-87);
}

.mt_confirmation_item_keyname {
  color: var(--text-black-60);
}

.mt_confirmation_item_value {
  color: var(--text-black-87);
}

.mt_confirmation_remarks_key {
  color: var(--primary_color);
}

.mt_confirmation_dialog_form_34_row {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  row-gap: 1rem;
  column-gap: 1.5rem;
}

.space_between_div {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.cal_bg {
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  width: 2.5rem;
  height: 2.5rem;
  /* background-color: var(--primary_background); */
}

.secondary_bg {
  background-color: var(--secondary_background);
}

.primary_bg {
  background-color: var(--primary_background);
}

.mt_task_confirmation_rupee {
  margin-right: 0rem;
}

.mt_confirmation_sq_ft {
  background-color: var(--primary_background);
  border-radius: 100px;
  padding: 0.2rem 0.75rem;
  color: var(--text-black-100);
}
.mt_confirmation_item_value_subtask {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}
.mt_confirmation_item_value_drawing {
  display: flex;
  gap: 1rem;
}

.mt_confirmation_item_value_drawing p {
  color: var(--primary_color);
  text-decoration: underline;
  text-underline-offset: 0.1rem;
  cursor: pointer;
}

.mt_confirmation_button_div {
  margin-top: 2rem;
  display: flex;
  column-gap: 1.5rem;
  justify-content: center;
  position: relative;
}

@media (max-width: 1536px) {
  .mt_confirmation_dialog_form_container {
    width: 32rem;
  }
  .mt_confirmation_dialog_form_first_main {
    max-height: 60vh;
  }
}
@media (max-height: 800px) {
  .mt_confirmation_dialog_form_first_main {
    max-height: 55vh;
  }
}

/* 🍁 Monthly target Decline Dialogue Form css from here */

.mt_confirmation_decline_dialog_form_container {
  margin: 4rem; /*line to remove*/
  width: 36.75rem;
  padding: 1.5rem 1rem;
  min-height: 40rem;
  position: absolute;
  top: -3rem;
  right: -3rem;
  background: var(--primary_background);
  border-radius: 2.3rem;
  backdrop-filter: blur(150px);
  box-shadow: 0px 4px 40px 0px #00000080;
  z-index: 5;
}

.mt_confirmation_decline_dialog_form_top_text {
  color: var(--warning_color);
  text-align: center;
  margin-bottom: 2rem;
}

.mt_confirmation_decline_form_main {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  justify-content: space-between;

  /* background-color: aquamarine; */
  min-height: 33rem;
}

.mt_confirmation_decline_button_div {
  display: flex;
  justify-content: center;
  gap: 1.5rem;
  height: auto;
  /* background-color: #F6E6E6; */
}

/* 🍁 Monthly target Confirmation subcard css  from here */

.mt_task_confirmation_subcard {
  padding: 1rem;
  border-radius: 0.75rem;
  /* width: 100%; */
  background-color: #ffffff99;
}

.mt_confirmation_item_keyname {
  color: var(--text-black-60);
}

.mt_confirmation_item_value {
  color: var(--text-black-87);
}

.column {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.row {
  display: flex;
  gap: 1rem;
  justify-content: space-between;
  align-items: center;
  /* width: 100%; */
}
.childRow {
  display: flex;
  gap: 1rem;
  row-gap: 0;
  align-items: center;
  flex-wrap: wrap;
}

.mt_task_confirmation_subcard_right_icon {
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  width: 2.5rem;
  height: 2.5rem;
}

.w-100 {
  width: 100%;
}

.w-50 {
  width: 47.1%;
}

@media (max-width: 1536px) {
  .w-50 {
    width: 47.4%;
  }
}
