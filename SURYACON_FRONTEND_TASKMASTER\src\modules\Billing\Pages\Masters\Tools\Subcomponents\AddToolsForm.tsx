//author jagroop singh

//In All four masters some of the functions and logics are same so comments might not be there, take reference from this component
import { useEffect, useRef, useState } from "react";
import {
  AddCategoryIcon,
  Attachment,
  AudioIcon,
  CloseIcon,
  Cross,
  CopyIcon,
  DeleteIcon,
  ImageIcon,
  PasteIcon,
  RedCross,
  ReverseArrow,
  VideoIcon,
} from "../../../../../../assets/icons";
import Button from "../../../../../../components/Reusble/Global/Button";
import RadioBtns from "../../../../../../components/Reusble/Global/RadioBtns";
import styles from "../Styles/Tools.module.css";
import FloatingLabelInput from "./../../../../../../components/Reusble/Global/FloatingLabel/index";
import Datafield from "../../../../../../components/Reusble/Billing/Masters/Datafield";
import DynamicGradeInput from "../../Subcomponents/DynamicGradeInput";
import { useAppDispatch } from "../../../../../../redux/hooks/Modules/Reduxhooks/ReduxHooks";
import {
  compressImage,
  isValidValue,
  slicedData,
} from "../../../../../../functions/functions";
import {
  resetDeletedGradeData,
  resetDeletedToolData,
  resetDeleteFormData,
  setDeletedFormData,
  setDeletedGradeData,
  setDeletedToolData,
  setFormData,
} from "../../../../../../redux/features/Modules/TaskMaster/Slices/MastersSlice";
import { useSelector } from "react-redux";
import { RootState, store } from "../../../../../../redux/store";
import UnitPopup from "../../../../../../components/Reusble/Global/UnitPopup";
import { useGetAllBrandsQuery } from "../../../../../../redux/api/Modules/TaskMaster/TaskMasterapi";
import {
  useAddToolDesignationMutation,
  useUpdateToolDesignationMutation,
} from "../../../../../../redux/api/Modules/Billing/Billingapi";
import { useParams } from "react-router-dom";
import { setBackupChange, setIsLocalChange } from "../../../../../../redux/features/Modules/Reusble/backupSlice";
import ToolsSummary from "./ToolsSummary";
import { useToast } from "../../../../../../hooks/ToastHook";

const AddToolsForm: React.FC<{
  isClosing?: boolean;
  setIsClosing?: React.Dispatch<React.SetStateAction<boolean>>; //for form closing logic
  handleClose: (targetForm: string) => void;
}> = ({ isClosing = false, handleClose, setIsClosing }) => {
  //validation error handling
  const [errors, setErrors] = useState<{
    Type: boolean;
    Name: boolean;
    Brand: string[];
    Grade: string[];
  }>({
    Type: false,
    Name: false,
    Brand: [],
    Grade: [],
  });
  const [emptyError, setEmptyError] = useState<{
    Photo: boolean;
    Name: boolean;
    Brand: boolean;
    Grade: boolean;
    Fuels: boolean;
  }>({
    Photo: false,
    Name: false,
    Brand: false,
    Grade: false,
    Fuels: false,
  });
  const contentRef = useRef<HTMLDivElement>(null);
  const [fileLoader, setFileLoader] = useState(false);
  //state which keeps the track of form data
  const formData = useSelector(
    (state: RootState) => state.masterForm.formToolsData
  );

  console.log("issempty>>: form", formData);
  //state which keeps the track of deleted form data
  const deletedFormData = useSelector(
    (state: RootState) => state.masterForm.deleteFormData
  );
  //state which keeps the track of deleted form data
  const deletedGradeData = useSelector(
    (state: RootState) => state.masterForm.deleteGradeData
  );
  const deletedToolData = useSelector(
    (state: RootState) => state.masterForm.deleteToolData
  );
  //params
  const { toolsCategoryId } = useParams();
  const testt = useParams();
  console.log('toolsCategoryId>>:', testt);

  //this state is for edit tool
  const initialFormData = useSelector(
    (state: RootState) => state.masterForm.initialFormToolsData
  );

  //mode to handle add and edit page
  const formMode = useSelector((state: RootState) => state.masterForm.formMode);

  //this is for managing type radio buttons but may change this according to needs
  const [selectedOption, setSelectedOption] = useState<string>(
    formData?.type ?? ""
  );

  //this is for managing suggestion popup
  const [isOpen, setIsOpen] = useState<{ [key: number]: boolean } | null>({});

  //this is for suggestion in brand name
  const [searchKey, setSearchKey] = useState<{ [key: number]: string } | null>(
    {}
  );

  //this is for summary page
  const [showSummary, setShowSummary] = useState(false);

  //this keeps the track of whether before discard user was on summary page or not
  const [wasTrue, setWasTrue] = useState(false);

  //this is to handle the file, this logic may get removed in near future
  const [file, setFile] = useState<{
    name: string;
    type: string;
    file: File;
  } | null>(formData?.Photo || null);

  //this is for discard screen, same in all masters
  const [discard, setDiscard] = useState(false);
  const dispatch = useAppDispatch();
  const showToast = useToast();

  //api to fetch all the brands
  const { data: allBrands, refetch } = useGetAllBrandsQuery({});

  //api to add tool
  const [addToolDesignation] = useAddToolDesignationMutation();

  //update api
  const [updateToolDesignation] = useUpdateToolDesignationMutation();

  //radio button options static as we have only two optins
  const options = [
    { value: "Consumable", label: "Consumable" },
    { value: "Returnable", label: "Returnable" },
  ];

  const [copyGrades, setCopyGrades] = useState<string[]>([]);
  const currentFileState = useRef(fileLoader);
  //copy function
  const copyGradesfun = (grades: string[]) => {
    setCopyGrades(grades);
    showToast({
      messageContent: "Grades Copied!",
      type: "success",
    });
  };

  //paste function
  const pasteGradesfun = (id: string) => {
    dispatch(
      setFormData({
        ...formData,
        Brands: formData?.Brands?.map((brand, _) => {
          if (brand?._id === id) {
            return {
              ...brand,
              Grade: copyGrades,
            };
          }
          return brand;
        }),
      })
    );

    //empty the copy grades after pasting
    setCopyGrades([]);
  };

  //this is for adding brand sections
  const addBrandSection = () => {
    if (formData?.Brands?.[formData?.Brands?.length - 1]?.brand?.name === "") {
      showToast({
        messageContent: "Please Enter Brand Name!",
        type: "warning",
      });
      return;
    }

    dispatch(
      setFormData({
        ...formData,
        Brands: Array.isArray(formData?.Brands)
          ? [
              ...formData.Brands,
              {
                _id: Date.now().toString(),
                brand: {
                  name: "",
                },
                Grade: [],
              },
            ]
          : [
              {
                _id: Date.now().toString(),
                brand: {
                  name: "",
                },
                Grade: [],
              },
            ],
      })
    );

    showToast({
      messageContent: "Brand Section Added!",
      type: "success",
    });
  };

  // Handle Input Change
  const handleInputChange = (id: string, value: string) => {
    dispatch(
      setFormData({
        ...formData,
        [id]: value,
      })
    );
  };

  // Next Button Click this navigates user to summary page and validation
  const handleNext = () => {
    if (
      !selectedOption ||
      !formData?.Name?.trim() ||
      formData?.Brands?.some(
        (brand) => !brand?.brand?.name?.trim() || !brand?.Grade?.[0]?.trim()
      ) ||
      formData?.Brands?.length === 0
    ) {
      setErrors((prevErrors) => ({
        ...prevErrors,
        Type: !selectedOption || prevErrors.Type,
        Name: !formData?.Name?.trim() || prevErrors.Name,
        Brand: formData?.Brands?.map((item) =>
          !item?.brand?.name?.trim() ? item._id : undefined
        ).filter((id): id is string => Boolean(id)), // ensures only strings
        Grade: formData?.Brands?.map((item, index) => {
          if (!item?.Grade?.[0]?.trim()) {
            return item._id;
          }
          return undefined;
        }).filter((id): id is string => Boolean(id)),
      }));
      setEmptyError((prevErrors) => ({
        ...prevErrors,
        Brand: !formData.Brands[0]?.brand?.name?.trim() ? true : false,
        Grade: !formData.Brands[0]?.Grade.length ? true : false,
      }));
      showToast({
        messageContent: "Enter Required Fields!",
        type: "warning",
      });
      return;
    }
    setShowSummary(true);
  };

  // Back Button Click
  const handleBack = () => {
    setDiscard(false);
    setShowSummary(false);
  };

  //for submission api call
  const handleSubmit = async () => {
    try {
      //formatted the data into the form acceptable by backend so that in future minimum changes are required
      const formatedData = {
        ...(formMode === "Edit" && formData?._id ? { _id: formData._id } : {}),
        type: formData?.type,
        name: formData?.Name,
        Description: formData?.Description,
        Brand: formData?.Brands?.map((item) => ({
          ...(formMode === "Add"
            ? { Brandname: item?.brand?.name }
            : item?.brand?._id && !/^\d{13}$/.test(item?.brand?._id)
            ? { BrandId: [item?.brand?._id] }
            : { name: item?.brand?.name }),
          Specs: item?.Grade,
          ...(item?.brand?._id && !/^\d{13}$/.test(item?.brand?._id)
            ? { _id: item?.brand?._id }
            : {}),
        })),
        DesignationId: formData?.Users?.map((item) => item?._id),
        ...(formMode === "Add" ? { ToolCategoryId: toolsCategoryId } : {}),
        images: formData?.Photo?.file,
      };

      if (formMode === "Edit") {
        const noChanges =
          formData?.Name === initialFormData?.Name &&
          formData?.Description === initialFormData?.Description &&
          formData?.Photo?.name === initialFormData?.Photo?.name &&
          formData?.type === initialFormData?.type &&
          JSON.stringify(formData?.Brands) ===
            JSON.stringify(initialFormData?.Brands) &&
          JSON.stringify(formData?.Users) ===
            JSON.stringify(initialFormData?.Users);
        console.log(noChanges, "this is no change");
        if (noChanges) {
          setShowSummary(false);
          showToast({
            messageContent: "There were no changes!",
            type: "warning",
          });
          return;
        }
      }

      if (formMode === "Add") {
        const response = await addToolDesignation(formatedData).unwrap();
        showToast({
          messageContent: "Tool added successfully!",
          type: "success",
        });
        dispatch(setBackupChange());
      } else {
        //update api call
        const response = await updateToolDesignation(formatedData).unwrap();
        showToast({
          messageContent: "Tool updated successfully!",
          type: "success",
        });
      }
      refetch();

      handleClose("AddToolsForm");
      dispatch(setIsLocalChange(true))
    } catch (error) {
      console.log("error to he", error);
      showToast({
        messageContent:
          (error as { data?: { message?: string } })?.data?.message ||
          "Oops! Something went wrong",
        type: "danger",
      });
    }
  };

  //function to handle cover photo change
  const handleFileChange = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    setFile(null);
    const files = event.target.files;
    if (files && files.length > 0) {
      setFileLoader(true);
      const selectedFile = await compressImage(files[0], 0.2);

      if (currentFileState.current) {
        setFile({
          name: selectedFile.name,
          type: selectedFile.name.split(".").pop()?.toLowerCase() || "",
          file: selectedFile,
        });

        const latestFormData = store.getState().masterForm.formToolsData;

        dispatch(
          setFormData({
            ...latestFormData,
            Photo: {
              name: selectedFile.name,
              type: selectedFile.name.split(".").pop()?.toLowerCase() || "",
              file: selectedFile,
            },
          })
        );
        updateClipPath("coverphoto", false, selectedFile.name);
      }
      setFileLoader(true);
    }
  };

  const areArraysDifferent = (arr1: {}[], arr2: {}[]) => {
    if (!arr1 || !arr2) return true;
    if (arr1.length !== arr2.length) return true;

    return arr1.some(
      (item, index) => JSON.stringify(item) !== JSON.stringify(arr2[index])
    );
  };

  const hasFormChanged = () => {
    if (formMode === "Add") {
      return (
        formData?.Name?.trim() ||
        formData?.Description?.trim() ||
        formData?.Photo?.name?.trim() ||
        formData?.type ||
        formData?.Brands?.[0]?.brand?.name?.trim() ||
        formData?.Brands?.[0]?.Grade?.[0]?.trim() ||
        formData?.Users?.[0]?.name?.trim()
      );
    } else {
      return (
        formData?.Name?.trim() !== initialFormData?.Name?.trim() ||
        formData?.Description?.trim() !==
          initialFormData?.Description?.trim() ||
        formData?.Photo?.name?.trim() !==
          initialFormData?.Photo?.name?.trim() ||
        formData?.type !== initialFormData?.type ||
        areArraysDifferent(formData?.Brands, initialFormData?.Brands) ||
        areArraysDifferent(formData?.Users, initialFormData?.Users)
      );
    }
  };
  console.log('haschanged??',hasFormChanged())

  const handleCancel = () => {
    const hasChanged = hasFormChanged();

    if (hasChanged) {
      setDiscard(true);
      return;
    }

    handleClose("AddToolsForm");
  };

  function updateClipPath(
    id: string,
    resetBorder: boolean,
    value?: string | number
  ) {
    const inputWrapper =
      document.getElementById(id)?.parentElement?.parentElement;

    if (!inputWrapper) return;

    const label = document.querySelector(".photo_tag");

    const input = inputWrapper;

    if (label && input) {
      if (!resetBorder || value) {
        const labelWidth = (label as HTMLElement).offsetWidth + 20;
        const inputWidth = input.offsetWidth;

        // Calculating clip-path based on label width
        const leftPercentage = (labelWidth / inputWidth) * 100;
        // input.style.clipPath = "none";
        input.style.clipPath = `polygon(0 0, ${leftPercentage}% 0, ${leftPercentage}% 2px, 0 2px, 0 100%, 100% 100%, 100% 0, 100% 0, 100% 0, 100% 2px, 100% 0)`;
      } else {
        input.style.clipPath = "none";
      }
    }
  }

  useEffect(() => {
    if (file?.name) {
      updateClipPath("coverphoto", false, file.name);
    } else {
      updateClipPath("coverphoto", true);
    }
  }, [discard, showSummary, file]);

  useEffect(() => {
    currentFileState.current = fileLoader;
  }, [fileLoader]);
  console.log("error", emptyError);

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if(isClosing) return;
    if (e.key === "Enter" && e.shiftKey) return;

    if (e.key === "Enter") {
      e.preventDefault();
      e.stopPropagation();
      if (showSummary) {
        handleSubmit();
        dispatch(resetDeleteFormData());
        dispatch(resetDeletedGradeData());
        dispatch(resetDeletedToolData());
      }
      if (!showSummary && !discard) {
        handleNext();
      }
      if (discard) {
        handleClose("AddToolsForm");
        dispatch(resetDeleteFormData());
        dispatch(resetDeletedGradeData());
        dispatch(resetDeletedToolData());
      }
    }

    if (e.key === "Escape") {
      e.preventDefault();
      e.stopPropagation();
      if (!showSummary && !discard) {
        handleCancel();
      }

      if (showSummary) {
        handleBack();
      }
      if (discard) {
        if (discard && wasTrue) {
          setDiscard(false);
          setShowSummary(true);
          setWasTrue(false);
          return;
        }
        setDiscard(false);
      }
    }
  };
  const formRef = useRef(null);

 
 

  useEffect(() => {
    if (showSummary || discard) {
      if (formRef.current) {
        formRef.current.focus();
      }
    }
  }, [showSummary, discard]);

  // const isEmpty = (value: any) => {
  //     console.log('outisde click tools inside if>>val:', value);
  //     return !Object.values(value).some((val) => {
  //       // console.log('outisde click tools inside if>>val:', val)
  //       return val !== undefined && val !== null && val !== ""
  //     }
  const [formEmpty, setFormEmpty] = useState(true);

  const isEmpty = (data): boolean => {
    if (
      data?.type === "" &&
      data?.Name === "" &&
      data?.Description === "" &&
      data?.Photo === null &&
      data?.Brands?.[0]?.brand?.name === "" &&
      data?.Brands?.[0]?.Grade.length === 0 &&
      data?.Users.length === 0
    ) {
      console.log("issempty>>: true");
      return true;
    } else {
      console.log("issempty>>: false");
      return false;
    }
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (formRef.current && !formRef.current.contains(event.target as Node)) {
        const isEmp = isEmpty(formData);
        setFormEmpty(isEmp);
        if (isEmp) {
          handleClose("AddToolsForm");
        }
        if (!hasFormChanged() && !discard) {
          handleClose("AddToolsForm");
        }
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [formData, dispatch]);


  useEffect(() => {
    requestAnimationFrame(() => {
      if (contentRef.current) {
        contentRef.current.scrollTop = 0;
      }
    });
  }, [showSummary,discard]);


  return (
    <div
      className={`${styles.addtoolsform_container} ${
        isClosing ? styles.closing : ""
      }`}
      tabIndex={0}
      onKeyDown={handleKeyDown}
      ref={formRef}
    >
      <div
        className={styles.addtoolsform_header}
        style={{ color: discard ? "var(--warning_color)" : "" }}
      
      >
        <h3 className={styles.addtoolsform_header_text}>
          {showSummary
            ? `Are you sure you want to ${
                formMode === "Add" ? "add" : "update"
              } this Tool?`
            : discard
            ? "Are you sure you want to discard these changes ?"
            : formMode === "Add"
            ? "Add Tool"
            : "Edit Tool"}
        </h3>
        <button
          onClick={() => {
            if (showSummary && !hasFormChanged()) {
              handleClose("AddToolsForm");
              return;
            }
            if (showSummary) {
              setDiscard(true);
              setWasTrue(true);
              setShowSummary(false);
              return;
            }
            if (discard && !wasTrue) {
              setDiscard(false);
              return;
            }
            if (discard && wasTrue) {
              setDiscard(false);
              setWasTrue(false);
              setShowSummary(true);
              return;
            }

            handleCancel();
          }}
          className={styles.closeButton}
        >
          <CloseIcon />
        </button>
      </div>
      <div className={styles.addtoolsform_datainputs}   ref={contentRef}>
        {/* will create component of summary page and discard page in near future */}
        {showSummary ? (
          <ToolsSummary
            formData={formData}
            initialFormData={initialFormData}
            formMode={formMode}
            deletedFormData={deletedFormData}
            deletedGradeData={deletedGradeData}
            deletedToolData={deletedToolData}
          />
        ) : discard ? (
          <ToolsSummary
            formData={formData}
            initialFormData={initialFormData}
            formMode={formMode}
            deletedFormData={deletedFormData}
            deletedGradeData={deletedGradeData}
            deletedToolData={deletedToolData}
          />
        ) : (
          <div className={styles.addtoolsform_datainputs_wrapper} >
            <h4>Type</h4>
            <RadioBtns
              options={options}
              errors={errors?.Type}
              selectedValue={selectedOption}
              onValueChange={(value) => {
                setSelectedOption(value);
                setErrors({ ...errors, Type: false });
                handleInputChange("type", value);
              }}
            />
    
            <div className={styles.addtoolsform_datainputs_flexrow} >
              <FloatingLabelInput
                label="Name"
                focusOnInput={true}
                error={errors?.Name}
                id="name"
                placeholder="Name"
                value={formData?.Name}
                props="one_line"
                onInputChange={(value: any) => {
                  handleInputChange("Name", value);
                  setErrors({ ...errors, Name: false });
                }}
                // props="one_line"
              />
              {/* photo has some design issues will tend to this in near future */}
              <div className={`${styles.photo_input_wrapper}`}>
                {
                  <label
                    className={`photo_tag `}
                    style={{
                      position: "absolute",
                      left: file ? 20 : 16,
                      color: file
                        ? "var(--text-black-87)"
                        : "var(--text-black-60)",
                      zIndex: 9999,
                      top: file ? "" : "30%",
                      borderRadius: "5px",
                      padding: "0.2rem",
                      fontSize: file ? "0.75rem" : "",
                      transform: file ? "translateY(40%)" : "translateY(40%)",
                      transition: "transform 0.3s ease-in-out",
                    }}
                  >
                    Cover Photo
                  </label>
                }
                <div
                  className={styles.cover_photo}
                  style={{
                    justifyContent: file ? "space-between" : "flex-end",
                    marginTop: "1.3rem",
                  }}
                >
                  {file && (
                    <div className={styles.tcr_fileNames_div}>
                      <div
                        style={{
                          display: "flex",
                          alignItems: "center",
                          margin: "-0.5rem",
                          paddingInline: "0.5rem",
                          position: "relative",
                          maxWidth: "10rem",
                          overflow: "hidden",
                        }}
                        className={`${styles.tcr_fileNames} small_text_p_400`}
                      >
                        {file.type === "jpg" ||
                        file.type === "jpeg" ||
                        file.type === "png" ? (
                          <ImageIcon />
                        ) : null}
                        {file.type === "mp4" ? <VideoIcon /> : null}
                        {file.type === "mp3" ? <AudioIcon /> : null}

                        <p
                          style={{
                            marginLeft: "0.5rem",
                            width: "80%",
                            whiteSpace: "nowrap",
                            textOverflow: "ellipsis",
                            overflow: "hidden",
                          }}
                          className="small_text_p_400"
                        >
                          {file.name}
                        </p>
                      </div>
                    </div>
                  )}

                  <div
                    className={
                      !file ? styles.tcrpopup_header_attachmentbtn : ""
                    }
                    style={{
                      cursor: "pointer",
                      display: "flex", 
                      justifyContent: "center",
                      alignItems: "center",
                    }}
                    onClick={
                      fileLoader
                        ? () => {
                            setFileLoader(false);
                          }
                        : () => document.getElementById("coverphoto")?.click()
                    }
                  >
                    {file ? (
                      <ReverseArrow />
                    ) : fileLoader ? (
                      <>
                        <Cross />
                      </>
                    ) : (
                      <Attachment />
                    )}
                    <input
                      id="coverphoto"
                      type="file"
                      style={{ display: "none" }}
                      accept="image/jpeg,image/jpg,image/png"
                      onChange={handleFileChange}
                    />
                  </div>
                  {fileLoader && (
                    <div className={styles.progress_bar_container}>
                      <div className={styles.progress_bar}></div>
                    </div>
                  )}
                </div>
              </div>
            </div>
            <FloatingLabelInput
              label="Description"
              id="description"
              placeholder="Description"
              props="description_prop"
              value={formData?.Description}
              onInputChange={(value: any) => {
                handleInputChange("Description", value);
              }}
            />

            <div
              className={styles.addtoolsform_brandheader}
              style={{ marginTop: "1.5rem" }}
            >
              <h4>Brand</h4>
              <div
                onClick={addBrandSection}
                className={styles.addBrandsectionCategoryIcon}
              >
                <AddCategoryIcon />
              </div>
            </div>

            {formData &&
              formData?.Brands?.map((brandItem: any, index: any) => (
                <div
                  key={index}
                  className={styles.addtoolsform_brandsection}
                  style={{
                    border:
                      errors?.Brand.find((val) => val == brandItem._id) ||
                      errors?.Grade.find((id) => id == brandItem._id) ||
                      (emptyError.Brand && !brandItem?.brand?.name) ||
                      (emptyError?.Grade && !brandItem?.Grade[0])
                        ? "1px solid var(--warning_color) !important"
                        : "",
                  }}
                >
                  <div
                    className={styles.addtoolsform_brandsectioninputs}
                    style={{ position: "relative" }}
                  >
                    <div style={{ position: "relative" }}>
                      <FloatingLabelInput
                        label="Name"
                        id={`brand-name-${brandItem?._id}`}
                        error={
                          errors?.Brand.find((val) => val == brandItem._id) ||
                          (emptyError.Brand && !brandItem?.brand?.name)
                            ? true
                            : false
                        }
                        marginTop="0rem"
                        placeholder="Name"
                        props="one_line"
                        value={
                          searchKey?.[brandItem?._id] ??
                          formData?.Brands?.find(
                            (item: any) => item?._id === brandItem?._id
                          )?.brand?.name ??
                          ""
                        }
                        onInputChange={(value: any) => {
                          setErrors((prevErrors) => ({
                            ...prevErrors,
                            Brand: prevErrors.Brand.filter(
                              (val, i) => val !== brandItem?._id
                            ),
                          }));
                          setEmptyError((prevErrors) => ({
                            ...prevErrors,
                            Brand: false,
                            Grade: false,
                          }));

                          //setting the value to the form data
                          dispatch(
                            setFormData({
                              ...formData,
                              Brands: formData.Brands?.map(
                                (brand: any, i: number) =>
                                  brand?._id === brandItem?._id
                                    ? {
                                        ...brand,
                                        brand: { name: value },
                                        Grade: [...brand?.Grade],
                                        ...(brand?.updateId
                                          ? { updateId: brand?.updateId }
                                          : {}),
                                      }
                                    : brand
                              ),
                            })
                          );

                          //if value is empty no suggestion should be given
                          if (value?.trim() == "") {
                            setIsOpen(() => ({ [brandItem?._id]: false }));
                            setSearchKey(() => ({
                              [brandItem?._id]: value,
                            }));
                            return;
                          }

                          //if value is present give suggestion
                          setIsOpen(() => ({ [brandItem?._id]: true }));
                          setSearchKey(() => ({ [brandItem?._id]: value }));

                          //logic to close the isOpen if no match is found
                          const brands =
                            allBrands.data.length > 0
                              ? allBrands.data.filter((item: any) => {
                                  const safePattern = value?.replace(
                                    /[-\/\\^$*+?.()|[\]{}]/g,
                                    "\\$&"
                                  );
                                  return safePattern
                                    ? new RegExp(safePattern, "i").test(
                                        item?.Brandname
                                      )
                                    : false;
                                })
                              : [];

                          if (brands.length === 0) {
                            setIsOpen(() => ({ [brandItem?._id]: false }));
                          }
                        }}
                      />

                      {/* this is the dropdown in which suggestion will be shown */}
                      {isOpen?.[brandItem?._id] && (
                        <div
                          className={`${styles.unit_popup_mt_container} ${
                            isOpen?.[brandItem?._id]
                              ? `${styles.selected}`
                              : "notSelected"
                          }`}
                        >
                          <UnitPopup
                            property={"unit_popup_class"}
                            alignment="absolute"
                            left="0"
                            top="57px"
                            width="100%"
                            data={(() => {
                              const filtered =
                                (searchKey
                                  ? allBrands?.data?.filter((item: any) => {
                                      const safePattern = searchKey[
                                        brandItem?._id
                                      ].replace(
                                        /[-\/\\^$*+?.()|[\]{}]/g,
                                        "\\$&"
                                      );
                                      return new RegExp(safePattern, "i").test(
                                        item?.Brandname
                                      );
                                    })
                                  : allBrands?.data) || [];

                              const mapped = filtered
                                .map((item: any) => ({
                                  id: item?._id,
                                  label: item?.Brandname,
                                }))
                                .filter((currentItem: any) => {
                                  const isInFormData = formData?.Brands?.some(
                                    (b) => b.brand._id === currentItem?.id
                                  );
                                  return !isInFormData;
                                });

                              if (mapped.length === 0) {
                                setIsOpen?.(null);
                              }

                              return mapped;
                            })()}
                            onSelect={(item) => {
                              //setting brand name with the _id
                              dispatch(
                                setFormData({
                                  ...formData,
                                  Brands: formData.Brands?.map(
                                    (brand: any, i: number) =>
                                      brand?._id === brandItem?._id
                                        ? {
                                            ...brand,
                                            brand: {
                                              _id: item?.id,
                                              name: item?.label,
                                            },
                                          }
                                        : brand
                                  ),
                                })
                              );
                              setSearchKey(null);
                              setIsOpen(null);
                            }}
                            selectedId={null} //we dont need this as of now, will see if there is any need
                          />
                        </div>
                      )}
                    </div>
                    <div
                      style={{
                        display: "flex",
                        justifyContent: "space-between",
                        alignItems: "center",
                        marginTop: "1rem",
                      }}
                    >
                      <h4>Grade / Model</h4>
                      {formData?.Brands?.length > 1 &&
                        (!formData?.Brands[formData?.Brands?.length - 1]
                          ?.Grade?.[0] &&
                        index !== formData?.Brands?.length - 1 ? (
                          <div
                            onClick={() =>
                              copyGradesfun(
                                formData?.Brands?.find(
                                  (item) => item?._id === brandItem?._id
                                )?.Grade!
                              )
                            }
                            className={styles.copy_button}
                          >
                            <span style={{ marginInlineEnd: "0.5rem" }}>
                              Copy
                            </span>{" "}
                            <CopyIcon />
                          </div>
                        ) : (
                          copyGrades?.length > 0 &&
                          !formData?.Brands[formData?.Brands?.length - 1]?.Grade
                            ?.length && (
                            <div
                              onClick={() => pasteGradesfun(brandItem?._id)}
                              className={styles.copy_button}
                            >
                              <span style={{ marginInlineEnd: "0.5rem" }}>
                                Paste
                              </span>
                              <PasteIcon />
                            </div>
                          )
                        ))}
                    </div>
                    <div style={{ paddingTop: "0.5rem" }}>
                      {/* <GradeInputbox /> */}
                      <DynamicGradeInput
                        variant="grade"
                        label="Add"
                        callbackDelete={(deleteIndex) => {
                          const deletedGradeValue = formData?.Brands?.find(
                            (item: any) =>
                              item?.updateId === brandItem?.updateId
                          )?.Grade[deleteIndex];
                          const deletedGrade =
                            formData?.Brands?.find(
                              (item: any) =>
                                item?.updateId === brandItem?.updateId
                            )?.Grade?.includes(deletedGradeValue!) &&
                            initialFormData?.Brands?.find(
                              (item: any) =>
                                item?.updateId === brandItem?.updateId
                            )?.Grade.includes(deletedGradeValue!);
                          dispatch(
                            setFormData({
                              ...formData,
                              Brands: formData?.Brands?.map(
                                (brand, brandIdx) =>
                                  brand?._id === brandItem?._id
                                    ? {
                                        ...brand,
                                        Grade: Array.isArray(brand?.Grade)
                                          ? brand.Grade.filter(
                                              (_, i) => i !== deleteIndex
                                            ) // Remove Grade at `index`
                                          : [],
                                      }
                                    : brand // Keep other brands unchanged
                              ),
                            })
                          );

                          if (formMode !== "Add" && deletedGrade) {
                            // const deletedGrade = formData?.Brands?.find(
                            //   (item: any) =>
                            //     item?.updateId === brandItem?.updateId
                            // )?.Grade[deleteIndex];
                            const existing = deletedGradeData?.find(
                              (item: any) =>
                                item[brandItem.updateId] !== undefined
                            );

                            let updatedDeleteGradeData;

                            if (existing) {
                              updatedDeleteGradeData = deletedGradeData?.map(
                                (item: any) => {
                                  if (item[brandItem._id] !== undefined) {
                                    const updatedSet = new Set([
                                      ...item[brandItem._id],
                                      deletedGradeValue,
                                    ]);
                                    return {
                                      [brandItem._id]: Array.from(updatedSet),
                                    };
                                  }
                                  return item;
                                }
                              );
                            } else {
                              updatedDeleteGradeData = [
                                ...deletedGradeData,
                                { [brandItem._id]: [deletedGradeValue] },
                              ];
                            }

                            // Dispatch with the new data
                            dispatch(
                              setDeletedGradeData(updatedDeleteGradeData)
                            );
                          }
                        }}
                        error={
                          errors?.Grade.find((id) => id == brandItem._id) ||
                          (emptyError.Grade && !brandItem?.Grade[0])
                            ? true
                            : false
                        }
                        initialData={
                          formData?.Brands?.find(
                            (item: any) => item?._id === brandItem?._id
                          )?.Grade || []
                        }
                        onGradesUpdate={(grades) => {
                          setErrors((prevErrors) => ({
                            ...prevErrors,
                            Brand: prevErrors.Brand.filter(
                              (val, i) => val !== brandItem?._id
                            ),
                            Grade: prevErrors.Grade.filter(
                              (val, i) => val !== brandItem?._id
                            ),
                          }));
                          setEmptyError((prevErrors) => ({
                            ...prevErrors,
                            Brand: false,
                            Grade: false,
                          }));
                          dispatch(
                            setFormData({
                              ...formData,
                              Brands: formData.Brands?.map(
                                (brand: any, i: any) =>
                                  brand?._id === brandItem?._id
                                    ? { ...brand, Grade: grades }
                                    : brand
                              ),
                            })
                          );
                        }}
                      />
                    </div>
                    {formData?.Brands?.length > 1 && (
                      <div
                        className={styles.delete_icon_tooltip}
                        onClick={() => {
                          // setSearchKey({ [index]: "" });
                          setSearchKey(null);

                          const isInInitial = initialFormData?.Brands?.find(
                            (item) => item?._id === brandItem?._id
                          )?.updateId;

                          if (isInInitial) {
                            dispatch(
                              setDeletedFormData([
                                ...deletedFormData,
                                initialFormData?.Brands?.find(
                                  (item) => item?._id === brandItem?._id
                                ),
                              ])
                            );
                          }

                          dispatch(
                            setFormData({
                              ...formData,
                              Brands: formData?.Brands?.filter(
                                (brand, brandIdx) =>
                                  brand?._id !== brandItem?._id
                              ),
                            })
                          );
                        }}
                      >
                        <DeleteIcon />
                      </div>
                    )}
                  </div>
                </div>
              ))}

            <div style={{ marginTop: "1.5rem" }}>
              <Datafield
                label="User"
                setIsClosing={setIsClosing}
                selectedValues={formData?.Users ?? []}
                varient="AddToolsForm"
                callbackDelete={(id) => {
                  const isInInitial =
                    !!formData?.Users?.find((user) => user?._id === id) &&
                    !!initialFormData?.Users?.find((user) => user?._id === id);

                  if (isInInitial) {
                    dispatch(
                      setDeletedToolData([
                        ...deletedToolData,
                        initialFormData?.Users?.find(
                          (user) => user?._id === id
                        ),
                      ])
                    );
                  }
                  dispatch(
                    setFormData({
                      ...formData,
                      Users: formData?.Users?.filter(
                        (user) => user?._id !== id
                      ),
                    })
                  );
                }}
                heading="Users"
              />
            </div>
          </div>
        )}
      </div>
      <div className={styles.addtoolsform_btngroup}>
        {showSummary ? (
          <>
            <Button type="Cancel" Content="Back" Callback={handleBack} />
            <Button
              type="Next"
              Content="Submit"
              Callback={() => {
                handleSubmit();
                dispatch(resetDeleteFormData());
                dispatch(resetDeletedGradeData());
                dispatch(resetDeletedToolData());
              }}
            />
          </>
        ) : discard ? (
          <>
            <Button
              type="Cancel"
              Content="No"
              Callback={() => {
                if (discard && wasTrue) {
                  setDiscard(false);
                  setShowSummary(true);
                  setWasTrue(false);
                  return;
                }
                setDiscard(false);
              }}
            />
            <Button
              type="Next"
              Content="Yes"
              Callback={() => {
                handleClose("AddToolsForm");
                dispatch(resetDeleteFormData());
                dispatch(resetDeletedGradeData());
                dispatch(resetDeletedToolData());
              }}
            />
          </>
        ) : (
          <>
            <Button type="Cancel" Content="Cancel" Callback={handleCancel} />
            <Button
              type="Next"
              Content={formMode === "Add" ? "Add" : "Update"}
              Callback={handleNext}
            />
          </>
        )}
      </div>
    </div>
  );
};

export default AddToolsForm;

