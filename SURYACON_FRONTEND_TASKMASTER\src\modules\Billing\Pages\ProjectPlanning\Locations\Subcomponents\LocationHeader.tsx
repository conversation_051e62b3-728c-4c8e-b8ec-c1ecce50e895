import React, { FC, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";

import styles from "../Subcomponents/LocationCardView/LocationHeader.module.css";

import { useNavigate, useParams } from "react-router-dom";
import { RootState } from "../../../../../../redux/store";
import {
  closePopup,
  openPopup,
  togglePopup,
} from "../../../../../../redux/features/Modules/Reusble/popupSlice";
import MainMenu from "../../../../../../components/Common/Sidebar/SubComponents/MainMenu";
import NavigationComponent from "../../../../../../components/Reusble/Global/navigationComponents/commonHeaderComponent";
import { ToggleSwitch } from "../../../../../../components/Reusble/Billing/ToggleSwitch/ToggleSwitch";
import { DashboardSvg } from "../../../../../../assets/SidebarAssets/SVGs";
import {
  AddIcon,
  DeleteIcon,
  SiteDrawingIcon,
  TableViewBilling,
  Uploadicon,
} from "../../../../../../assets/icons";
import { ThreeToggleTowerSwitch } from "../../../BillingApproval/Subcomponents/ThreeToggleTowerSwitch";
import { AddLocationForm } from "./AddLocation";
import { LocationHeaderProps } from "../../../../../../interfaces/Modules/Billing/ProjectPlanning/ProjectPlanning";
import AddCategoryType from "../../../../../../components/Reusble/Global/AddCategoryType";
import { TowerLocationData } from "./AddLocation/Interfaces/Interface";
import {
  setprojectDeleted,
  setSelectedtowerType,
  setTowerLocationDeleted,
  setTowerLocationFormData,
} from "../../../../../../redux/features/Modules/Billing/ProjectPlanning/Slices/ProjectSlice";
import { slicedData } from "../../../../../../functions/functions";
import { ToggleTowerSwitch } from "../../../BillingApproval/Subcomponents/ToggleTowerSwitch";
import { DeletePopup } from "../../../../../../components/Reusble/Global/DeletePopup";
import { setBackupChange } from "../../../../../../redux/features/Modules/Reusble/backupSlice";
import { useToast } from "../../../../../../hooks/ToastHook";
import { FetchBaseQueryError } from "@reduxjs/toolkit/query";
import { useDeleteTowerLocationMutation } from "../../../../../../redux/api/Modules/Billing/Billingapi";
import { setIsDeleted } from "../../../../../../redux/features/Modules/Reusble/deletedSlice";
import { TowerLocationInterface } from "../../../../../../components/Reusble/Billing/TowerDetailCard";
import { initializeDatabase } from "../../../../../../functions/functions";

const LocationHeader = () => {
  const [deleteTowerLocation] = useDeleteTowerLocationMutation();
  const currentOpenPopup = useSelector(
    (state: RootState) => state.popup.popups
  );
  const handleOpenAddLocationForm = () => {
    if (!navigator.onLine) {
      showToast({
        messageContent: "Oops! no internet connection!",
        type: "danger",
      });
      return;
    }
    dispatch(setTowerLocationFormData({} as any));
    dispatch(togglePopup("AddLocationForm"));
  };
  const allTowerLocations = useSelector(
    (state: RootState) => state?.projectLocalDb?.towerLocations
  ) as TowerLocationInterface[];

  const selectedTowerLocationType = useSelector(
    (state: RootState) => state.projectLocalDb?.selectedTowerType
  );

  const [towerCount, setTowerCount] = React.useState(0);
  const [nonTowerCount, setNonTowerCount] = React.useState(0);

  const dispatch = useDispatch();
  const navigate = useNavigate();
  const selectedFloorType = useSelector(
    (state: RootState) => state.projectLocalDb.selectedFloorType
  );
  // Close the popup when navigation happens (using navigate hook)
  useEffect(() => {
    // Dispatch action to close any open popup when navigating to another page
    dispatch(closePopup("AddCategoryForm"));
    dispatch(closePopup("AddTaskForm"));
  }, [navigate, dispatch]);
  const towerLocationFormdata = useSelector(
    (state: RootState) => state.projectLocalDb.towerLocationFormdata
  );
  const isProjectDeleted = useSelector(
    (state: RootState) => state.projectLocalDb.isProjectDeleted
  );
  const isTowerLocationDeleted = useSelector(
    (state: RootState) => state.projectLocalDb.isTowerLocationDeleted
  );
  const pramsss = useParams();
  const selectOpenedProject = useSelector(
    (state: RootState) => state.projectLocalDb.openedProject
  );
  console.log("these are params in locationheader", pramsss);
  console.log("valuesssssss", towerLocationFormdata);
  const showToast = useToast();
  const isbackupchange=useSelector((state:RootState) => state.backupSlice.isOpen);
  const getTowerNonTowerCounts = async (projectId: string) => {
    try {
      const dbName = await initializeDatabase("countsTN");
      // const tower = await window.electron.getDataById({
      //   dbName,
      //   id: `nonDeletedTowerCount-${projectId}`,
      // });
      // const nonTower = await window.electron.getDataById({
      //   dbName,
      //   id: `nonDeletedNonTowerCount-${projectId}`,
      // });
      const countData = await window.electron.getDataById({
        dbName,
        id: projectId,
      });
      console.log("Tower DB Result:", countData);
      setTowerCount(countData[0]?.nonDeletedTowerCount || 0);
      setNonTowerCount(countData[0]?.nonDeletedNonTowerCount || 0);
    } catch (err) {
      setTowerCount(0);
      setNonTowerCount(0);
    }
  };

  React.useEffect(() => {
    if (pramsss.projectId) {
      getTowerNonTowerCounts(pramsss.projectId);
    }
  }, [pramsss.projectId,isbackupchange]);

  return (
    <>
      <div className={styles.three_toggle_tower_swich}>
        <ThreeToggleTowerSwitch
          defaultSelectedPosition={(() => {
            switch (selectedTowerLocationType) {
              case "Tower":
                return "left";
              case "Non-Tower":
                return "center";
              case "Miscellaneous":
                return "right";
              default:
                return "left"; // Default to left if none match
            }
          })()}
          onToggle={(e) => {
            const key = (() => {
              switch (e) {
                case "left":
                  return "Tower";
                case "center":
                  return "Non-Tower";
                case "right":
                  return "Miscellaneous";
                default:
                  return "";
              }
            })();
            dispatch(setSelectedtowerType(key));
            console.log(key, "this is the value of three toggle switch");
          }}
          id="three-toggle-tower"
          leftBubbleValue={towerCount.toString()}
          bubbleTextClassName="small_text_p"
          centerBubbleValue={nonTowerCount.toString()}
          rightBubbleValue="0"
          leftLabel={<p>Tower</p>}
          centerLabel={<p>Non-Tower</p>}
          rightLabel={<p>Miscellaneous</p>}
          // width="30rem"
          width="20rem"
        />
      </div>
      <div className={styles.location_header}>
        <div className={styles.tasknav_conatiner}>
          {/* <div className={styles.location_header_left_container}>
        
          <MainMenu />
          {!isTowerLocationDeleted && !isProjectDeleted && (
            <NavigationComponent
              handleOutsideNavigation={(title: string, route: string) => {
                dispatch(setTowerLocationDeleted(false));
                dispatch(setprojectDeleted(false));
                navigate(route);
              }}
              route={[
                { route: "/billing", title: "Projects" },

                {
                  route: `/billing/location/${pramsss.projectId}`,
                  title: selectOpenedProject,
                },
              ]}
            />
          )}
          {!isProjectDeleted && isTowerLocationDeleted && (
            <NavigationComponent
              handleOutsideNavigation={(title: string, route: string) => {
                if (route == "/billing") {
                  dispatch(setTowerLocationDeleted(false));
                  dispatch(setprojectDeleted(false));
                  navigate(route);
                } else if (route == `/billing/location/${pramsss.projectId}`) {
                  dispatch(setTowerLocationDeleted(false));
                  navigate(route);
                }
              }}
              route={[
                { route: "/billing", title: "Projects" },
                {
                  route: `/billing/location/${pramsss.projectId}`,
                  title: selectOpenedProject,
                },
                { route: "deletedtowers", title: "Deleted" },
              ]}
            />
          )}
          {isProjectDeleted && !isTowerLocationDeleted && (
            <NavigationComponent
              handleOutsideNavigation={(title: string, route: string) => {
                if (route == "/billing") {
                  dispatch(setTowerLocationDeleted(false));
                  dispatch(setprojectDeleted(false));
                  navigate(route);
                } else if (route == `/billing/location/${pramsss.projectId}`) {
                  dispatch(setTowerLocationDeleted(false));
                  navigate(route);
                }
              }}
              route={[
                { route: "/billing", title: "Projects" },

                {
                  route: `/billing/location/${pramsss.projectId}`,
                  title: selectOpenedProject,
                },
              ]}
            />
          )}
          {isTowerLocationDeleted && isProjectDeleted && (
            <NavigationComponent
              handleOutsideNavigation={(title: string, route: string) => {
                if (route == "/billing") {
                  dispatch(setTowerLocationDeleted(false));
                  dispatch(setprojectDeleted(false));
                  navigate(route);
                } else if (route == "/billing/deletedprojects") {
                  navigate("/billing");
                }
              }}
              route={[
                { route: "/billing", title: "Projects" },
                { route: "/billing/deletedprojects", title: "Deleted" },
                {
                  route: `/billing/location/${pramsss.projectId}`,
                  title: selectOpenedProject,
                },
              ]}
            />
          )}

         
        </div>  */}

          <div className={styles.tasknav_rightbtns}>
            {/* {!isTowerLocationDeleted && (
            <button
              onClick={() => {
                dispatch(setTowerLocationDeleted(true));
              }}
              className={styles.taskdexportbtn}
            >
              <h4>Deleted</h4>
              <DeleteIcon />
            </button>
          )} */}

            <button className={styles.taskdexportbtn}>
              <h4>Site Drawings </h4>
              <SiteDrawingIcon />
            </button>
            <button className={styles.taskdexportbtn}>
              <h4>Export</h4>
              <Uploadicon />
            </button>
            <button
              className={styles.taskaddcategorybtn}
              onClick={handleOpenAddLocationForm}
            >
              <h4> Location</h4>
              <AddIcon />
            </button>
          </div>
        </div>
        {currentOpenPopup["AddLocationForm"] && <AddLocationForm />}
        {currentOpenPopup["DeleteLocationForm"] && (
          <DeletePopup
            header="Are you sure you want to delete this location"
            callbackDelete={async () => {
              console.log(towerLocationFormdata, "this is selectedprojectid");
              const response = await deleteTowerLocation({
                towerId: towerLocationFormdata?._id,
              });
              // const response = await upd({
              //   projectId: towerLocationFormdata?._id,
              // });
              console.log(
                response,
                "this is responseeeee for deleting towerlocation"
              );
              if (response.data.success) {
                showToast({
                  messageContent: `Location Deleted Successfully!`,
                  type: "success",
                });
                // HEREEE

                // console.log(allProjects, "this is new project dataaa");
                // const newProjectData = allProjects?.map(
                //   (project: ProjectData) => {
                //     if (project._id === selectedProjectData?._id) {
                //       return {
                //         ...project,
                //         isDeleted: true,
                //       };
                //     } else {
                //       return project;
                //     }
                //   }
                // );

                // dispatch(SetProjects(newProjectData));
                dispatch(setBackupChange());
              } else {
                showToast({
                  messageContent:
                    (
                      (response?.error as FetchBaseQueryError)?.data as {
                        message?: string;
                      }
                    )?.message || "An error occurred",

                  type: "danger",
                });
              }

              dispatch(setBackupChange());
            }}
            children={
              <>
                <DeleteLocationSummary />
              </>
            }
            // onsubmit={async (data: ProjectData) => {
            //   console.log(data, "this is form data");
            //   const formData = new FormData();

            //   Object.keys(data).forEach((key) => {
            //     if (key === "photo") {
            //       // Handle single photo upload
            //       if (Array.isArray(data.photo)) {
            //         formData.append("photo", data.photo[0]); // Assuming one file is uploaded
            //       } else {
            //         formData.append("photo", data.photo as File);
            //       }
            //     } else if (key === "project_drawing") {
            //       // Handle multiple project drawings
            //       if (Array.isArray(data.project_drawing)) {
            //         data.project_drawing.forEach((file) => {
            //           formData.append("project_drawing", file); // Append each file separately
            //         });
            //       }
            //     } else {
            //       formData.append(key, (data as any)[key]);
            //     }
            //   });

            //   const res = await AddProjectApi(formData);
            //   if (res?.data?.success) {
            //     dispatch(closePopup("AddProjectForm"));
            //   }
            //   console.log(res, "this is response4");
            // }}
            onClose={() => {
              dispatch(closePopup("DeleteLocationForm"));
            }}
          />
        )}
        {currentOpenPopup["AddSubForm"] && (
          <AddCategoryType
            title={"Add Floor"}
            data={Array.from(
              { length: Number(towerLocationFormdata?.number_of_floors) || 0 }, // Ensure it's a valid number
              (_, i) => ({ id: `${i + 1}`, category: `${i + 1}th floor` })
            )}
            initialSelected={[
              ...(towerLocationFormdata?.conventionals?.map((e) =>
                typeof e === "string" ? { _id: e } : e
              ) || []),
              ...(towerLocationFormdata?.mivan?.map((e) =>
                typeof e === "string" ? { _id: e } : e
              ) || []),
            ]}
            label={"Floor"}
            placeholder="Search"
            buttonLabel="Add Category"
            onSelect={(items: any) => {
              dispatch(
                setTowerLocationFormData({
                  ...towerLocationFormdata,
                  [selectedFloorType]: [
                    ...(Array.isArray(
                      towerLocationFormdata[
                        selectedFloorType as keyof TowerLocationData
                      ]
                    )
                      ? (towerLocationFormdata[
                          selectedFloorType as keyof TowerLocationData
                        ] as any[])
                      : []),
                    ...(Array.isArray(items)
                      ? items.map((e: any) => ({
                          _id: e?.id,
                          name: e?.category,
                        }))
                      : []),
                  ],
                })
              );
            }}
            onClose={() => {
              dispatch(closePopup("AddSubForm"));
              dispatch(openPopup("AddLocationForm"));
            }}
          />
        )}
      </div>
    </>
  );
};

export default LocationHeader;
const DeleteLocationSummary: FC<{}> = () => {
  const selectedItems = useSelector(
    (state: RootState) => state?.projectLocalDb?.towerLocationFormdata
  );
  console.log(selectedItems, "projectdataaa");
  return (
    <>
      {selectedItems?.category && (
        <div className={styles.summaryDataContent}>
          <p style={{ color: "var(--text-black-60)" }} className="p_tag_14px">
            Location Type
          </p>
          <div
            className={styles.summaryItems}
            style={{ color: "var(--text-black-87)" }}
          >
            <h4
              className={styles.summaryItem}
              style={{
                paddingRight: "16px",
                color: "var(--text-black-87)",
              }}
            >
              {selectedItems?.category}
            </h4>
          </div>
        </div>
      )}
      {selectedItems?.name && (
        <div className={styles.summaryDataContent}>
          <p style={{ color: "var(--text-black-60)" }} className="p_tag_14px">
            Location Name
          </p>
          <div
            className={styles.summaryItems}
            style={{ color: "var(--text-black-87)" }}
          >
            <h4
              className={styles.summaryItem}
              style={{
                paddingRight: "16px",
                color: "var(--text-black-87)",
              }}
            >
              {selectedItems?.name}
            </h4>
          </div>
        </div>
      )}
      {selectedItems?.location_drawing &&
        selectedItems?.location_drawing?.length > 0 && (
          <div className={styles.summaryDataContent}>
            <p style={{ color: "var(--text-black-60)" }} className="p_tag_14px">
              Drawings
            </p>
            <div
              className={styles.summaryItems}
              style={{ color: "var(--text-black-87)" }}
            >
              {Array.isArray(selectedItems?.location_drawing) &&
                selectedItems?.location_drawing.map((e) => (
                  <h4
                    className={styles.summaryItem}
                    style={{
                      paddingRight: "16px",
                      color: "var(--text-black-87)",
                    }}
                  >
                    {`${
                      e instanceof File
                        ? slicedData(e?.name, 12)
                        : slicedData(e?.split("/")[2] as string, 12)
                    }`}
                  </h4>
                ))}
            </div>
          </div>
        )}
      <div className={styles.summaryDivData}>
        {selectedItems?.area && (
          <div className={styles.summaryDataContent}>
            <p style={{ color: "var(--text-black-60)" }} className="p_tag_14px">
              Area
            </p>
            <div
              className={styles.summaryItems}
              style={{ color: "var(--text-black-87)" }}
            >
              <h4
                className={styles.summaryItem}
                style={{
                  paddingRight: "16px",
                  color: "var(--text-black-87)",
                }}
              >
                {selectedItems?.area}
              </h4>
            </div>
          </div>
        )}
        {selectedItems?.number_of_floors && (
          <div className={styles.summaryDataContent}>
            <p style={{ color: "var(--text-black-60)" }} className="p_tag_14px">
              Number of Floors
            </p>
            <div
              className={styles.summaryItems}
              style={{ color: "var(--text-black-87)" }}
            >
              <h4
                className={styles.summaryItem}
                style={{
                  paddingRight: "16px",
                  color: "var(--text-black-87)",
                }}
              >
                {selectedItems?.number_of_floors}
              </h4>
            </div>
          </div>
        )}
      </div>
      <div className={styles.summaryDivData}>
        {selectedItems?.number_of_basements && (
          <div className={styles.summaryDataContent}>
            <p style={{ color: "var(--text-black-60)" }} className="p_tag_14px">
              Basements
            </p>
            <div
              className={styles.summaryItems}
              style={{ color: "var(--text-black-87)" }}
            >
              <h4
                className={styles.summaryItem}
                style={{
                  paddingRight: "16px",
                  color: "var(--text-black-87)",
                }}
              >
                {selectedItems?.number_of_basements}
              </h4>
            </div>
          </div>
        )}
        {selectedItems?.location_duration && (
          <div className={styles.summaryDataContent}>
            <p style={{ color: "var(--text-black-60)" }} className="p_tag_14px">
              Duration
            </p>
            <div
              className={styles.summaryItems}
              style={{ color: "var(--text-black-87)" }}
            >
              <h4
                className={styles.summaryItem}
                style={{
                  paddingRight: "16px",
                  color: "var(--text-black-87)",
                }}
              >
                {selectedItems?.location_duration}
              </h4>
            </div>
          </div>
        )}
      </div>
      {selectedItems?.structure_type && (
        <div className={styles.summaryDataContent}>
          <p style={{ color: "var(--text-black-60)" }} className="p_tag_14px">
            Structure Type
          </p>
          <div
            className={styles.summaryItems}
            style={{ color: "var(--text-black-87)" }}
          >
            <h4
              className={styles.summaryItem}
              style={{
                paddingRight: "16px",
                color: "var(--text-black-87)",
              }}
            >
              {selectedItems?.structure_type}
            </h4>
          </div>
        </div>
      )}
      {selectedItems?.conventionals &&
        selectedItems?.conventionals?.length > 0 && (
          <div className={styles.summaryDataContent}>
            <p style={{ color: "var(--text-black-60)" }} className="p_tag_14px">
              Conventionals
            </p>
            <div
              className={styles.summaryItems}
              style={{
                color: "var(--text-black-87)",
                display: "flex",
                gap: "1rem",
              }}
            >
              {selectedItems?.conventionals?.map((e) => (
                <h4
                  className={styles.summaryItem}
                  style={{
                    paddingRight: "16px",
                    color: "var(--text-black-87)",
                  }}
                >
                  {e?.name || `${e}th floor`}
                </h4>
              ))}
            </div>
          </div>
        )}
      {selectedItems?.mivan && selectedItems?.mivan?.length > 0 && (
        <div className={styles.summaryDataContent}>
          <p style={{ color: "var(--text-black-60)" }} className="p_tag_14px">
            Mivan
          </p>
          <div
            className={styles.summaryItems}
            style={{
              color: "var(--text-black-87)",
              display: "flex",
              gap: "1rem",
            }}
          >
            {selectedItems?.mivan?.map((e) => (
              <h4
                className={styles.summaryItem}
                style={{
                  paddingRight: "16px",
                  color: "var(--text-black-87)",
                }}
              >
                {e?.name || `${e}th floor`}
              </h4>
            ))}
          </div>
        </div>
      )}
      {selectedItems?.remarks && (
        <div className={styles.summaryDataContent}>
          <p style={{ color: "var(--text-black-60)" }} className="p_tag_14px">
            Structure Type
          </p>
          <div
            className={styles.summaryItems}
            style={{ color: "var(--text-black-87)" }}
          >
            <h4
              className={styles.summaryItem}
              style={{
                paddingRight: "16px",
                color: "var(--text-black-87)",
              }}
            >
              {selectedItems?.remarks}
            </h4>
          </div>
        </div>
      )}
    </>
  );
};
