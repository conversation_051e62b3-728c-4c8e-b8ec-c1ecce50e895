// ---------------------------------- Author: <PERSON><PERSON> --------------------------------
import React, { useState, useEffect } from "react";
import Styles from "../Styles/ToastMessage.module.css";

interface ToastMessageProps {
  message: string;
  heading: string;
  icon: string;
  type: string;
  duration?: number;
}

const ToastMessage: React.FC<ToastMessageProps> = ({
  message,
  heading,
  icon,
  type,
  duration = 3000,
}) => {
  const [isVisible, setIsVisible] = useState(true);
  const [reverseAnimate, setReverseAnimate] = useState(false);

  useEffect(() => {
    const animationTimer = setTimeout(() => {
      setReverseAnimate(true);
    }, duration);

    const hideTimer = setTimeout(() => {
      setIsVisible(false);
    }, duration + 1000);

    return () => {
      clearTimeout(animationTimer);
      clearTimeout(hideTimer);
    };
  }, [duration]);

  return (
    <>
      {isVisible && (
        <div
          className={`${Styles.toast_parent} ${
            reverseAnimate ? Styles.toast_animate_reverse : Styles.toast_animate
          }`}
        >
          <div className={`${Styles.toast_child}`}>
            <div className={`${Styles.Toast_icon}`}>
              <img src={icon} alt="Toast Icon" />
            </div>
            <div className={`${Styles.toast_message_box}`}>
              <p className={`${Styles.toast_message_heading}`}>{heading}</p>
              <p className={`${Styles.toast_message}`}>{message}</p>
            </div>
          </div>
          <div className={`${Styles.toast_type}`}>{type}</div>
        </div>
      )}
    </>
  );
};

export default ToastMessage;
