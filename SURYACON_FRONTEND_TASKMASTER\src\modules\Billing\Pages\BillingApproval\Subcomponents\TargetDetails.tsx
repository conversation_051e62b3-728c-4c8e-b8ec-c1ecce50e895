// Author: <PERSON><PERSON>,<PERSON><PERSON>
import React, { useEffect, useState, useCallback, useMemo } from "react";
import { useDispatch, useSelector } from "react-redux";
import styles from "../Styles/BillingApproval.module.css";
import { RootState } from "../../../../../redux/store";
import { Cross, TaskEditPencil, SuryconLogo } from "../../../../../assets/icons";
import MTSubTaskApprovalCard from "./MTSubTaskApprovalCard";
import TargetBadge from "../../../../../components/Reusble/Global/TargetBadge/TargetBadge";
import MonthlyTargetCard from "./MonthlyTargetCard";
import { 
  openTaskApproveDialog, 
  openDeclineDialog,
  setSelectedTowerAndFloor,
  setMonthlyTargetSubTaskData,
  setMonthlyTargetTaskData
} from "../../../../../redux/features/Modules/Billing/BillingApproval/Slices/BillingApprovalSlice";
import { useTowerData } from "../../../../../redux/api/Modules/Billing/DemoMonthlyTargetApi/useTowerData";

export interface FloorDetail {
  Floor: number;
  MainTaskDescription: string;
  StartDate: string;
  EndDate: string;
  Area: number;
  Tasks: any[];
  Remarks: string;
  
  status?: 'approved' | 'rejected' | 'declined' | 'pending' | string;
}

interface TargetDetailsProps {
  selectedTowerName: string | null;
  isTower: boolean;
  projectId: string; // Add projectId to props
}

interface TaskType {
  TaskName: string;
  Quantity: number;
  Weightage: number;
  Manpower?: Record<string, any>;
  Machinery?: Record<string, any>;
  Tools?: Record<string, any>;
  Materials?: Record<string, any>;
}

const TargetDetails: React.FC<TargetDetailsProps> = ({
  selectedTowerName,
  isTower,
  projectId // Use projectId in component
}) => {
  const [edit, setEdit] = useState(false);
  const [selectedFloor, setSelectedFloor] = useState<number | null>(null);



  const dispatch = useDispatch();
  const { data, loading, error } = useTowerData(projectId);

  // Get tower data based on selection
  const towerData = useMemo(() => {
    if (!data) return null;

    const structures = isTower ? data.Towers : data.NonTowers;

    // If no tower is selected or the selected tower doesn't exist, use the first available
    if (!selectedTowerName || !structures[selectedTowerName]) {
      const firstTowerName = Object.keys(structures)[0];
      return firstTowerName ? structures[firstTowerName] : null;
    }

    return structures[selectedTowerName];
  }, [data, isTower, selectedTowerName]);

  // Get the actual tower name to display
  const displayTowerName = useMemo(() => {
    if (!data) return "No Tower Selected";

    const structures = isTower ? data.Towers : data.NonTowers;

    if (selectedTowerName && structures[selectedTowerName]) {
      return selectedTowerName;
    }

    // Fallback to first tower if selected tower is invalid
    return Object.keys(structures)[0] || "No Tower Selected";
  }, [data, isTower, selectedTowerName]);

  // Get selected floor data
  const floorData = useMemo(() => {
    if (!towerData || selectedFloor === null) return null;
    return towerData.FloorDetails.find(
      (floor) => floor.Floor === selectedFloor
    );
  }, [towerData, selectedFloor]);

  // Define the updateSubTaskData function
  const updateSubTaskData = useCallback((towerName: string, floor: number) => {
    if (!data) return;
    
    const towerCategory = isTower ? 'Towers' : 'NonTowers';
    const selectedTower = data[towerCategory]?.[towerName];
    
    if (!selectedTower || !selectedTower.FloorDetails) return;
    
    // Find the selected floor data
    const floorData = selectedTower.FloorDetails.find(
      floorItem => floorItem.Floor === floor
    );
    
    if (!floorData) return;
    
    // Dispatch actions to set selected tower and floor
    dispatch(setSelectedTowerAndFloor({
      tower: towerName,
      floor: floor
    }));

    // Transform floor data to match MTSubTaskDataProps interface
    const subTaskData = {
      _id: `${towerName}-${floor}`,
      location: towerName,
      floor: floor,
      taskDescription: floorData.MainTaskDescription || "",
      startDate: floorData.StartDate || "",
      endDate: floorData.EndDate || "",
      area: floorData.Area || 0,
      Budget: selectedTower.Details?.Budget || 0,
      subtask: floorData.Tasks?.map(task => task.TaskName) || [],
      drawing: [], // You might need to add drawing logic
      remarks: typeof floorData.Remarks === 'string' ? floorData.Remarks : "",
      reason: "",
      decline: false
    };

    // Dispatch the subtask data
    dispatch(setMonthlyTargetSubTaskData(subTaskData));
  }, [data, isTower, dispatch]);

  // Initial data load when tower changes
  useEffect(() => {
    if (selectedTowerName && data) {
      const towerCategory = isTower ? 'Towers' : 'NonTowers';
      const selectedTower = data[towerCategory]?.[selectedTowerName];

      if (selectedTower && selectedTower.FloorDetails && selectedTower.FloorDetails.length > 0) {
        const firstFloor = selectedTower.FloorDetails[0];
        updateSubTaskData(selectedTowerName, firstFloor.Floor);
      }
    }
  }, [selectedTowerName, data, isTower, updateSubTaskData]);

  // Update data when floor changes
  useEffect(() => {
    if (selectedFloor !== null && selectedTowerName) {
      updateSubTaskData(selectedTowerName, selectedFloor);
    }
  }, [selectedFloor, selectedTowerName, updateSubTaskData]);

  // Reset selected floor when tower changes
  useEffect(() => {
    if (towerData && towerData.FloorDetails?.length > 0) {
      const firstFloor = towerData.FloorDetails[0];
      setSelectedFloor(firstFloor.Floor);
    } else {
      setSelectedFloor(null);
    }
  }, [towerData]);

  // const handleApprove = useCallback(() => {
  //   if (!selectedTowerName || !towerData || selectedFloor === null) return;
    
  //   // Find the selected floor data
  //   const floorData = towerData.FloorDetails.find(
  //     floorItem => floorItem.Floor === selectedFloor
  //   );
    
  //   if (!floorData) return;
    
  //   // Format task data to match MTTaskDataProps interface
  //   const taskData = {
  //     _id: `${selectedTowerName}-${selectedFloor}`,
  //     tower: selectedTowerName,
  //     floor: selectedFloor,
  //     towerDescription: floorData.MainTaskDescription || "",
  //     startDate: floorData.StartDate || "",
  //     endDate: floorData.EndDate || "",
  //     area: floorData.Area || 0,
  //     Budget: towerData.Details?.Budget || 0,
  //     subtasks: floorData.Tasks?.map(task => ({
  //       name: task.TaskName,
  //       quantity: task.Quantity,
  //       weightage: task.Weightage,
  //       resources: {
  //         manpower: task.Manpower || {},
  //         machinery: task.Machinery || {},
  //         tools: task.Tools || {},
  //         materials: task.Materials || {}
  //       }
  //     })) || [],
  //     drawing: [], // Add drawing data if available
  //     remarks: typeof floorData.Remarks === 'string' ? floorData.Remarks : "",
  //     reason: "",
  //     decline: false
  //   };
    
  //   // Dispatch the task data to Redux
  //   dispatch(setMonthlyTargetTaskData(taskData));
    
  //   // Open the task approval dialog
  //   dispatch(openTaskApproveDialog(taskData._id));
  // }, [selectedTowerName, towerData, selectedFloor, dispatch]);
  
  // const handleDecline = useCallback(() => {
  //   if (!selectedTowerName || !towerData || selectedFloor === null) return;
    
  //   // Find the selected floor data
  //   const floorData = towerData.FloorDetails.find(
  //     floorItem => floorItem.Floor === selectedFloor
  //   );
    
  //   if (!floorData) return;
    
  //   // Create task ID
  //   const taskId = `${selectedTowerName}-${selectedFloor}`;
    
  //   // Format task data similar to approval but with decline flag
  //   const taskData = {
  //     _id: taskId,
  //     tower: selectedTowerName,
  //     floor: selectedFloor,
  //     towerDescription: floorData.MainTaskDescription || "",
  //     startDate: floorData.StartDate || "",
  //     endDate: floorData.EndDate || "",
  //     area: floorData.Area || 0,
  //     Budget: towerData.Details?.Budget || 0,
  //     subtasks: floorData.Tasks?.map(task => ({
  //       name: task.TaskName,
  //       quantity: task.Quantity,
  //       weightage: task.Weightage,
  //       resources: {
  //         manpower: task.Manpower || {},
  //         machinery: task.Machinery || {},
  //         tools: task.Tools || {},
  //         materials: task.Materials || {}
  //       }
  //     })) || [],
  //     drawing: [],
  //     remarks: typeof floorData.Remarks === 'string' ? floorData.Remarks : "",
  //     reason: "",
  //     decline: true // Set decline flag to true
  //   };
    
  //   // Set the task data in Redux
  //   dispatch(setMonthlyTargetTaskData(taskData));
    
  //   // Open the decline dialog
  //   dispatch(openDeclineDialog(taskId));
  // }, [selectedTowerName, towerData, selectedFloor, dispatch]);

  // Handle floor selection
  const handleFloorSelect = useCallback((floorNumber: number) => {
    setSelectedFloor(floorNumber);
  }, []);

  const getSubtaskResourcesForTask = useCallback((task: TaskType) => {
    return {
      manpower: Object.entries(task.Manpower || {}),
      machinery: Object.entries(task.Machinery || {}),
      tools: Object.entries(task.Tools || {}),
      materials: Object.entries(task.Materials || {}),
    };
  }, []);

  const toggleEdit = useCallback(() => {
    setEdit(prev => !prev);
  }, []);

  if (loading) {
    return (
      <div className={styles.target_details_container}>
        Loading tower data...
      </div>
    );
  }

  if (error) {
    return <div className={styles.target_details_container}>{error}</div>;
  }

  if (!towerData) {
    return (
      <div className={styles.target_details_container}>
        No data available for {isTower ? "towers" : "non-towers"}. Please select
        a valid structure.
      </div>
    );
  }

  return (
    <div className={styles.target_details_container}>
      {/* Header Section */}
      <div className={styles.target_details_header_outer_container}>
        <div className={styles.target_details_header_container}>
          <div className={styles.target_details_task_description}>
            <h3 className={styles.target_details_task_description_title}>
              {displayTowerName}
            </h3>
            <p
              className={`${styles.target_details_task_description_text} small_text_p_400`}
            >
              This structure has a total of {towerData.Details.Floors} floors
              {towerData.Details.BasementLevels > 0
                ? `, ${towerData.Details.BasementLevels} basement levels`
                : ""}
              {towerData.Details.SwimmingPool > 0
                ? `, ${towerData.Details.SwimmingPool} swimming pool`
                : ""}
              {towerData.Details.ParkArea > 0
                ? `, ${towerData.Details.ParkArea} park areas`
                : ""}
              .
            </p>
          </div>
          {/* <div className={styles.target_details_header_buttons_container}>
            <div className={styles.target_details_decline_button}>
            / <p onClick={handleDecline} className="small_text_p">Decline</p> 
            </div>
            <div className={styles.targetListtextBubble}>
              <p onClick={handleApprove} className="small_text_p">
                Approvez`
              </p>
            </div>
          </div> */}
          <div className={styles.target_details_header_right}>
            <div
              className={styles.edit_pencil_container}
              onClick={toggleEdit}
            >
              {edit ? <Cross /> : <TaskEditPencil />}
            </div>
            <div className={styles.target_list_header_warning}>
              <h4 className="h4">
                {towerData.Details.Budget.toLocaleString()}
              </h4>
            </div>
          </div>
        </div>
      </div>

      {/* Lower Section */}
      <div className={styles.target_details_lower_container}>
    

        <div className={styles.target_details_lower_outer_container}>
          {/* Floor Selection */}
          <div  className={`${styles.floor_selection_container}`}>
            {towerData.FloorDetails.map((floor) => (
              <TargetBadge
                key={floor.Floor}
                outerContainerClassName={
                  selectedFloor === floor.Floor
                    ? "floor_badge_selected"
                    : ((floor as FloorDetail).status === "approved")
                    ? "floor_badge_approved"
                    : ((floor as FloorDetail).status !== undefined && 
                       ((floor as FloorDetail).status === "rejected" || 
                        (floor as FloorDetail).status === "declined"))
                    ? "floor_badge_rejected"
                    : "floor_badge"
                }
                valueTextTagName="h4"
                valueTextClassName={
                  selectedFloor === floor.Floor
                    ? "floor_badge_text_selected"
                    : ((floor as FloorDetail).status === "approved")
                    ? "floor_badge_text_approved"
                    : ((floor as FloorDetail).status !== undefined && 
                       ((floor as FloorDetail).status === "rejected" || 
                        (floor as FloorDetail).status === "declined"))
                    ? "floor_badge_text_rejected"
                    : "floor_badge_text"
                }
                bubbletextClassName={
                  selectedFloor === floor.Floor
                    ? ["floor_badge_bubble_selected", "small_text_p"]
                    : ["floor_badge_bubble", "small_text_p"]
                }
                value={`${floor.Floor}${getOrdinalSuffix(floor.Floor)} Floor`}
                bubbleValue={floor.Tasks.length.toString()}
                onClick={() => handleFloorSelect(floor.Floor)}
              />
            ))}
          </div>
    { floorData && (
          <MTSubTaskApprovalCard
            floorNumber={floorData.Floor}
            taskCount={floorData.Tasks?.length || 0}
            taskDescription={floorData.MainTaskDescription || ""}
            budget={towerData.Details.Budget || 0}
            area={floorData.Area || ""}
            startDate={floorData.StartDate || ""}
            endDate={floorData.EndDate || ""}
            remark={floorData.Remarks || ""}
          />
        )}
          {/* Subtask and Resource Display */}
          {floorData &&
            floorData.Tasks.map((task: TaskType, taskIndex) => {
              const subtaskResources = getSubtaskResourcesForTask(task);

              return (
                <React.Fragment key={taskIndex}>
                  <div className={styles.subtask_container}>
                    {/* Display Subtask Name */}
                    <div className={styles.subtask_header}>
                      <div className={styles.subtask_name_container}>
                        <div className={styles.subtask_number}>
                          {taskIndex + 1}
                        </div>
                        {task.TaskName.length > 12
                          ? `${task.TaskName.slice(0, 12)}...`
                          : task.TaskName}
                      </div>
                      <div className={styles.subtask_pill}>
                        <div className={styles.subtask_pill_percentage}>Cum</div>
                        <div className={styles.subtask_pill_data}>
                          <span className={styles.subtask_pill_label}>
                            Quantity
                          </span>
                          <span className={styles.subtask_pill_value}>
                            {task.Quantity}
                          </span>
                        </div>
                      </div>
                      <div className={styles.subtask_pill}>
                        <div className={styles.subtask_pill_percentage}>%</div>
                        <div className={styles.subtask_pill_data}>
                          <span className={styles.subtask_pill_label}>
                            Weightage
                          </span>
                          <span className={styles.subtask_pill_value}>
                            {task.Weightage}%
                          </span>
                        </div>
                      </div>
                    </div>

                    {/* Resources sections */}
                    {subtaskResources.manpower.length > 0 && (
                      <div className={styles.mt_card_outer_container}>
                        <div className={styles.resource_section_header}>
                          <h4>Manpower</h4>
                        </div>
                        <div className={styles.mt_cards_container}>
                          {subtaskResources.manpower.map(([name, details], i) => (
                            <MonthlyTargetCard
                              key={`manpower-${name}-${i}`}
                              _id={name}
                              title={name}
                              quantity={details.Quantity}
                              property={details.Skill}
                              brand=""
                              edit={edit}
                              towerName={selectedTowerName || ""}
                              floorNumber={selectedFloor || 0}
                              taskIndex={taskIndex}
                              isTower={isTower}
                            />
                          ))}
                        </div>
                      </div>
                    )}

                    {subtaskResources.machinery.length > 0 && (
                      <div className={styles.mt_card_outer_container}>
                        <div className={styles.resource_section_header}>
                          <h4>Machinery</h4>
                        </div>
                        <div className={styles.mt_cards_container}>
                          {subtaskResources.machinery.map(
                            ([name, details], i) => (
                              <MonthlyTargetCard
                                key={`machinery-${name}-${i}`}
                                _id={name}
                                title={name}
                                quantity={details.Quantity}
                                property={details.Skill || ""}
                                brand={details.CompanyName || ""}
                                edit={edit}
                                towerName={selectedTowerName || ""}
                                floorNumber={selectedFloor || 0}
                                taskIndex={taskIndex}
                                isTower={isTower}
                              />
                            )
                          )}
                        </div>
                      </div>
                    )}

                    {subtaskResources.tools.length > 0 && (
                      <div className={styles.mt_card_outer_container}>
                        <div className={styles.resource_section_header}>
                          <h4>Tools</h4>
                        </div>
                        <div className={styles.mt_cards_container}>
                          {subtaskResources.tools.map(([name, details], i) => (
                            <MonthlyTargetCard
                              key={`tools-${name}-${i}`}
                              _id={name}
                              title={name}
                              quantity={details.Quantity}
                              property={details.Unit || ""}
                              brand={details.CompanyName || ""}
                              edit={edit}
                              towerName={selectedTowerName || ""}
                              floorNumber={selectedFloor || 0}
                              taskIndex={taskIndex}
                              isTower={isTower}
                            />
                          ))}
                        </div>
                      </div>
                    )}

                    {subtaskResources.materials.length > 0 && (
                      <div className={styles.mt_card_outer_container}>
                        <div className={styles.resource_section_header}>
                          <h4>Materials</h4>
                        </div>
                        <div className={styles.mt_cards_container}>
                          {subtaskResources.materials.map(
                            ([name, details], i) => (
                              <MonthlyTargetCard
                                key={`materials-${name}-${i}`}
                                _id={name}
                                title={name}
                                quantity={details.Quantity}
                                property={details.Unit || ""}
                                brand={details.CompanyName || ""}
                                edit={edit}
                                towerName={selectedTowerName || ""}
                                floorNumber={selectedFloor || 0}
                                taskIndex={taskIndex}
                                isTower={isTower}
                              />
                            )
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                  
                  {/* Add separator after each subtask except the last one */}
                  {taskIndex < floorData.Tasks.length - 1 && (
                    <div className={styles.separator_line_container}>
                      <span className={styles.dottedline_wrapper}></span>
                      <SuryconLogo />
                      <span className={styles.dottedline_wrapper}></span>
                    </div>
                  )}
                </React.Fragment>
              );
            })}
        </div>
      </div>
    </div>
  );
};

const getOrdinalSuffix = (num: number): string => {
  const j = num % 10;
  const k = num % 100;
  if (j === 1 && k !== 11) return "st";
  if (j === 2 && k !== 12) return "nd";
  if (j === 3 && k !== 13) return "rd";
  return "th";
};

export default TargetDetails;