import React, { useEffect, useLayoutEffect } from "react";
import styles from "../../Styles/TaskCreationForm.module.css";
import { TodoBoard } from "../../../../../../assets/icons";
import { useAppSelector } from "../../../../../../redux/hooks/Modules/Reduxhooks/ReduxHooks";

import { RootState, store } from "../../../../../../redux/store";
import { useDispatch, useSelector } from "react-redux";

import { useLocation, useNavigate, useParams } from "react-router-dom";

import {
  setChangeAPiFlag,
  setcurrentSubtaskData,
  setCurrentSubTaskRoute,
  setSearchKey,
  setSubTaskRoute,
  settaskChangeAPiFlag,
  setTypeSearchKey,
} from "../../../../../../redux/features/Modules/TaskMaster/Slices/TaskMasterSlice";
import {
  removeNavigate,
  setNavigate,
} from "../../../../../../redux/features/Modules/Reusble/navigationSlice";
import { setToast } from "../../../../../../redux/features/Modules/Reusble/ToastSlice";

const TaskSidebar: React.FC = () => {
  const currentSubTaskRoute = useAppSelector(
    (state: RootState) => state.taskMaster.currentSubTaskRoute
  );

  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { pathname } = useLocation();
  const [subtaskdetails, setSubtaskdetails] = React.useState<any>([]);
  const params = useParams();

  const taskdetail = useSelector(
    (state: RootState) => state?.taskForm?.currentSubtaskData
  );
  const subTaskData = useSelector(
    (state: RootState) => state.taskMaster.currentSubtaskData
  );
  console.log("subTaskData", subTaskData);

  useEffect(() => {
    if (params.subtaskId) {
      dispatch(setCurrentSubTaskRoute(params.subtaskId));
    } else {
      dispatch(setCurrentSubTaskRoute(""));
      navigate(`/category/${params.catId}/task/${params.taskId}`);
    }
  }, [dispatch, params?.subtaskId]);

  useEffect(() => {
    setSubtaskdetails(taskdetail?.Subtaskdetails);
  }, [taskdetail?.Subtaskdetails]);

  return (
    <div className={styles.subtask_sidebar_tasks_container}>
      <div
        onClick={() => {
          // dispatch(settaskChangeAPiFlag(false));
          dispatch(setcurrentSubtaskData(null as any));
          dispatch(setChangeAPiFlag(false));
          dispatch(setCurrentSubTaskRoute(""));
          navigate(`/category/${params.catId}/task/${params.taskId}`);
          if (pathname.includes("/subtask")) {
            dispatch(removeNavigate());
          }
        }}
        style={
          currentSubTaskRoute == ""
            ? { background: "var(--primary_color)", color: "white" }
            : undefined
        }
        className={styles.subtask_sidebar_task_detail_container}
      >
        <TodoBoard color={currentSubTaskRoute == "" ? "white" : "black"} />
        <div>
          <p>Task Details</p>
        </div>
      </div>

      <div className={styles.subtask_sidebar_subtasks_lists_container}>
        <h4 style={{ color: "var(--text-black-87)" }}>Subtasks</h4>
        <div className={styles.subtask_sidebar_subtasks_lists_container_scroll}>
          {subtaskdetails &&
            subtaskdetails?.map((e: any) => (
              <>
                <div className={styles.subtask_sidebar_subtask_container}>
                  <div
                    className={styles.subtask_sidebar_subtask_dotted_container}
                  >
                    {[1, 2, 3, 4, 5].map((e) => (
                      <div
                        key={e}
                        className={styles.subtask_sidebar_subtask_dots}
                      ></div>
                    ))}
                  </div>
                  <div
                    className={styles.subtask_sidebar_subtask_right_container}
                  >
                    <div
                      className={
                        styles.subtask_sidebar_subtask_right_dots_container
                      }
                    >
                      {[1, 2, 3].map((e) => (
                        <div
                          className={styles.subtask_sidebar_subtask_right_dots}
                        ></div>
                      ))}
                    </div>
                    <div
                      onClick={() => {
                        if (e?._id) {
                          if (currentSubTaskRoute != e?._id) {
                            dispatch(setCurrentSubTaskRoute(e?._id));
                            navigate(
                              `/category/${params.catId}/task/${params.taskId}/subtask/${e?._id}`
                            );
                            dispatch(
                              setNavigate({
                                route: `/category/${params.catId}/task/${params.taskId}/subtask/${e?._id}`,
                                title: e?.name,
                                isSubtask: true,
                              })
                            );
                            // dispatch(settaskChangeAPiFlag(false));
                            dispatch(setChangeAPiFlag(false));
                            // dispatch(setcurrentSubtaskData(e)); // Set the actual subtask data
                          }
                        } else {
                          dispatch(
                            setToast({
                              isOpen: true,
                              messageContent: "Waiting for Server Response!",
                              type: "warning",
                            })
                          );
                        }
                      }}
                      className={
                        currentSubTaskRoute == e?._id
                          ? styles.active_subtaskRoute
                          : styles.unactive_subtaskRoute
                      }
                    >
                      <h4
                        style={{
                          maxWidth: "100px",
                          whiteSpace: "nowrap",
                          overflow: "hidden",
                          textOverflow: "ellipsis",

                        }}
                      >
                        {e?.name}
                      </h4>

                      {/* dots to reflect the status of the subtask whether it is approved or not */}
                      
                        {/* <div
                          className={
                            e?.isMDApproval ? styles.glowGreen : styles.glowRed
                          }
                        ></div> */}
                    </div>
                  </div>
                </div>
              </>
            ))}
        </div>
      </div>
    </div>
  );
};

export default TaskSidebar;
