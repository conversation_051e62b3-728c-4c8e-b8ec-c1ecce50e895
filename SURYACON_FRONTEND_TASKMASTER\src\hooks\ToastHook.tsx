import { useRef, useCallback } from "react";
import { useDispatch, useSelector } from "react-redux";
import { setToast, clearToast } from "../redux/features/Modules/Reusble/ToastSlice";
import { RootState } from "../redux/store";

export function useToast() {
  const toast = useSelector((state: RootState) => state.toasthandel);
  const dispatch = useDispatch();
  const toastTimerRef = useRef<NodeJS.Timeout | null>(null);

  const showToast = useCallback(
    async (toastObj: { messageContent: string; type: string }) => {
      if (toastTimerRef.current) {
        clearTimeout(toastTimerRef.current);
        toastTimerRef.current = null;
      }
      if (toast.isOpen) {
        dispatch(clearToast());
        await new Promise((resolve) => setTimeout(resolve, 300));
      }
      dispatch(setToast({ ...toastObj, isOpen: true }));
      toastTimerRef.current = setTimeout(() => {
        dispatch(clearToast());
      }, 4000);
    },
    [toast.isOpen, dispatch]
  );

  return showToast;
}