import { FC } from "react";
import styles from "./Styles/SearchBar.module.css";
import { SearchIcon } from "../../../../assets/icons";
import { SearchBarProps } from "../GlobalInterfaces/GlobalInterface";
import { useAppDispatch, useAppSelector } from "../../../../redux/hooks/Modules/Reduxhooks/ReduxHooks";
import { setSearchKey, setTypeSearchKey } from "../../../../redux/features/Modules/TaskMaster/Slices/TaskMasterSlice";

const SearchBar: FC<SearchBarProps> = ({
  placeholder = "Search",
  isTypeForm = false,
  placeholderClassName = "",
  className = "",
  height = "3.8rem",
  onChange,
  value,
}) => {
  const dispatch = useAppDispatch();

  const reduxValue = useAppSelector((state) =>
    isTypeForm ? state.taskMaster.typeSearchKey : state.taskMaster.searchKey
  );
console.log("istypeform",isTypeForm)
  const inputValue = value !== undefined ? value : reduxValue;

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const val = e.target.value;
console.log("istypeform0",val)
    if (value !== undefined && onChange) {
      // Only local state usage (like in table)
      onChange(val);
    } else {
      // Default behavior for all existing usages
      if (isTypeForm) {
        dispatch(setTypeSearchKey(val));
      } else {
        dispatch(setSearchKey(val));
      }
    }
  };

  return (
    <div
      className={`${styles.searchBar} ${styles[className]}`}
      style={{ height: height }}
    >
      <span>
        <SearchIcon />
      </span>
      <input
        autoFocus
        type="text"
        id={isTypeForm ? "typeSearch" : "search"}
        placeholder={placeholder}
        value={inputValue}
        className={`${styles[placeholderClassName]} ${placeholderClassName} p_tag_21px`}
        onChange={handleInputChange}
      />
    </div>
  );
};

export default SearchBar;
