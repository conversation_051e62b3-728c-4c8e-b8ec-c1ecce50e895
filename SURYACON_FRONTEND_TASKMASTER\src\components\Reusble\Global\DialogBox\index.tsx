import React from "react";
import styles from "./Styles/DialogBox.module.css";
import { DialogboxProps } from "../GlobalInterfaces/GlobalInterface";

const Dialogbox: React.FC<DialogboxProps> = ({
  isOpen,
  customClass,
  children,
}) => {
  return (
    <>
      <dialog className={`${styles.dialog}  ${customClass}`} open={isOpen}>
        <div className={styles.dialogBox_content}>{children}</div>
      </dialog>
    </>
  );
};

export default Dialogbox;
