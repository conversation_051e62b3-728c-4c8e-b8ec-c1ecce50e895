.loader_loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.loader_loading_image {
  width: 500px;
  height: 500px;
}


/*  AUTHOR NAME : CHARVI */


.loader_loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.loader_loading_image {
  width: 500px;
  height: 500px;
}

.addcategoryform_container {
  position: fixed;
  top: 49%;
  right: 1.6rem;
  transform: translate(-5%, -43%);
  /* background: var(--main_background); */
  padding: 1.25rem;
  z-index: 89;
  width: 34rem;
  animation: slideIn 0.5s ease-out;
  backdrop-filter: blur(100px);
  border-radius: 2.6rem;
  border: 1px solid;
  border-image-source: linear-gradient(
    130.72deg,
    rgba(237, 231, 231, 0.07) -22.43%,
    rgba(251, 251, 251, 0.05) 75.66%
  );
  backdrop-filter: blur(150px);
  box-shadow: 0px 4px 40px 0px #00000080;
}

.taskContainer_items::-webkit-scrollbar {
  width: 4px; /* Width of the scrollbar */
}

/* Style for the scrollbar track (the background area) */
.taskContainer_items::-webkit-scrollbar-track {
background-color: transparent; /* Light grey track */
border-radius: 10px;
}

/* Style for the scrollbar thumb (the draggable part) */
.taskContainer_items::-webkit-scrollbar-thumb {
background-color: var(--primary_color); /* Darker thumb */
border-radius: 10px;
}

@keyframes slideIn {
  from {
    transform: translate(100%, -43%);
  }

  to {
    transform: translate(-5%, -43%);
  }
}

@keyframes slideOut {
  from {
    transform: translate(0%, 0%);
  }

  to {
    transform: translate(100%, 0%);
  }
}

.addcategoryform_container.closing {
  animation: slideOut 0.5s ease-out;
}

@media only screen and (max-width: 1280px) {
  .addcategoryform_container {
    width: 32rem;
  }
}

.closeButton {
  position: absolute;
  top: 1.5em;
  right: 0.625rem;
  padding: 1rem;
  background: transparent;
  border: none;
  cursor: pointer;
}

.categoryform_header {
  color: var(--primary_color);
  display: flex;
  justify-content: center;
  padding: 0.6rem;
}

.categoryform_datainputs {
  height: 87%;
  padding: 0.45rem;
  overflow-y: scroll;
}

.categoryform_btngrp {
  display: flex;
  justify-content: center;
  gap: 1rem;
  padding: 1.05rem  0 1.5rem 0;
}

.subfeild_container {
  background-color: var(--main_background);
  border-radius: 1.5rem;
  border: 1px solid var(--text-black-28);
  margin: 0.938rem;
  min-height: 3.375rem;
}

.subcategories {
  box-sizing: border-box;
  padding: 0 1rem 1rem 1rem;
  display: flex;
  flex-wrap: wrap;
  gap: 0.7rem;
}

.summaryDivData {
  display: flex;
  align-items: center;
}

.summaryDataContent {
  display: flex;
  flex-direction: column;
  background: #ededed47;
  border-radius: 0.75rem;
  width: 30.8rem;
  /* max-width: 28.5rem; */
  min-height: 3.188rem;
  padding: 1rem;
  white-space: normal;
  margin: 0.6rem;
  gap: 0.2rem;
  line-height: 1.363rem;
  text-align: left;
}

.categoryitems {
  display: flex;

  min-height: 3.188rem;
  overflow-wrap: break-word;
  word-wrap: break-word;
  white-space: normal;
  flex-wrap: wrap;
  color: #000000de;

  line-height: 2rem;
}

.summaryheadings {
  color: #00000047;

  line-height: 1.363rem;
  text-align: left;
}

.summaryicon {
  width: 1.5rem;
  padding-left: 0.3rem;
}

.categoryitems_list {
  padding-right: 1.5rem;
}

.taskContainer {
  display: flex;
  flex-direction: column;
  max-height: calc(100vh - 7.5rem);
  /* background-color: yellow; */
}

.taskcreation_header {
  position: sticky;
  top: 0;
  z-index: 10;
  /* padding: 0.625rem; */
  margin-right: 0.625rem;
}

.taskContainer_items {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1rem;
  overflow-y: auto;
  overflow-x: hidden;
  margin: 2rem 0rem 0.625rem 0.5rem !important;
  max-height: calc(100% - 4rem);
  padding: 0 0.75rem 0.2rem 0rem;
  /* background-color: aquamarine; */
}

@media only screen and (max-width: 1636px) {
  .taskContainer_items {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
  }

  .taskContainer_items {
    margin: 0.625rem 1rem 0.625rem 0;
  }
}

@media only screen and (max-width: 1280px) {
  .taskContainer_items {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
    margin: 0.5rem 3.5rem 0.5rem 0;
  }
}
@media only screen and (max-width: 1200px) {
  .taskContainer_items {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
    margin: 0.5rem 3.5rem 0.5rem 0;
  }

}
