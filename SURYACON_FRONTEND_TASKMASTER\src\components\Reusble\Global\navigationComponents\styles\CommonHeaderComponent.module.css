.common_header {
    display: flex;
    align-items: center;
    gap: 0.2rem;
  }

  .common_header_navlink_container{
    display: flex;
    align-items: center;
    gap: 0.2rem;
  }
  
  .common_header_navlink {
    color: var(--text-black-28);
    text-decoration: none;
    border: 1px solid;
    border-radius: 1rem;
    padding: 0.4rem 0.75rem;
    cursor: pointer;
    white-space: nowrap;

  }
  
  .common_header_icon_container {
    display: flex;
    align-items: center;
    transform: rotate(-90deg);
  }
  
  .common_header_title {
    color: var(--primary_color);
    margin: 0;
    border: 1px solid;
    border-radius: 1rem;
    padding: 0.4rem 0.75rem;
    white-space: nowrap;
  }
  