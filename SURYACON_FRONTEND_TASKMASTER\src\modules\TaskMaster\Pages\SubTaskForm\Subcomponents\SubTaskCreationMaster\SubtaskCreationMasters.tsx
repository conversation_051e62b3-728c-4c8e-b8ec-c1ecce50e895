import React, { useCallback, useState } from "react";
import styles from "../../Styles/SubtaskCreationForm.module.css";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../../../../../redux/store";
import { useGetTaskBuildingBlocksQuery } from "../../../../../../redux/api/Modules/TaskMaster/TaskMasterapi";
import {
  setChangeAPiFlag,
  setIsChangeSubtask,
  settaskChangeAPiFlag,
  updateSubtaskData,
  updateTaskData,
} from "../../../../../../redux/features/Modules/TaskMaster/Slices/TaskMasterSlice";
import {
  closePopup,
  openPopup,
} from "../../../../../../redux/features/Modules/Reusble/popupSlice";

import AddToolTip from "../../../../../../components/Reusble/Global/AddToolTip";
import AddCategoryType from "../../../../../../components/Reusble/Global/AddCategoryType";
import { DeletePopup } from "../../../../../../components/Reusble/Global/DeletePopup";
import {
  AddDataMaster,
  requiredthings,
  taskBuildingBlocks,
  TaskDataType,
} from "../../../../../../interfaces/Modules/TaskMaster/TaskMasterInterface/TaskMaster";
import { saveSyncData } from "../../../../../../Backup/BackupFunctions/BackupFunctions";
import { initializeDatabase } from "../../../../../../functions/functions";
import { useToast } from "../../../../../../hooks/ToastHook";

interface SubtaskCreationMastersProps {
  isEdit?: boolean;
}

const SubtaskCreationMasters: React.FC<SubtaskCreationMastersProps> = ({
  isEdit = false,
}) => {
  const showtoast = useToast();
  const dispatch = useDispatch();
  const [selectedOption, setSelectedOption] = useState("");
  const [selectedOptionApidata, setSelectedOptionApidata] = useState([]);
  const [primaryLabelForAddCategoryType, SetprimaryLabelForAddCategoryType] =
    useState("");
  const subTaskData = useSelector(
    (state: RootState) =>
      state.taskMaster.currentSubtaskData || {
        _id: "",
        name: "",
        Unit: "",
        description: "",
        subtaskWeighatages: 0,
        TaskmasterId: {},
        Tracking: "",
        MaterialId: [] as { _id: string; name: string }[],
        ToolId: [] as { _id: string; name: string }[],
        MachinaryId: [] as { _id: string; name: string }[],
        ManpowerId: [] as { _id: string; name: string }[],
        AdminId: [],
        AssigneeId: [],
        Reporter: [],
        MethodId: {
          work_instruction_id: [],
          task_closing_requirement: [],
          Controlplan: [],
          Failuremode: [],
        },
      }
  );
  const { popups } = useSelector((state: RootState) => state.popup);
  // const { data } = useGetTaskBuildingBlocksQuery();

  // Map API response to task building blocks
  const taskBuildingBlocks: taskBuildingBlocks = {
    // manpower:
    //   data?.data?.response?.manpower.map((item: any) => ({
    //     id: item._id,
    //     category: item.name,
    //   })) || [],
    // machinery:
    //   data?.data?.response?.machinery.map((item: any) => ({
    //     id: item._id,
    //     category: item.name,
    //   })) || [],
    // tools:
    //   data?.data?.response?.tools.map((item: any) => ({
    //     id: item._id,
    //     category: item.name,
    //   })) || [],
    // material:
    //   data?.data?.response?.material.map((item: any) => ({
    //     id: item._id,
    //     category: item.name,
    //     unit: item.unit,
    //   })) || [],
  };

  // task data for add to the task data master if something is not present and added in the subtaskmaster
  const TaskData = useSelector(
    (state: RootState) =>
      state.taskForm.currentSubtaskData || {
        name: "",
        Unit: "",
        description: "",
        subtaskWeighatages: 0,
        Tracking: "",
        MaterialId: [],
        ToolId: [],
        MachinaryId: [],
        ManpowerId: [],
        Adminid: [],
        AssigneeId: [],
        Reporter: [],
        MethodId: {
          work_instruction_id: [],
          task_closing_requirement: [],
          Controlplan: [],
        },
      }
  );
  // Handle selection of categories and update subtask data
  const handleSelect = useCallback(
    (
      categoryName: keyof AddDataMaster,
      selectedItems: TaskDataType[],
      label?: string
    ) => {
      const newSelectedItems = selectedItems.map((item) => ({
        _id: item.id,
        name: item.category,
      }));

      if (selectedItems.length > 0) {
        dispatch(
          updateSubtaskData({
            ...subTaskData,
            [categoryName]: [
              ...(subTaskData[categoryName] || []),
              ...newSelectedItems,
            ],
          })
        );

        saveSyncData(
          {
            ...subTaskData,
            [categoryName]: [
              ...(subTaskData[categoryName] || []),
              ...newSelectedItems,
            ],
          },
          "time",
          "SubTaskForm"
        );
        const uniqueItemsInTask = newSelectedItems.filter(
          (newItem: any) =>
            !(TaskData as any)[categoryName as any]?.some(
              (existingItem: any) => existingItem._id === newItem._id
            )
        );
        dispatch(
          updateTaskData({
            ...(TaskData as any),
            [categoryName]: [
              ...((TaskData as any)[categoryName] || []),
              ...uniqueItemsInTask,
            ],
          })
        );

        saveSyncData(
          {
            ...(TaskData as any),
            [categoryName]: [
              ...((TaskData as any)[categoryName] || []),
              ...uniqueItemsInTask,
            ],
          },
          "time",
          "TaskForm"
        );

        dispatch(settaskChangeAPiFlag(true));

        dispatch(setChangeAPiFlag(true));
        showtoast({
           messageContent: `${
              selectedItems?.length > 1 ? label : selectedItems[0]?.category
            } added Successfully!`,
            type: "success",
        }) 
      }
    },
    [dispatch, subTaskData]
  );

  // Toggle dropdown visibility
  const handleToggleDropdown = async (name: string, modelname?: string) => {
    if (modelname) {
      const data = await getCategories(modelname);
      setSelectedOptionApidata(data);
      (() => {
        switch (modelname) {
          case "MaterialCategory":
            SetprimaryLabelForAddCategoryType("Add Materials Category");
            return;
          case "machinaryCategory":
            SetprimaryLabelForAddCategoryType("Add Machinery Category");
            return;
          case "ToolCategory":
            SetprimaryLabelForAddCategoryType("Add Tool Category");
            return;
          case "Manpowercategory":
            SetprimaryLabelForAddCategoryType("Add Manpower Category");
            return;
          case "departmentdetails":
            SetprimaryLabelForAddCategoryType("Add Department");
            return;
          default:
            return <>Unknown </>;
        }
      })();
    }

    dispatch(openPopup(name));
  };
  const getCategories = async (tablename: string) => {
    console.log("fetheced category called");

    let response;
    let dbName;
    switch (tablename) {
      case "MaterialCategory":
        dbName = await initializeDatabase("MaterialCategory");
        response = await window.electron.getCategoryData({ dbName });
        break;
      case "Manpowercategory":
        dbName = await initializeDatabase("ManpowerCategory");
        response = await window.electron.getCategoryData({ dbName });
        break;
      case "ToolCategory":
        dbName = await initializeDatabase("ToolCategory");
        response = await window.electron.getCategoryData({ dbName });
        break;
      case "machinaryCategory":
        dbName = await initializeDatabase("MachinaryCategory");
        response = await window.electron.getCategoryData({ dbName });
        break;
      case "departmentdetails":
        dbName = await initializeDatabase("Departments");
        response = await window.electron.getCategoryData({ dbName });
        break;
      case "designationdetails":
        dbName = await initializeDatabase("MaterialCategory");
        response = await window.electron.getCategoryData({ dbName });
        break;
      default:
        console.log("Invalid table name");
        response = null;
    }
    console.log(response, "this is response for selecte option");
    return (
      response?.map((e: { _id: string; name: string }) => ({
        id: e._id,
        category: e.name,
      })) || []
    );
  };
  const [requiredThingsDelete, setRequiredThingsDelete] =
    useState<requiredthings>();
  const [requiredThingsDeleteName, setRequiredThingsDeleteName] =
    useState<string>();
  const [deleteId, setDeleteId] = useState<string>();

  return (
    <div className={styles.subtask_creation_master_container}>
      <div
        style={{ display: "grid", gridTemplateColumns: "1fr 1fr", gap: "1rem" }}
      >
        <div>
          {/* Manpower Section */}
          <div style={{ minWidth: "50px", position: "relative" }}>
            <AddToolTip
              showAddIcon={false}
              label="Manpower"
              onClick={(event: any) => {
                event.stopPropagation();
                handleToggleDropdown("Manpower", "Manpowercategory");
                setSelectedOption("Manpowercategory");
              }}
              className={`${styles.subtask_creation_add_tooltip} ${styles.three_cols}`}
              data={subTaskData.ManpowerId?.filter((item) => item?.name || item?._id)}
              isEdit={isEdit}
              // handleDelete={(item) => {
              //   setDeleteId("ManpowerId");
              //   setRequiredThingsDelete(item);
              //   setRequiredThingsDeleteName("Manpower");
              //   dispatch(openPopup("DeleteRequiredThings"));
              // }}
            />
            {popups["Manpower"] && (
              <AddCategoryType
                modelname={selectedOption}
                isStepForm={true}
                primaryLabel={primaryLabelForAddCategoryType}
                singleSelected={true}
                title="Add Manpower"
                data={selectedOptionApidata as TaskDataType[]}
                initialSelected={subTaskData?.ManpowerId}
                label="Manpower"
                placeholder="Search"
                buttonLabel="Add Category"
                onSelect={(item) =>
                  handleSelect("ManpowerId", item, "Manpower")
                }
                onClose={() => dispatch(closePopup("Manpower"))}
              />
            )}
          </div>

          {/* Tools Section */}
          <div
            style={{
              minWidth: "50px",
              position: "relative",
              marginTop: "1rem",
            }}
          >
            <AddToolTip
              showAddIcon={false}
              label="Tools"
              onClick={() => handleToggleDropdown("Tools")}
              className={`${styles.subtask_creation_add_tooltip} ${styles.three_cols}`}
              data={subTaskData?.ToolId?.filter((item) => item?.name || item?._id)}
              isEdit={isEdit}
              // handleDelete={(item) => {
              //   setDeleteId("ToolId");
              //   setRequiredThingsDelete(item);
              //   setRequiredThingsDeleteName("Tool");
              //   dispatch(openPopup("DeleteRequiredThings"));
              // }}
            />
            {popups["Tools"] && (
              <AddCategoryType
                title="Add Tools"
                label="Tool"
                data={taskBuildingBlocks.tools as TaskDataType[]}
                placeholder="Search"
                isColorChange={true}
                initialSelected={subTaskData?.ToolId}
                buttonLabel="Add Category"
                onSelect={(item) =>
                  handleSelect(
                    "ToolId",
                    item,
                    `${item?.length > 1 ? "Tools" : "Tool"}`
                  )
                }
                onClose={() => dispatch(closePopup("Tools"))}
              />
            )}
            {popups["DeleteRequiredThings"] && (
              <DeletePopup
                width="23rem"
                height="calc(100% - 9rem)"
                heightupperlimit="0rem"
                header={`Are you sure you want to delete this ${requiredThingsDeleteName} ?`}
                callbackDelete={async () => {
                  if (deleteId) {
                    const newRequiredThings = (
                      subTaskData[deleteId as keyof AddData] as {
                        _id: string;
                        name: string;
                      }[]
                    )?.filter((item) => item._id !== requiredThingsDelete?._id);
                    dispatch(
                      updateSubtaskData({
                        ...subTaskData,
                        [deleteId]: newRequiredThings,
                      })
                    );

                    saveSyncData(
                      {
                        ...subTaskData,
                        [deleteId]: newRequiredThings,
                      },
                      "time",
                      "SubTaskForm"
                    );
                    showtoast({
                        messageContent: `${requiredThingsDelete?.name} deleted Successfully!`,
                        type: "success",
                    });
                    dispatch(setChangeAPiFlag(true));
                  }
                  dispatch(closePopup("DeleteRequiredThings"));
                }}
                onClose={() => {
                  dispatch(closePopup("DeleteRequiredThings"));
                }}
              >
                <div className={styles.summaryDivData}>
                  <div className={styles.summaryDataContent}>
                    <p
                      style={{ color: "var(--text-black-60)" }}
                      className="p_tag_14px"
                    >
                      {requiredThingsDeleteName}
                    </p>
                    <div
                      className=""
                      style={{ display: "flex", gap: "1rem", flexWrap: "wrap" }}
                    >
                      <h4 style={{ color: "var(--text-black-87)" }}>
                        {requiredThingsDelete?.name}
                      </h4>
                    </div>
                  </div>
                </div>
              </DeletePopup>
            )}
          </div>
        </div>
        <div>
          {/* Machinery Section */}
          <div style={{ minWidth: "50px", position: "relative" }}>
            <AddToolTip
              showAddIcon={false}
              label="Machinery"
              onClick={() => handleToggleDropdown("Machinery")}
              className={`${styles.subtask_creation_add_tooltip} ${styles.three_cols}`}
              data={subTaskData?.MachinaryId?.filter((item) => item?.name || item?._id)}
              isEdit={isEdit}
              // handleDelete={(item) => {
              //   setDeleteId("MachinaryId");
              //   setRequiredThingsDelete(item);
              //   setRequiredThingsDeleteName("Machine");
              //   dispatch(openPopup("DeleteRequiredThings"));
              // }}
            />
            {popups["Machinery"] && (
              <AddCategoryType
                title="Add Machinery"
                label="Machinery"
                data={taskBuildingBlocks.machinery as TaskDataType[]}
                placeholder="Search"
                isColorChange={true}
                buttonLabel="Add Category"
                initialSelected={subTaskData?.MachinaryId}
                onSelect={(item) =>
                  handleSelect(
                    "MachinaryId",
                    item,
                    `${item?.length > 1 ? "Machinery" : "Machine"}`
                  )
                }
                onClose={() => dispatch(closePopup("Machinery"))}
              />
            )}
          </div>

          {/* Materials Section */}
          <div
            style={{
              minWidth: "50px",
              position: "relative",
              marginTop: "1rem",
            }}
          >
            <AddToolTip
              showAddIcon={false}
              label="Materials"
              onClick={() => handleToggleDropdown("Material")}
              className={`${styles.subtask_creation_add_tooltip} ${styles.three_cols}`}
              data={subTaskData?.MaterialId?.filter((item) => item?.name || item?._id)}
              isEdit={isEdit}
              // handleDelete={(item) => {
              //   setDeleteId("MaterialId");
              //   setRequiredThingsDelete(item);
              //   setRequiredThingsDeleteName("Material");
              //   dispatch(openPopup("DeleteRequiredThings"));
              // }}
            />
            {popups["Material"] && (
              <AddCategoryType
                title="Add Materials"
                initialSelected={subTaskData?.MaterialId}
                label="Material"
                isColorChange={true}
                data={taskBuildingBlocks.material as TaskDataType[]}
                placeholder="Search"
                buttonLabel="Add Category"
                onSelect={(item) =>
                  handleSelect(
                    "MaterialId",
                    item,
                    `${item?.length > 1 ? "Materials" : "Material"}`
                  )
                }
                onClose={() => dispatch(closePopup("Material"))}
              />
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SubtaskCreationMasters;
