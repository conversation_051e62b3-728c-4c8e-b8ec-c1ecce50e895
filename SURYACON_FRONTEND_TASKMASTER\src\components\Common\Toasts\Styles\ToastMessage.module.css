/* // ---------------------------------- Author: <PERSON><PERSON> -------------------------------- */
.toast_parent {
    max-width: 25rem;
    border-radius: 20px;
    padding: 1rem;
    background-color: var(--main_background);
    box-shadow: var(--primary-shadow);
    position: relative;
    overflow: hidden;
    padding-bottom: 2.5rem;
    max-width: 25rem;
    min-width: 25rem;
}

.toast_child {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.Toast_icon {
    background: var(--primary_background);
    padding: 1rem;
    border-radius: 100px;
    display: flex;
    justify-content: center;
    align-items: center;
    scale: 1.1;
}

.toast_message_box {
    display: flex;
    flex-direction: column;
    align-items: start;
    justify-content: center;
    gap: 0.3rem;
}

.toast_message_heading {


}

.toast_message {

    color: var(--text-black-light);
}

.toast_type {
    color: var(--main_background);
 
    background-color: var(--primary_color);
    width: fit-content;
    padding: 0.5rem 2rem 2rem 1.4rem;
    border-radius: 110px;
    position: absolute;
    right: -1rem;
    top: 5rem;
}

.toast_animate {
    animation: ToastAnnimation 1s ease-in-out;
}

.toast_animate_reverse {
    animation: ToastAnnimationReverse 1s ease-in-out;
}

@keyframes ToastAnnimation {
    0% {
        transform: translateX(100%);
        display: none;
    }

    100% {
        transform: translateX(0);
        display: block;
    }
}

@keyframes ToastAnnimationReverse {
    0% {
        transform: translateX(0%);
    }

    100% {
        transform: translateX(100%);
        opacity: 0;
    }
}