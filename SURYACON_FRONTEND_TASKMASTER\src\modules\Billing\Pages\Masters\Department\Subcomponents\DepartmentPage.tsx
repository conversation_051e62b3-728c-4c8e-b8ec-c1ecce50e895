import React, { useEffect, useRef, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import TMMMNav from "../../../../../../components/Reusble/TMMMNav";
import {
  initializeDatabase,
  pathTableMap,
} from "../../../../../../functions/functions";
import {
  clearFetchedMasters,
  setFetchedMasters,
  setSearchData,
} from "../../../../../../redux/features/Modules/Masters";
import DepartmentCard from "./DepartmentCard/DepartmentCard";

import { Loader } from "../../../../../../assets/loader";
import { usePouchSearch } from "../../../../../../functions/useLocalSearch";
import { setNavigate } from "../../../../../../redux/features/Modules/Reusble/navigationSlice";
import { useAppSelector } from "../../../../../../redux/hooks/Modules/Reduxhooks/ReduxHooks";
import { RootState } from "../../../../../../redux/store";
import styles from "../Styles/Department.module.css";

const DepartmentPage = () => {
  const [page, setPage] = useState<number>(1);
  const [searchLocalKey, setSearchLocalKey] = useState<string>("");
  // const { data, isError, isLoading } = useGetAllDepartmentsQuery({});
  const [width, setWidth] = useState<number | null>(
    window.innerWidth < 1200 ? window.innerWidth : 1400
  );
  const [params, setParams] = useState<string>();
  const navRef = useRef<HTMLDivElement>(null);
  const mainContentRef = useRef<HTMLDivElement>(null);
  const condition =
    mainContentRef.current &&
    mainContentRef.current.getBoundingClientRect().width < 1200;
  const condition2 = window.innerWidth < 1200;
  const searchData = useAppSelector(
    (state) => state.masterReduxSlice.searchedData
  );
  const navigationArray = useSelector(
    (state: RootState) => state.navigateData.navigateArray
  );
  const depStateData = useSelector(
    (state: RootState) => state.masterReduxSlice.fetchedDepartment
  );
  const dispatch = useDispatch();
  const searchKey = useAppSelector((state) => state.taskMaster.searchKey);

  // console.log("Get Data from Local DB", depStateData, "API DATA ->>", data);

  // console.count("Department Page Render->>>");

  // Update params when navigation changes
  useEffect(() => {
    setParams(navigationArray[1]?.route);
    if (navigationArray[1]?.route === "/deleted/#") {
      dispatch(
        setFetchedMasters({ data: [], page: 1, type: "masterDepartment" })
      );
      setPage(1);
    }
  }, [navigationArray]);

  // Fetch data when page or params change
  useEffect(() => {
    if (page) {
      getDatafromDb(page);
    }
  }, [page, params]);

  // Reset to first page if local data changes
  // useEffect(() => {
  //   if (localChange) {
  //     setPage(1);
  //   }
  // }, []);

  // Cleanup: reset page and clear data on params change/unmount
  useEffect(() => {
    return () => {
      setPage(1);
      dispatch(clearFetchedMasters());
      dispatch(setSearchData([]));
    };
  }, [params]);

  // Sync search key with local state
  useEffect(() => {
    if (searchKey) {
      setSearchLocalKey(searchKey);
    } else {
      setSearchLocalKey("");
    }
  }, [searchKey]);

  // Local search for departments
  usePouchSearch({
    pathTableMap,
    searchKey: searchLocalKey,
    setData: setSearchData,
    setPage,
    isDeleted: params === "/deleted/#",
  });

  // Fetch department data from local DB and update Redux state
  const getDatafromDb = async (p: any) => {
    const dbName = await initializeDatabase("Departmentmaster");
    const fetchedData = await window.electron.bulkGet({
      dbName,
      page: p,
      deleted: params ? true : false,
      needSorting: true,
    });

    if (p === 1) {
      dispatch(
        setFetchedMasters({
          data: fetchedData.docs,
          page: p,
          type: "masterDepartment",
        })
      );
    } else {
      const newData = [...depStateData, ...fetchedData.docs];
      dispatch(
        setFetchedMasters({ data: newData, page: p, type: "masterDepartment" })
      );
    }
  };

  // Infinite scroll: load next page when scrolled to bottom
  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const target = e.target as HTMLDivElement;
    const { scrollHeight, clientHeight, scrollTop } = target;
    if (scrollTop + clientHeight >= scrollHeight - 5) {
      setPage((prev) => prev + 1);
    }
  };

  // Adjust sidebar/nav width responsively
  const clientFunction = () => {
    if (window.innerWidth <= 1200) {
      navRef.current?.style.setProperty("width", `${width}px`);
    }
    const mainContentWidth =
      mainContentRef?.current?.getBoundingClientRect()?.width;
    if (mainContentWidth && mainContentWidth > width) {
      setWidth(mainContentWidth);
      navRef.current?.style.setProperty("width", `${mainContentWidth}px`);
    }
  };
  // Handle responsive sidebar/nav width on mount and resize
  useEffect(() => {
    console.log("here i am");
    if (window.innerWidth <= 1200) {
      //   setWidth(window.innerWidth);
      // console.log
      navRef.current?.style.setProperty("width", "auto");
    }
    clientFunction();
    const handleResize = () => clientFunction();
    window.addEventListener("resize", handleResize);
    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, [window.innerWidth, condition]);

  return (
    <div style={{ width: condition2 ? "fit-content" : "" }}>
      <div ref={navRef}>
        <TMMMNav
          Label={"Department"}
          variant={"materialCategory"}
          TargetForm={"AddDepartmentForm"}
          deletedNeeded={params ? false : true}
          handleDelete={() => {
            dispatch(
              setNavigate({
                route: `/deleted/#`,
                title: "Deleted",
              })
            );
            dispatch(searchData([]));
          }}
        />
      </div>
      {/* <AddDesignationForm /> */}

      <div
        className={styles.department_content}
        onScroll={(e) => handleScroll(e)}
        ref={mainContentRef}
        style={{ width: depStateData.length <= 0 && condition2 ? width : "" }}
      >
        {depStateData && depStateData.length > 0 ? (
          (searchData && searchData.length > 0
            ? searchData
            : depStateData || []
          ).map((department: any, index: number) => {
            const isDeleted = department?.isDeleted;
            return (
              <DepartmentCard
                data={department}
                key={department._id || index}
                cardHandler={() => {}}
                type="Department"
                isSoftDeleted={isDeleted || params === "/deleted/#"}
              />
            );
          })
        ) : (
          <div className={styles.loader_loading}>
            <img
              src={Loader.suryaconLogo}
              alt="Loading..."
              className={styles.loader_loading_image}
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default DepartmentPage;
