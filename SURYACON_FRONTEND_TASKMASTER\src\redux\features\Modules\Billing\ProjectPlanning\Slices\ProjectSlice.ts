import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { ProjectData } from "../../../../../../modules/Billing/Pages/ProjectPlanning/Projects/AddProjectForm/Interfaces/interface";
import { TowerLocationData } from "../../../../../../modules/Billing/Pages/ProjectPlanning/Locations/Subcomponents/AddLocation/Interfaces/Interface";
import { TowerLocationInterface } from "../../../../../../components/Reusble/Billing/TowerDetailCard";

const initialProjectState = {
  projects: [] as ProjectData[],
  selectedProjectIddata: null as ProjectData | null,
  selectedProjectCategory: "" as "Ongoing" | "Completed",
  selectedFloorType: "" as string,
  selectedTowerType: "" as string,
  selectedTowerLocationName: "" as string,

  isProjectDeleted: false as boolean,
  isTowerLocationDeleted: false as boolean,

  openedProject: "" as string,
  // the towerlocation data is used beacuse we are opening another form in the middle of addtowerlocation form  ,this state is used to save the data so that we can get it again when the second form close
  towerLocationFormdata: {
    category: "",
    name: "",
    location_drawing: [],
    project_id: "",
    area: "",
    number_of_floors: "",
    number_of_basements: "",
    location_duration: "",
    structure_type: "",
    conventionals: [],
    mivan: [],

    remarks: "",
  } as TowerLocationData,
  towerLocations: [] as any,
  page: 0 as number,
  selectedTowerLocationdata: null as TowerLocationData | null,
};

const projectLocalDb = createSlice({
  name: "projectLocalDb",
  initialState: initialProjectState,
  reducers: {
    setpage: (state, action: PayloadAction<number>) => {
      state.page = action.payload;
    },
    setopenedProject(state, action: PayloadAction<string>) {
      state.openedProject = action.payload;
    },
    setSelectedTowerLocationName(state, action: PayloadAction<string>) {
      state.selectedTowerLocationName = action.payload;
    },
    setProjectLocalDb(state, action: PayloadAction<any[]>) {
      const newdata = action.payload.filter(
        (newCat: any) =>
          !state.projects?.some(
            (project: ProjectData) => project._id === newCat._id
          )
      );
      const arr = state.projects?.filter((el: ProjectData) => {
        return !action.payload.some((item) => item._id === el._id);
      });
      arr.forEach((el: ProjectData) => {
        const indx = state.projects?.indexOf(el);
        if (indx !== -1) {
          state.projects?.splice(indx, 1);
        }
      });

      console.log("newdatpusheda", newdata);
      state.projects?.push(...newdata);
    },
    SetProjects(state, action: PayloadAction<any[]>) {
      if (action.payload.length === 0) {
        state.projects = []; // Set state to empty array if payload is empty
      } else {
        action.payload.forEach((newProject: ProjectData) => {
          const existingIndex = state.projects.findIndex(
            (project: ProjectData) => project._id === newProject._id
          );

          if (existingIndex !== -1) {
            console.log("action.payload", newProject);
            if (
              newProject.isDeleted == state.isProjectDeleted &&
              newProject.project_status == state.selectedProjectCategory
            ) {
              state.projects[existingIndex] =
                typeof newProject.client_id === "object"
                  ? {
                      ...newProject,
                      clientName: newProject.client_id.clientName,
                      ClientPhoneNumber: newProject.client_id.ClientPhoneNumber,
                      photo: newProject.photo,
                      client_id: newProject.client_id._id,
                    }
                  : newProject; // Replace existing project
            } else {
              state.projects.splice(existingIndex, 1); // Remove project from the state
            }
          } else {
            if (
              newProject.isDeleted == state.isProjectDeleted &&
              newProject.project_status == state.selectedProjectCategory
            ) {
              console.log(
                "about to push new project",
                newProject,
                state.isProjectDeleted
              );
              state.projects.push(newProject); // Add new project
            }

            // state.projects.filter(
            //   (project: ProjectData) =>
            //     project.isDeleted === state.selectedDeleted.isProjectDeleted
            // );
            const filteredAndSortedProjects = state.projects.filter(
              (e: ProjectData) =>
                e.project_status == state.selectedProjectCategory
            );

            state.projects = filteredAndSortedProjects;
          }
        });
      }
    },
    setNewProject(state, action) {
      if (action.payload.length === 0) {
        state.projects = []; // Set state to empty array if payload is empty
      } else {
        action.payload.forEach((newProject: ProjectData) => {
          const existingIndex = state.projects.findIndex(
            (project: ProjectData) => project._id === newProject._id
          );

          if (existingIndex !== -1) {
            console.log("action.payload", newProject);
            if (
              newProject.isDeleted == state.isProjectDeleted &&
              newProject.project_status == state.selectedProjectCategory
            ) {
              state.projects[existingIndex] =
                typeof newProject.client_id === "object"
                  ? {
                      ...newProject,
                      clientName: newProject.client_id.clientName,
                      ClientPhoneNumber: newProject.client_id.ClientPhoneNumber,
                      photo: newProject.photo,
                      client_id: newProject.client_id._id,
                    }
                  : newProject; // Replace existing project
            } else {
              state.projects.splice(existingIndex, 1); // Remove project from the state
            }
          } else {
            if (
              newProject.isDeleted == state.isProjectDeleted &&
              newProject.project_status == state.selectedProjectCategory
            ) {
              console.log(
                "about to push new project",
                newProject,
                state.isProjectDeleted
              );
              state.projects.unshift(newProject); // Add new project
            }

            // state.projects.filter(
            //   (project: ProjectData) =>
            //     project.isDeleted === state.selectedDeleted.isProjectDeleted
            // );
            const filteredAndSortedProjects = state.projects.filter(
              (e: ProjectData) =>
                e.project_status == state.selectedProjectCategory
            );

            state.projects = filteredAndSortedProjects;
          }
        });
      }
    },
    setTowerLocations(state, action: PayloadAction<any[]>) {
      state.towerLocations = action.payload;
    },

    setNewLocation(state, action: PayloadAction<any[]>) {
      if (action.payload.length === 0) {
        state.towerLocations = []; // Set state to empty array if payload is empty
      } else {
        action.payload.forEach((newLocation: TowerLocationInterface) => {
          const existingIndex = state.towerLocations.findIndex(
            (location: TowerLocationInterface) => location._id === newLocation._id
          );

          if (existingIndex !== -1) {
            if (
              newLocation.isDeleted == state.isTowerLocationDeleted
            ) {
              state.towerLocations[existingIndex] = newLocation; // Replace existing location
            } else {
              state.towerLocations.splice(existingIndex, 1); // Remove location from the state
            }
          } else {
            if (
              newLocation.isDeleted == state.isTowerLocationDeleted
            ) {
              state.towerLocations.unshift(newLocation); // Add new location at the beginning
            }

            const filteredLocations = state.towerLocations.filter(
              (e: TowerLocationInterface) => e.isDeleted == state.isTowerLocationDeleted
            );

            state.towerLocations = filteredLocations;
          }
        });
      }
    },

    setSelectedProjectIddata(state, action: PayloadAction<ProjectData>) {
      state.selectedProjectIddata = action.payload;
    },
    setSelectedTowerLocationData(
      state,
      action: PayloadAction<TowerLocationData>
    ) {
      state.selectedTowerLocationdata = action.payload;
    },
    setTowerLocationFormData(state, action: PayloadAction<TowerLocationData>) {
      state.towerLocationFormdata = action.payload;
    },
    setSelectedFloorType(state, action: PayloadAction<string>) {
      state.selectedFloorType = action.payload;
    },
    setSelectedProjectCategory(
      state,
      action: PayloadAction<"Ongoing" | "Completed">
    ) {
      state.selectedProjectCategory = action.payload;
    },
    setSelectedtowerType(state, action: PayloadAction<string>) {
      state.selectedTowerType = action.payload;
    },
    setprojectDeleted(state, action: PayloadAction<any>) {
      state.isProjectDeleted = action.payload;
    },
    setTowerLocationDeleted(state, action: PayloadAction<any>) {
      state.isTowerLocationDeleted = action.payload;
    },
  },
});
export const {
  setProjectLocalDb,
  SetProjects,
  setTowerLocations,
  setSelectedProjectIddata,
  setSelectedTowerLocationName,
  setTowerLocationFormData,
  setSelectedFloorType,
  setSelectedTowerLocationData,
  setSelectedProjectCategory,
  setprojectDeleted,
  setTowerLocationDeleted,
  setopenedProject,
  setSelectedtowerType,
  setNewProject,
  setpage,
  setNewLocation
} = projectLocalDb.actions;

export const projectReducer = projectLocalDb.reducer;
