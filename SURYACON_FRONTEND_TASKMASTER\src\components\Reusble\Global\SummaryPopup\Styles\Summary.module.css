.closeButton {
  position: absolute;
  top: .75rem;
  right: 0.625rem;
  padding: 1rem;
  background: transparent;
  border: none;
  cursor: pointer;
}

.summary_popup_form_container {
  /* margin: 4rem; line to remove */
 position: fixed;
  top: 7rem;
  right: 1.5rem;
  transform: translate(0%, 0%);
  /* background: var(--main_background); */
  padding: 0.75rem 0.9rem;
  z-index: 999;
  width: 34rem;
  animation: slideIn 0.5s ease-out;
  backdrop-filter: blur(100px);
  border-radius: 2.6rem;
  border: 1px solid;
  border-image-source: linear-gradient(
    130.72deg,
    rgba(237, 231, 231, 0.07) -22.43%,
    rgba(251, 251, 251, 0.05) 75.66%
  );
  backdrop-filter: blur(150px);
  box-shadow: 0px 4px 40px 0px #00000080;
  height: calc(100% - 8.5rem);
}

@keyframes slideIn {
  from {
    transform: translate(100%, 0);
  }

  to {
    transform: translate(0%, 0);
  }
}

@keyframes slideOut {
  from {
    transform: translate(0, 0);
  }

  to {
    transform: translate(100%, 0);
  }
}

.summary_popup_form_container.closing {
  animation: slideOut 0.5s ease-out forwards;
}


.summary_popup_form_top_text {
  /* margin: auto; */
  color: var(--primary_color);
  margin-bottom: 1.5rem;
  position: relative;
  max-width: 80%;
  overflow: hidden;
  text-overflow: ellipsis;
  /* white-space: nowrap; */
}

.summary_popup_header {
  color: var(--warning_color);
  display: flex;
  justify-content: center;
  padding: 0.6rem;
}

.summary_popup_main_body {
  height: calc(100% - 7rem);
  /* padding: 0.45rem; */
  overflow-y: scroll;
  scrollbar-width: none;
}

.summary_popup_button_div {
  margin-top: 2rem;
  display: flex;
  column-gap: 1.5rem;
  justify-content: center;
  position: relative;
}

@media (max-width: 1536px) {
  .summary_popup_form_container {
    width: 31rem;
  }
}
