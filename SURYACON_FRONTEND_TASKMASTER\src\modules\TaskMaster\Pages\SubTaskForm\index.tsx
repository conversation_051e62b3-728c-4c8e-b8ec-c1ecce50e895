/* AUTHOR NAME: CHARVI */
import React, { useCallback, useEffect, useRef, useState } from "react";
import { useLocation, useParams } from "react-router-dom"; // Import useParams to access dynamic route parameters
import {
  useGetsubTaskDetailsQuery,
  useLazyGetsubTaskDetailsLazyQuery,
} from "../../../../redux/api/Modules/TaskMaster/TaskMasterapi";
import { useDispatch, useSelector } from "react-redux";
import { useAppSelector } from "../../../../redux/hooks/Modules/Reduxhooks/ReduxHooks";
import { AppDispatch, RootState, store } from "../../../../redux/store";
import {
  resetCurrentSubtaskData,
  setChangeAPiFlag,
  setcurrentSubtaskData,
  settaskChangeAPiFlag,
} from "../../../../redux/features/Modules/TaskMaster/Slices/TaskMasterSlice";
import { useUpdateSubtask } from "../../../../redux/hooks/Modules/TaskMaster/TaskMasterHook";
import { debounce, initializeDatabase } from "../../../../functions/functions";
import SubTaskCreationHeader from "./Subcomponents/SubTaskCreationHeader/SubTaskCreationHeader";
import SubtaskCreationMasters from "./Subcomponents/SubTaskCreationMaster/SubtaskCreationMasters";
import { SuryconLogo } from "../../../../assets/icons";
import SubTaskCreationMethod from "./Subcomponents/SubTaskCreationMethod/SubTaskCreationMethod";
import TEFormComponent from "../TriggerEventForm";
import { closePopup } from "../../../../redux/features/Modules/Reusble/popupSlice";
import { resetInputValues } from "../../../../redux/features/Modules/Reusble/floatinglabelslice";
import styles from "./Styles/SubtaskCreationForm.module.css";
import SubaskCreationDesignation from "./Subcomponents/SubTaskCreationDesignation/SubTaskCreationDesignation";
import { setWorkInstructionDeleteEmpty } from "../../../../redux/features/Modules/TaskMaster/Slices/WorkInstructionSlice";
import SubTaskCreationAutomation from "./Subcomponents/SubTaskCreationAutomation/SubTaskCreationAutomation";
import {
  changeActiveState,
  setIsOnlyResponse,
  setTriggerFormData,
  setTriggerMode,
  TriggerEventData,
} from "../../../../redux/features/Modules/TaskMaster/Slices/TriggerEventSlice";
import {
  useOpenclosesubtaskMutation,
  useOpenCloseTaskMasterMutation,
} from "../../../../redux/api/Modules/Reusble/Reusble";
import { Subtaskmaster } from "../../../../Backup/TaskMasterBackup";
import { saveSyncData } from "../../../../Backup/BackupFunctions/BackupFunctions";
import { useToast } from "../../../../hooks/ToastHook";

const SubTaskCreationForm: React.FC = () => {
  const showToast = useToast();
  const [isEdit, SetisEdit] = useState<boolean>(false);
  const { subtaskId } = useParams<{ subtaskId: string }>();
  const { taskId } = useParams<{ taskId: string }>();

  const location = useLocation();
  const [finalSubtaskId, setFinalSubtaskId] = useState<string | null>(null);
  const isCalledRef = useRef(false);
  // const [isTaskEmpty, setIsTaskEmpty] = useState(false);
  const [getSubTask] = useLazyGetsubTaskDetailsLazyQuery();
  // Ensure state updates correctly on route change

  const dispatch = useDispatch();
  const subTaskData = useSelector(
    (state: RootState) => state.taskMaster.currentSubtaskData
  );
  // const { openDocData } = useSelector((state: RootState) => state.popup);
  const { user } = useSelector((state: RootState) => state.auth) as any;
  const [tracking, setTracking] = useState<string>("");
  const handleSyncRef = useRef<() => Promise<void>>(() => Promise.resolve());
  const isSubTaskChange = useAppSelector((state) => state.taskMaster.isChange);
  const isApiCall = useSelector(
    (state: RootState) => state.isEditTaskReducer.issubtaskApiCall
  );
  console.log(isApiCall);
  const popups = useSelector((state: RootState) => state.popup.popups);

  const subTaskDataRef = useRef(subTaskData);

  const triggerMode = useSelector(
    (state: RootState) => state.triggerEvent.triggerMode
  );
  const isApiCallRef = useRef(isApiCall);
  const [updateStatus] = useOpenCloseTaskMasterMutation();
  const [updateSubtask] = useUpdateSubtask();
  const finalSubtaskIdRef = useRef<string | undefined>(undefined);
  const finalTaskIdRef = useRef<string | undefined>(undefined);
  const handleUpdateStatus = async (data?: {
    status: string;
    taskid: string;
  }) => {
    try {
      const response = await updateStatus(data).unwrap();
      return response;
    } catch (error) {
      console.log(error);
      throw error;
    }
  };

  console.log("Changes");

  const state = store.getState();
  // const openDocData = state.popup.openDocData;
  const handleGetSubTask = async (id: string) => {
    const response = await getSubTask(id);

    return response;
  };

  const handleToggleisEdit = async () => {
    try {
      const response = (await handleUpdateStatus({
        status: `${!isEdit}`,
        taskid: taskId!,
      })) as any;

      console.log("response12", response?.data);
      if (response?.success) {
        SetisEdit((prev: any) => !prev);
      } else {
        SetisEdit(false);
        showToast({
          messageContent: "Someone working on this form",
          type: "warning",
        });
      }
      // if(response)
    } catch (error) {
      SetisEdit(false);
      console.log("response4", error);
      showToast({
        messageContent: "Oops! Something went wrong",
        type: "danger",
      });
    }
  };

  const transformFunctionForGet = (response: any) => {
    const triggerResponseName = response?.AutoId?.TriggerResponse?.name ?? "";

    const triggerActions = TriggerEventData(triggerResponseName).action;
    const matchingAction = triggerActions?.find(
      (item) => item.name === response?.AutoId?.TriggerAction?.ActionName
    );

    const formattedData = {
      ...response,
      MethodId: {
        ...response?.MethodId,
        work_instruction_id:
          response?.MethodId?.work_instruction_id?.map((item: any) => ({
            photoref: {
              photos: (item.photoRef || []).map((photo: any) => ({
                photo: photo?.photos,
                details: photo?.Decription || "",
              })),
            },
            _id: item._id || "",
            Description: item.Description || "",
            optionselected: item.optionselected || "",
            materialId: item.materialId || [],
            manpowerId: item.manpowerId || [],
            toolsId: item.toolsId || [],
            machinaryId: item.machinaryId || [],
          })) || [],
        task_closing_requirement:
          response?.MethodId?.task_closing_requirement?.map((item: any) => ({
            photoref: {
              photos: (item.photoRef || []).map((photo: any) => ({
                photo: photo?.photos,
                details: photo?.Decription || "",
              })),
            },
            _id: item._id || "",
            Description: item.Description || "",
            optionselected: item.optionselected || "",
          })) || [],
      },
      AutoId: {
        TriggerResponse: {
          _id: response?.AutoId?.isFirst
            ? ""
            : response?.AutoId?.TriggerResponse?._id ?? "",
          name: response?.AutoId?.isFirst
            ? "This is a First Subtask"
            : response?.AutoId?.TriggerResponse?.name ?? "",
          isFirst: response?.AutoId?.isFirst ?? false,
        },
        TriggerAction: {
          ActionName: {
            id: matchingAction?.id ?? null,
            name: response?.AutoId?.TriggerAction?.ActionName ?? "",
          },
          ActionTime: response?.AutoId?.TriggerAction?.ActionTime ?? "",
        },
        ResponseTime: response?.AutoId?.ResponseTime ?? "",
      },
    };

    return formattedData;
  };

  const transformTaskData = (task: any) => {
    console.log("taskkk geting transformed", task);
    const extractIds = (data: any) => data?.map((item: any) => item._id) || [];
    const excludeIds = (data: any[]) => data.map(({ _id, ...rest }) => rest);
    const transformWorkInstructions = (workInstructions: any[]) => {
      return workInstructions.map((item: any) => {
        const isTimestampId = /^\d{13}$/.test(String(item._id)); // Check if _id is a timestamp

        return {
          ...(item._id && !isTimestampId && { _id: item._id }), // Include _id only if it's not a timestamp
          // _id: item?._id,
          Description: item.Description || "",
          optionselected: item.optionselected?.toLowerCase() || "",
          departmentId: extractIds(item.departmentId),
          designationId: extractIds(item.designationId),
          materialId: extractIds(item.materialId),
          manpowerId: extractIds(item.manpowerId),
          toolsId: extractIds(item.toolsId),
          machinaryId: [...extractIds(item.machinaryId)],
          photoRef:
            (item.photoref?.photos?.length &&
              item.photoref?.photos?.map((photo: any) => ({
                photos: photo?.photo || "",
                Decription: photo?.details || "",
              }))) ||
            [],
        };
      });
    };

    const transformTaskClosing = (taskClosing: any[]) => {
      return taskClosing.map((item: any) => {
        const isTimestampId = /^\d{13}$/.test(String(item._id));
        return {
          ...(item._id && !isTimestampId && { _id: item._id }),
          Description: item.Description,
          optionselected: item.optionselected?.toLowerCase() || "",
          photoRef:
            (item.photoref?.photos?.length &&
              item.photoref?.photos?.map((photo: any) => ({
                photos: photo?.photo || "",
                Decription: photo?.details || "",
              }))) ||
            [],
        };
      });
    };

    const transformReporters = (Reporters: any[]) => {
      return Reporters.map((item: any) => {
        const isTimestampId = /^\d{13}$/.test(String(item._id));

        return {
          ...(item._id && !isTimestampId && { _id: item._id }),
          Level: item?.Level,
          designationId: Array.isArray(item?.designationId)
            ? extractIds(item.designationId)
            : [item.designationId], // Ensure designationId is always an array
        };
      });
    };

    return {
      _id: task._id,
      name: task.name,
      Unit: task.Unit,
      Description: task.Description,
      subtaskWeighatages: task.subtaskWeighatages,
      Tracking: task.Tracking,
      DepartmentId: extractIds(task.DepartmentId),
      DesignationId: extractIds(task.DesignationId),
      MaterialId: extractIds(task.MaterialId),
      ToolId: extractIds(task.ToolId),
      MachinaryId: extractIds(task.MachinaryId),
      ManpowerId: extractIds(task.ManpowerId),
      AdminId: extractIds(task.AdminId),
      Reporters: transformReporters(task.ReporterId?.Reporter || []),
      AssigneeId: extractIds(task.AssigneeId) || [],
      WorkInstructions: transformWorkInstructions(
        task.MethodId?.work_instruction_id || []
      ),
      TaskClosing: transformTaskClosing(
        task.MethodId?.task_closing_requirement || []
      ),
      ControlPlan: excludeIds(task?.MethodId?.Controlplan || []),
      Failuremode: excludeIds(task?.MethodId?.Failuremode || []),
      TriggerEvent: {
        TriggerResponse:
          task?.AutoId?.TriggerResponse?.name === "This is a First Subtask"
            ? task?._id
            : task?.AutoId?.TriggerResponse?._id || null,
        ActionName: task?.AutoId?.TriggerAction?.ActionName?.name ?? "",
        ActionTime: Number(task?.AutoId?.TriggerAction?.ActionTime) ?? 0,
        ResponseTime: Number(task?.AutoId?.ResponseTime) ?? 0,
        isFirst:
          task?.AutoId?.TriggerResponse?.name === "This is a First Subtask",
      },
      Tobedeleted: {
        workinstruction: task?.Tobedeleted?.workinstruction || [],
      },
    };
  };
  // const workinstructiondelete = useAppSelector(
  //   (state) => state.WorkInstructionReducer.workinstruction
  // );

  const example = useCallback(async () => {
    console.log("update");
    if (!subTaskDataRef.current) return;
    const transformedData = transformTaskData(subTaskDataRef.current);
    const state = store.getState(); // Get the current state
    // let workinstructiondelete = state.WorkInstructionReducer.workinstruction;

    // const uniqueArray = (arr: string) => [...new Set(arr)];

    if (!finalSubtaskIdRef.current) {
      console.log("update");
      return;
    }

    try {
      const response = await updateSubtask({
        SubtaskId: finalSubtaskIdRef.current,
        name: transformedData.name,
        Unit: transformedData.Unit,
        Description: transformedData.Description,
        subtaskWeighatages: transformedData.subtaskWeighatages,
        Tracking: transformedData.Tracking,
        machinaryId: transformedData.MachinaryId,
        toolId: transformedData.ToolId,
        manpowerId: transformedData.ManpowerId,
        materialId: transformedData.MaterialId,
        AdminId: transformedData.AdminId,
        AssigneeId: transformedData.AssigneeId,
        Reporters: transformedData.Reporters,
        WorkInstructions: transformedData.WorkInstructions,
        TaskClosing: transformedData.TaskClosing,
        Failuremode: transformedData.Failuremode,
        ControlPlan: transformedData.ControlPlan,
        Tobedeleted: {
          workinstruction: transformedData?.Tobedeleted?.workinstruction,
        },
        TriggerEvent: transformedData.TriggerEvent,
      }).unwrap();
    } catch (error) {
      console.error("Task update failed:", error);
      throw error;
    }
  }, [finalSubtaskId]);

  const callStatusApi = async () => {
    await handleUpdateStatus({ status: "false", taskid: taskId! });
  };

  useEffect(() => {
    isApiCallRef.current = isApiCall;
  }, [isApiCall]);

  useEffect(() => {
    if (isApiCall && !isEdit) {
      try {
        (async () => {
          await example();
          const result = await handleGetSubTask(finalSubtaskId!);

          if (result.data) {
            const formattedData = transformFunctionForGet(
              result.data.data.response
            );

            saveSyncData(formattedData, "time", "SubTaskForm", false, dispatch);
          }
        })();
        dispatch(setChangeAPiFlag(false));
      } catch (error: any) {
        console.log("error message", error.message);
      }
    }
  }, [isEdit, finalSubtaskId]);

  useEffect(() => {
    subTaskDataRef.current = subTaskData;
  }, [subTaskData]);

  console.log("taskClosing", subTaskData);

  useEffect(() => {
    const trackingFirst = subTaskData?.Tracking?.charAt(0)?.toUpperCase();
    const trackingLast = subTaskData?.Tracking?.slice(1);
    setTracking((trackingFirst ?? "").concat(trackingLast ?? ""));
  }, [subTaskData]);

  const getDatafromDb = async (subtaskid: string) => {
    const dbName = await initializeDatabase("SubTaskForm");
    const fetchedData = await window.electron.getDataById({
      dbName,
      id: subtaskid,
    });
    console.log("response", fetchedData);
    if (!fetchedData.length && navigator.onLine) {
      const result = await handleGetSubTask(subtaskid);
      console.log("formattedData", result);
      if (result.data && result.data.data.response) {
        const formattedData = transformFunctionForGet(
          result.data.data.response
        );

        if (subtaskid === formattedData._id) {
          dispatch(setcurrentSubtaskData(formattedData));
        }

        saveSyncData(formattedData, "time", "SubTaskForm", false, dispatch);
      }
      return;
    }
    if (
      fetchedData[0]?.AutoId?.TriggerResponse?.name ===
      "This is a First Subtask"
    ) {
      dispatch(changeActiveState({ active: false }));
    }

    dispatch(resetCurrentSubtaskData());
    dispatch(setWorkInstructionDeleteEmpty());
    console.log("fetchedData", fetchedData);
    dispatch(setcurrentSubtaskData(fetchedData[0]));
  };

  useEffect(() => {
    setFinalSubtaskId(subtaskId || null);
    dispatch(closePopup("TEForm"));
    dispatch(changeActiveState({ active: true }));
    dispatch(resetInputValues());
    dispatch(
      setTriggerFormData({
        TriggerAction: {
          ActionName: null,
          ActionTime: "",
        },
        TriggerResponse: null,
        ResponseTime: "",
      })
    );
    SetisEdit(false);
  }, [location.pathname]);

  
  const isEditRef = useRef(isEdit);

  useEffect(() => {
    isEditRef.current = isEdit;
  }, [isEdit]);

  useEffect(() => {
    if (subtaskId !== finalSubtaskId) {
      setFinalSubtaskId(subtaskId || null);
    }

    return () => {
      const cleanupSubtaskId = finalSubtaskIdRef?.current!;

      (async () => {
        console.log("isApiCallRef", isApiCallRef.current);

        try {
          if (isApiCallRef.current) {
            await example();
            const result = await handleGetSubTask(cleanupSubtaskId);
            if (result.data) {
              const formattedData = transformFunctionForGet(
                result.data.data.response
              );

              await saveSyncData(
                formattedData,
                "time",
                "SubTaskForm",
                false,
                dispatch
              );
            }
            dispatch(setChangeAPiFlag(false));
          }
          if (isEditRef.current) {
            await callStatusApi();
          }
        } catch (error) {
          console.error("Error during cleanup sync:", error);
        }
      })();
    };
  }, [subtaskId]);

  // useEffect(() => {
  //   return () => {
  //     callStatusApi();
  //   };
  // }, [subtaskId]);

  useEffect(() => {
    if (finalSubtaskId) {
      getDatafromDb(finalSubtaskId);
      finalSubtaskIdRef.current = finalSubtaskId!;
    }
  }, [finalSubtaskId]);

  useEffect(() => {
    finalTaskIdRef.current = taskId!;
  }, [taskId]);

  useEffect(() => {
    handleSyncRef.current = async () => {
      if (isCalledRef.current) return;
      isCalledRef.current = true;

      try {
        if (!isApiCallRef.current) {
          await window.electron.syncComplete();
          return;
        }

        console.log("agyaaaaaa");

        await example();

        await handleUpdateStatus({
          status: "false",
          taskid: finalTaskIdRef?.current!,
        });

        const result = await handleGetSubTask(finalSubtaskIdRef.current!);

        if (result.data?.data?.response) {
          const formattedData = transformFunctionForGet(
            result.data?.data?.response
          );

          await saveSyncData(
            formattedData,
            "time",
            "SubTaskForm",
            false,
            dispatch
          );
        }
      } catch (error) {
        console.error("SubtaskForm - error during sync:", error);
      } finally {
        isCalledRef.current = false;
        await window.electron.syncComplete(); // notify Electron to quit
      }
    };
  }, [example, dispatch, location.pathname]);

  useEffect(() => {
    window.electron.setSyncContext("subtask");
    const stableHandler = () => {
      handleSyncRef.current?.();
    };

    window.electron.removeAllSubtaskSyncListeners();
    window.electron.onTriggerSubtaskSync(stableHandler);

    return () => {
      window.electron.removeTriggerSubtaskSync(stableHandler);
      window.electron.setSyncContext(null);
    };
  }, []);

  return (
    <>
      <div className={styles.subtaskcreation_container}>
        <SubTaskCreationHeader
          isEdit={isEdit}
          name={subTaskData?.name || ""}
          onclick={() => {
            if (!navigator.onLine) {
              showToast({
                messageContent: "Oops! No Internet Connection",
                type: "danger",
              });
              return;
            }
            handleToggleisEdit();
          }}
          unit={subTaskData?.Unit || ""}
          Description={subTaskData?.Description}
          weightage={subTaskData?.subtaskWeighatages || 0}
          tracking={tracking}
        />
        <div className={styles.subtaskcreation_form_container}>
          <SubtaskCreationMasters isEdit={isEdit} />
          <div className={styles.subtaskcreation_line_container}>
            <span className={styles.dottedline_wrapper}></span>

            <SuryconLogo />
            <span className={styles.dottedline_wrapper}></span>
          </div>
          <SubaskCreationDesignation isEdit={isEdit} />
          {/* <div className={styles.subtaskcreation_line_container}>
            <span className={styles.dottedline_wrapper}></span>
            <SuryconLogo />
            <span className={styles.dottedline_wrapper}></span>
          </div>
          <SubTaskCreationAutomation isEdit={isEdit} /> */}
          <div className={styles.subtaskcreation_line_container}>
            <span className={styles.dottedline_wrapper}></span>
            <SuryconLogo />
            <span className={styles.dottedline_wrapper}></span>
          </div>
          <SubTaskCreationMethod isEdit={isEdit} />
        </div>
      </div>

      {/* popups are written here */}
      {popups["TEForm"] && (
        <TEFormComponent
          mode={triggerMode === "edit" ? "edit" : "add"}
          header={
            triggerMode === "edit" ? "Edit Trigger Event" : "Add Trigger Event"
          }
          button2Type={"Next"}
          button1Type={"Cancel"}
          button1Content="Cancel"
          button2Content={triggerMode === "edit" ? "Update" : "Add"}
          callBackCancel={() => {
            dispatch(closePopup("TEForm"));
            dispatch(setIsOnlyResponse(false));
            dispatch(setTriggerMode(""));
            dispatch(
              setTriggerFormData({
                TriggerAction: {
                  ActionName: null,
                  ActionTime: "",
                },
                TriggerResponse: null,
                ResponseTime: "",
              })
            );
            dispatch(resetInputValues());
          }}
        />
      )}
    </>
  );
};

export default SubTaskCreationForm;
