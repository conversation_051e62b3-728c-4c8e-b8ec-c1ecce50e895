/*  AUTHOR NAME : CHARVI */
.addTaskForm_container {
  position: fixed;
  top: 7rem;
  right: 1.7rem;
  transform: translate(0%, 0%);
  height: calc(100% - 8.5rem);
  background: var(--blur-background);
  padding: 1.25rem 0.9rem;
  backdrop-filter: blur(60px);
  box-shadow: 0px 4px 40px 0px #00000080;
  border-radius: 0.5rem;
  z-index: 70;
  width: 33.6rem;
  /* min-height: 85vh; */
  border-radius: 2.6rem;
  animation: slideIn 0.5s ease-out;
}
[tabindex]:focus {
  outline: none;
}

.addtaskform_f2_inputs{
  margin: 0 0.6rem;
}
.addtaskform_qtyinputs{
  margin: 0 0.6rem;
}
@keyframes slideIn {
  from {
    transform: translate(100%, 0%);
  }

  to {
    transform: translate(0%, 0%);
  }
}

@keyframes slideOut {
  from {
    transform: translate(0%, 0%);
  }

  to {
    transform: translate(100%, 0%);
  }
}

.addTaskForm_container.closing {
  animation: slideOut 0.5s ease-out;
}

/* 


@media only screen and (max-width: 1280px) {
  .addTaskForm_container {
    width: 31rem;
  }
} */

/* @media only screen and (max-width: 1636px) {

} */
.addTaskForm_header {
  color: var(--primary_color);
  display: flex;
  justify-content: center;
  padding: 0.6rem;
}


.addTaskForm_datainputs {
  height: calc(100% - 7.25rem);
  overflow-y: auto;
}

.closeButton {
  position: absolute;
  top: 1.5em;
  right: 0.625rem;
  padding: 1rem;
  background: transparent;
  border: none;

  cursor: pointer;
}

.addtaskform_btngrp {
  display: flex;
  justify-content: center;
  gap: 1rem;
  padding: 1.5rem 0 1.5rem 0;
}

.addtaskform_qtyinputs {
  display: flex;
  gap: 1rem;
  /* flex-wrap: wrap; */
  justify-content: space-between;
  position: relative;
}

.subfeild_container {
  background-color: var(--blur_background);
  border-radius: 1.5rem;
  border: 1px solid var(--text-black-28);
  margin: 0.8rem;

  min-height: 3.375rem;
}

.subfeild_header {
  display: flex;
  justify-content: space-between;
  padding: 1rem 1rem 0 1rem;
  align-items: center;
  color: var(--text-black-60);
}

.subfeild_addicon {
  cursor: pointer;

  height: 18px;
}

.subcategories {
  box-sizing: border-box;
  padding: 0 1rem 1rem 1rem;
  display: flex;
  flex-wrap: wrap;
  gap: 0.7rem;
}

.summaryDivData {
  display: flex;
  align-items: center;
  gap: 0.6rem;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}

.summaryDataContent {
  display: flex;
  flex-direction: column;
  background: #ffffff99;
  /* this bg color not in root */
  border-radius: 0.75rem;
  padding: 1rem;
  white-space: normal;
  line-height: 1.363rem;
  text-align: left;
  color: var(--text-black-87);
  flex: 1;
  max-width: 100%;
  width: 100%;
  margin: 0.6rem;
}

.summaryicon {
  width: 1.5rem;
  padding-left: 0.3rem;
}

.categoryitems_list {
  padding-right: 1.5rem;
}

.summaryheadings {
  color: #00000047;

  line-height: 1.363rem;
  text-align: left;
}

.taskitems {
  display: flex;

  overflow-wrap: break-word;
  word-wrap: break-word;
  white-space: normal;
  flex-wrap: wrap;
  color: #000000de;

  line-height: 2rem;
}

.flexContainer {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  width: 100%;
  max-width: 100%;
}

.flexContainer > .summaryDivData {
  flex: 1;
  min-width: calc(50% - 0.5rem);
  box-sizing: border-box;
}

.unitpopup_container {
  min-width: 14rem;
  max-height: 10rem;
  border-radius: 24px;
  overflow-y: auto;
  /* backdrop-filter: blur(100px); */
  box-shadow: 0px 0px 4px 0px #91a1a1bf;
  border: 1px solid;
  border-image-source: linear-gradient(
    130.72deg,
    rgba(237, 231, 231, 0.07) -16.06%,
    rgba(251, 251, 251, 0.05) 82.03%
  );
  z-index: 1;
  position: fixed;
  right: 33px;
}

.unitpopup_list {
  list-style-type: none;
  position: relative;
  /* text-align: center; */
  line-height: 18px;
  color: var(--text-black-60);
  background: var(--main_background);
  max-height: 9rem;
  overflow: auto;
}

.unitpopup_list li {
  padding: 0.5rem;
}

.unit {
  cursor: pointer;
  margin: 10px;
}

.unit:hover {
  border: 1px solid;
  border-image-source: linear-gradient(
    130.72deg,
    rgba(237, 231, 231, 0.07) -16.06%,
    rgba(251, 251, 251, 0.05) 82.03%
  );
  /* color: var(--primary_color); */
  backdrop-filter: blur(100px);
  box-shadow: 0px 0px 4px 0px #91a1a1bf;
  border-radius: 24px;
}

.unitselected {
  border: 1px solid;

  color: var(--primary_color);
  /* backdrop-filter: blur(100px); */
  box-shadow: 0px 0px 4px 0px #91a1a1bf;
  border-radius: 24px;
  border-color: var(--primary_color);
}


.unitPopup {
  position: absolute;
  left: 53%;
  top: 96%;
  z-index: 10;
  transform: translateY(0rem); 
  opacity: 1;
  transition: transform 0.3s ease-in-out, 
}

.unitPopup.hidden {
  transform: translateY(-2rem); 
  opacity: 0;
}