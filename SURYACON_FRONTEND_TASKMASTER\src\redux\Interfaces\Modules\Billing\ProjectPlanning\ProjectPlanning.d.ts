// interface for project planning slice
export interface InitialStateProp {
  editMode: boolean;
  planningProgress: any;
    isEdit: any;
  Todeletedata: {
    Tasks: [];
    Subtasks: string[];
    machinaries: string[];
    tools: string[];
    manpowers: string[];
    materials: string[];
  };
  deletingtask:string[];
  allSubtasksdata: any[] ;
  progressTowerData: Task[] | null;
  selectedMaterialId: string | null;
  selectedLocationId: string | null;
  selectedLocationTaskId: string | null;
  selectedLocationSubTaskId: string | null;
  selectedLocationTaskIdData: FinalData | null;
  currentTaskbasicDetails: any;
  allSubtasksBasicDetails: any;
  curretSelectedData: any;
  selectedprojectRate: string;
  selectedprojectestimate: string;
  towerRoutesRefreshFlag: number;
}
export interface TransformedRoutesProp {
  data: Task[];
}
