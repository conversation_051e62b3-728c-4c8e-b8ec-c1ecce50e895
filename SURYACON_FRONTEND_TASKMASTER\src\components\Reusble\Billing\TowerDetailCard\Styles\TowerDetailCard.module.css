.towercard_outer_container {
  width: 100%;
  border-radius: 2.3rem;
  position: relative;
  z-index: 0;
  padding: 0.3rem;
  background-color: var(--primary_background) !important;
  box-shadow: 0px 0px 3px 0px #91a1a180;
  overflow: visible;
  margin: auto;
}

.towercard_inner_container {
  height: 100%;
  width: 100%;
  /* overflow: hidden; */
  background: #ffffff;
  box-shadow: 0rem 0rem 0.1875rem 0rem #91a1a180;
  border-radius: 2.1rem;
  padding: 1.5rem;
  position: relative;
}

.towercard_top_container {
  display: flex;
  justify-content: space-between;
  position: relative;
  align-items: center;
  bottom: 0.5rem;
}

.tower_card_title {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.tower_card_topright_container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}
.progress_card_top_left_container {
  display: flex;
  gap: 0.5rem;
  align-items: center;
  max-width: 70%;
}
.tower_card_percentage_card {
  height: 1.75rem;
  width: 5rem;
  background-color: var(--primary_background);
  padding: 0.25rem;
  border-radius: 6.25rem;
  text-align: center;

  box-shadow: var(--extra-shdow-second);
}

.project_sft_card {
  width: 5.6875rem;
  height: 1.75rem;
  background-color: var(--primary_background);

  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0.2rem;
  border-radius: 6.25rem;
  text-align: center;
}
.progress_card_title {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.towercard_lower_container {
  padding-top: 0.8rem;
}

.towercarditem_container {
  width: 100%;
  border: 1px solid;
  border-image-source: linear-gradient(
    130.72deg,
    rgba(237, 231, 231, 0.07) -16.06%,
    rgba(251, 251, 251, 0.05) 82.03%
  );

  box-shadow: 0px 0px 3px 0px #91a1a180;

  border-radius: 0.75rem;
}

.towercarditem_content {
  display: flex;
  gap: 0.625rem;
  padding: 0.625rem;
  align-items: center;
}

.towercarditem_icon {
  background-color: var(--primary_background);
  border-radius: 0.25rem;
  width: 2.3rem;
  height: 2.3rem;
  text-align: center;
  padding-top: 0.313rem;
}

.towercarditem_head {
  color: var(--text-black-60);
}
.towercarditem_num {
  color: var(--text-black-87);
}

.towercarditem_details {
  text-align: left;
}

.towercard_lower_container {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  flex-grow: 1;
  gap: 0.9rem;
  justify-items: center;
}
@media (max-width: 1650px) {
  .towercard_outer_container {
    min-width: 22rem;
  }
}
/* @media (max-width: 1650px) {
  .towercard_outer_container {
    min-width: 22rem;
  }
  .towercarditem_container {
    width: 7rem;
    overflow: ellipsis;
    text-overflow: ellipsis;
  }
} */

.dots_container {
    cursor: pointer;
    DISPLAY: flex;
    align-items: center;
}

/* styles for two dots end by rattandeep singh */
/* styles for action card start by rattandeep singh */
.cat_popup {
  position: absolute;
  top: 2.5rem;
  right: 0;
  padding: 1rem;
  z-index: 10;
  background-color: var(--white-50-background);
  color: #00000099 !important;

  border: 1px solid;
  border-radius: 20px;
  border-image-source: linear-gradient(
    130.72deg,
    rgba(237, 231, 231, 0.07) -22.43%,
    rgba(251, 251, 251, 0.05) 75.66%
  );
  backdrop-filter: blur(150px);

  box-shadow: 0px 4px 20px 0px #00000033;
}

.cat_popup_view,
.cat_popup_edit,
.cat_popup_dlt {
  display: flex;
  /* padding: 0.6rem 0.7rem; */
  gap: 0.6rem;
  color: var(--text-black-60);
  align-items: center;
  background-color: var();
  /* background-color: var(--main_background); */
  padding: 0.25rem;
  border-radius: 50px;
  transition: background-color 0.2s ease-in-out;
}

@keyframes animateIn {
  0% {
    width: 0rem;
    height: 0rem;
    left: 1.5rem;
  }
  20% {
    width: 2.25rem;
    height: 1.7rem;
    left: 1.3rem;
  }
  100% {
    width: calc(100% - 2.2rem);
    height: 2rem;
    left: 1.1rem;
  }
  }
  
  @keyframes animateOut {
  0% {
    width: calc(100% - 2.2rem);
    height: 2rem;
    left: 1.1rem;
  }
  40% {
    width: 2.25rem;
    height: 2rem;
    left: 1.3rem;
  }
  100% {
    width: 0rem;
    height: 0rem;
    left: 1.5rem;
  }
  } 

.cat_popup_viewicon {
  background: var(--primary_background);
  width: 28px;
  height: 28px;
  border-radius: 29px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.cat_popup_editicon {
  background: #fff6d9;
  width: 28px;
  height: 28px;
  border-radius: 29px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  z-index: 2;
}

.cat_popup_transition_div_edit {
  position: absolute;
  height: 0rem;
  width: 0rem;
  border-radius: 100px;
  z-index: 1;
  background-color: var(--extra_color);
  transition: all 0.3s ease-in-out;
  }
.cat_popup_edit>h4{
  position: relative;
  z-index: 2;
}

.cat_popup_edit.edit_hovered .cat_popup_transition_div_edit {
  animation: animateIn 0.3s ease-in-out forwards !important;
}

.cat_popup_edit.edit_notHovered .cat_popup_transition_div_edit {
  animation: animateOut 0.3s ease-in-out forwards !important;
}





.cat_popup_dlticon {
  background: #f6e6e6;

  width: 28px;
  height: 28px;
  border-radius: 29px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  z-index: 2;
}

.cat_popup_dlt>h4 {
  position: relative;
  z-index: 2;
}

.cat_popup_transition_div_delete {
  position: absolute;
  height: 0rem;
  width: 0rem;
  border-radius: 100px;
  z-index: 1;
  background-color:  #f6e6e6;
  transition: all 0.3s ease-in-out;
  
}

.cat_popup_dlt.dlt_hovered .cat_popup_transition_div_delete {
  animation: animateIn 0.3s ease-in-out forwards !important;
}

.cat_popup_dlt.dlt_notHovered .cat_popup_transition_div_delete {
  animation: animateOut 0.3s ease-out forwards !important;
}  


@media (max-width: 1818px) {
  
  
  
  .towercard_inner_container{
    padding: 1.5rem 1rem !important;
  }

  .project_sft_card {
    width: 4rem;

  }
  .tower_card_percentage_card {
    height: 1.75rem;
    width: 3.5rem;
  }
}


