import { AppDispatch } from "./../../redux/store";

import { saveSyncData, saveSyncTime } from "../BackupFunctions/BackupFunctions";
import PQueue from "p-queue";
import {
  getTime,
  initializeDatabase,
  masterFilterImages,
} from "../../functions/functions";
import { BillingApi } from "../../redux/api/Modules/Billing/Billingapi";
import { Responses } from "./../../interfaces/Modules/TaskMaster/TaskMasterInterface/TaskMaster.d";

export const ToolCategory = async (
  dispatch: AppDispatch,
  categoryIds: string[]
): Promise<void> => {
  console.log(categoryIds, "Processing category IDs");

  const date = await getTime("ToolCategory");

  let time = "";
  if (date) {
    time = date.date;
  }

  const result = await dispatch(
    BillingApi.endpoints.getToolsCategories.initiate(time as string, {
      forceRefetch: true,
    })
  );

  console.log(result, "cat result");

  if (
    result?.data?.data?.response &&
    result?.data?.data?.response?.length > 0
  ) {
    saveSyncTime(result?.data?.data?.date, "ToolCategory");
    saveSyncData(
      result?.data?.data?.response?.map((el: any) => {
        return {
          ...el,
          lowercase_name: el.name ? el.name.toLowerCase() : "",
        };
      }),
      time,
      "ToolCategory",
      false,
      dispatch
    );
  }
};

export const ToolDesignation = async (
  dispatch: AppDispatch,
  Ids: string[],
  parentIds: string[]
): Promise<string | void> => {
  const taskQueue = new PQueue({ concurrency: 1 });

  const date = await getTime("ToolDesignation");
  let time = "";
  if (date) {
    time = date.date;
  }

  if (Ids.length === 0 || (parentIds && parentIds.length === 0)) {
    return "No Category Id found";
  }

  for (const data of parentIds) {
    taskQueue.add(async () => {
      if (!data) {
        return null;
      }
      try {
        const [toolformdata]: any = await Promise.all([
          dispatch(
            BillingApi.endpoints.getToolsByCartegoryId.initiate(
              {
                category_id: data,
                time: time,
              },
              {
                forceRefetch: true,
              }
            )
          ),
        ]);

        console.log(toolformdata, "tools form data>>>>>>>>");

        const transformedData = [
          {
            ...toolformdata?.data?.data?.response?.[0],
            _id: toolformdata?.data?.data?.response?.[0]?.categoryId,
            name: toolformdata?.data?.data?.response?.[0]?.name,
          },
        ];

        if (toolformdata.data && toolformdata?.data?.data) {
          const getLocalDbData = async () => {
            try {
              const dbName = await initializeDatabase("ToolDesignation");
              const fetchedData = await window.electron.allbulkGet({
                dbName,
              });

              return fetchedData.docs;
            } catch (error) {
              console.error("Error fetching project data", error);
            }
          };
          const localdbcatData = await getLocalDbData();

          const localDbData = localdbcatData?.filter(
            (data: any) =>
              data.categoryId ===
              toolformdata?.data?.data?.response?.[0]?.categoryId
          )?.[0]?.data;

          const newdata = toolformdata?.data?.data?.response?.[0]?.data;

          console.log("check images", newdata, localdbcatData);
          console.log("check images", newdata, localdbcatData);
          const FilterimagesResult = await masterFilterImages({
            newdata,
            localDbData,
            dispatch,
          });

          console.log(
            "this is material designation images",
            FilterimagesResult
          );
          saveSyncTime(toolformdata?.data?.data?.date, "ToolDesignation");
          saveSyncData(
            transformedData?.map((el: any) => {
              return {
                ...el,
                lowercase_name: el.name ? el.name.toLowerCase() : "",
              };
            }),
            "time",
            "ToolDesignation",
            false,
            dispatch
          );
        }
      } catch (error) {
        console.error(`Error  task backup[]`, error);
      }
    });
  }

  await taskQueue.onIdle();
};

export const MaterialCategory = async (
  dispatch: AppDispatch,
  categoryIds: string[]
): Promise<void> => {
  console.log(categoryIds, "Processing category IDs");

  const date = await getTime("MaterialCategory");

  let time = "";
  if (date) {
    time = date.date;
  }

  console.log("date00", time);
  const result = await dispatch(
    BillingApi.endpoints.getMaterialsCategories.initiate(time as string, {
      forceRefetch: true,
    })
  );

  console.log("mat result", result);

  if (
    Array.isArray(result?.data?.data?.response) &&
    result?.data?.data?.response.length > 0
  ) {
    saveSyncTime(result?.data?.data?.date, "MaterialCategory");
    saveSyncData(
      result?.data?.data?.response?.map((el: any) => {
        return {
          ...el,
          lowercase_name: el.name ? el.name.toLowerCase() : "",
        };
      }),
      time,
      "MaterialCategory",
      false,
      dispatch
    );
  }
};

export const MaterialDesignation = async (
  dispatch: AppDispatch,
  Ids: string[],
  parentIds: string[]
): Promise<string | void> => {
  const taskQueue = new PQueue({ concurrency: 1 });

  if (Ids.length === 0 || (parentIds && parentIds?.length === 0)) {
    return "No Category Id found";
  }
  const date = await getTime("MaterialDesignation");
  let time = "";
  if (date) {
    time = date.date;
  }
  for (const data of parentIds) {
    taskQueue.add(async () => {
      if (!data) {
        return null;
      }
      try {
        const [materialdata]: any = await Promise.all([
          dispatch(
            BillingApi.endpoints.getMaterialsByCategoryId.initiate(
              {
                category_id: data,
                time: time,
              },
              {
                forceRefetch: true,
              }
            )
          ),
        ]);
        console.log(materialdata?.data?.data?.response, "thisi sdfasdfasdta");
        if (
          Array.isArray(materialdata?.data?.data?.response) &&
          materialdata?.data?.data?.response.length > 0
        ) {
          const getLocalDbData = async () => {
            try {
              const dbName = await initializeDatabase("MaterialDesignation");
              const fetchedData = await window.electron.allbulkGet({
                dbName,
              });

              return fetchedData.docs;
            } catch (error) {
              console.error("Error fetching project data", error);
            }
          };
          const localDbData = await getLocalDbData();
          console.log(localDbData, "getLocalDbData");
          const newdata = materialdata?.data?.data?.response;
          const FilterimagesResult = await masterFilterImages({
            newdata,
            localDbData,
            dispatch,
          });

          console.log(
            "this is material designation images",
            FilterimagesResult
          );
          console.log(materialdata, "check for work");

          saveSyncTime(materialdata?.data?.data?.date, "MaterialDesignation");

          saveSyncData(
            materialdata?.data?.data?.response.map((el: any) => {
              return {
                ...el,
                lowercase_name: el.name ? el.name.toLowerCase() : "",
              };
            }),
            "time",
            "MaterialDesignation",
            false,
            dispatch
          );
        }
      } catch (error) {
        console.error(`Error  task backup[]`, error);
      }
    });
  }

  await taskQueue.onIdle();
};

export const Manpowercategory = async (
  dispatch: AppDispatch,
  categoryIds: string[]
): Promise<void> => {
  console.log(categoryIds, "Processing category IDs");

  const date = await getTime("Manpowercategory");

  let time = "";
  if (date) {
    time = date.date;
  }

  const result = await dispatch(
    BillingApi.endpoints.getManpowerCategories.initiate(time as string, {
      forceRefetch: true,
    })
  );

  console.log("this is the result", result);

  if (
    Array.isArray(result?.data?.data?.response) &&
    result?.data?.data?.response.length > 0
  ) {
    saveSyncTime(result?.data?.data?.date, "Manpowercategory");
    saveSyncData(
      result?.data?.data?.response.map((el: any) => {
        return {
          ...el,
          lowercase_name: el.name ? el.name.toLowerCase() : "",
        };
      }),
      time,
      "Manpowercategory",
      false,
      dispatch
    );
  }
};

export const Manpowerdesignation = async (
  dispatch: AppDispatch,
  Ids: string[],
  parentIds: string[]
): Promise<string | void> => {
  const taskQueue = new PQueue({ concurrency: 1 });

  if (Ids.length === 0 || (parentIds && parentIds.length === 0)) {
    return "No Category Id found";
  }
  const date = await getTime("Manpowerdesignation");

  let time = "";
  if (date) {
    time = date.date;
  }

  for (const data of parentIds) {
    taskQueue.add(async () => {
      if (!data) {
        return null;
      }
      try {
        const [manpowerdata]: any = await Promise.all([
          dispatch(
            BillingApi.endpoints.getManpowerByCartegoryId.initiate(
              {
                category_id: data,
                time: time,
              },
              {
                forceRefetch: true,
              }
            )
          ),
        ]);

        console.log(manpowerdata?.data, "check for work");
        if (
          Array.isArray(manpowerdata?.data?.data?.response) &&
          manpowerdata?.data?.data?.response?.length > 0
        ) {
          saveSyncTime(manpowerdata?.data?.data?.date, "Manpowerdesignation");
          saveSyncData(
            manpowerdata?.data?.data?.response.map((el: any) => {
              return {
                ...el,
                lowercase_name: el.name ? el.name.toLowerCase() : "",
              };
            }),
            "time",
            "Manpowerdesignation",
            false,
            dispatch
          );
        }
      } catch (error) {
        console.error(`Error  task backup[]`, error);
      }
    });
  }

  await taskQueue.onIdle();
};

export const MachinaryCategory = async (
  dispatch: AppDispatch,
  categoryIds: string[]
): Promise<void> => {
  console.log(categoryIds, "Processing category IDs");

  const date = await getTime("MachineryCategory");

  let time = "";
  if (date) {
    time = date.date;
  }
  console.log("date00", date);
  const result = await dispatch(
    BillingApi.endpoints.getMachineryCategories.initiate(time as string, {
      forceRefetch: true,
    })
  );

  console.log(result, "this is resuly");

  if (
    result?.data?.data?.response &&
    result?.data?.data?.response?.length > 0
  ) {
    console.log("i am in sdfsdfsdf", result);
    saveSyncTime(result?.data?.data?.date, "MachineryCategory");
    saveSyncData(
      result?.data?.data?.response.map((el: any) => {
        return {
          ...el,
          lowercase_name: el.name ? el.name.toLowerCase() : "",
        };
      }),

      time,
      "MachinaryCategory",
      false,
      dispatch
    );
  }
};

export const MachinaryDesignation = async (
  dispatch: AppDispatch,
  Ids: string[],
  parentIds: string[]
): Promise<string | void> => {
  const taskQueue = new PQueue({ concurrency: 1 });

  if (Ids.length === 0 || (parentIds && parentIds.length === 0)) {
    return "No Category Id found";
  }
  const date = await getTime("MachinaryDesignation");

  let time = "";
  if (date) {
    time = date.date;
  }
  for (const data of parentIds) {
    taskQueue.add(async () => {
      if (!data) {
        return null;
      }
      try {
        const [machinerydata]: any = await Promise.all([
          dispatch(
            BillingApi.endpoints.getMachineryByCartegoryId.initiate(
              {
                category_id: data,
                time: time,
              },
              {
                forceRefetch: true,
              }
            )
          ),
        ]);

        if (
          Array.isArray(machinerydata?.data?.data?.response) &&
          machinerydata?.data?.data?.response.length > 0
        ) {
          const getLocalDbData = async () => {
            try {
              const dbName = await initializeDatabase("MachinaryDesignation");
              const fetchedData = await window.electron.allbulkGet({
                dbName,
              });

              console.log(fetchedData.docs, "asdjfaksdjfaskd9");
              return fetchedData.docs;
            } catch (error) {
              console.error("Error fetching project data", error);
            }
          };

          const localDbData = await getLocalDbData();
          console.log(localDbData, "getLocalDbData");
          const newdata = machinerydata?.data?.data?.response;

          console.log("check images", newdata, localDbData);
          await masterFilterImages({
            newdata,
            localDbData,
            dispatch,
          });
          saveSyncTime(machinerydata?.data?.data?.date, "MachinaryDesignation");
          saveSyncData(
            machinerydata?.data?.data?.response.map((el: any) => {
              return {
                ...el,
                lowercase_name: el.name ? el.name.toLowerCase() : "",
              };
            }),

            time,
            "MachinaryDesignation",
            false,
            dispatch
          );
        }
      } catch (error) {
        // console.error(Error  task backup[], error);
      }
    });
  }

  await taskQueue.onIdle();
};

// export const getImageUrl= async ({
//   toAdd,
//   dispatch,

// }:{
//   toAdd:string[],
//   dispatch:AppDispatch
// }
// ): Promise<void> => {
//   const taskQueue = new PQueue({ concurrency: 1 });

//   for (const image of toAdd) {

//     taskQueue.add(async () => {
//       if (!image) {
//         return null;
//       }
//       try {
//         const [result] = await Promise.all([
//           dispatch(
//             TaskMasterApi.endpoints.getImageUrlApi.initiate(image, {
//               forceRefetch: true,
//             })
//           ).unwrap(),
//         ]);

//         console.log("image is here atleast", result)

//         //upload images from here
//         const resultaddimage=await window.electron.addNewImages({url:result?.data,name:image});

//       } catch (error) {
//         console.error(`Error  task backup[]`, error);
//       }
//     });
//   }

// }
export const Departments = async (dispatch: AppDispatch): Promise<void> => {
  const date = await getTime("Departments");

  let time = "";
  if (date) {
    time = date.date;
  }

  const result = await dispatch(
    BillingApi.endpoints.getDepartments.initiate("")
  );

  console.log("mat result", result);

  if (result?.data?.data && result?.data?.data?.length > 0) {
    // saveSyncTime(result?.data?.date, "MaterialCategory");
    saveSyncData(
      result?.data?.data,
      time,
      "Departments",
      false,

      dispatch
    );
  }
};
export const Designations = async (dispatch: AppDispatch): Promise<void> => {
  const date = await getTime("Departments");

  let time = "";
  if (date) {
    time = date.date;
  }

  const result = await dispatch(
    BillingApi.endpoints.getDesignations.initiate("")
  );

  console.log("mat result", result);

  if (result?.data?.data && result?.data?.data?.length > 0) {
    // saveSyncTime(result?.data?.date, "MaterialCategory");
    saveSyncData(result?.data?.data, time, "Designations", false, dispatch);
  }
};

// zvxcvzxcv
export const Designationmaster = async (
  dispatch: AppDispatch,
  Ids: string[],
  parentIds: string[]
): Promise<void> => {
  const date = await getTime("Designationmaster");
  let time = "";
  if (date) {
    time = date?.date;
  }
  console.log("DATA>>>:", date);
  console.log("ids>>", Ids, "parentsIds", parentIds, "dispatch", dispatch);

  console.log("logging before first check");
  const taskQueue = new PQueue({ concurrency: 1 });

  if (Ids.length === 0 || (parentIds && parentIds?.length === 0)) {
    return "No Category Id found";
  }
  console.log("check for work before for loop");
  for (const data of parentIds) {
    console.log("data in designationmaster", data);
    taskQueue.add(async () => {
      if (!data) {
        return null;
      }
      console.log("check for work before try catch");
      try {
        console.log("check for work in try catch", data);

        const designationdata = await dispatch(
          BillingApi.endpoints.getDepartmentDesignations.initiate({
            departmentId: data,
            sinceTime: time,
          })
        );

        
          // const newtime = new Date();
          // time = new Date(newtime.getTime() + (5.5 * 60 * 60 * 1000)).toISOString();
          console.log("time11", time);
          if(designationdata?.data?.data?.date){
            time = designationdata?.data?.data?.date;
          }

        console.log("time11", time);

        console.log(designationdata, "check for worktt");
        if (designationdata?.data && designationdata?.data?.data) {
          const getLocalDbData = async () => {
            try {
              const dbName = await initializeDatabase("Designationmaster");
              const fetchedData = await window.electron.allbulkGet({
                dbName,
              });

              return fetchedData.docs;
            } catch (error) {
              console.error("Error fetching project data", error);
            }
          };
          const localDbData = await getLocalDbData();
          console.log(localDbData, "getLocalDbData");
          const newdata = designationdata?.data?.data?.response;
          console.log("newdata in designation master", newdata);
          saveSyncTime(time, "Designationmaster");
          saveSyncData(
            newdata?.map((el: any) => {
              return {
                ...el,
                DepartmentId: el?.DepartmentId?._id || el?.DepartmentId,
                lowercase_name: el.name ? el.name.toLowerCase() : "",
              };
            }),
            time,
            "Designationmaster",
            false,
            dispatch
          );
        }
      } catch (error) {
        console.error(`Error  task backup[]`, error);
      }
    });
  }
  await taskQueue.onIdle();
};
export const Departmentmaster = async (
  dispatch: AppDispatch,
  Ids: string[],
  parentIds: string[]
): Promise<void> => {
  const date = await getTime("Departmentmaster");
  let time = "";
  if (date) {
    time = date.date;
  }
  console.log("ids>>", Ids, "parentsIds", parentIds, "dispatch", dispatch);

  console.log("logging before first check");
  const taskQueue = new PQueue({ concurrency: 1 });

  if (Ids.length === 0) {
    return "No Category Id found";
  }
  console.log("check for work before for loop");
  for (const data of Ids) {
    console.log("data in department", data);
    taskQueue.add(async () => {
      if (!data) {
        return null;
      }
      console.log("check for work before try catch");
      try {
        console.log("check for work in try catch", data);
        // const designationdata = await dispatch(
        //     BillingApi.endpoints.getDepartmentDesignations.initiate({
        //       departmentId: data,
        //     })
        //   );
        console.log('time logging',time)
        const departmentdata = await dispatch(
          BillingApi.endpoints.getAllDepartments.initiate({
            // departmentId: "6773931e7ef2dcfbd5164bf7",
            sinceTime: time,
          })
        );
         if(departmentdata?.data?.data?.date){
            time = departmentdata?.data?.data?.date;
          }

        console.log(departmentdata?.data?.data, "check for work>>");
        if (departmentdata?.data && departmentdata?.data?.data?.response) {
          const getLocalDbData = async () => {
            try {
              const dbName = await initializeDatabase("Departmentmaster");
              const fetchedData = await window.electron.allbulkGet({
                dbName,
              });

              return fetchedData.docs;
            } catch (error) {
              console.error("Error fetching project data", error);
            }
          };
          const localDbData = await getLocalDbData();
          console.log(localDbData, "getLocalDbData");
          const newdata = departmentdata?.data?.data?.response;
          console.log("newdata in department master", newdata);
          saveSyncTime(time, "Departmentmaster");
          saveSyncData(
            newdata?.map((el: any) => {
              return {
                ...el,
                // DepartmentId: el?._id,
                lowercase_name: el.name ? el.name.toLowerCase() : "",
              };
            }),
            time,
            "Departmentmaster",
            false,
            dispatch
          );
        }
      } catch (error) {
        console.error(`Error  task backup[]`, error);
      }
    });
  }
  await taskQueue.onIdle();
};
