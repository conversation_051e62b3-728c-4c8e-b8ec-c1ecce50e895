/* remove default  height and width , add padding in circle class :: <PERSON><PERSON> */
.circle {
    inline-size: 30px;
    block-size: 30px;
    background-color: var(--primary_background);
    border-radius: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}


.material_bubble {
    background-color: var(--primary_color);
    height: 1.5rem;
    width: 1.5rem;
}

.material_header_bubble {
    height: 2.125rem;
    width: 2.125rem;
}

.material_summary_circle {
    height: 1.6rem;
    width: 1.9rem;
    background-color: var(--primary_color);
    color: var(--text-white-100);
}
.sub_material_summary_circle{
    height: 1.6rem;
    width: 1.7rem;
    background-color: var(--primary_color);
    color: var(--text-white-100);
}


/* material card container box here buddy  */

.material_card_container_content_box {
    border-radius: 8px;
    height: 2.75rem;
    width: 2.75rem;
}

/* Styles for bubble in pm single progress card bubble start by rattan<PERSON><PERSON> singh */
.pm_tower_progress_bubble {
    background-color: var(--primary_background);
    color: var(--primary_color);
    height: 28px;
    width: 28px;
}

/* Styles for bubble in pm single progress card bubble end by rattan<PERSON><PERSON> singh */