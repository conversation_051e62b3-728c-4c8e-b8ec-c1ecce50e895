import { useEffect, useState } from "react";
import styles from "./Styles/ValuePopup.module.css";
import { useDispatch, useSelector } from "react-redux";
import {
  resetInputValues,
  setInputValue,
} from "../../../../redux/features/Modules/Reusble/floatinglabelslice";
import { RootState } from "../../../../redux/store";
import ValuePopupSummary from "./Subcomponents/ValuePopupSummary";
import { CloseIcon } from "../../../../assets/icons";
import FloatingLabelInput from "../FloatingLabel";
import Button from "../Button";
import RadioBtns from "../RadioBtns";
import { ValuePopupProps } from "../GlobalInterfaces/GlobalInterface";

function ValuePopup({ onClose, onSubmit }: ValuePopupProps) {
  const [isSummaryPage, setIsSummaryPage] = useState<boolean>(false);
  const [isClosing, setIsClosing] = useState<boolean>(false);
  const [categoryError, setCategoryError] = useState<boolean>(false);
  const [selectedCategory, setSelectedCategory] = useState<string>("");
  const [quantity, setQuantity] = useState<string>(""); // Local state for quantity
  const [error, setError] = useState<boolean>(false);

  const handleClose = () => {
    setIsClosing(true);
    setTimeout(() => {
      onClose();
    }, 400);
  };

  const options = [
    { value: "Fixed", label: "Fixed" },
    { value: "Calculated", label: "Calculated" },
  ];

  const handleValueChange = (value: string) => {
    setSelectedCategory(value);
    setCategoryError(false);
  };

  const dispatch = useDispatch();
  useEffect(() => {
    dispatch(resetInputValues());
  }, [dispatch]);

  const inputValues = useSelector(
    (state: RootState) => state.floatingLabel.inputValues
  );

  const handleAddClick = () => {
    if (!quantity || !selectedCategory) {
      setCategoryError(true);
      return;
    }
    setIsSummaryPage(true);
  };

  const handleSubmit = () => {
    if (onSubmit) {
      onSubmit(selectedCategory, quantity); // Pass data to parent
    }
    onClose(); // Close the popup
  };

  return (
    <div
      className={`${styles.valuepopup_maindiv} ${
        isClosing ? styles.closing : ""
      }`}
    >
      <div className={styles.valuepopup_header}>
        <h3 style={{ textAlign: "center" }}>
          {isSummaryPage
            ? "Are you sure you want to add this Value?"
            : "Add Value"}
        </h3>
        <button className={styles.closeButton} onClick={handleClose}>
          <CloseIcon />
        </button>
      </div>

      {isSummaryPage ? (
        <ValuePopupSummary
          selectedAction={selectedCategory}
          quantity={quantity}
        />
      ) : (
        <>
          <RadioBtns
            options={options}
            selectedValue={selectedCategory}
            onValueChange={handleValueChange}
            error={categoryError}
          />
          <div style={{ paddingTop: "0.2rem" }}>
            <FloatingLabelInput
              label="Quantity"
              id="quantity"
              placeholder="Quantity"
              isInvalid={error}
              onInputChange={(value: string) => {
                setQuantity(value);
                {
                }
                dispatch(setInputValue({ quantity: value }));
                {
                }
                setError(false);
              }}
            />
          </div>
        </>
      )}

      <div className={styles.valuepopup_btngrp}>
        {isSummaryPage ? (
          <>
            <Button
              type="Cancel"
              Content="Back"
              Callback={() => setIsSummaryPage(false)}
            />
            <Button type="Next" Content="Submit" Callback={handleSubmit} />
          </>
        ) : (
          <>
            <Button type="Cancel" Content="Cancel" Callback={onClose} />
            <Button type="Next" Content="Add" Callback={handleAddClick} />
          </>
        )}
      </div>
    </div>
  );
}

export default ValuePopup;
