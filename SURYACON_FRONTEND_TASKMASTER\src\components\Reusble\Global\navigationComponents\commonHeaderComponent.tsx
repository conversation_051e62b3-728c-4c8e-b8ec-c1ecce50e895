import { FC, useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import styles from "./styles/CommonHeaderComponent.module.css";
import { useDispatch, useSelector } from "react-redux";
import {
  backToPreviousTask,
  resetNavigate,
} from "../../../../redux/features/Modules/Reusble/navigationSlice";
import { DoubleArrow } from "../../../../assets/icons";
import { NavigationProps } from "../GlobalInterfaces/GlobalInterface";
import {
  setIsDeleted,
  setisDeletedNext,
} from "../../../../redux/features/Modules/Reusble/deletedSlice";
import { RootState } from "../../../../redux/store";
import { useAppSelector } from "../../../../redux/hooks/Modules/Reduxhooks/ReduxHooks";
import { setIsDeleteTask } from "../../../../redux/features/Modules/TaskMaster/Slices/CategorySlice";
import {
  setChangeAPiFlag,
  setcurrentSubtaskData,
  settaskChangeAPiFlag,
} from "../../../../redux/features/Modules/TaskMaster/Slices/TaskMasterSlice";
import { setTowerLocationDeleted } from "../../../../redux/features/Modules/Billing/ProjectPlanning/Slices/ProjectSlice";

const NavigationComponent: FC<NavigationProps> = ({
  route,
  handleOutsideNavigation,
}) => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const isDeleted = useSelector(
    (state: RootState) => state.isDeletedSLice.isDeleted
  );

  const popups = useSelector((state: RootState) => state.popup.popups);
  const isAnyPopupOpen = Object.values(popups).some((val) => val === true);
  const { pathname } = useLocation();
  const navigateArray = useAppSelector(
    (state) => state.navigateData.navigateArray
  );
  console.log("navigateArray", navigateArray, pathname);

  const handleNavigate = (title: string, route: string) => {
    dispatch(resetNavigate({ title }));
    console.log(route, "route in common header component");
    if (route == "/category") {
      dispatch(setIsDeleted(false));
      dispatch(setisDeletedNext({ isDelete: false }));
      dispatch(setIsDeleteTask(false));
    } else {
      if (isDeleted == false) {
        //this is to keep the isDeleted true for task and subtask if we are viewing deleted tasks
        //change this logic if better approach applicable
        if (
          navigateArray[navigateArray.length - 2]?.title === "Deleted" ||
          navigateArray[navigateArray.length - 3]?.title === "Deleted"
        ) {
          dispatch(setisDeletedNext({ isDelete: true }));
          dispatch(setIsDeleteTask(false));
        } else {
          dispatch(setisDeletedNext({ isDelete: false }));
          dispatch(setIsDeleteTask(false));
        }
      }
    }
    navigate(route);
  };

  useEffect(() => {
    const handleEscKey = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        if (isAnyPopupOpen) {
          return;
        }
        if (navigateArray.length < 2) return;

        const last = navigateArray[navigateArray.length - 1];
        const secondLast = navigateArray[navigateArray.length - 2];

        if (last?.title === "Deleted" || pathname === "/category") {
          dispatch(setisDeletedNext({ isDelete: false }));
          dispatch(setIsDeleted(false));
        }

        dispatch(backToPreviousTask());
        navigate(secondLast.route);
      }
    };

    window.addEventListener("keydown", handleEscKey);
    return () => {
      window.removeEventListener("keydown", handleEscKey);
    };
  }, [dispatch, navigate, isAnyPopupOpen, navigateArray, pathname]);

  return (
    <div className={`${styles.common_header} small_text_p`}>
      {route?.map((item, index) => (
        <div key={index} className={styles.common_header_navlink_container}>
          {route.length > 1 && index === route.length - 1 && (
            <div className={styles.common_header_icon_container}>
              <DoubleArrow color="#00596b" />
            </div>
          )}

          <p
            className={` small_text_p ${
              index === route.length - 1
                ? `${styles.common_header_title} ${styles.active}`
                : styles.common_header_navlink
            }`}
            onClick={() => {
              // const formChecks = store.getState().isEditTaskReducer;
              // const taskApiCall = formChecks.istaskApiCall;
              // const subtaskApiCall = formChecks.issubtaskApiCall;

              // if (!taskApiCall && !subtaskApiCall) {
              if (index !== route.length - 1) {
                if (handleOutsideNavigation) {
                  handleOutsideNavigation(item.title, item.route);
                } else if (!handleOutsideNavigation) {
                  handleNavigate(item.title, item.route);
                }
                dispatch(settaskChangeAPiFlag(false));
                dispatch(setChangeAPiFlag(false));
                dispatch(setcurrentSubtaskData(null as any));
                //   }
                // } else {
                // dispatch(settaskChangeAPiFlag(false));
                // dispatch(
                //   setToast({
                //     isOpen: true,
                //     messageContent: "Please first close the form",
                //     type: "warning",
                //   })
                // );
              }
            }}
          >
            {item.title}
          </p>

          {index < route.length - 2 && (
            <div className={styles.common_header_icon_container}>
              <DoubleArrow color="var(--text-black-28)" />
            </div>
          )}
        </div>
      ))}
    </div>
  );
};

export default NavigationComponent;
