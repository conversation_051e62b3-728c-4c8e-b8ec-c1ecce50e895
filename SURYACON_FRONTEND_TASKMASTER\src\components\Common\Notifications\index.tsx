/* Author name: CHARVI */
import { useState } from "react";

import styles from './Styles/Notifications.module.css'
import { WarningIcon } from "../../../assets/icons";

export function Notifications() {
  const [selected, setSelected] = useState<"ActionRequired" | "Alert">(
    "ActionRequired"
  );

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSelected(event.target.value as "ActionRequired" | "Alert");
  };
  return (
    <div className={styles.notification_container}>
      <h3>Notifications</h3>
      <div className={styles.notification_toggleswitch}>
        <input
          type="radio"
          id="ActionRequired"
          name="switchPlan"
          value="ActionRequired"
          checked={selected === "ActionRequired"}
          onChange={handleChange}
        />
        <input
          type="radio"
          id="Alert"
          name="switchPlan"
          value="Alert"
          checked={selected === "Alert"}
          onChange={handleChange}
        />
        <label htmlFor="ActionRequired">Action Required</label>
        <label htmlFor="Alert">Alert</label>
        <div className={styles.notification_switch_wrapper}>
          <div className={styles.notification_switch}>
            <div>Action Required</div>
            <div>Alert</div>
          </div>
        </div>
      </div>
      <div className={styles.notification_toasts}>
        <div className={styles.notification_toastIcon}>
          <WarningIcon />
        </div>
        <div className={styles.notification_toastDetails}>
          <h4 className={styles.notification_toastHeading}>
            Add Category Request
          </h4>
          <p className={styles.notification_toastMessage}>
            Billing Engineer is requesting for the creation of a new category.
            Please approve the request.
          </p>
        </div>
        <div className={styles.notification_statusDiv}> </div>
        <div className={styles.notification_timestamp}>12:02 pm</div>
      </div>
    </div>
  );
}
