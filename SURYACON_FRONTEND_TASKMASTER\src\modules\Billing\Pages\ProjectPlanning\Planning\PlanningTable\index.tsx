import React, {
  useEffect,
  useCallback,
  useRef,
  useState,
  useLayoutEffect,
} from "react";

import styles from "./Styles/PlanningTable.module.css";
import PlanningtableHeader from "./SubComponents/PlanningtableHeader";
import SubtaskCard from "./SubComponents/SubtaskCard";
import { useAppDispatch } from "../../../../../../redux/hooks/Modules/Reduxhooks/ReduxHooks";
import { useSelector } from "react-redux";
import { RootState } from "../../../../../../redux/store";
import {
  closePopup,
  openPopup,
} from "../../../../../../redux/features/Modules/Reusble/popupSlice";
import AddToolTip from "../../../../../../components/Reusble/Global/AddToolTip";

import TargetBadge from "../../../../../../components/Reusble/Global/TargetBadge/TargetBadge";
import { getAllSubtaskForTaskRouteForPlanning } from "../../../../../../redux/hooks/Modules/Billing/billinghooks";
import {
  clearDeleteSubtaskInPlanning,
  selectLocationTaskId,
  selectLocationTaskIdData,
  setallSubtasksData,
  setDeletingTaskId,
  setIsEditPlanning,
  setPlanningProgress,
  setTaskplanningPercentage,
  setToDeleteData,
  setTowerBasicDetails,
  toggleEditMode,
} from "../../../../../../redux/features/Modules/Billing/ProjectPlanning/Slices/projectPlanningSlice";
import {
  useLazyGetSubTaskDetailByIdQuery,
  useUpdateLocationDetailsMutation,
} from "../../../../../../redux/api/Modules/Billing/ProjectPlanningApi";
import MonthlyTargetCard from "../../../BillingApproval/Subcomponents/MonthlyTargetCard";
import AddPlanningThings from "../../../../../../components/Reusble/Global/AddPlanningThings";
import { taskBuildingBlocks } from "../../../../../../interfaces/Modules/TaskMaster/TaskMasterInterface/TaskMaster";
import { useGetTaskBuildingBlocksQuery } from "../../../../../../redux/api/Modules/TaskMaster/TaskMasterapi";

import { setToast } from "../../../../../../redux/features/Modules/Reusble/ToastSlice";
import { setCurrentCardId } from "../../../../../../redux/features/Modules/Billing/BillingApproval/Slices/BillingApprovalSlice";
import { PercentageIcon } from "../../../../../../assets/icons";
import { SuryconLogo } from "../../../../../../assets/icons";
import { DeletePopup } from "../../../../../../components/Reusble/Global/DeletePopup";
import { useParams } from "react-router-dom";
import { initializeDatabase } from "../../../../../../functions/functions";
import { saveSyncData } from "../../../../../../Backup/BackupFunctions/BackupFunctions";
import { Loader } from "../../../../../../assets/loader";
import { useToast } from "../../../../../../hooks/ToastHook";
import { useOpencloseprojectplanningMutation } from "../../../../../../redux/api/Modules/Reusble/Reusble";
import { useSocket } from "../../../../../../SocketProvider";
import { setCredentials } from "../../../../../../redux/features/Modules/Auth/authSlice";

const PlanningTable = () => {
  const [focusedCardId, setFocusedCardId] = useState<string | null>(null);
  const [edit, setEdit] = React.useState(false);
  const dispatch = useAppDispatch();
  const { popups } = useSelector((state: RootState) => state.popup);
  const { towerLocationId: tower_Id } = useParams();
  // function to toggle edit state start here===========================================
  const toggleEditState = () => {
    console.log("edit changed", edit);
    setEdit((prev) => !prev);
  };

  // function to open popup start ere ======================================================
  const handleToggleDropdown = (name: string) => {
    dispatch(openPopup(name));
  };
  const originalDataRef = useRef<any>(null);

  const [updateStatus] = useOpencloseprojectplanningMutation();

  const selectedLocationTaskId = useSelector(
    (state: RootState) => state.projectPlanning.selectedLocationTaskId
  );
  const planningProgress = useSelector(
    (state: RootState) => state.projectPlanning.planningProgress
  );
  const isCalledRef = useRef(false);
  const lastProgressRef = useRef(0);

  const currentTaskDetails = useSelector(
    (state: RootState) => state.projectPlanning.currentTaskbasicDetails
  );
  const isEdit = useSelector(
    (state: RootState) => state.projectPlanning.isEdit
  );
  const showToast = useToast();
  const editMode = useSelector(
    (state: RootState) => state.projectPlanning.editMode
  );
  const initialTowerIdRef = useRef(tower_Id);
  const initialTaskIdRef = useRef(selectedLocationTaskId);
  const wasOpenedRef = useRef(false);

  // func to call api in loop for subtasks and combine all the data set in redux start by rattandev
  // const [getsubtasksData] = useLazyGetSubTaskDetailByIdQuery();

  // func to c all api in loop for subtasks and combine all the data set in redux end by rattandev================
  const SubtasksApiCall = async (data: any) => {
    try {
      if (!data) {
        dispatch(setallSubtasksData([]));
        return;
      }

      console.log(data, "Starting subtasks API call");
      const SubTasksIds = data?.map((e: any) => {
        return `${e?._id}`;
      });
      console.log(SubTasksIds, "these are ids to seavasdf");
      const allSubtasksData = await Promise.all(
        SubTasksIds.map(async (id: string) => {
          try {
            const dbname = await initializeDatabase("SubtasklocDetail");
            let singleSubtaskDatafromLocalDb2 =
              await window.electron.getsubtaskDetailById({
                id: id,
                dbName: dbname,
                towerId: tower_Id,
                taskId: selectedLocationTaskId,
              });

            let singleSubtaskData = singleSubtaskDatafromLocalDb2[0];

            console.log(
              singleSubtaskDatafromLocalDb2,
              "akele subtask ka data:"
            );

            if (singleSubtaskData) {
              return singleSubtaskData;
            }
            return null;
          } catch (error) {
            console.error(`Error loading subtask ${id}:`, error);
            return null;
          }
        })
      );

      const validSubtasks = allSubtasksData.filter(
        (subtask) => subtask !== null
      );
      console.log("Loaded subtasks:", validSubtasks);

      dispatch(setallSubtasksData(validSubtasks));
    } catch (error) {
      console.error("Error in SubtasksApiCall:", error);
      makeToast("error", "Failed to load subtask data");
    }
  };

  // func to call api in loop for subtasks and combine all the data set in redux end=========================
  //This 👇 objectId and this is selected tower id will come from top level component

  const selectedLocationTaskIdData = useSelector(
    (state: RootState) => state?.projectPlanning?.currentTaskbasicDetails
  );
  console.log(selectedLocationTaskIdData, "sfsasfdsfasfdasf");

  //Sselecting all subtaskdata to show in the table =====================================================start
  const selectAllSubtasksData = useSelector(
    (state) => state.projectPlanning.allSubtasksdata
  );
  console.log(selectAllSubtasksData, "thisisselectedsubtaksdsata");

  // task detail api calling here ===============================================================================================================================================================================================================

  // const {
  //   isFetching: isTaskDataFetching,
  //   refetch: refetchAllsubtaskfortaskrouteForPlanning,
  //   isSuccess: isTaskDataSuccess,
  //   data: Taskdetails,
  // } = getAllSubtaskForTaskRouteForPlanning(
  //   objectId && seletectedTask ? objectId : "",
  //   objectId && seletectedTask ? seletectedTask : ""
  // );
  const handleSyncRef = useRef<() => Promise<void>>(() => Promise.resolve());
  // function to handle inputchange on edit start
  const handleInput = async (
    e: React.ChangeEvent<HTMLInputElement>,
    element: any,
    id: any,
    key: any
  ) => {
    try {
      console.log(e?.target?.value, "the input is changing");
      const updatedElement = {
        ...element,
        quantity: e?.target?.value,
      };

      const SubTaskToBeUpdated = selectAllSubtasksData.find((e) => e._id == id);

      if (!SubTaskToBeUpdated) {
        console.error("Subtask not found for update");
        return;
      }

      const updatedSubtaskData = {
        ...SubTaskToBeUpdated,
        towerId: tower_Id,
        TaskmasterId: selectedLocationTaskId,
        [key]: SubTaskToBeUpdated[key]?.map((e: any) =>
          e._id == updatedElement._id ? updatedElement : e
        ),
      };

      // Update Redux state first
      dispatch(
        setallSubtasksData(
          selectAllSubtasksData?.map((e) =>
            e._id == updatedSubtaskData?._id ? updatedSubtaskData : e
          )
        )
      );

      await saveSyncData(
        {
          ...updatedSubtaskData,
        },
        Date.now().toString(),
        "SubtasklocDetail",
        false,
        dispatch
      );

      // Update materialtable
      const materialCategoryMap: Record<
        string,
        {
          categoryId: string;
          categoryName: string;
          materials: any[];
          taskId: string;
          _id: string;
        }
      > = {};

      const materials = updatedSubtaskData?.materialId || [];
      for (const material of materials) {
        const catId = material?.Mastermaterial_id?.materialCategoryId?._id;
        const catName =
          material?.Mastermaterial_id?.materialCategoryId?.name || "";
        const qty = Number(material.quantity) || 0;

        if (!catId) continue;

        if (!materialCategoryMap[catId]) {
          materialCategoryMap[catId] = {
            categoryId: catId,
            categoryName: catName,
            materials: [],
            taskId: selectedLocationTaskId,
            _id: `${catId}-${selectedLocationTaskId}`,
          };
        }

        const existingMaterial = materialCategoryMap[catId].materials.find(
          (existing) =>
            existing.Masterbrand_id === material?.Masterbrand_id?._id &&
            existing.Mastermaterial_id === material?.Mastermaterial_id?._id
        );

        if (existingMaterial) {
          existingMaterial.quantity += qty;

          existingMaterial.subtasks.push({
            name: updatedSubtaskData?.subtaskId?.name,
            id: updatedSubtaskData?._id,
            quantity: qty,
          });
        } else {
          materialCategoryMap[catId].materials.push({
            Masterbrand_id: material?.Masterbrand_id?._id || "",
            Brandname: material?.Masterbrand_id?.Brandname || "",
            Mastermaterial_id: material?.Mastermaterial_id?._id || "",
            name: material?.Mastermaterial_id?.name || "",
            unit: material?.Mastermaterial_id?.unit || [],
            quantity: qty,
            spec: material?.spec || "",
            subtasks: [
              {
                name: updatedSubtaskData?.subtaskId?.name,
                id: updatedSubtaskData?._id,
                quantity: qty,
              },
            ],
            _id: material?._id || "",
          });
        }
      }

      const dbName = await initializeDatabase("materialtable");
      const existingDocs = await window.electron.allbulkGet({ dbName });

      const summaryArray = Object.values(materialCategoryMap).map(
        (category) => {
          const existingDoc = existingDocs?.docs?.find(
            (doc: any) =>
              doc._id === `${category.categoryId}-${category.taskId}`
          );

          if (existingDoc) {
            window.electron.deletedocbyid({
              dbName: "materialtable",
              idname: "_id",
              _id: existingDoc._id,
            });
          }

          return category;
        }
      );

      console.log(summaryArray, "Updated materialtable data");

      await saveSyncData(summaryArray, "", "materialtable", false, dispatch);

      console.log("Data saved successfully");
    } catch (error) {
      console.error("Error updating subtask data:", error);
      makeToast("error", "Failed to save changes");
    }
  };

  const [updateLocationDetails] = useUpdateLocationDetailsMutation();
  const deletingtask = useSelector(
    (state: RootState) => state.projectPlanning.deletingtask
  );

  // function for dynamic toasts start
  const makeToast = (type: string, message: string) => {
    dispatch(
      setToast({
        isOpen: true,
        messageContent: `${message}`,
        type: `${type}`,
      })
    );
  };
  const generateTransformedData = () => {
    const taskId =
      selectedLocationTaskIdData?.Master_taskid ||
      selectedLocationTaskIdData?.taskId;
    const taskName = selectedLocationTaskIdData?.name;

    if (!taskId || !taskName) {
      return null;
    }

    return {
      location_id: tower_Id,
      Todeletedata: toBeDeleteData,
      Tasks: [
        {
          subtask_details: selectAllSubtasksData?.map((task: any) => ({
            ...(task?._id &&
              String(task._id).length !== 13 && { _id: task._id }),
            Master_subtask_id: task?.subtaskId?._id,
            quantity: task?.quantity,
            weightage: task?.weightage,
            Description: task?.Description,

            manpower_details: task?.manpowerId?.map((manpower: any) => ({
              ...(manpower?._id &&
                manpower._id.length === 24 &&
                !/^\d{13}-\d+\.\d+$/.test(manpower._id) && {
                  _id: manpower._id,
                }),
              Mastermanpower_id: manpower?.Mastermanpower_id?._id,
              quantity: Number(manpower?.quantity),
              type: manpower?.type,
            })),

            machinary_details: task?.machinaryId?.map((machinery: any) => ({
              ...(machinery?._id &&
                machinery._id.length === 24 &&
                !/^\d{13}-\d+\.\d+$/.test(machinery._id) && {
                  _id: machinery._id,
                }),
              Mastermachinary_id: machinery?.Mastermachinary_id?._id,
              quantity: machinery?.quantity,
              Masterbrand_id: machinery?.Masterbrand_id?._id,
              spec: machinery?.spec,
            })),

            material_details: task?.materialId?.map((material: any) => ({
              ...(material?._id &&
                material._id.length === 24 &&
                !/^\d{13}-\d+\.\d+$/.test(material._id) && {
                  _id: material._id,
                }),
              Mastermaterial_id: material?.Mastermaterial_id?._id,
              quantity: material?.quantity,
              Masterbrand_id: material?.Masterbrand_id?._id,
              spec: material?.spec,
            })),

            Tools_details: task?.tool_id?.map((tool: any) => ({
              ...(tool?._id &&
                tool._id.length === 24 &&
                !/^\d{13}-\d+\.\d+$/.test(tool._id) && { _id: tool._id }),
              MasterTool_id: tool?.MasterTool_id?._id,
              quantity: tool?.quantity,
              spec: tool?.spec,
              Masterbrand_id: tool?.Masterbrand_id?._id,
            })),
          })),
          payment: selectedLocationTaskIdData?.payment,
          Area: selectedLocationTaskIdData?.area,
          Duration: selectedLocationTaskIdData?.duration,
          Shuttering: selectedLocationTaskIdData?.shuttering,
          progressPercentage: planningProgress,
          Master_taskid: taskId,
          rate: currentSelectedRate,
          name: taskName,
          ...(selectedLocationTaskIdData?.Master_taskid && {
            _id: selectedLocationTaskIdData?.taskid,
          }),
        },
      ],
      deletingtask: deletingtask,
    };
  };

  useEffect(() => {
    originalDataRef.current = generateTransformedData();
  }, [isEdit]);

  const handleUpdateLocation = async () => {
    dispatch(clearDeleteSubtaskInPlanning());
  };

  const latestDataRef = useRef<any>(null);

  useEffect(() => {
    latestDataRef.current = generateTransformedData();
  }, [
    selectedLocationTaskIdData,
    selectAllSubtasksData,
    editMode,
    isEdit,
    location.pathname,
  ]);

  useEffect(() => {
    return () => {
      (async () => {
        const latestData = latestDataRef.current;
        console.log(latestData, "hello console");
        const previousData = generateTransformedData();
        if (!nochange(latestData, previousData) && editMode) {
          await updateLocationDetails({ data: latestData });
        }
      })();
    };
  }, [location.pathname, tower_Id, editMode, selectedLocationTaskId]);

  useEffect(() => {
    return () => {
      (async () => {
        if (editMode) {
          await updateStatus({
            status: "false",
            towerid: tower_Id,
            taskid: selectedLocationTaskId,
          });

          dispatch(setIsEditPlanning(false));
        }
      })();
    };
  }, [location.pathname, tower_Id, editMode]);

  useEffect(() => {
    window.electron.setSyncContext("locationDetail");
    const stableHandler = () => {
      handleSyncRef.current?.();
    };

    window.electron.removeAlllocationSyncListeners();
    window.electron.onTriggerlocationSync(stableHandler);

    return () => {
      window.electron.removeTriggerlocationSync(stableHandler);
      window.electron.setSyncContext(null);
    };
  }, []);
  // useeffect to hit api when app quit
  useEffect(() => {
    handleSyncRef.current = async () => {
      if (isCalledRef.current) return;
      isCalledRef.current = true;

      try {
        // if (!isApiCallRef.current) {
        //   await window.electron.syncComplete();
        //   return;
        // }

        console.log("agyaaaaaa");

        await handleUpdateLocation();

        await updateStatus({
          status: "false",
          towerid: tower_Id,
          taskid: selectedLocationTaskId,
        });

        console.log("this is called");

        // const result = await handleGetSubTask(finalSubtaskIdRef.current!);

        // if (result.data?.data?.response) {
        //   const formattedData = transformFunctionForGet(
        //     result.data?.data?.response
        //   );
        // }
      } catch (error) {
        console.error("SubtaskForm - error during sync:", error);
      } finally {
        // dispatch(
        //   setCredentials({
        //     isAuthenticated: false,
        //     user: null,
        //   })
        // );
        isCalledRef.current = false;
        await window.electron.syncComplete(); // notify Electron to quit
      }
    };
  }, [handleUpdateLocation, dispatch, location.pathname]);

  // useselector to get the subtasks basic details from the redux to fetch the all the detail from the id start ===========================start
  const allSubtasksBasicDetails = useSelector(
    (state: RootState) => state.projectPlanning.allSubtasksBasicDetails
  );
  console.log(allSubtasksBasicDetails, "these are all subtask basic details");

  // select the selected subtask id  from redux
  const selectedSubtaskTask = useSelector(
    (state: RootState) => state.projectPlanning?.selectedLocationSubTaskId
  );

  const handleHeaderFieldChange = useCallback(
    (fieldName: string, value: any) => {
      // Update header field in Redux or local state
      dispatch(
        setTowerBasicDetails({
          ...currentTaskDetails,
          [fieldName]: value,
        })
      );

      // Percentage calculation yahin trigger ho jayegi
      // (Aapka useEffect already currentTaskDetails pe depend hai, toh yeh auto ho jayega)
    },
    [dispatch, currentTaskDetails]
  );

  // useEffect(() => {
  //   if (!selectedSubtaskTask) return;

  //   const element = document.getElementById(selectedSubtaskTask);
  //   const scrollContainer = document.getElementById("scroll-container");

  //   console.log("Element:", element, "ScrollContainer:", scrollContainer);

  //   if (element && scrollContainer) {
  //     const elementRect = element.getBoundingClientRect();
  //     const parentRect = scrollContainer.getBoundingClientRect();

  //     const offsetTop =
  //       elementRect.top - parentRect.top + scrollContainer.scrollTop;

  //     console.log("OffsetTop:", offsetTop); // Debugging output

  //     scrollContainer.scrollTo({ top: offsetTop, behavior: "smooth" });
  //   }
  // }, [selectedSubtaskTask]);

  const isFilled = (val: any): boolean => {
    // 0 ko filled NAHI mano, blank, null, undefined, NaN ko unfilled mano
    if (val === undefined || val === null || val === "" || isNaN(val))
      return false;
    // 0 ko bhi unfilled mano (agar aapke use-case me 0 allowed nahi hai)
    if (val === 0) return false;
    return true;
  };

  const getTotalEditableFields = (header: any, subtasks: any[]): number => {
    let total = 0;

    if (header) {
      // Only 4 header fields are user-filled
      total += 4; // area, payment, duration, shuttering (rate from backend, ignored)
    }

    subtasks?.forEach((subtask) => {
      if (!subtask) return;

      // Only quantity is user-filled (weightage comes from backend)
      total += 2;

      // Unique manpower (skilled/unskilled pair)
      const manpower = Array.isArray(subtask.manpowerId)
        ? subtask.manpowerId.filter(
            (m, idx, arr) =>
              m?.Mastermanpower_id?._id &&
              m?.type &&
              idx ===
                arr.findIndex(
                  (x) =>
                    x?.Mastermanpower_id?._id === m?.Mastermanpower_id?._id &&
                    x?.type === m?.type
                )
          )
        : [];

      total += manpower.length;

      // machinaryId, tool_id, materialId
      ["machinaryId", "tool_id", "materialId"].forEach((key) => {
        if (Array.isArray(subtask[key])) {
          total += subtask[key].length;
        }
      });
    });

    return total;
  };

  const getFilledFields = (header: any, subtasks: any[]): number => {
    let filled = 0;

    if (isFilled(header?.area)) filled++;
    if (isFilled(header?.payments ?? header?.payment)) filled++;
    if (isFilled(header?.durations ?? header?.duration)) filled++;
    if (isFilled(header?.shuttering)) filled++;

    subtasks?.forEach((subtask) => {
      if (!subtask) return;

      if (isFilled(subtask?.quantity)) filled++;
      if (isFilled(subtask?.weightage)) filled++;

      if (Array.isArray(subtask.manpowerId)) {
        const uniqueManpower = subtask.manpowerId.filter(
          (m, idx, arr) =>
            m?.Mastermanpower_id?._id &&
            m?.type &&
            idx ===
              arr.findIndex(
                (x) =>
                  x?.Mastermanpower_id?._id === m?.Mastermanpower_id?._id &&
                  x?.type === m?.type
              )
        );
        uniqueManpower.forEach((m) => {
          if (isFilled(m?.quantity)) filled++;
        });
      }

      ["machinaryId", "tool_id", "materialId"].forEach((key) => {
        if (Array.isArray(subtask[key])) {
          subtask[key].forEach((item) => {
            if (isFilled(item?.quantity)) filled++;
          });
        }
      });
    });

    return filled;
  };

  const calculationTimeout = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    // Only recalculate if in edit mode (i.e., user is editing)
    if (
      !selectAllSubtasksData ||
      selectAllSubtasksData.length === 0 ||
      !editMode // Only run when editMode is true
    )
      return;

    // Debounce: clear previous timeout
    if (calculationTimeout.current) clearTimeout(calculationTimeout.current);

    calculationTimeout.current = setTimeout(() => {
      const total = getTotalEditableFields(
        currentTaskDetails,
        selectAllSubtasksData
      );
      const filled = getFilledFields(currentTaskDetails, selectAllSubtasksData);
      const percent = total === 0 ? 0 : Math.round((filled / total) * 100);

      if (lastProgressRef.current !== percent) {
        lastProgressRef.current = percent;
        dispatch(setPlanningProgress(percent));

        if (selectedLocationTaskId) {
          dispatch(
            setTaskplanningPercentage({
              taskId: selectedLocationTaskId,
              percentage: percent,
            })
          );
        }
      }
    }, 100);

    return () => {
      if (calculationTimeout.current) clearTimeout(calculationTimeout.current);
    };
  }, [
    currentTaskDetails,
    selectAllSubtasksData,
    selectedLocationTaskId,
    editMode,
  ]);

  // // Run on selectedSubtaskTask change
  // function to handle target_badge value change
  const handleTargetBadgeValuechange = async (
    e: any,
    key: any,
    subtaskId: any
  ) => {
    const SubTaskToBeUpdated = selectAllSubtasksData.find(
      (e) => e._id == subtaskId
    );
    const updatedSubtaskData = {
      ...SubTaskToBeUpdated,
      towerId: tower_Id, // Ensure tower context
      TaskmasterId: selectedLocationTaskId, // Ensure task context
      [key]: e.target.value,
    };
    dispatch(
      setallSubtasksData(
        selectAllSubtasksData?.map((e) =>
          e._id == updatedSubtaskData?._id ? updatedSubtaskData : e
        )
      )
    );
    await saveSyncData(
      {
        ...updatedSubtaskData,
      },
      "",
      "SubtasklocDetail",
      false,
      dispatch
    );
  };

  // api to fetch required things start
  const { data } = useGetTaskBuildingBlocksQuery();
  console.log(data, "yamatekudasais");
  // states for delete start
  const [deleteElememt, setdeleteElement] = useState<any>(null);
  const [deleteKkey, setdeleteKkey] = useState(null);
  const [subtaskDeleteId, setsubtaskDeleteId] = useState(null);
  console.log(deleteElememt, "deleteelement");
  // states for delete end
  // Map API response to task building blocks
  const currentSelectedData = useSelector(
    (state: RootState) => state.projectPlanning.curretSelectedData
  );
  const taskBuildingBlocks: taskBuildingBlocks = {
    manpower:
      data?.data?.response?.manpower.map((item: any) => ({
        id: item._id,
        category: item.name,
        parentId: item.parentId || "", // Provide a default value if parentId is missing
      })) || [],
    machinery:
      data?.data?.response?.machinery.map((item: any) => ({
        id: item._id,
        category: item.name,
        parentId: item.parentId || "", // Add parentId to match TaskDataType
        unit: item.unit,
        BrandDetails: item.BrandDetails,
      })) || [],
    tools:
      data?.data?.response?.tools.map((item: any) => ({
        id: item._id,
        category: item.name,
        parentId: item.parentId || "", // Add parentId to match TaskDataType
      })) || [],
    material:
      data?.data?.response?.material.map((item: any) => ({
        id: item._id,
        category: item.name,
        parentId: item.parentId || "", // Add parentId to match TaskDataType
        unit: item.unit,
        BrandDetails: item.BrandDetails,
      })) || [],
  };
  console.log(taskBuildingBlocks, "thisistaskbuildingblocks");
  // api to fetch required thigns end

  // select the tobedeletedata
  const toBeDeleteData = useSelector(
    (state: RootState) => state?.projectPlanning?.Todeletedata
  );
  console.log(toBeDeleteData, "tobeDeleteData");
  // function to handle targetcard Delete start
  const handledelete = async (e: any, key: any, subtaskId: any) => {
    console.log(e, key, subtaskId, "thisiswhatiswanttosee");
    const SubTaskToBeUpdated = selectAllSubtasksData.find(
      (e) => e._id == subtaskId
    );
    const updatedSubtaskData = {
      ...SubTaskToBeUpdated,
      [key]: SubTaskToBeUpdated[key]?.filter(
        (element: any) => element._id !== e._id
      ),
    };
    console.log(updatedSubtaskData, "theupdatedsubtasksishereman");
    dispatch(
      setallSubtasksData(
        selectAllSubtasksData?.map((e) =>
          e._id == updatedSubtaskData?._id ? updatedSubtaskData : e
        )
      )
    );
    switch (key) {
      case "manpowerId":
        {
          // Create a new array with the existing `manpowers` and the new ID
          const updatedManpowers = [
            ...(toBeDeleteData.manpowers || []), // Ensure `manpowers` exists as an array
            e?._id, // Add the new ID
          ];

          // Create a new `toBeDeleteData` object with the updated `manpowers`
          const updatedToBeDeleteData = {
            ...toBeDeleteData,
            manpowers: updatedManpowers,
          };

          console.log(updatedToBeDeleteData, "tobede2");

          // Dispatch the updated data
          dispatch(setToDeleteData(updatedToBeDeleteData));
          const updatedTaskDataWithToBeDeleted = {
            ...currentSelectedData,
            tobeDeleted: updatedToBeDeleteData,
          };
          console.log(
            updatedTaskDataWithToBeDeleted,
            "this is current selected datasdf"
          );
          await saveSyncData(
            updatedTaskDataWithToBeDeleted,
            Date.now().toString(),
            "TowerRoutes",
            false,
            dispatch
          );
        }
        break;
      case "machinaryId":
        {
          console.log(toBeDeleteData, "simsim");

          // Create a new array with the existing `manpowers` and the new ID
          const updatedMachinery = [
            ...(toBeDeleteData.machinaries || []), // Ensure `manpowers` exists as an array
            e?._id, // Add the new ID
          ];

          // Create a new `toBeDeleteData` object with the updated `manpowers`
          const updatedToBeDeleteData = {
            ...toBeDeleteData,
            machinaries: updatedMachinery,
          };

          console.log(updatedToBeDeleteData, "tobede2");

          // Dispatch the updated data
          dispatch(setToDeleteData(updatedToBeDeleteData));
          const updatedTaskDataWithToBeDeleted = {
            ...currentSelectedData,
            tobeDeleted: updatedToBeDeleteData,
          };
          await saveSyncData(
            updatedTaskDataWithToBeDeleted,
            Date.now().toString(),
            "TowerRoutes",
            false,
            dispatch
          );
        }
        break;

      case "tool_id":
        {
          console.log(toBeDeleteData, "simsim");

          // Create a new array with the existing `manpowers` and the new ID
          const updatedtools = [
            ...(toBeDeleteData.tools || []), // Ensure `manpowers` exists as an array
            e?._id, // Add the new ID
          ];

          // Create a new `toBeDeleteData` object with the updated `manpowers`
          const updatedToBeDeleteData = {
            ...toBeDeleteData,
            tools: updatedtools,
          };

          console.log(updatedToBeDeleteData, "tobede2");

          // Dispatch the updated data
          dispatch(setToDeleteData(updatedToBeDeleteData));
          const updatedTaskDataWithToBeDeleted = {
            ...currentSelectedData,
            tobeDeleted: updatedToBeDeleteData,
          };
          await saveSyncData(
            updatedTaskDataWithToBeDeleted,
            Date.now().toString(),
            "TowerRoutes",
            false,
            dispatch
          );
        }
        break;

      case "materialId":
        {
          console.log(toBeDeleteData, "simsim");

          // Create a new array with the existing `manpowers` and the new ID
          const updatedMaterials = [
            ...(toBeDeleteData.materials || []), // Ensure `manpowers` exists as an array
            e?._id, // Add the new ID
          ];

          // Create a new `toBeDeleteData` object with the updated `manpowers`
          const updatedToBeDeleteData = {
            ...toBeDeleteData,
            materials: updatedMaterials,
          };

          console.log(updatedToBeDeleteData, "tobede2");

          // Dispatch the updated data
          dispatch(setToDeleteData(updatedToBeDeleteData));
          const updatedTaskDataWithToBeDeleted = {
            ...currentSelectedData,
            tobeDeleted: updatedToBeDeleteData,
          };
          await saveSyncData(
            updatedTaskDataWithToBeDeleted,
            Date.now().toString(),
            "TowerRoutes",
            false,
            dispatch
          );
        }
        break;
    }
  };

  // temporary function to updatedetails start

  // state for selected subtaskid for adding things
  const [selectedsubtaskidForAddingThings, setlectedsubtaskidForAddingThings] =
    useState();
  console.log(selectAllSubtasksData, "thisiselectedlocationtaskid");
  const getkeyByName = (name: string) => {
    switch (name) {
      case "machinaryId":
        return "Mastermachinary_id";
      case "manpowerId":
        return "Mastermanpower_id";
      case "tool_id":
        return "MasterTool_id";
      case "materialId":
        return "Mastermaterial_id";
      default:
        return "";
    }
    selectAllSubtasksData;
  };
  // function to handle add location detail start here ================================================================
  // const allsubtaksBasicDetails = useSelector(
  //   (state: RootState) => state.projectPlanning.allSubtasksBasicDetails
  // );
  // function to handle inputchange on edit start

  // Also modify your existing useEffect
  useLayoutEffect(() => {
    console.log("useeffectcalledasdfasdfasdfasdf", allSubtasksBasicDetails);
    if (!allSubtasksBasicDetails) return;
    SubtasksApiCall(allSubtasksBasicDetails);
    return () => {
      setallSubtasksData([]);
    };
  }, [allSubtasksBasicDetails]);
  const currentSelectedRate = useSelector(
    (state: RootState) => state.projectPlanning.selectedprojectRate
  );
  const nochange = (obj1: any, obj2: any): boolean => {
    if (obj1 === obj2) return true;
    if (typeof obj1 !== typeof obj2 || obj1 === null || obj2 === null)
      return false;
    if (typeof obj1 === "object") {
      const keys1 = Object.keys(obj1);
      const keys2 = Object.keys(obj2);
      if (keys1.length !== keys2.length) return false;
      for (const key of keys1) {
        if (!keys2.includes(key) || !nochange(obj1[key], obj2[key]))
          return false;
      }
      return true;
    }
    return false;
  };

  //funciton to handle add location detail end here=================================================
  // function to groupbynmeandbrand start here =============================================
  function groupByNameAndBrand(data: any) {
    const grouped: {
      [key: string]: {
        name: string;
        _id: string;
        brands: {
          [key: string]: { brandId: string; specs: Set<string> };
        };
      };
    } = {};
    data?.forEach(
      (item: {
        name: string;
        brand: string;
        spec: string;
        _id: string;
        brandId: string;
      }) => {
        if (!grouped[item.name]) {
          grouped[item.name] = {
            name: item.name,
            _id: item._id,
            brands: {} as {
              [key: string]: { brandId: string; specs: Set<string> };
            },
          };
        }

        if (!grouped[item.name].brands[item.brand]) {
          grouped[item.name].brands[item.brand] = {
            brandId: item.brandId,
            specs: new Set(),
          };
        }

        grouped[item.name].brands[item.brand].specs.add(item.spec);
      }
    );

    return Object.values(grouped).map((group) => ({
      name: group.name,
      _id: group._id,
      brands: Object.entries(group.brands).map(
        ([brand, { brandId, specs }]) => ({
          brand,
          brandId,
          specs: Array.from(specs), // Convert Set back to array
        })
      ),
    }));
  }
  // function to groupbynmeandbrand end here =============================================

  const RequiredData = (key: string, TaskbuildingBlockKey: string) => {
    const selectedSubtask = selectAllSubtasksData?.find(
      (e: any) => e._id == selectedsubtaskidForAddingThings
    );
    console.log(selectedSubtask, "heybro");
    if (!selectedSubtask) return [];

    let allMaterials = [];
    switch (key) {
      case "materialId":
        allMaterials = selectedSubtask[key]?.map((e: any) => ({
          _id: e?.Mastermaterial_id?._id,
          name: e?.Mastermaterial_id?.name,
          spec: e?.spec,
          brandId: e?.Masterbrand_id?._id,
          brand: e?.Masterbrand_id?.Brandname,
        }));
        break;
      case "machinaryId":
        allMaterials = selectedSubtask[key]?.map((e: any) => ({
          _id: e?.Mastermachinary_id?._id,
          name: e?.Mastermachinary_id?.name,
          spec: e?.spec,
          brandId: e?.Masterbrand_id?._id,
          brand: e?.Masterbrand_id?.Brandname,
        }));
        break;
      case "tool_id":
        allMaterials = selectedSubtask[key]?.map((e: any) => ({
          _id: e?.MasterTool_id?._id,
          name: e?.MasterTool_id?.name,
          spec: e?.spec,
          brandId: e?.Masterbrand_id?._id,
          brand: e?.Masterbrand_id?.Brandname,
        }));
        break;
      // Add other cases if needed
      default:
        break;
    }
    console.log(allMaterials, "thisisallmaterials");
    const groupedMaterials = groupByNameAndBrand(allMaterials);

    const materialsArray =
      (taskBuildingBlocks[
        TaskbuildingBlockKey as keyof taskBuildingBlocks
      ] as any[]) || [];
    const finalData = materialsArray
      .map((material: any) => {
        const existingMaterial = groupedMaterials?.find(
          (group: any) => group?._id == material?.id
        );

        // Ensure BrandDetails is an array
        const brandDetailsArray = Array.isArray(material?.BrandDetails)
          ? material.BrandDetails
          : [];

        if (existingMaterial) {
          const remainingBrands = brandDetailsArray.map(
            (brandDetail: any) => {
              const existingBrand = existingMaterial?.brands?.find(
                (brand: any) =>
                  brand?.brand === brandDetail?.BrandId?.[0]?.Brandname
              );

              if (existingBrand) {
                const remainingSpecs = Array.isArray(brandDetail?.Specs)
                  ? brandDetail.Specs.filter(
                      (spec: any) => !existingBrand?.specs?.includes(spec)
                    )
                  : [];

                if (remainingSpecs.length > 0) {
                  return {
                    BrandId: [
                      {
                        Brandname: brandDetail?.BrandId?.[0]?.Brandname,
                        _id: brandDetail?.BrandId?.[0]?._id,
                      },
                    ],
                    Specs: remainingSpecs,
                    _id: brandDetail?._id,
                  };
                }
              } else {
                return brandDetail;
              }
            }
          ).filter(Boolean);

          if (remainingBrands.length > 0) {
            return {
              ...material,
              BrandDetails: remainingBrands,
            };
          }
        } else {
          return material;
        }
      })
      .filter(Boolean);

    return finalData;
  };

  // switch statement
  const getNameByKey = (key: string) => {
    switch (key) {
      case "manpowerId":
        return "Manpower";
      case "machinaryId":
        return "Machine";
      case "tool_id":
        return "Tool";
      case "materialId":
        return "Material";
      default:
        return "Unknown";
    }
  };

  useEffect(() => {
    if (!selectedSubtaskTask) return;
    console.log(
      selectedSubtaskTask,
      selectAllSubtasksData,
      "this is selected subtask checko ut hers"
    );
    // Add a small delay to ensure the DOM is updated
    setTimeout(() => {
      scrollToSubtaskBadge(selectedSubtaskTask?.split("-")[2]);
    }, 100);
  }, [selectedSubtaskTask]);

  const scrollToSubtaskBadge = (subtaskId: string) => {
    console.log("Attempting to scroll to:", subtaskId);

    setTimeout(() => {
      const element = document.getElementById(subtaskId);
      const scrollContainer = document.getElementById("scroll-container");

      console.log("Element found:", element);
      console.log("Scroll container found:", scrollContainer);

      if (element && scrollContainer) {
        const elementOffsetTop = element.offsetTop;
        const containerOffsetTop = scrollContainer.offsetTop;

        // This works only if the element is a descendant of scrollContainer
        const scrollTop = elementOffsetTop - containerOffsetTop - 40;

        scrollContainer.scrollTo({
          top: scrollTop,
          behavior: "smooth",
        });
      } else {
        console.log("Element or container not found for scrolling");
      }
    }, 100);
  };

  const handleUpdateStatus = async (data?: {
    status: string;
    towerid: string;
    taskid: string;
  }) => {
    try {
      const response = await updateStatus(data);
      console.log(
        response,
        "this is response  asdf asfs34asdf asdfasdjfhasiodfh"
      );
      return response;
    } catch (error) {
      console.log(error);
    }
  };

  const handleToggleEdit = async () => {
    try {
      console.log(isEdit, "akjsdfhajkl");
      if (!isEdit) {
        const response = (await handleUpdateStatus({
          status: "true",
          towerid: tower_Id as string,
          taskid: selectedLocationTaskId as string,
        })) as any;
        if (response?.data?.success) {
          dispatch(setIsEditPlanning(true));
          dispatch(toggleEditMode());
          wasOpenedRef.current = true;
        } else {
          dispatch(setIsEditPlanning(false));
          if (!navigator.onLine) {
            showToast({
              messageContent: "Someone working on this form!",
              type: "danger",
            });
            return;
          }
        }
      } else {
        dispatch(setIsEditPlanning(false));
        dispatch(toggleEditMode());
      }
    } catch (error) {
      dispatch(setIsEditPlanning(false));
      console.log("response4", error);
      showToast({
        messageContent: "Oops someting went wrong!",
        type: "warning",
      });
    }
  };

  console.log(selectedLocationTaskId, "selectedLocationTaskId");
  console.log(tower_Id, "tower_Id>>>>>>>>>>");
  const [expandedSubtaskId, setExpandedSubtaskId] = useState<string | null>(
    null
  );

  useEffect(() => {
    const scrollContainer = document.getElementById("scroll-container");
    if (!scrollContainer) return;

    let animationFrameId: number;

    const handleScroll = () => {
      cancelAnimationFrame(animationFrameId);
      animationFrameId = requestAnimationFrame(() => {
        const containerRect = scrollContainer.getBoundingClientRect();
        let minDiff = Infinity;
        let closestId: string | null = null;

        selectAllSubtasksData?.forEach((subtask) => {
          const el = document.getElementById(subtask._id);
          if (el) {
            const rect = el.getBoundingClientRect();
            const diff = Math.abs(rect.top - containerRect.top);
            if (diff < minDiff) {
              minDiff = diff;
              closestId = subtask._id;
            }
          }
        });

        if (closestId && closestId !== expandedSubtaskId) {
          setExpandedSubtaskId(closestId);
        }
      });
    };

    scrollContainer.addEventListener("scroll", handleScroll);
    handleScroll(); // trigger initially

    return () => {
      scrollContainer.removeEventListener("scroll", handleScroll);
      cancelAnimationFrame(animationFrameId);
    };
  }, [selectAllSubtasksData, expandedSubtaskId]);

  return (
    <>
      <div
        onClick={(e) => {
          e.stopPropagation();
          dispatch(setCurrentCardId(null as any));
        }}
        className={styles.planningTableContainer}
      >
        <PlanningtableHeader
          handleToggleEdit={handleToggleEdit}
          isPlanning={true}
          edit={editMode}
          toggleEditState={toggleEditState}
          handleUpdateLocation={handleUpdateLocation}
          onHeaderFieldChange={handleHeaderFieldChange}
        />
        {/* <button onClick={handleUpdateLocation}>hitapi</button> */}
        <div className={styles.target_details_upper_outer_container}>
          <div className={styles.target_details_lower_outer_container}>
            <div id="scroll-container" className={styles.monthly_target_cards}>
              {selectAllSubtasksData?.length === 0 ? (
                <div className={styles.loader_loading}>
                  <img
                    src={Loader.suryaconLogo}
                    alt="No Data Found"
                    className={styles.loader_loading_image}
                  />
                </div>
              ) : (
                selectAllSubtasksData?.map((subtask, index) => (
                  <React.Fragment key={subtask?._id}>
                    <div className={styles.Subtask_target_badge_containers}>
                      <div
                        id={subtask?._id}
                        className={styles.target_badge_container}
                      >
                        <TargetBadge
                          outerContainerClassName="target_badge_for_subtasks"
                          bubbleClassname="target_badge_bubble_for_subtasks"
                          bubbleTextTagName="h4"
                          valueTextTagName="h3"
                          order
                          bubbleValue={(index + 1).toString()}
                          value={
                            subtask?.subtaskId
                              ? (subtask?.subtaskId?.name || "").slice(0, 15) +
                                ((subtask?.subtaskId?.name || "").length > 15
                                  ? "..."
                                  : "")
                              : (subtask?.name || "").slice(0, 15) +
                                ((subtask?.name || "").length > 15 ? "..." : "")
                          }
                        />
                      </div>
                      <div className={styles.target_badge_container}>
                        <TargetBadge
                          id="quantity"
                          outerContainerClassName="secondtarget_badge_for_subtasks"
                          bubbleClassname="secondtarget_badge_bubble_for_subtasks"
                          bubbleTextTagName="h4"
                          valueTextTagName="p"
                          order
                          maxlength={8}
                          bubbletextClassName={
                            "secondtarget_badge_for_subtasks_bubble"
                          }
                          bubbleValue={"Cum"}
                          input={true}
                          value={"Quantity"}
                          edit={editMode} // CHANGE TO editMode
                          handleValuechange={(e) => {
                            handleTargetBadgeValuechange(
                              e,
                              "quantity",
                              subtask?._id
                            );
                          }}
                          onClick={() => {}}
                          secondvalue={subtask?.quantity || ""}
                          key={subtask?._id} // Ensure re-render when subtask changes
                        />
                      </div>
                      <div className={styles.target_badge_container}>
                        <TargetBadge
                          id="weightage"
                          outerContainerClassName="secondtarget_badge_for_subtasks"
                          bubbleClassname="secondtarget_badge_bubble_for_subtasks"
                          bubbleTextTagName="h4"
                          valueTextTagName="p"
                          order
                          input={true}
                          icon={
                            <div className={`${styles.targetBadge_icon}`}>
                              <PercentageIcon />
                            </div>
                          }
                          value={"Weightage"}
                          edit={editMode}
                          handleValuechange={(e) => {
                            handleTargetBadgeValuechange(
                              e,
                              "weightage",
                              subtask?._id
                            );
                          }}
                          onClick={() => {}}
                          maxlength={3}
                          secondvalue={subtask?.weightage}
                          key={subtask?._id}
                        />
                      </div>
                    </div>

                    <>
                      <div className={`${styles.mt_card_outer_container}`}>
                        <div>
                          <AddToolTip
                            label={"Manpower"}
                            isEdit={editMode}
                            onClick={() => {
                              setlectedsubtaskidForAddingThings(subtask?._id);
                              handleToggleDropdown("Manpower");
                            }}
                            isPllaning={true}
                            className={`${styles.plannig_add_tooltip_subcontainer}`}
                          />
                        </div>
                        <div className={styles.mt_cards_container}>
                          {subtask?.manpowerId?.map((element: any, i: any) => (
                            <div
                              key={i}
                              onFocus={() => setFocusedCardId(element._id)}
                              onBlur={() => setFocusedCardId(null)}
                            >
                              <MonthlyTargetCard
                                toggleEditFunc={() => {}}
                                onInputchange={async (e: any) => {
                                  return handleInput(
                                    e,
                                    element,
                                    subtask?._id,
                                    "manpowerId"
                                  );
                                }}
                                onDelete={() => {
                                  dispatch(
                                    openPopup("DeletePlanningRequiredThings")
                                  );
                                  setsubtaskDeleteId(subtask?._id);
                                  setdeleteElement(element);
                                  setdeleteKkey("manpowerId");
                                  dispatch(
                                    setDeletingTaskId([selectedLocationTaskId])
                                  );
                                }}
                                _id={element?._id}
                                type={element?.Mastermanpower_id?.name}
                                quantity={element?.quantity}
                                brand={element?.Masterbrand_id?.Brandname}
                                property={
                                  element?.type?.charAt(0).toUpperCase() +
                                  element?.type?.slice(1)
                                }
                                edit={editMode} // CHANGE TO editMode
                                key={i}
                                name={""}
                                towerName={""}
                                floorNumber={0}
                                taskIndex={0}
                                isTower={false}
                                showDelete={
                                  focusedCardId !== element._id && editMode
                                }
                              />
                            </div>
                          ))}
                        </div>
                      </div>
                      <div className={`${styles.mt_card_outer_container}`}>
                        <div>
                          <AddToolTip
                            label={"Machinery"}
                            isEdit={editMode} // CHANGE TO editMode
                            onClick={() => {
                              setlectedsubtaskidForAddingThings(subtask?._id);
                              handleToggleDropdown("Machinery");
                            }}
                            isPllaning={true}
                            className={`${styles.plannig_add_tooltip_subcontainer}`}
                          />
                        </div>
                        <div className={styles.mt_cards_container}>
                          {subtask?.machinaryId?.map((element: any, i: any) => (
                            <div
                              key={i}
                              onFocus={() => setFocusedCardId(element._id)}
                              onBlur={() => setFocusedCardId(null)}
                            >
                              <MonthlyTargetCard
                                toggleEditFunc={() => {}}
                                onInputchange={(e) => {
                                  handleInput(
                                    e,
                                    element,
                                    subtask?._id,
                                    "machinaryId"
                                  );
                                }}
                                _id={element?._id}
                                onDelete={() => {
                                  dispatch(
                                    openPopup("DeletePlanningRequiredThings")
                                  );
                                  setsubtaskDeleteId(subtask?._id);
                                  setdeleteElement(element);
                                  setdeleteKkey("machinaryId");
                                  dispatch(
                                    setDeletingTaskId([selectedLocationTaskId])
                                  );
                                }}
                                type={element?.Mastermachinary_id?.name}
                                quantity={element.quantity}
                                brand={element?.Masterbrand_id?.Brandname}
                                property={element?.spec}
                                edit={editMode} // CHANGE TO editMode
                                key={i}
                                name={""}
                                towerName={""}
                                floorNumber={0}
                                taskIndex={0}
                                isTower={false}
                                showDelete={
                                  focusedCardId !== element._id && editMode
                                }
                              />
                            </div>
                          ))}
                        </div>
                      </div>
                      <div className={`${styles.mt_card_outer_container}`}>
                        <div>
                          <AddToolTip
                            label={"Tools"}
                            isEdit={editMode} // CHANGE TO editMode
                            onClick={() => {
                              setlectedsubtaskidForAddingThings(subtask?._id);
                              handleToggleDropdown("Tools");
                            }}
                            isPllaning={true}
                            className={`${styles.plannig_add_tooltip_subcontainer}`}
                          />
                        </div>
                        <div className={styles.mt_cards_container}>
                          {subtask?.tool_id?.map((element: any, i: any) => (
                            <div
                              key={i}
                              onFocus={() => setFocusedCardId(element._id)}
                              onBlur={() => setFocusedCardId(null)}
                            >
                              <MonthlyTargetCard
                                toggleEditFunc={() => {}}
                                onInputchange={(e) => {
                                  handleInput(
                                    e,
                                    element,
                                    subtask?._id,
                                    "tool_id"
                                  );
                                }}
                                _id={element?._id}
                                onDelete={() => {
                                  dispatch(
                                    openPopup("DeletePlanningRequiredThings")
                                  );
                                  setsubtaskDeleteId(subtask?._id);
                                  setdeleteElement(element);
                                  setdeleteKkey("tool_id");
                                  dispatch(
                                    setDeletingTaskId([selectedLocationTaskId])
                                  );
                                }}
                                type={element?.MasterTool_id?.name}
                                quantity={element.quantity}
                                brand={element?.Masterbrand_id?.Brandname}
                                property={element?.spec}
                                edit={editMode} // CHANGE TO editMode
                                key={i}
                                name={""}
                                towerName={""}
                                floorNumber={0}
                                taskIndex={0}
                                isTower={false}
                                showDelete={
                                  focusedCardId !== element._id && editMode
                                }
                              />
                            </div>
                          ))}
                        </div>
                      </div>
                      <div className={`${styles.mt_card_outer_container}`}>
                        <div>
                          <AddToolTip
                            label={"Materials"}
                            isEdit={editMode} // CHANGE TO editMode
                            onClick={() => {
                              setlectedsubtaskidForAddingThings(subtask?._id);
                              handleToggleDropdown("Material");
                            }}
                            isPllaning={true}
                            className={`${styles.plannig_add_tooltip_subcontainer}`}
                          />
                        </div>
                        <div className={styles.mt_cards_container}>
                          {subtask?.materialId?.map((element: any, i: any) => (
                            <div
                              key={i}
                              onFocus={() => setFocusedCardId(element._id)}
                              onBlur={() => setFocusedCardId(null)}
                            >
                              <MonthlyTargetCard
                                toggleEditFunc={() => {}}
                                _id={element?._id}
                                onInputchange={(e) => {
                                  handleInput(
                                    e,
                                    element,
                                    subtask?._id,
                                    "materialId"
                                  );
                                }}
                                // Hide delete button when this card has focus
                                showDelete={
                                  focusedCardId !== element._id && editMode
                                }
                                onDelete={() => {
                                  dispatch(
                                    openPopup("DeletePlanningRequiredThings")
                                  );
                                  setsubtaskDeleteId(subtask?._id);
                                  setdeleteElement(element);
                                  setdeleteKkey("materialId");
                                  dispatch(
                                    setDeletingTaskId([selectedLocationTaskId])
                                  );
                                }}
                                type={element?.Mastermaterial_id?.name}
                                quantity={element.quantity}
                                brand={element?.Masterbrand_id?.Brandname}
                                property={element?.spec}
                                edit={editMode}
                                key={i}
                                name={""}
                                towerName={""}
                                floorNumber={0}
                                taskIndex={0}
                                isTower={false}
                              />
                            </div>
                          ))}
                        </div>
                      </div>
                    </>
                    {index !== selectAllSubtasksData.length - 1 && (
                      <div className={styles.taskcreation_line_container}>
                        <span className={styles.dottedline_wrapper}></span>
                        <SuryconLogo />
                        <span className={styles.dottedline_wrapper}></span>
                      </div>
                    )}
                  </React.Fragment>
                ))
              )}
            </div>
            {/* <div className={styles.subtask_card_container}> */}
            <SubtaskCard
              edit={editMode}
              onSubtaskClick={scrollToSubtaskBadge as any}
              expandedSubtaskId={expandedSubtaskId}
              handleUpdateLocation={handleUpdateLocation}
            />
            {/* </div> */}
          </div>
        </div>

        {popups["Material"] && (
          <AddPlanningThings
            title={"Material"}
            data={RequiredData("materialId", "material")}
            placeholder="search"
            buttonLabel="Add Category"
            onSelect={async (items: any) => {
              //
              const SubTaskToBeUpdated = selectAllSubtasksData?.find(
                (e) => e._id == selectedsubtaskidForAddingThings
              );
              console.log(SubTaskToBeUpdated, "thisissubtasktobeupdated");
              const updatedSubtaskData = {
                ...SubTaskToBeUpdated,
                materialId: [
                  ...SubTaskToBeUpdated["materialId"],
                  {
                    _id: `${Date.now()}-${Math.random()}`,
                    Mastermaterial_id: {
                      name: items?.material,
                      _id: items?.MaterialId,
                    },
                    Masterbrand_id: {
                      Brandname: items?.brand,
                      _id: items?.brandId,
                    },

                    spec: items?.grade,
                  },
                ],
              };
              await saveSyncData(
                updatedSubtaskData,
                Date.now().toString(),
                "SubtasklocDetail",
                false,
                dispatch
              );
              console.log(updatedSubtaskData, "theupdatedsubtasksishereman");
              dispatch(
                setallSubtasksData(
                  selectAllSubtasksData?.map((e) =>
                    e._id == updatedSubtaskData?._id ? updatedSubtaskData : e
                  )
                )
              );
              // call the function to make toast
              makeToast("success", `${items?.material} Added Successfully`);
              //
              console.log(
                selectedsubtaskidForAddingThings,
                items,
                "seledsdfcted"
              );
            }}
            onClose={() => dispatch(closePopup("Material"))}
            initialSelected={[]}
            singleSelected={true}
            brandType={true}
            grade={true}
          />
        )}
        {popups["Tools"] && (
          <AddPlanningThings
            title={"Tool"}
            data={RequiredData("tool_id", "tools")}
            placeholder="search"
            buttonLabel="Add Category"
            onSelect={async (items: any) => {
              //
              const SubTaskToBeUpdated = selectAllSubtasksData.find(
                (e) => e._id == selectedsubtaskidForAddingThings
              );

              const updatedSubtaskData = {
                ...SubTaskToBeUpdated,
                tool_id: [
                  ...SubTaskToBeUpdated["tool_id"],
                  {
                    _id: `${Date.now()}-${Math.random()}`,

                    MasterTool_id: {
                      name: items?.material,
                      _id: items?.MaterialId,
                    },
                    Masterbrand_id: {
                      Brandname: items?.brand,
                      _id: items?.brandId,
                    },

                    spec: items?.grade,
                  },
                ],
              };
              console.log(updatedSubtaskData, "theupdatedsubtasksishereman");
              dispatch(
                setallSubtasksData(
                  selectAllSubtasksData?.map((e) =>
                    e._id == updatedSubtaskData?._id ? updatedSubtaskData : e
                  )
                )
              );
              await saveSyncData(
                updatedSubtaskData,
                Date.now().toString(),
                "SubtasklocDetail",
                false,
                dispatch
              );
              //
              console.log(
                selectedsubtaskidForAddingThings,
                items,
                "seledsdfcted"
              );
              // call the function to make toast
              makeToast("success", `${items?.material} Added Successfully`);
            }}
            onClose={() => dispatch(closePopup("Tools"))}
            initialSelected={[]}
            singleSelected={true}
            brandType={true}
            grade={true}
          />
        )}
        {popups["Machinery"] && (
          <AddPlanningThings
            title={"Machine"}
            data={RequiredData("machinaryId", "machinery")}
            placeholder="search"
            buttonLabel="Add Category"
            onSelect={async (items: any) => {
              //
              const SubTaskToBeUpdated = selectAllSubtasksData.find(
                (e) => e._id == selectedsubtaskidForAddingThings
              );
              console.log(SubTaskToBeUpdated, "thisissubtasktobeupdated");
              const updatedSubtaskData = {
                ...SubTaskToBeUpdated,
                machinaryId: [
                  ...SubTaskToBeUpdated["machinaryId"],
                  {
                    _id: `${Date.now()}-${Math.random()}`,

                    Mastermachinary_id: {
                      name: items?.material,
                      _id: items?.MaterialId,
                    },
                    Masterbrand_id: {
                      Brandname: items?.brand,
                      _id: items?.brandId,
                    },

                    spec: items?.grade,
                  },
                ],
              };
              console.log(updatedSubtaskData, "theupdatedsubtasksishereman");
              dispatch(
                setallSubtasksData(
                  selectAllSubtasksData?.map((e) =>
                    e._id == updatedSubtaskData?._id ? updatedSubtaskData : e
                  )
                )
              );
              //
              console.log(
                selectedsubtaskidForAddingThings,
                items,
                "seledsdfcted"
              );
              await saveSyncData(
                updatedSubtaskData,
                Date.now().toString(),
                "SubtasklocDetail",
                false,
                dispatch
              );
              // call the function to make toast
              makeToast("success", `${items?.material} Added Successfully`);
            }}
            onClose={() => dispatch(closePopup("Machinery"))}
            initialSelected={[]}
            singleSelected={true}
            brandType={true}
            grade={true}
          />
        )}

        {/* Manpower popup start */}
        {popups["Manpower"] && (
          <AddPlanningThings
            title={"Manpower"}
            data={taskBuildingBlocks?.manpower as any}
            placeholder="search"
            buttonLabel="Add Category"
            onSelect={async (items: any) => {
              console.log(items, "thisisitems");
              const SubTaskToBeUpdated = selectAllSubtasksData.find(
                (e) => e._id == selectedsubtaskidForAddingThings
              );
              console.log(SubTaskToBeUpdated, "thisissub");
              const updatedSubtaskData = {
                ...SubTaskToBeUpdated,
                manpowerId: [
                  ...(SubTaskToBeUpdated &&
                  Array.isArray(SubTaskToBeUpdated["manpowerId"])
                    ? SubTaskToBeUpdated["manpowerId"]
                    : []),
                  ...items?.grade?.map((e: any, index: any) => {
                    return {
                      _id: `${Date.now()}-${Math.random()}` + index,
                      Mastermanpower_id: {
                        name: items?.material,
                        _id: items?.MaterialId,
                      },
                      type: e.toLowerCase(),
                    };
                  }),
                ],
              };
              console.log(updatedSubtaskData, "theupdatedsubtasksishereman");
              dispatch(
                setallSubtasksData(
                  selectAllSubtasksData?.map((e) =>
                    e._id == updatedSubtaskData?._id ? updatedSubtaskData : e
                  )
                )
              );
              await saveSyncData(
                updatedSubtaskData,
                Date.now().toString(),
                "SubtasklocDetail",
                false,
                dispatch
              );
              //
              console.log(
                selectedsubtaskidForAddingThings,
                items,
                "seledsdfcted"
              );
              // call the function to make toast
              makeToast("success", `${items?.material} Added Successfully`);
            }}
            onClose={() => dispatch(closePopup("Manpower"))}
            initialSelected={selectAllSubtasksData
              .find((e) => e._id == selectedsubtaskidForAddingThings)
              ?.manpowerId?.map((e: any) => {
                return {
                  _id: e?.Mastermanpower_id?._id,
                  category: e?.type,
                };
              })}
            singleSelected={true}
            brandType={true}
            grade={true}
          />
        )}
        {popups["DeletePlanningRequiredThings"] && (
          <DeletePopup
            header={`Are you sure you want to delete this ${getNameByKey(
              deleteKkey as any
            )}?`}
            height="calc(100% - 9rem)"
            callbackDelete={async () => {
              handledelete(deleteElememt, deleteKkey, subtaskDeleteId);
              dispatch(closePopup("DeletePlanningRequiredThings"));
            }}
            onClose={() => {
              dispatch(closePopup("DeletePlanningRequiredThings"));
            }}
          >
            <>
              {deleteKkey == "manpowerId" ? (
                <>
                  <div className={styles.flexContainer}>
                    {deleteElememt?.Mastermanpower_id?.name && (
                      <div className={styles.summaryDivData}>
                        <div className={styles.summaryDataContent}>
                          <p
                            style={{ color: "var(--text-black-60)" }}
                            className="p_tag_14px"
                          >
                            Name
                          </p>
                          <h4 style={{ color: "var(--text-black-87)" }}>
                            {deleteElememt?.Mastermanpower_id?.name}
                          </h4>
                        </div>
                      </div>
                    )}
                  </div>

                  <div className={styles.flexContainer}>
                    {deleteElememt?.quantity !== undefined &&
                      deleteElememt?.quantity !== 0 &&
                      deleteElememt?.quantity !== "" && (
                        <div className={styles.summaryDivData}>
                          <div className={styles.summaryDataContent}>
                            <p
                              style={{ color: "var(--text-black-60)" }}
                              className="p_tag_14px"
                            >
                              Quantity
                            </p>
                            <h4 style={{ color: "var(--text-black-87)" }}>
                              {deleteElememt?.quantity}
                            </h4>
                          </div>
                        </div>
                      )}
                    {deleteElememt?.type && (
                      <div className={styles.summaryDivData}>
                        <div className={styles.summaryDataContent}>
                          <p
                            style={{ color: "var(--text-black-60)" }}
                            className="p_tag_14px"
                          >
                            Type
                          </p>
                          <h4 style={{ color: "var(--text-black-87)" }}>
                            {deleteElememt?.type}
                          </h4>
                        </div>
                      </div>
                    )}
                  </div>
                </>
              ) : (
                <>
                  <>
                    <div className={styles.flexContainer}>
                      {deleteElememt &&
                        deleteKkey &&
                        getkeyByName(deleteKkey) && (
                          <div className={styles.summaryDivData}>
                            <div className={styles.summaryDataContent}>
                              <p
                                style={{ color: "var(--text-black-60)" }}
                                className="p_tag_14px"
                              >
                                Name
                              </p>
                              <h4 style={{ color: "var(--text-black-87)" }}>
                                {
                                  deleteElememt?.[getkeyByName(deleteKkey)]
                                    ?.name
                                }
                              </h4>
                            </div>
                          </div>
                        )}
                    </div>

                    <div className={styles.flexContainer}>
                      {deleteElememt?.quantity !== undefined &&
                        deleteElememt?.quantity !== 0 &&
                        deleteElememt?.quantity !== "" && (
                          <div className={styles.summaryDivData}>
                            <div className={styles.summaryDataContent}>
                              <p
                                style={{ color: "var(--text-black-60)" }}
                                className="p_tag_14px"
                              >
                                Quantity
                              </p>
                              <h4 style={{ color: "var(--text-black-87)" }}>
                                {deleteElememt?.quantity}
                              </h4>
                            </div>
                          </div>
                        )}
                      {deleteElememt?.quantity !== undefined &&
                        deleteElememt?.quantity !== 0 &&
                        deleteElememt?.quantity !== "" && (
                          <div className={styles.summaryDivData}>
                            <div className={styles.summaryDataContent}>
                              <p
                                style={{ color: "var(--text-black-60)" }}
                                className="p_tag_14px"
                              >
                                Unit
                              </p>
                              <h4 style={{ color: "var(--text-black-87)" }}>
                                {deleteElememt?.quantity}
                              </h4>
                            </div>
                          </div>
                        )}
                    </div>

                    <div className={styles.flexContainer}>
                      <div className={styles.summaryDivData}>
                        <div className={styles.summaryDataContent}>
                          <p
                            style={{ color: "var(--text-black-60)" }}
                            className="p_tag_14px"
                          >
                            Brand
                          </p>
                          <h4 style={{ color: "var(--text-black-87)" }}>
                            {deleteElememt?.Masterbrand_id?.Brandname}
                          </h4>
                        </div>
                      </div>

                      {deleteElememt?.spec && deleteElememt?.spec !== "" && (
                        <div className={styles.summaryDivData}>
                          <div className={styles.summaryDataContent}>
                            <p
                              style={{ color: "var(--text-black-60)" }}
                              className="p_tag_14px"
                            >
                              Spec
                            </p>
                            <h4 style={{ color: "var(--text-black-87)" }}>
                              {deleteElememt?.spec?.substring(0, 20)}
                            </h4>
                          </div>
                        </div>
                      )}
                    </div>
                  </>
                </>
              )}
            </>
          </DeletePopup>
        )}
      </div>
    </>
  );
};

export default PlanningTable;
