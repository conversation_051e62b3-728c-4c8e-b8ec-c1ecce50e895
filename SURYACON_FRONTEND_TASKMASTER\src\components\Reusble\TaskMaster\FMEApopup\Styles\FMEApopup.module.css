.fmea_container {
  position: fixed;
  top: 7rem;
  right: 1.5rem;

  transform: translate(0%, 0%);
  background: var(--blur-background);
  /* background-color: var(--main_background); */
  padding: 0.9rem 0.9rem 1.5rem 0.9rem;
  box-shadow: 0px 4px 40px 0px #00000080;
  border-radius: 2.6rem;
  z-index: 9999;
  width: 35rem;
  height: calc(100% - 8.5rem);
  animation: slideIn 0.5s ease-out;
  backdrop-filter: blur(150px);
}

@keyframes slideIn {
  from {
    transform: translate(100%, 0%);
  }

  to {
    transform: translate(0%, 0%);
  }
}

@keyframes slideOut {
  from {
    transform: translate(0%, 0%);
  }

  to {
    transform: translate(100%, 0%);
  }
}

.fmea_container.closing {
  animation: slideOut 0.5s ease-out;
}

.fmea_header {
  color: var(--primary_color);
  display: flex;
  justify-content: space-around !important;
  text-align: center !important;
  padding: 0.6rem;
}
.summary_main_content{
  height: calc(100% - 8.5rem);
  overflow: auto;
}
.closeButton {
  position: absolute;
  top: 1.5em;
  right: 0.625rem;
  padding: 1rem;
  background: transparent;
  border: none;

  cursor: pointer;
}

.fmea_btngrp {
  position: fixed;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 100%;
  padding:1.5rem 1rem;
  display: flex;
  justify-content: center;
  gap: 1rem;

  border-radius: 2.6rem;
  backdrop-filter: blur(60px);
}

.fmea_severity_div {
  position: relative;
  border-radius: 1.5rem;
  border: 1px solid var(--text-black-28);
  margin-top: 1rem;
  height: 54px;
  padding-inline: 1rem;
  padding-block: 1.8rem;
  color: var(--text-black-87);
  display: flex;
  gap: 0.5rem;
  align-items: center;
  justify-content: space-between;
}

.fmea_severity_div.error {
  border-color: var(--warning_color) !important;
}

.fmea_severity_counter {
  display: flex;
  gap: 1rem;
  background: var(--main_background);
  border-radius: 100px;
  align-items: center;
  backdrop-filter: blur(40px);
  padding: 0.4rem;
  box-shadow: 0px 0px 8px 0px #91a1a180;
}

input::placeholder {
  color: var(--text-black-28);

  opacity: 1;
}

.severity_minus,
.severity_plus {
  width: 28px;
  height: 28px;
  border-radius: 100px;
  background-color: var(--primary_color);
  display: flex;
  align-items: center;
  justify-content: center;
}

.summaryDivData {
  display: flex;
  align-items: center;
}

.summaryDataContent {
  display: flex;
  flex-direction: column;
  background: var(--main_background);
  border-radius: 0.75rem;
  /* width: 30.8rem; */
  width: 100%;
  /* max-width: 28.5rem; */
  min-height: 3rem;
  padding: 1rem;
  white-space: normal;
  margin: 0.6rem;
  /* gap: 0.2rem; */
  line-height: 1.363rem;
  text-align: left;
  /* word-break: break-all; */
  /* box-shadow: 0px 0px 4px 0px #91a1a1bf; */
}

.input_tags_wrapper{
  padding: 0 0.6rem;
}