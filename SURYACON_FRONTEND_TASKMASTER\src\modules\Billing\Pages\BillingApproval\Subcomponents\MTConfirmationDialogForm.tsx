/*👨‍🎓Author: <PERSON><PERSON><PERSON><PERSON>*/
import React, { FC, useState } from "react";
import styles from "../Styles/BillingApprovalSubcomponent.module.css";
import FloatingLabelInput from "../../../../../components/Reusble/Global/FloatingLabel";
import MTConfirmationSubCard from "./MTConfirmationSubCard";
import Button from "../../../../../components/Reusble/Global/Button";
import { MTSubTaskConfirmationDilalogueProps } from "../../../../../interfaces/Modules/Billing/BillingApproval/BillingApproval";
import { Cross } from "../../../../../assets/icons";

const MTConfirmationDialogForm: FC<MTSubTaskConfirmationDilalogueProps> = ({
  data,
  callBack1,
  callBack2,
  callBackCross,
  button1Content,
  button1Type,
  button2Content,
  button2Type,
  header,
  headerColor,
  inputDecline,
}) => {
  const CDdata = data ? Object.entries(data) : null;

  console.log("this is cd data", CDdata);
  // console.log('cddata hai:',CDdata?.[10][1].value);
  // console.log('data:',CDdata)
  const keysnotRender = (item: string) => {
    return item != "_id" && item != "Decline" && item != "Reason";
  };
  const [reason, setReason] = useState("");

  // Handle reason input change
  const handleReasonChange = (value: string) => {
    setReason(value);
  };

  // Modified callback for decline button
  const handleDeclineClick = () => {
    if (callBack2) {
      // Pass the reason to the callback
      callBack2(reason);
    }
  };

  return (
    <div className={`${styles.mt_confirmation_dialog_form_container}`}>
      <div className={`${styles.mt_confirmation_dialog_form_top}`}>
        <h3
          className={`${styles.mt_confirmation_dialog_form_top_text}`}
          style={{ color: headerColor ? headerColor : "var(--primary_color)" }}
        >
          {header}
        </h3>
        <div
          onClick={callBackCross}
          className={`${styles.mt_confirmation_cross_icon}`}
        >
          {" "}
          <Cross />{" "}
        </div>
        {inputDecline ? (
          <FloatingLabelInput
            onInputChange={handleReasonChange}
            id="decline_reason"
            props="mt_confirmation_decline_reason_text_area"
            label="Reason"
            value={reason}
          />
        ) : (
          <div className={`${styles.mt_confirmation_dialog_form_first_main}`}>
            {CDdata?.map(
              (item, index) =>
                keysnotRender(item[0]) && (
                  <MTConfirmationSubCard
                    key={index}
                    keyProp={item[0]}
                    valueProp={item[1].value}
                    property={item[1].className}
                    displayType="row"
                    displayChildType="childRow"
                  />
                )
            )}
          </div>
        )}
      </div>
      {header !== "Summary" && (
        <div className={`${styles.mt_confirmation_button_div} `}>
          <Button
            Callback={callBack1}
            type={button1Type || "Normal"}
            Content={button1Content || ""}
            property="mtcd_form_button"
          />
          <Button
            Callback={inputDecline ? handleDeclineClick : callBack2}
            type={button2Type || "Normal"}
            Content={button2Content || ""}
            property="mtcd_form_button"
          />
        </div>
      )}
    </div>
  );
};

export default MTConfirmationDialogForm;
