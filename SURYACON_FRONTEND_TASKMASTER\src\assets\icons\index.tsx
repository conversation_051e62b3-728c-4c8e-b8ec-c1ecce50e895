/* eslint-disable react-refresh/only-export-components */

import React, { FC } from "react";

import {
  crossProps,
  IconProps,
  TodoBoardProps,
} from "../../interfaces/Modules/IconsInterface/iconInterface";
// import ArrowIcon from "./downArrow.svg";
// import PlumberIcon from "./Plumber.svg";
// import CaprenterIcon from "./Carpenter.svg";
// import WelderIcon from "./Welder.svg";
// import SteelBinding from "./SteelBinding.svg";
// import ElectricianIcon from "./Electrician.svg";
// import ProfileIcon from "./profileicon.svg";
// import ActivityIcon from "./ActivityIcon.svg";
// export {
//   search_icon,
//   theme_icon,
//   notification_icon,
//   Warning,
//   deleteIcon,
//   cross,
//   ActionCross,
//   commentIcon,
//   reset,
//   ArrowIcon,
//   PlumberIcon,
//   CaprenterIcon,
//   WelderIcon,
//   SteelBinding,
//   ElectricianIcon,
//   ProfileIcon,
//   ActivityIcon,
// };
export interface Redcross {
  height?: string;
  width?: string;
}
// Restore Icon by rattandeep singh start

export const RestoreIcon: FC<IconProps> = ({
  color = "var(--primary_color)",
  width = "18",
  height = "18",
}) => {
  return (
    <>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={width}
        height={height}
        viewBox="0 0 24 24"
        fill="none"
      >
        <path
          d="M3.0625 13.0001C3.30791 15.1939 4.35109 17.2211 5.99364 18.696C7.63618 20.1709 9.76352 20.9907 11.9711 20.9995C14.1786 21.0082 16.3124 20.2053 17.9665 18.7434C19.6207 17.2816 20.6799 15.2628 20.9427 13.0709C21.2055 10.879 20.6535 8.66702 19.3918 6.85557C18.1301 5.04412 16.2466 3.75958 14.0995 3.24624C11.9525 2.7329 9.69166 3.02658 7.74701 4.07141C5.80237 5.11625 4.30956 6.83937 3.5525 8.91306"
          stroke={color}
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path
          d="M3 4V9H8"
          stroke={color}
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </svg>
    </>
  );
};

export const PasteIcon: FC = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="18"
      height="18"
      viewBox="0 0 18 20"
      fill="none"
    >
      <path
        d="M2.30775 20C1.81058 20 1.385 19.8106 1.031 19.4319C0.677 19.0531 0.5 18.5977 0.5 18.0658V3.74487C0.5 3.21292 0.677 2.75756 1.031 2.37879C1.385 2.00003 1.81058 1.81064 2.30775 1.81064H6.7135C6.852 1.28939 7.135 0.857307 7.5625 0.514384C7.99 0.171461 8.46917 0 9 0C9.55133 0 10.0382 0.171461 10.4605 0.514384C10.883 0.857307 11.1635 1.28939 11.302 1.81064H15.6923C16.1894 1.81064 16.615 2.00003 16.969 2.37879C17.323 2.75756 17.5 3.21292 17.5 3.74487V18.0658C17.5 18.5977 17.323 19.0531 16.969 19.4319C16.615 19.8106 16.1894 20 15.6923 20H2.30775ZM2.30775 18.3951H15.6923C15.7692 18.3951 15.8397 18.3607 15.9037 18.2921C15.9679 18.2236 16 18.1482 16 18.0658V3.74487C16 3.66248 15.9679 3.58705 15.9037 3.51857C15.8397 3.44991 15.7692 3.41559 15.6923 3.41559H13.5V5.24683C13.5 5.52092 13.4138 5.7506 13.2413 5.93588C13.0688 6.12116 12.855 6.21381 12.6 6.21381H5.39975C5.14475 6.21381 4.93108 6.12116 4.75875 5.93588C4.58625 5.7506 4.5 5.52092 4.5 5.24683V3.41559H2.30775C2.23075 3.41559 2.16025 3.44991 2.09625 3.51857C2.03208 3.58705 2 3.66248 2 3.74487V18.0658C2 18.1482 2.03208 18.2236 2.09625 18.2921C2.16025 18.3607 2.23075 18.3951 2.30775 18.3951ZM9.00175 3.53917C9.25825 3.53917 9.47275 3.44626 9.64525 3.26044C9.81758 3.0748 9.90375 2.84467 9.90375 2.57005C9.90375 2.2956 9.817 2.0661 9.6435 1.88153C9.47 1.69714 9.25492 1.60494 8.99825 1.60494C8.74175 1.60494 8.52725 1.69776 8.35475 1.8834C8.18242 2.06922 8.09625 2.29935 8.09625 2.5738C8.09625 2.84824 8.183 3.07775 8.3565 3.26232C8.53 3.44688 8.74508 3.53917 9.00175 3.53917Z"
        fill="#005968"
      />
    </svg>
  );
};

export const CopyIcon: FC = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="18"
      height="18"
      viewBox="0 0 17 20"
      fill="none"
    >
      <path
        d="M5.73811 16.2162C5.19198 16.2162 4.72973 16.027 4.35135 15.6486C3.97297 15.2703 3.78378 14.808 3.78378 14.2619V1.95432C3.78378 1.4082 3.97297 0.945946 4.35135 0.567568C4.72973 0.189189 5.19198 0 5.73811 0H14.8024C15.3486 0 15.8108 0.189189 16.1892 0.567568C16.5676 0.945946 16.7568 1.4082 16.7568 1.95432V14.2619C16.7568 14.808 16.5676 15.2703 16.1892 15.6486C15.8108 16.027 15.3486 16.2162 14.8024 16.2162H5.73811ZM5.73811 14.5946H14.8024C14.8857 14.5946 14.9619 14.5599 15.0311 14.4905C15.1005 14.4214 15.1351 14.3451 15.1351 14.2619V1.95432C15.1351 1.87108 15.1005 1.79486 15.0311 1.72567C14.9619 1.65631 14.8857 1.62162 14.8024 1.62162H5.73811C5.65486 1.62162 5.57865 1.65631 5.50946 1.72567C5.44009 1.79486 5.40541 1.87108 5.40541 1.95432V14.2619C5.40541 14.3451 5.44009 14.4214 5.50946 14.4905C5.57865 14.5599 5.65486 14.5946 5.73811 14.5946ZM1.95432 20C1.4082 20 0.945946 19.8108 0.567568 19.4324C0.189189 19.0541 0 18.5918 0 18.0457V4.9273C0 4.69721 0.0776575 4.5045 0.232973 4.34919C0.388108 4.19405 0.580721 4.11649 0.810811 4.11649C1.0409 4.11649 1.2336 4.19405 1.38892 4.34919C1.54405 4.5045 1.62162 4.69721 1.62162 4.9273V18.0457C1.62162 18.1289 1.65631 18.2051 1.72568 18.2743C1.79487 18.3437 1.87108 18.3784 1.95432 18.3784H11.8295C12.0595 18.3784 12.2523 18.4559 12.4076 18.6111C12.5627 18.7664 12.6403 18.9591 12.6403 19.1892C12.6403 19.4193 12.5627 19.6119 12.4076 19.767C12.2523 19.9223 12.0595 20 11.8295 20H1.95432Z"
        fill="#005968"
      />
    </svg>
  );
};

// Restore Icon by rattandeep singh end

export const ReverseArrow: FC = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="18"
      height="16"
      viewBox="0 0 18 16"
      fill="none"
    >
      <path
        d="M2.58594 8.00065C2.58594 11.5445 5.45878 14.4173 9.0026 14.4173C11.5623 14.4173 13.7719 12.9185 14.8018 10.7507M2.58594 8.00065L4.64844 9.37565M2.58594 8.00065L1.21094 10.0632M15.4193 8.00065C15.4193 4.45682 12.5464 1.58398 9.0026 1.58398C6.44291 1.58398 4.23327 3.08278 3.20343 5.25065M15.4193 8.00065L13.3568 6.62565M15.4193 8.00065L16.7943 5.93815"
        stroke="#005968"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  );
};
export const User: React.FC = () => {
  return (
    <svg
      width="16"
      height="20"
      viewBox="0 0 16 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M12 5C12 7.20914 10.2091 9 8 9C5.79086 9 4 7.20914 4 5C4 2.79086 5.79086 1 8 1C10.2091 1 12 2.79086 12 5Z"
        stroke="#005968"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M1 15V16C1 17.6569 2.34315 19 4 19H12C13.6569 19 15 17.6569 15 16V15C15 13.3431 13.6569 12 12 12H4C2.34315 12 1 13.3431 1 15Z"
        stroke="#005968"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  );
};
export const Area: React.FC = () => {
  return (
    <svg
      width="22"
      height="22"
      viewBox="0 0 22 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      xmlnsXlink="http://www.w3.org/1999/xlink"
    >
      <rect width="22" height="22" fill="url(#pattern0)" />
      <defs>
        <pattern
          id="pattern0"
          patternContentUnits="objectBoundingBox"
          width="1"
          height="1"
        >
          <use xlinkHref="#image0" transform="scale(0.00390625)" />
        </pattern>
        <image
          id="image0"
          width="256"
          height="256"
          xlinkHref="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAQAAAAEACAYAAABccqhmAAAACXBIWXMAAA7DAAAOwwHHb6hkAAAAGXRFWHRTb2Z0d2FyZQB3d3cuaW5rc2NhcGUub3Jnm+48GgAADWFJREFUeJzt3e2TleV9wPHvdfYAGmuZjIEEY2OdZDq1QZtExSaTWKwatDM1tTMrD6LjtA5BdgXT6R/Ay07fgAgLoRF3Utlds87YmTZpQ2JqnDSNgEw7YCftxGriAwpOIZNElN09V18sG8GCnPvsOffT9f28dO9775/O/r6e5wOSJEmSJEmSJEmSJEmSJEmSJEmSJEmSJEmSJEmSJEmSJEmSJEmS..."
        />
      </defs>
    </svg>
  );
};
export const MenuIcon: React.FC = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
    >
      <path
        d="M3.5 5.5C3.5 4.39543 4.39543 3.5 5.5 3.5H8.5C9.60457 3.5 10.5 4.39543 10.5 5.5V8.5C10.5 9.60457 9.60457 10.5 8.5 10.5H5.5C4.39543 10.5 3.5 9.60457 3.5 8.5V5.5Z"
        stroke="white"
        stroke-width="1.5"
        stroke-linejoin="round"
      />
      <path
        d="M13.5 5.5C13.5 4.39543 14.3954 3.5 15.5 3.5H18.5C19.6046 3.5 20.5 4.39543 20.5 5.5V8.5C20.5 9.60457 19.6046 10.5 18.5 10.5H15.5C14.3954 10.5 13.5 9.60457 13.5 8.5V5.5Z"
        stroke="white"
        stroke-width="1.5"
        stroke-linejoin="round"
      />
      <path
        d="M13.5 15.5C13.5 14.3954 14.3954 13.5 15.5 13.5H18.5C19.6046 13.5 20.5 14.3954 20.5 15.5V18.5C20.5 19.6046 19.6046 20.5 18.5 20.5H15.5C14.3954 20.5 13.5 19.6046 13.5 18.5V15.5Z"
        stroke="white"
        stroke-width="1.5"
        stroke-linejoin="round"
      />
      <path
        d="M3.5 15.5C3.5 14.3954 4.39543 13.5 5.5 13.5H8.5C9.60457 13.5 10.5 14.3954 10.5 15.5V18.5C10.5 19.6046 9.60457 20.5 8.5 20.5H5.5C4.39543 20.5 3.5 19.6046 3.5 18.5V15.5Z"
        stroke="white"
        stroke-width="1.5"
        stroke-linejoin="round"
      />
    </svg>
  );
};
export const RedCross: React.FC<Redcross> = ({
  height = "14",
  width = "14",
}) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 14 14"
      fill="none"
    >
      <path
        d="M13 1L7 7M7 7L1 13M7 7L1 1M7 7L13 13"
        stroke="#A80000"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  );
};
export const Cost: React.FC = () => {
  return (
    <svg
      width="20"
      height="19"
      viewBox="0 0 20 19"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M9 3.5C9 2.11929 7.20914 1 5 1C2.79086 1 1 2.11929 1 3.5M9 3.5C9 4.88071 7.20914 6 5 6C2.79086 6 1 4.88071 1 3.5M9 3.5V15.5C9 16.8807 7.20914 18 5 18C2.79086 18 1 16.8807 1 15.5V3.5M9 7.5C9 8.88071 7.20914 10 5 10C2.79086 10 1 8.88071 1 7.5M9 12C9 13.3807 7.20914 14.5 5 14.5C2.79086 14.5 1 13.3807 1 12M19 12.5C19 11.1193 17.2091 10 15 10C12.7909 10 11 11.1193 11 12.5M19 12.5C19 13.8807 17.2091 15 15 15C12.7909 15 11 13.8807 11 12.5M19 12.5V15.5C19 16.8807 17.2091 18 15 18C12.7909 18 11 16.8807 11 15.5V12.5"
        stroke="#005968"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  );
};
export const PaperBoard: React.FC = () => {
  return (
    <svg
      width="17"
      height="20"
      viewBox="0 0 17 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M11.5474 3H12.5474C14.2043 3 15.5474 4.34315 15.5474 6V16C15.5474 17.6569 14.2043 19 12.5474 19H4.54742C2.89057 19 1.54742 17.6569 1.54742 16V6C1.54742 4.34315 2.89057 3 4.54742 3H6.04742M11.5474 3C11.5474 4.10457 10.652 5 9.54742 5H7.54742C6.44285 5 5.54742 4.10457 5.54742 3C5.54742 1.89543 6.44285 1 7.54742 1H9.54742C10.652 1 11.5474 1.89543 11.5474 3Z"
        stroke="#005968"
        stroke-width="1.5"
        stroke-linejoin="round"
      />
    </svg>
  );
};
export const CompletionCost: React.FC = () => {
  return (
    <svg
      width="20"
      height="19"
      viewBox="0 0 20 19"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M9 3.5C9 2.11929 7.20914 1 5 1C2.79086 1 1 2.11929 1 3.5M9 3.5C9 4.88071 7.20914 6 5 6C2.79086 6 1 4.88071 1 3.5M9 3.5V15.5C9 16.8807 7.20914 18 5 18C2.79086 18 1 16.8807 1 15.5V3.5M9 7.5C9 8.88071 7.20914 10 5 10C2.79086 10 1 8.88071 1 7.5M9 12C9 13.3807 7.20914 14.5 5 14.5C2.79086 14.5 1 13.3807 1 12M19 12.5C19 11.1193 17.2091 10 15 10C12.7909 10 11 11.1193 11 12.5M19 12.5C19 13.8807 17.2091 15 15 15C12.7909 15 11 13.8807 11 12.5M19 12.5V15.5C19 16.8807 17.2091 18 15 18C12.7909 18 11 16.8807 11 15.5V12.5"
        stroke="#E8B000"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  );
};
export const Duration: React.FC = () => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M1 10C1 14.9706 5.02944 19 10 19C14.9706 19 19 14.9706 19 10C19 5.02944 14.9706 1 10 1C6.46628 1 3.40822 3.03656 1.93552 6M1.93552 6L1 3M1.93552 6L5 5.5M10 5.00002V9.38199C10 9.76076 10.214 10.107 10.5528 10.2764L13 11.5"
        stroke="#005968"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  );
};

export const Calendar: React.FC = () => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M6 5V1M14 5V1M6.01 8H6M6.01 11H6M6.01 14H6M10.01 11H10M10.01 8H10M14.01 11H14M10.01 14H10M14.01 8H14M4 19H16C17.6569 19 19 17.6569 19 16V6C19 4.34315 17.6569 3 16 3H4C2.34315 3 1 4.34315 1 6V16C1 17.6569 2.34315 19 4 19Z"
        stroke="#005968"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  );
};

export const Twodots = ({ stroke = "#00596B" }: { stroke?: string }) => {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M16.8 9.5C15.2779 9.5 14.1 10.6492 14.1 12C14.1 13.3508 15.2779 14.5 16.8 14.5C18.3221 14.5 19.5 13.3508 19.5 12C19.5 10.6492 18.3221 9.5 16.8 9.5ZM7.2 9.5C5.67793 9.5 4.5 10.6492 4.5 12C4.5 13.3508 5.67793 14.5 7.2 14.5C8.72207 14.5 9.9 13.3508 9.9 12C9.9 10.6492 8.72207 9.5 7.2 9.5Z"
        stroke={stroke}
      />
    </svg>
  );
};

export const AddIcon = () => {
  return (
    <svg
      width="14"
      height="14"
      viewBox="0 0 14 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M7 1V7M7 7V13M7 7H1M7 7H13"
        stroke="white"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
export const SiteDrawingIcon = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
    >
      <path
        d="M11.5 8.5L6 3L3 6L8.5 11.5M16.25 13.25L20 17L20.5 20.5L17 20L13.25 16.25M17 3L3 17V21H7L21 7L17 3Z"
        stroke="black"
        stroke-opacity="0.6"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M14 6L18 10"
        stroke="black"
        stroke-opacity="0.6"
        stroke-width="2"
        stroke-linejoin="round"
      />
    </svg>
  );
};

export const DeleteIcon = () => {
  return (
    <svg
      width="14"
      height="14"
      viewBox="0 0 14 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M3.66704 4L4.1205 2.63962C4.34735 1.95905 4.98425 1.5 5.70164 1.5H8.2991C9.01649 1.5 9.65339 1.95905 9.88024 2.63962L10.3337 4M3.66704 4H1.68206C1.06258 4 0.659667 4.65193 0.936709 5.20601L1.34423 6.02106C1.50209 6.33677 1.5913 6.68234 1.606 7.03501L1.90054 14.1041C1.95633 15.4431 3.05814 16.5 4.39837 16.5H9.60237C10.9426 16.5 12.0444 15.4431 12.1002 14.1041L12.3947 7.03501C12.4094 6.68234 12.4987 6.33677 12.6565 6.02105L13.064 5.20601C13.3411 4.65193 12.9382 4 12.3187 4H10.3337M3.66704 4H10.3337M9.03365 12.75L9.24199 7.75M4.96709 12.75L4.75875 7.75"
        stroke="#A80000"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const History = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
    >
      <path
        d="M1 10C1 14.9706 5.02944 19 10 19C14.9706 19 19 14.9706 19 10C19 5.02944 14.9706 1 10 1C6.46628 1 3.40822 3.03656 1.93552 6M1.93552 6L1 3M1.93552 6L5 5.5M10 5.00002V9.38199C10 9.76076 10.214 10.107 10.5528 10.2764L13 11.5"
        stroke="#00596B"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  );
};

export const Uploadicon: FC<IconProps> = ({
  height = 14,
  width = 14,
  color = "var(--primary_color)",
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 14 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M3.81188 12.959L3.69456 13.6997H3.69456L3.81188 12.959ZM1.04104 10.1881L1.78181 10.0708L1.04104 10.1881ZM12.959 10.1881L13.6997 10.3054V10.3054L12.959 10.1881ZM10.1881 12.959L10.3054 13.6997H10.3054L10.1881 12.959ZM12.9333 5.8829C12.6845 5.55171 12.2143 5.48489 11.8831 5.73366C11.552 5.98242 11.4851 6.45257 11.7339 6.78376L12.9333 5.8829ZM2.2661 6.78376C2.51486 6.45257 2.44805 5.98242 2.11686 5.73366C1.78566 5.48489 1.31552 5.55171 1.06675 5.8829L2.2661 6.78376ZM6.25 10.3333C6.25 10.7475 6.58579 11.0833 7 11.0833C7.41422 11.0833 7.75 10.7475 7.75 10.3333H6.25ZM3.74565 3.2007C3.4883 3.52526 3.5428 3.997 3.86736 4.25435C4.19193 4.5117 4.66367 4.45721 4.92102 4.13264L3.74565 3.2007ZM5.26523 2.49135L4.67755 2.02538H4.67755L5.26523 2.49135ZM8.73477 2.49135L9.32245 2.02538L8.73477 2.49135ZM9.07898 4.13264C9.33633 4.45721 9.80807 4.5117 10.1326 4.25435C10.4572 3.997 10.5117 3.52527 10.2544 3.2007L9.07898 4.13264ZM6.83289 1.01326L6.71489 0.272601L6.71488 0.272602L6.83289 1.01326ZM7.16711 1.01326L7.28512 0.272602L7.28512 0.272601L7.16711 1.01326ZM12.25 8.33333V9H13.75V8.33333H12.25ZM9 12.25H5V13.75H9V12.25ZM1.75 9V8.33333H0.25V9H1.75ZM5 12.25C4.352 12.25 4.11532 12.2477 3.92921 12.2182L3.69456 13.6997C4.02666 13.7523 4.40932 13.75 5 13.75V12.25ZM0.25 9C0.25 9.59068 0.247672 9.97333 0.300273 10.3054L1.78181 10.0708C1.75233 9.88467 1.75 9.64799 1.75 9H0.25ZM3.92921 12.2182C2.82382 12.0431 1.95688 11.1762 1.78181 10.0708L0.300273 10.3054C0.577006 12.0527 1.94733 13.423 3.69456 13.6997L3.92921 12.2182ZM12.25 9C12.25 9.64799 12.2477 9.88467 12.2182 10.0708L13.6997 10.3054C13.7523 9.97333 13.75 9.59068 13.75 9H12.25ZM9 13.75C9.59068 13.75 9.97334 13.7523 10.3054 13.6997L10.0708 12.2182C9.88468 12.2477 9.648 12.25 9 12.25V13.75ZM12.2182 10.0708C12.0431 11.1762 11.1762 12.0431 10.0708 12.2182L10.3054 13.6997C12.0527 13.423 13.423 12.0527 13.6997 10.3054L12.2182 10.0708ZM13.75 8.33333C13.75 7.41495 13.4459 6.56542 12.9333 5.8829L11.7339 6.78376C12.0582 7.21545 12.25 7.75091 12.25 8.33333H13.75ZM1.75 8.33333C1.75 7.75091 1.94184 7.21545 2.2661 6.78376L1.06675 5.8829C0.554082 6.56542 0.25 7.41495 0.25 8.33333H1.75ZM7.75 10.3333V1.66667H6.25V10.3333H7.75ZM4.92102 4.13264L5.85292 2.95732L4.67755 2.02538L3.74565 3.2007L4.92102 4.13264ZM8.14709 2.95732L9.07898 4.13264L10.2544 3.2007L9.32245 2.02538L8.14709 2.95732ZM5.85292 2.95732C6.23588 2.47432 6.48402 2.16323 6.68926 1.95719C6.78717 1.8589 6.85409 1.8068 6.89843 1.77913C6.91924 1.76614 6.93272 1.76005 6.93979 1.75728C6.94639 1.75469 6.94957 1.75413 6.9509 1.75392L6.71488 0.272602C6.25494 0.345883 5.90947 0.614575 5.62656 0.898577C5.34699 1.17922 5.03775 1.5711 4.67755 2.02538L5.85292 2.95732ZM9.32245 2.02538C8.96225 1.5711 8.65301 1.17922 8.37344 0.898578C8.09054 0.614576 7.74506 0.345884 7.28512 0.272602L7.04911 1.75392C7.05043 1.75413 7.05361 1.75469 7.06021 1.75728C7.06728 1.76005 7.08076 1.76614 7.10157 1.77913C7.14591 1.8068 7.21283 1.8589 7.31074 1.95719C7.51599 2.16323 7.76412 2.47432 8.14709 2.95732L9.32245 2.02538ZM6.9509 1.75392C6.96749 1.75127 6.98385 1.75 7 1.75V0.25C6.90452 0.25 6.80925 0.257566 6.71489 0.272601L6.9509 1.75392ZM7 1.75C7.01615 1.75 7.03251 1.75127 7.04911 1.75392L7.28512 0.272601C7.19075 0.257566 7.09548 0.25 7 0.25V1.75ZM7.75 1.66667V1H6.25V1.66667H7.75Z"
        fill={color}
      />
    </svg>
  );
};

export const ChatIcon: FC<IconProps> = ({
  height = 18,
  width = 18,
  color = "var(--text-white-100)",
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M5.99996 8.5H14M5.99996 12.5H9.99996M0.999956 10C0.999956 5.02944 5.02939 1 9.99996 1C14.9705 1 19 5.02944 19 10C19 14.9706 14.9705 19 9.99996 19C9.30693 19 8.63219 18.9217 7.98412 18.7734C7.35813 18.6301 6.70593 18.6103 6.08102 18.7581C4.68549 19.0884 2.75928 19.3545 1.44146 18.9801C0.886145 18.8224 0.831783 18.1354 1.09941 17.6239C1.41072 17.0289 1.68154 16.2964 1.88622 15.6456C2.11544 14.9167 1.97001 14.1402 1.67829 13.434C1.24114 12.3758 0.999956 11.2161 0.999956 10Z"
        stroke={color}
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  );
};

export const LocationIcon = () => {
  return (
    <svg
      width="18"
      height="23"
      viewBox="0 0 18 23"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M12.5026 9.66667C12.5026 11.5997 10.9356 13.1667 9.0026 13.1667C7.06961 13.1667 5.5026 11.5997 5.5026 9.66667C5.5026 7.73367 7.06961 6.16667 9.0026 6.16667C10.9356 6.16667 12.5026 7.73367 12.5026 9.66667Z"
        stroke="black"
        strokeOpacity="0.87"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M9.0026 1.5C4.49228 1.5 0.835938 5.15634 0.835938 9.66667C0.835938 11.6658 1.55423 13.4971 2.7468 14.9167L7.10449 21.0118C8.03514 22.3135 9.97007 22.3135 10.9007 21.0118L15.2584 14.9167C16.451 13.4971 17.1693 11.6658 17.1693 9.66667C17.1693 5.15634 13.5129 1.5 9.0026 1.5Z"
        stroke="black"
        strokeOpacity="0.87"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const CloseIcon: React.FC<{
  pos?: React.CSSProperties["position"];
  right?: string;
}> = ({ pos, right }) => {
  return (
    <svg
      width="14"
      height="14"
      viewBox="0 0 14 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      style={{
        position: pos || undefined,
        right: right || undefined,
        cursor: "pointer",
      }}
    >
      <path
        d="M13 1L7 7M7 7L1 13M7 7L1 1M7 7L13 13"
        stroke="black"
        strokeOpacity="0.87"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const MiniCloseIcon = () => {
  return (
    <svg
      width="10"
      height="10"
      viewBox="0 0 10 10"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M9 1L5 5M5 5L1 9M5 5L1 1M5 5L9 9"
        stroke="black"
        strokeOpacity="0.87"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const AddCategoryIcon = ({
  marginTop = 0,
  color = "#00596B",
  fill = "none",
}) => {
  return (
    <svg
      width="18"
      height="18"
      viewBox="0 0 18 18"
      fill={fill}
      xmlns="http://www.w3.org/2000/svg"
      style={{ marginTop: `${marginTop}rem` }}
    >
      <path
        d="M9 6.5V9M9 9V11.5M9 9H6.5M9 9H11.5M16.5 9C16.5 4.85786 13.1421 1.5 9 1.5C4.85786 1.5 1.5 4.85786 1.5 9C1.5 13.1421 4.85786 16.5 9 16.5C13.1421 16.5 16.5 13.1421 16.5 9Z"
        stroke={color}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
export const SearchIcon = () => {
  return (
    <svg
      width="18"
      height="18"
      viewBox="0 0 25 25"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M21.875 21.875L15.625 15.625M17.7083 10.4167C17.7083 14.4437 14.4437 17.7083 10.4167 17.7083C6.38959 17.7083 3.125 14.4437 3.125 10.4167C3.125 6.38959 6.38959 3.125 10.4167 3.125C14.4437 3.125 17.7083 6.38959 17.7083 10.4167Z"
        stroke="black"
        strokeOpacity="0.6"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
export const Rupee: React.FC<RupeeProps> = ({
  color,
  width = 13,
  height = 16,
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 13 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      aria-hidden="true"
      focusable="false"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M5.45935 2.16667H1.29268C1.07167 2.16667 0.859709 2.07887 0.703428 1.92259C0.547148 1.76631 0.459351 1.55435 0.459351 1.33333C0.459351 1.11232 0.547148 0.900358 0.703428 0.744078C0.859709 0.587797 1.07167 0.5 1.29268 0.5H11.2927C11.5137 0.5 11.7257 0.587797 11.8819 0.744078C12.0382 0.900358 12.126 1.11232 12.126 1.33333C12.126 1.55435 12.0382 1.76631 11.8819 1.92259C11.7257 2.07887 11.5137 2.16667 11.2927 2.16667H8.79268C9.16367 2.65944 9.41991 3.22886 9.54268 3.83333H11.2927C11.5137 3.83333 11.7257 3.92113 11.8819 4.07741C12.0382 4.23369 12.126 4.44565 12.126 4.66667C12.126 4.88768 12.0382 5.09964 11.8819 5.25592C11.7257 5.4122 11.5137 5.5 11.2927 5.5H9.54268C9.35029 6.44099 8.83883 7.28667 8.0948 7.89405C7.35077 8.50142 6.41981 8.83322 5.45935 8.83333H4.97102L10.2152 14.0775C10.367 14.2347 10.451 14.4452 10.4491 14.6637C10.4472 14.8822 10.3595 15.0912 10.205 15.2457C10.0505 15.4002 9.84152 15.4878 9.62302 15.4897C9.40452 15.4916 9.19402 15.4076 9.03685 15.2558L2.37018 8.58917C2.25368 8.47262 2.17434 8.32415 2.1422 8.16253C2.11006 8.0009 2.12656 7.83337 2.18962 7.68112C2.25268 7.52887 2.35946 7.39874 2.49647 7.30717C2.63348 7.21559 2.79456 7.1667 2.95935 7.16667H5.45935C5.97642 7.16677 6.4808 7.00654 6.90301 6.70805C7.32522 6.40956 7.6445 5.9875 7.81685 5.5H1.29268C1.07167 5.5 0.859709 5.4122 0.703428 5.25592C0.547148 5.09964 0.459351 4.88768 0.459351 4.66667C0.459351 4.44565 0.547148 4.23369 0.703428 4.07741C0.859709 3.92113 1.07167 3.83333 1.29268 3.83333H7.81685C7.6445 3.34584 7.32522 2.92378 6.90301 2.62529C6.4808 2.3268 5.97642 2.16657 5.45935 2.16667Z"
        fill={color}
      />
    </svg>
  );
};
export const CategoryIcon = () => {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M22.5 15C22.5 14.403 22.263 13.8307 21.8407 13.4092C21.632 13.2002 21.384 13.0343 21.111 12.9212C20.8381 12.8081 20.5455 12.7499 20.25 12.75H15C14.403 12.75 13.8307 12.987 13.4092 13.4092C13.2002 13.618 13.0343 13.866 12.9212 14.139C12.8081 14.4119 12.7499 14.7045 12.75 15V20.25C12.75 20.847 12.987 21.4193 13.4092 21.8407C13.618 22.0498 13.866 22.2157 14.139 22.3288C14.4119 22.4419 14.7045 22.5001 15 22.5H20.25C20.847 22.5 21.4193 22.263 21.8407 21.8407C22.0498 21.632 22.2157 21.384 22.3288 21.111C22.4419 20.8381 22.5001 20.5455 22.5 20.25V15ZM11.25 15C11.25 14.403 11.013 13.8307 10.5907 13.4092C10.382 13.2002 10.134 13.0343 9.86103 12.9212C9.58807 12.8081 9.29548 12.7499 9 12.75H3.75C3.153 12.75 2.58075 12.987 2.15925 13.4092C1.95017 13.618 1.78432 13.866 1.6712 14.139C1.55808 14.4119 1.4999 14.7045 1.5 15V20.25C1.5 20.847 1.737 21.4193 2.15925 21.8407C2.36804 22.0498 2.61601 22.2157 2.88897 22.3288C3.16193 22.4419 3.45453 22.5001 3.75 22.5H9C9.597 22.5 10.1692 22.263 10.5907 21.8407C10.7998 21.632 10.9657 21.384 11.0788 21.111C11.1919 20.8381 11.2501 20.5455 11.25 20.25V15ZM21 15V20.25C21.0003 20.3486 20.9811 20.4462 20.9435 20.5374C20.9059 20.6285 20.8507 20.7113 20.781 20.781C20.7113 20.8507 20.6285 20.9059 20.5374 20.9435C20.4462 20.9811 20.3486 21.0003 20.25 21H15C14.9014 21.0003 14.8038 20.9811 14.7126 20.9435C14.6215 20.9059 14.5387 20.8507 14.469 20.781C14.3993 20.7113 14.3441 20.6285 14.3065 20.5374C14.2689 20.4462 14.2497 20.3486 14.25 20.25V15C14.2497 14.9014 14.2689 14.8038 14.3065 14.7126C14.3441 14.6215 14.3993 14.5387 14.469 14.469C14.5387 14.3993 14.6215 14.3441 14.7126 14.3065C14.8038 14.2689 14.9014 14.2497 15 14.25H20.25C20.3486 14.2497 20.4462 14.2689 20.5374 14.3065C20.6285 14.3441 20.7113 14.3993 20.781 14.469C20.8507 14.5387 20.9059 14.6215 20.9435 14.7126C20.9811 14.8038 21.0003 14.9014 21 15ZM9.75 15V20.25C9.7503 20.3486 9.7311 20.4462 9.69351 20.5374C9.65593 20.6285 9.6007 20.7113 9.53099 20.781C9.46129 20.8507 9.37849 20.9059 9.28736 20.9435C9.19624 20.9811 9.09857 21.0003 9 21H3.75C3.65143 21.0003 3.55376 20.9811 3.46264 20.9435C3.37151 20.9059 3.28871 20.8507 3.21901 20.781C3.1493 20.7113 3.09407 20.6285 3.05649 20.5374C3.0189 20.4462 2.9997 20.3486 3 20.25V15C2.9997 14.9014 3.0189 14.8038 3.05649 14.7126C3.09407 14.6215 3.1493 14.5387 3.21901 14.469C3.28871 14.3993 3.37151 14.3441 3.46264 14.3065C3.55376 14.2689 3.65143 14.2497 3.75 14.25H9C9.09857 14.2497 9.19624 14.2689 9.28736 14.3065C9.37849 14.3441 9.46129 14.3993 9.53099 14.469C9.6007 14.5387 9.65593 14.6215 9.69351 14.7126C9.7311 14.8038 9.7503 14.9014 9.75 15ZM11.25 3.75C11.25 3.153 11.013 2.58075 10.5907 2.15925C10.382 1.95017 10.134 1.78432 9.86103 1.6712C9.58807 1.55808 9.29548 1.4999 9 1.5H3.75C3.153 1.5 2.58075 1.737 2.15925 2.15925C1.95017 2.36804 1.78432 2.61601 1.6712 2.88897C1.55808 3.16193 1.4999 3.45453 1.5 3.75V9C1.5 9.597 1.737 10.1692 2.15925 10.5907C2.36804 10.7998 2.61601 10.9657 2.88897 11.0788C3.16193 11.1919 3.45453 11.2501 3.75 11.25H9C9.597 11.25 10.1692 11.013 10.5907 10.5907C10.7998 10.382 10.9657 10.134 11.0788 9.86103C11.1919 9.58807 11.2501 9.29548 11.25 9V3.75ZM9.75 3.75V9C9.7503 9.09857 9.7311 9.19624 9.69351 9.28736C9.65593 9.37849 9.6007 9.46129 9.53099 9.53099C9.46129 9.6007 9.37849 9.65593 9.28736 9.69351C9.19624 9.7311 9.09857 9.7503 9 9.75H3.75C3.65143 9.7503 3.55376 9.7311 3.46264 9.69351C3.37151 9.65593 3.28871 9.6007 3.21901 9.53099C3.1493 9.46129 3.09407 9.37849 3.05649 9.28736C3.0189 9.19624 2.9997 9.09857 3 9V3.75C2.9997 3.65143 3.0189 3.55376 3.05649 3.46264C3.09407 3.37151 3.1493 3.28871 3.21901 3.21901C3.28871 3.1493 3.37151 3.09407 3.46264 3.05649C3.55376 3.0189 3.65143 2.9997 3.75 3H9C9.09857 2.9997 9.19624 3.0189 9.28736 3.05649C9.37849 3.09407 9.46129 3.1493 9.53099 3.21901C9.6007 3.28871 9.65593 3.37151 9.69351 3.46264C9.7311 3.55376 9.7503 3.65143 9.75 3.75Z"
        fill="#00596B"
      />
      <path
        d="M22 6.5C22 8.98528 19.9853 11 17.5 11C15.0147 11 13 8.98528 13 6.5C13 4.01472 15.0147 2 17.5 2C19.9853 2 22 4.01472 22 6.5ZM14.7709 6.5C14.7709 8.00721 15.9928 9.22905 17.5 9.22905C19.0072 9.22905 20.2291 8.00721 20.2291 6.5C20.2291 4.99279 19.0072 3.77095 17.5 3.77095C15.9928 3.77095 14.7709 4.99279 14.7709 6.5Z"
        fill="#00596B"
      />
    </svg>
  );
};

export const PercentageIcon = () => {
  return (
    <svg
      width="12"
      height="16"
      viewBox="0 0 12 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M10.0782 1.70543C10.1311 1.6072 10.1638 1.49933 10.1743 1.38822C10.1848 1.27711 10.1729 1.16503 10.1392 1.05862C10.1056 0.952213 10.0509 0.853648 9.97843 0.768775C9.90596 0.683902 9.81719 0.61445 9.71737 0.56454C9.61755 0.51463 9.50872 0.485278 9.39734 0.478227C9.28596 0.471176 9.1743 0.486569 9.06898 0.523493C8.96366 0.560417 8.86683 0.618119 8.78424 0.693176C8.70165 0.768232 8.63497 0.859115 8.58817 0.96043L1.9215 14.2938C1.86853 14.392 1.83583 14.4999 1.82534 14.611C1.81485 14.7221 1.82679 14.8342 1.86044 14.9406C1.89409 15.047 1.94877 15.1455 2.02124 15.2304C2.09371 15.3153 2.18249 15.3847 2.28231 15.4347C2.38213 15.4846 2.49096 15.5139 2.60234 15.521C2.71372 15.528 2.82538 15.5126 2.9307 15.4757C3.03601 15.4388 3.13284 15.3811 3.21543 15.306C3.29803 15.231 3.3647 15.1401 3.4115 15.0388L10.0782 1.70543ZM2.6665 2.16626C2.44549 2.16626 2.23353 2.25406 2.07725 2.41034C1.92097 2.56662 1.83317 2.77858 1.83317 2.9996C1.83317 3.22061 1.92097 3.43257 2.07725 3.58885C2.23353 3.74513 2.44549 3.83293 2.6665 3.83293C2.88752 3.83293 3.09948 3.74513 3.25576 3.58885C3.41204 3.43257 3.49984 3.22061 3.49984 2.9996C3.49984 2.77858 3.41204 2.56662 3.25576 2.41034C3.09948 2.25406 2.88752 2.16626 2.6665 2.16626ZM0.166504 2.9996C0.166504 2.33656 0.429896 1.70067 0.898737 1.23183C1.36758 0.762988 2.00346 0.499596 2.6665 0.499596C3.32955 0.499596 3.96543 0.762988 4.43427 1.23183C4.90311 1.70067 5.1665 2.33656 5.1665 2.9996C5.1665 3.66264 4.90311 4.29852 4.43427 4.76736C3.96543 5.2362 3.32955 5.4996 2.6665 5.4996C2.00346 5.4996 1.36758 5.2362 0.898737 4.76736C0.429896 4.29852 0.166504 3.66264 0.166504 2.9996ZM9.33317 12.1663C9.11216 12.1663 8.9002 12.2541 8.74391 12.4103C8.58764 12.5666 8.49984 12.7786 8.49984 12.9996C8.49984 13.2206 8.58764 13.4326 8.74391 13.5889C8.9002 13.7451 9.11216 13.8329 9.33317 13.8329C9.55418 13.8329 9.76615 13.7451 9.92243 13.5889C10.0787 13.4326 10.1665 13.2206 10.1665 12.9996C10.1665 12.7786 10.0787 12.5666 9.92243 12.4103C9.76615 12.2541 9.55418 12.1663 9.33317 12.1663ZM6.83317 12.9996C6.83317 12.3366 7.09656 11.7007 7.5654 11.2318C8.03424 10.763 8.67013 10.4996 9.33317 10.4996C9.99621 10.4996 10.6321 10.763 11.1009 11.2318C11.5698 11.7007 11.8332 12.3366 11.8332 12.9996C11.8332 13.6626 11.5698 14.2985 11.1009 14.7674C10.6321 15.2362 9.99621 15.4996 9.33317 15.4996C8.67013 15.4996 8.03424 15.2362 7.5654 14.7674C7.09656 14.2985 6.83317 13.6626 6.83317 12.9996Z"
        fill="#005968"
      />
    </svg>
  );
};

// export const StarIcon = () => {
//   return (
//     <svg
//       width="16"
//       height="16"
//       viewBox="0 0 16 16"
//       fill="none"
//       xmlns="http://www.w3.org/2000/svg"
//       xmlns:xlink="http://www.w3.org/1999/xlink"
//     >
//       <rect width="16" height="16" fill="url(#pattern0_640_8872)" />
//       <defs>
//         <pattern
//           id="pattern0_640_8872"
//           patternContentUnits="objectBoundingBox"
//           width="1"
//           height="1"
//         >
//           <use
//             xlink:href="#image0_640_8872"
//             transform="translate(-0.291925 -0.298137) scale(0.00621118)"
//           />
//         </pattern>
//         <image
//           id="image0_640_8872"
//           width="256"
//           height="256"
//           xlink:href="data:image/png;base64,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"
//         />
//       </defs>
//     </svg>
//   );
// };
export const DropDownCategoryIcon = () => {
  return (
    <svg
      width="14"
      height="7"
      viewBox="0 0 14 7"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M1.66731 0.999975L5.58644 4.9191C6.36749 5.70015 7.63382 5.70014 8.41486 4.9191L12.334 0.999976"
        stroke="#004350"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

// export const DesignationIcon = () => {
//   return (
//     <svg
//       width="22"
//       height="22"
//       viewBox="0 0 22 22"
//       fill="none"
//       xmlns="http://www.w3.org/2000/svg"
//     >
//       <path
//         d="M8.85258 11.1544L8.67886 11.884L8.67886 11.884L8.85258 11.1544ZM13.1474 11.1544L13.3211 11.884L13.3211 11.884L13.1474 11.1544ZM11.0092 9.22917C11.4234 9.22917 11.7592 8.89338 11.7592 8.47917C11.7592 8.06495 11.4234 7.72917 11.0092 7.72917V9.22917ZM11 7.72917C10.5858 7.72917 10.25 8.06495 10.25 8.47917C10.25 8.89338 10.5858 9.22917 11 9.22917V7.72917ZM2 7.5625V8.34006H3.5V7.5625H2ZM9.48954 11.9792H12.5105V10.4792H9.48954V11.9792ZM20 8.34006V7.5625H18.5V8.34006H20ZM3.49295 10.6492L8.67886 11.884L9.02629 10.4248L3.84038 9.19004L3.49295 10.6492ZM2.91667 9.91964V15.8125H4.41667V9.91964H2.91667ZM6.41667 19.3125H15.5833V17.8125H6.41667V19.3125ZM13.3211 11.884L18.507 10.6492L18.1596 9.19004L12.9737 10.4248L13.3211 11.884ZM19.0833 15.8125V9.91964H17.5833V15.8125H19.0833ZM7.33333 4.97917H4.58333V6.47917H7.33333V4.97917ZM17.4167 4.97917H14.6667V6.47917H17.4167V4.97917ZM14.6667 4.97917H7.33333V6.47917H14.6667V4.97917ZM9.625 4.1875H12.375V2.6875H9.625V4.1875ZM15.5833 19.3125C17.5163 19.3125 19.0833 17.7455 19.0833 15.8125H17.5833C17.5833 16.9171 16.6879 17.8125 15.5833 17.8125V19.3125ZM2.91667 15.8125C2.91667 17.7455 4.48367 19.3125 6.41667 19.3125V17.8125C5.3121 17.8125 4.41667 16.9171 4.41667 15.8125H2.91667ZM20 7.5625C20 6.13576 18.8434 4.97917 17.4167 4.97917V6.47917C18.015 6.47917 18.5 6.96419 18.5 7.5625H20ZM18.5 8.34006C18.5 8.72378 18.2944 9.07808 17.9612 9.26846L18.7054 10.5708C19.506 10.1134 20 9.26207 20 8.34006H18.5ZM12.5105 11.9792C12.7835 11.9792 13.0556 11.9472 13.3211 11.884L12.9737 10.4248C12.8219 10.4609 12.6665 10.4792 12.5105 10.4792V11.9792ZM15.4167 5.72917C15.4167 4.0493 14.0549 2.6875 12.375 2.6875V4.1875C13.2264 4.1875 13.9167 4.87773 13.9167 5.72917H15.4167ZM9.48954 10.4792C9.33353 10.4792 9.17806 10.4609 9.02629 10.4248L8.67886 11.884C8.94445 11.9472 9.21652 11.9792 9.48954 11.9792V10.4792ZM2 8.34006C2 9.26207 2.49404 10.1134 3.29456 10.5708L4.03877 9.26846C3.70561 9.07808 3.5 8.72378 3.5 8.34006H2ZM8.08333 5.72917C8.08333 4.87773 8.77356 4.1875 9.625 4.1875V2.6875C7.94513 2.6875 6.58333 4.0493 6.58333 5.72917H8.08333ZM3.5 7.5625C3.5 6.96419 3.98502 6.47917 4.58333 6.47917V4.97917C3.1566 4.97917 2 6.13576 2 7.5625H3.5ZM11.0092 7.72917H11V9.22917H11.0092V7.72917Z"
//         fill="#00596B"
//       />
//     </svg>
//   );
// };
// export const DropDownArrowUpIcon = () => {
//   return (
//     <svg
//       width="13"
//       height="7"
//       viewBox="0 0 13 7"
//       fill="none"
//       xmlns="http://www.w3.org/2000/svg"
//     >
//       <path
//         d="M0.833657 6.00002L4.99772 2.0809C5.82759 1.29985 7.17306 1.29986 8.00293 2.08091L12.167 6.00002"
//         stroke="#004350"
//         strokeWidth="1.5"
//         strokeLinecap="round"
//         strokeLinejoin="round"
//       />
//     </svg>
//   );
// };

// export const DropDownCategoryIcon = () => {
//   return (
//     <svg
//       width="14"
//       height="7"
//       viewBox="0 0 14 7"
//       fill="none"
//       xmlns="http://www.w3.org/2000/svg"
//     >
//       <path
//         d="M1.66731 0.999975L5.58644 4.9191C6.36749 5.70015 7.63382 5.70014 8.41486 4.9191L12.334 0.999976"
//         stroke="#004350"
//         strokeWidth="1.5"
//         strokeLinecap="round"
//         strokeLinejoin="round"
//       />
//     </svg>
//   );
// };
export const DropDownArrowDownIcon = () => {
  return (
    <svg
      width="13"
      height="7"
      viewBox="0 0 13 7"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M1.72529 1.01431L5.6267 4.5361C6.40422 5.23797 7.66483 5.23796 8.44234 4.5361L12.3438 1.01431"
        stroke="#004350"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  );
};
export const DesignationIcon = () => {
  return (
    <svg
      width="22"
      height="22"
      viewBox="0 0 22 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M8.85258 11.1544L8.67886 11.884L8.67886 11.884L8.85258 11.1544ZM13.1474 11.1544L13.3211 11.884L13.3211 11.884L13.1474 11.1544ZM11.0092 9.22917C11.4234 9.22917 11.7592 8.89338 11.7592 8.47917C11.7592 8.06495 11.4234 7.72917 11.0092 7.72917V9.22917ZM11 7.72917C10.5858 7.72917 10.25 8.06495 10.25 8.47917C10.25 8.89338 10.5858 9.22917 11 9.22917V7.72917ZM2 7.5625V8.34006H3.5V7.5625H2ZM9.48954 11.9792H12.5105V10.4792H9.48954V11.9792ZM20 8.34006V7.5625H18.5V8.34006H20ZM3.49295 10.6492L8.67886 11.884L9.02629 10.4248L3.84038 9.19004L3.49295 10.6492ZM2.91667 9.91964V15.8125H4.41667V9.91964H2.91667ZM6.41667 19.3125H15.5833V17.8125H6.41667V19.3125ZM13.3211 11.884L18.507 10.6492L18.1596 9.19004L12.9737 10.4248L13.3211 11.884ZM19.0833 15.8125V9.91964H17.5833V15.8125H19.0833ZM7.33333 4.97917H4.58333V6.47917H7.33333V4.97917ZM17.4167 4.97917H14.6667V6.47917H17.4167V4.97917ZM14.6667 4.97917H7.33333V6.47917H14.6667V4.97917ZM9.625 4.1875H12.375V2.6875H9.625V4.1875ZM15.5833 19.3125C17.5163 19.3125 19.0833 17.7455 19.0833 15.8125H17.5833C17.5833 16.9171 16.6879 17.8125 15.5833 17.8125V19.3125ZM2.91667 15.8125C2.91667 17.7455 4.48367 19.3125 6.41667 19.3125V17.8125C5.3121 17.8125 4.41667 16.9171 4.41667 15.8125H2.91667ZM20 7.5625C20 6.13576 18.8434 4.97917 17.4167 4.97917V6.47917C18.015 6.47917 18.5 6.96419 18.5 7.5625H20ZM18.5 8.34006C18.5 8.72378 18.2944 9.07808 17.9612 9.26846L18.7054 10.5708C19.506 10.1134 20 9.26207 20 8.34006H18.5ZM12.5105 11.9792C12.7835 11.9792 13.0556 11.9472 13.3211 11.884L12.9737 10.4248C12.8219 10.4609 12.6665 10.4792 12.5105 10.4792V11.9792ZM15.4167 5.72917C15.4167 4.0493 14.0549 2.6875 12.375 2.6875V4.1875C13.2264 4.1875 13.9167 4.87773 13.9167 5.72917H15.4167ZM9.48954 10.4792C9.33353 10.4792 9.17806 10.4609 9.02629 10.4248L8.67886 11.884C8.94445 11.9472 9.21652 11.9792 9.48954 11.9792V10.4792ZM2 8.34006C2 9.26207 2.49404 10.1134 3.29456 10.5708L4.03877 9.26846C3.70561 9.07808 3.5 8.72378 3.5 8.34006H2ZM8.08333 5.72917C8.08333 4.87773 8.77356 4.1875 9.625 4.1875V2.6875C7.94513 2.6875 6.58333 4.0493 6.58333 5.72917H8.08333ZM3.5 7.5625C3.5 6.96419 3.98502 6.47917 4.58333 6.47917V4.97917C3.1566 4.97917 2 6.13576 2 7.5625H3.5ZM11.0092 7.72917H11V9.22917H11.0092V7.72917Z"
        fill="#00596B"
      />
    </svg>
  );
};
export const TodoBoard: React.FC<TodoBoardProps> = ({ color }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="20"
      height="22"
      viewBox="0 0 20 22"
      fill="none"
    >
      <path
        d="M0.427734 3.50007C0.427734 2.76125 0.721228 2.0527 1.24365 1.53027C1.76607 1.00785 2.47463 0.714355 3.21345 0.714355H14.7849C15.5237 0.714355 16.2323 1.00785 16.7547 1.53027C17.2771 2.0527 17.5706 2.76125 17.5706 3.50007V13.8964C17.4672 13.9649 17.3697 14.0449 17.2783 14.1364L16.2849 15.1298V3.50007C16.2849 3.10225 16.1268 2.72071 15.8455 2.43941C15.5642 2.15811 15.1827 2.00007 14.7849 2.00007H3.21345C2.81562 2.00007 2.43409 2.15811 2.15279 2.43941C1.87148 2.72071 1.71345 3.10225 1.71345 3.50007V18.5001C1.71345 19.3281 2.38545 20.0001 3.21345 20.0001H10.1289L11.4146 21.2858H3.21345C2.47463 21.2858 1.76607 20.9923 1.24365 20.4699C0.721228 19.9474 0.427734 19.2389 0.427734 18.5001V3.50007ZM5.99916 6.50007C5.99916 6.78423 5.88628 7.05675 5.68535 7.25768C5.48442 7.45862 5.21189 7.5715 4.92773 7.5715C4.64357 7.5715 4.37105 7.45862 4.17012 7.25768C3.96919 7.05675 3.85631 6.78423 3.85631 6.50007C3.85631 6.21591 3.96919 5.94339 4.17012 5.74246C4.37105 5.54152 4.64357 5.42864 4.92773 5.42864C5.21189 5.42864 5.48442 5.54152 5.68535 5.74246C5.88628 5.94339 5.99916 6.21591 5.99916 6.50007ZM4.92773 12.0715C5.21189 12.0715 5.48442 11.9586 5.68535 11.7577C5.88628 11.5568 5.99916 11.2842 5.99916 11.0001C5.99916 10.7159 5.88628 10.4434 5.68535 10.2425C5.48442 10.0415 5.21189 9.92864 4.92773 9.92864C4.64357 9.92864 4.37105 10.0415 4.17012 10.2425C3.96919 10.4434 3.85631 10.7159 3.85631 11.0001C3.85631 11.2842 3.96919 11.5568 4.17012 11.7577C4.37105 11.9586 4.64357 12.0715 4.92773 12.0715ZM4.92773 16.5715C5.21189 16.5715 5.48442 16.4586 5.68535 16.2577C5.88628 16.0568 5.99916 15.7842 5.99916 15.5001C5.99916 15.2159 5.88628 14.9434 5.68535 14.7425C5.48442 14.5415 5.21189 14.4286 4.92773 14.4286C4.64357 14.4286 4.37105 14.5415 4.17012 14.7425C3.96919 14.9434 3.85631 15.2159 3.85631 15.5001C3.85631 15.7842 3.96919 16.0568 4.17012 16.2577C4.37105 16.4586 4.64357 16.5715 4.92773 16.5715ZM7.92773 5.85721C7.75724 5.85721 7.59372 5.92494 7.47317 6.0455C7.35261 6.16606 7.28488 6.32957 7.28488 6.50007C7.28488 6.67057 7.35261 6.83408 7.47317 6.95464C7.59372 7.0752 7.75724 7.14293 7.92773 7.14293H13.4992C13.6697 7.14293 13.8332 7.0752 13.9537 6.95464C14.0743 6.83408 14.142 6.67057 14.142 6.50007C14.142 6.32957 14.0743 6.16606 13.9537 6.0455C13.8332 5.92494 13.6697 5.85721 13.4992 5.85721H7.92773ZM7.28488 11.0001C7.28488 10.8296 7.35261 10.6661 7.47317 10.5455C7.59372 10.4249 7.75724 10.3572 7.92773 10.3572H13.4992C13.6697 10.3572 13.8332 10.4249 13.9537 10.5455C14.0743 10.6661 14.142 10.8296 14.142 11.0001C14.142 11.1706 14.0743 11.3341 13.9537 11.4546C13.8332 11.5752 13.6697 11.6429 13.4992 11.6429H7.92773C7.75724 11.6429 7.59372 11.5752 7.47317 11.4546C7.35261 11.3341 7.28488 11.1706 7.28488 11.0001ZM7.92773 14.8572C7.75724 14.8572 7.59372 14.9249 7.47317 15.0455C7.35261 15.1661 7.28488 15.3296 7.28488 15.5001C7.28488 15.6706 7.35261 15.8341 7.47317 15.9546C7.59372 16.0752 7.75724 16.1429 7.92773 16.1429H13.4992C13.6697 16.1429 13.8332 16.0752 13.9537 15.9546C14.0743 15.8341 14.142 15.6706 14.142 15.5001C14.142 15.3296 14.0743 15.1661 13.9537 15.0455C13.8332 14.9249 13.6697 14.8572 13.4992 14.8572H7.92773ZM19.0963 15.9544L13.9534 21.0972C13.8329 21.2176 13.6695 21.2852 13.4992 21.2852C13.3288 21.2852 13.1654 21.2176 13.0449 21.0972L10.4752 18.5275C10.3616 18.4056 10.2998 18.2445 10.3027 18.0779C10.3057 17.9114 10.3731 17.7525 10.4909 17.6347C10.6087 17.5169 10.7676 17.4494 10.9341 17.4465C11.1007 17.4436 11.2619 17.5054 11.3837 17.6189L13.4992 19.7335L18.1877 15.0449C18.2471 14.9836 18.318 14.9346 18.3965 14.901C18.4749 14.8673 18.5593 14.8496 18.6446 14.8489C18.73 14.8482 18.8146 14.8645 18.8936 14.8969C18.9726 14.9292 19.0444 14.977 19.1047 15.0374C19.165 15.0978 19.2127 15.1696 19.245 15.2486C19.2773 15.3276 19.2935 15.4123 19.2927 15.4976C19.292 15.583 19.2742 15.6673 19.2405 15.7458C19.2067 15.8242 19.1577 15.8951 19.0963 15.9544Z"
        fill={color ? color : "black"}
        fillOpacity="0.87"
      />
    </svg>
  );
};
export const TaskDescriptionIcon = () => {
  return (
    <svg
      width="16"
      height="20"
      viewBox="0 0 16 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M11 0C11.3511 8.91844e-06 11.6959 0.0924258 12 0.267962C12.304 0.443498 12.5565 0.695969 12.732 1H14C14.5304 1 15.0391 1.21071 15.4142 1.58579C15.7893 1.96086 16 2.46957 16 3V15C16 16.3261 15.4732 17.5979 14.5355 18.5355C13.5979 19.4732 12.3261 20 11 20H2C1.46957 20 0.960859 19.7893 0.585786 19.4142C0.210714 19.0391 0 18.5304 0 18V3C0 2.46957 0.210714 1.96086 0.585786 1.58579C0.960859 1.21071 1.46957 1 2 1H3.268C3.44353 0.695969 3.696 0.443498 4.00003 0.267962C4.30406 0.0924258 4.64894 8.91844e-06 5 0H11ZM3 3H2V18H11C11.7956 18 12.5587 17.6839 13.1213 17.1213C13.6839 16.5587 14 15.7956 14 15V3H13C13 3.53043 12.7893 4.03914 12.4142 4.41421C12.0391 4.78929 11.5304 5 11 5H5C4.46957 5 3.96086 4.78929 3.58579 4.41421C3.21071 4.03914 3 3.53043 3 3ZM12.238 7.379C12.4255 7.56653 12.5308 7.82084 12.5308 8.086C12.5308 8.35116 12.4255 8.60547 12.238 8.793L7.288 13.743C7.10047 13.9305 6.84617 14.0358 6.581 14.0358C6.31584 14.0358 6.06153 13.9305 5.874 13.743L3.754 11.621C3.57621 11.4317 3.47905 11.1806 3.48309 10.9209C3.48712 10.6612 3.59203 10.4133 3.77561 10.2296C3.95919 10.0459 4.20705 9.9408 4.46673 9.93658C4.72641 9.93237 4.97755 10.0293 5.167 10.207L6.582 11.621L10.824 7.379C11.0115 7.19153 11.2658 7.08621 11.531 7.08621C11.7962 7.08621 12.0505 7.19153 12.238 7.379ZM11 2H5V3H11V2Z"
        fill="#005968"
      />
    </svg>
  );
};

export const DropDownArrowUpIcon = () => {
  return (
    <svg
      width="13"
      height="7"
      viewBox="0 0 13 7"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M0.833657 6.00002L4.99772 2.0809C5.82759 1.29985 7.17306 1.29986 8.00293 2.08091L12.167 6.00002"
        stroke="#004350"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const WarningIcon = () => {
  return (
    <svg
      width="20"
      height="21"
      viewBox="0 0 20 21"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M10.0491 7.00078V13.0008M10.0589 16.0008H10.0491M7.42971 3.36734L1.43014 15.6591C0.456538 17.6538 1.87249 20.0008 4.04954 20.0008H16.0487C18.2257 20.0008 19.6417 17.6538 18.6681 15.6591L12.6685 3.36734C11.5892 1.1562 8.50896 1.1562 7.42971 3.36734Z"
        stroke="black"
        strokeOpacity="0.87"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
export const Arrow = () => {
  return (
    <svg
      width="7"
      height="14"
      viewBox="0 0 7 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M1 1.66667L4.91912 5.58579C5.70017 6.36684 5.70017 7.63316 4.91912 8.41421L1 12.3333"
        stroke="#FFFFFF"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const Clock = (color?: string) => {
  return (
    <svg
      width="18"
      height="18"
      viewBox="0 0 18 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M9 4.41667V8.43347C9 8.78068 9.19617 9.09809 9.50672 9.25336L11.75 10.375M17.25 9C17.25 4.44365 13.5563 0.75 9 0.75C4.44365 0.75 0.75 4.44365 0.75 9C0.75 13.5563 4.44365 17.25 9 17.25C13.5563 17.25 17.25 13.5563 17.25 9Z"
        stroke="#00596B"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const Calender = () => {
  return (
    <svg
      width="18"
      height="18"
      viewBox="0 0 18 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M5.33333 4.41667V0.75M12.6667 4.41667V0.75M5.3425 7.16667H5.33333M5.3425 9.91667H5.33333M5.3425 12.6667H5.33333M9.00917 9.91667H9M9.00917 7.16667H9M12.6758 9.91667H12.6667M9.00917 12.6667H9M12.6758 7.16667H12.6667M3.5 17.25H14.5C16.0188 17.25 17.25 16.0188 17.25 14.5V5.33333C17.25 3.81455 16.0188 2.58333 14.5 2.58333H3.5C1.98122 2.58333 0.75 3.81455 0.75 5.33333V14.5C0.75 16.0188 1.98122 17.25 3.5 17.25Z"
        stroke="#00596B"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const MinimizeIcon = () => {
  return (
    <svg
      width="14"
      height="14"
      viewBox="0 0 14 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M5 7H9M13 7C13 3.68629 10.3137 1 7 1C3.68629 1 1 3.68629 1 7C1 10.3137 3.68629 13 7 13C10.3137 13 13 10.3137 13 7Z"
        stroke="#A80000"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  );
};

export const DoubleArrow: React.FC<{ color: string }> = ({ color }) => {
  return (
    <svg
      className="DesignerDownArrowSvg"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M4.99994 13.1001C5.00077 12.9023 5.06022 12.7092 5.17078 12.5452C5.28135 12.3812 5.43805 12.2537 5.62109 12.1788C5.80412 12.1039 6.00526 12.0849 6.19907 12.1243C6.39288 12.1637 6.57067 12.2596 6.70994 12.4001L11.2899 17.0001C11.3829 17.0938 11.4935 17.1682 11.6154 17.219C11.7372 17.2697 11.8679 17.2959 11.9999 17.2959C12.132 17.2959 12.2627 17.2697 12.3845 17.219C12.5064 17.1682 12.617 17.0938 12.7099 17.0001L17.2899 12.4001C17.3829 12.3063 17.4935 12.2319 17.6154 12.1812C17.7372 12.1304 17.8679 12.1043 17.9999 12.1043C18.132 12.1043 18.2627 12.1304 18.3845 12.1812C18.5064 12.2319 18.617 12.3063 18.7099 12.4001C18.8962 12.5874 19.0007 12.8409 19.0007 13.1051C19.0007 13.3692 18.8962 13.6227 18.7099 13.8101L14.1199 18.4001C13.5574 18.9619 12.7949 19.2774 11.9999 19.2774C11.2049 19.2774 10.4424 18.9619 9.87994 18.4001L5.28994 13.8101C5.19726 13.7166 5.12393 13.6058 5.07417 13.484C5.0244 13.3621 4.99918 13.2317 4.99994 13.1001Z"
        fill={color}
        fillOpacity="0.87"
      />
      <path
        d="M4.99994 6.10006C5.00077 5.90228 5.06022 5.7092 5.17078 5.54521C5.28135 5.38123 5.43805 5.25371 5.62109 5.17879C5.80412 5.10386 6.00526 5.08489 6.19907 5.12428C6.39288 5.16366 6.57067 5.25963 6.70994 5.40006L11.9999 10.6901L17.2899 5.40006C17.3829 5.30633 17.4935 5.23193 17.6154 5.18116C17.7372 5.1304 17.8679 5.10426 17.9999 5.10426C18.132 5.10426 18.2627 5.1304 18.3845 5.18116C18.5064 5.23193 18.617 5.30633 18.7099 5.40006C18.8962 5.58742 19.0007 5.84087 19.0007 6.10506C19.0007 6.36924 18.8962 6.62269 18.7099 6.81006L12.7099 12.8101C12.617 12.9038 12.5064 12.9782 12.3845 13.0289C12.2627 13.0797 12.132 13.1059 11.9999 13.1059C11.8679 13.1059 11.7372 13.0797 11.6154 13.0289C11.4935 12.9782 11.3829 12.9038 11.2899 12.8101L5.28994 6.81006C5.19726 6.71662 5.12393 6.6058 5.07417 6.48396C5.0244 6.36212 4.99918 6.23166 4.99994 6.10006Z"
        fill={color}
        fillOpacity="0.87"
      />
    </svg>
  );
};

export const undoIcon = () => {
  return (
    <svg
      width="8"
      height="10"
      viewBox="0 0 8 10"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M0.5 5.375C0.5 7.308 2.067 8.875 4 8.875C4.5368 8.875 5.04537 8.75415 5.5 8.53818C6.68247 7.97645 7.5 6.7712 7.5 5.375C7.5 3.442 5.933 1.875 4 1.875M4 1.875L4.75 3M4 1.875L5.125 1.125"
        stroke="#005968"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  );
};
export const VersionIcon = () => {
  return (
    <svg
      width="12"
      height="19"
      viewBox="0 0 12 19"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        x="10.1426"
        width="2"
        height="20.2852"
        rx="1"
        transform="rotate(30 10.1426 0)"
        fill="#FFFFFF"
      />
      <rect
        x="8.96289"
        y="8.67383"
        width="2"
        height="9.71826"
        rx="1"
        transform="rotate(30 8.96289 8.67383)"
        fill="#FFFFFF"
      />
      <rect
        x="5.96289"
        y="0.3125"
        width="2"
        height="10"
        rx="1"
        transform="rotate(30 5.96289 0.3125)"
        fill="#FFFFFF"
      />
    </svg>
  );
};

export const SuryconLogo = () => {
  return (
    <svg
      width="12"
      height="18"
      viewBox="0 0 12 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        width="1.98489"
        height="19.822"
        rx="0.992447"
        transform="matrix(0.872616 0.488407 -0.511684 0.859174 10.1406 0)"
        fill="#00596B"
      />
      <rect
        width="1.98489"
        height="9.49635"
        rx="0.992447"
        transform="matrix(0.872616 0.488407 -0.511684 0.859174 8.96094 8.41016)"
        fill="#00596B"
      />
      <rect
        width="1.98489"
        height="9.77166"
        rx="0.992447"
        transform="matrix(0.872616 0.488407 -0.511684 0.859174 5.96094 0.304688)"
        fill="#00596B"
      />
    </svg>
  );
};

export const DottedLine = () => {
  return (
    <svg
      width="681"
      height="2"
      viewBox="0 0 681 2"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <line
        x1="4.50895e-08"
        y1="1.22656"
        x2="681"
        y2="1.22662"
        stroke="#B0CCD1"
        stroke-dasharray="4 4"
      />
    </svg>
  );
};

export const SuryaconLogoSecondary = ({ color = "#E8B000" }) => {
  return (
    <svg
      width="10"
      height="14"
      viewBox="0 0 10 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        width="1.55623"
        height="15.4599"
        rx="0.778116"
        transform="matrix(0.874787 0.484507 -0.515654 0.856797 7.97168 0)"
        fill={color}
      />
      <rect
        width="1.55623"
        height="7.40655"
        rx="0.778116"
        transform="matrix(0.874787 0.484507 -0.515654 0.856797 7.0459 6.53906)"
        fill={color}
      />
      <rect
        width="1.55623"
        height="7.62127"
        rx="0.778116"
        transform="matrix(0.874787 0.484507 -0.515654 0.856797 4.6875 0.234375)"
        fill={color}
      />
    </svg>
  );
};

export const HidePassword = () => {
  return (
    <svg
      width="20"
      height="18"
      viewBox="0 0 20 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M10.3174 6.01659C11.6923 6.16116 12.7905 7.23442 12.9732 8.59759M7.76389 7C7.28885 7.53076 7 8.23164 7 9C7 10.6569 8.34315 12 10 12C10.7684 12 11.4692 11.7111 12 11.2361M4.5 3.72189C2.37097 5.15996 1 7.2501 1 9C1 12 5.02944 16 10 16C11.9572 16 13.7684 15.3798 15.2452 14.4448M6.97011 2.5C7.91688 2.18297 8.93691 2 10 2C14.9706 2 19 6 19 9C19 10.1141 18.4443 11.3661 17.491 12.5M18 17L2 1"
        stroke="black"
        strokeOpacity="0.28"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const ExpandIcon = () => {
  return (
    <svg
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M0.5 0.5L4.66667 4.625M0.5 0.5V4.25M0.5 0.5H4.25M0.5 15.4583L4.66667 11.3333M15.5 15.4583L11.375 11.3333M15.4583 0.5L11.3333 4.625M11.75 0.5H15.5V4.25M15.5 11.75V15.5H11.75M4.25 15.5H0.5V11.75"
        stroke="#005968"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  );
};

export const RemarkIcon = () => {
  return (
    <svg
      width="16"
      height="20"
      viewBox="0 0 16 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M10 1.49998V3C10 4.65686 11.3431 6 13 6H14.5M4 15H12M4 11H10M4 6.5H8M12 19H4C2.34315 19 1 17.6569 1 16V4C1 2.34315 2.34315 1 4 1H8.75736C9.55301 1 10.3161 1.31607 10.8787 1.87868L14.1213 5.12132C14.6839 5.68393 15 6.44699 15 7.24264V16C15 17.6569 13.6569 19 12 19Z"
        stroke="#005968"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  );
};

type EyeIconProps = {
  style?: React.CSSProperties;
};
export const EyeIcon: FC<EyeIconProps> = ({ style }) => {
  return (
    <svg
      style={style}
      width="18"
      height="15"
      viewBox="0 0 22 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M2.275 12.296C1.425 11.192 1 10.639 1 9C1 7.36 1.425 6.809 2.275 5.704C3.972 3.5 6.818 1 11 1C15.182 1 18.028 3.5 19.725 5.704C20.575 6.81 21 7.361 21 9C21 10.64 20.575 11.191 19.725 12.296C18.028 14.5 15.182 17 11 17C6.818 17 3.972 14.5 2.275 12.296Z"
        stroke="#005968"
        stroke-width="1.5"
      />
      <path
        d="M14 9C14 9.79565 13.6839 10.5587 13.1213 11.1213C12.5587 11.6839 11.7956 12 11 12C10.2044 12 9.44129 11.6839 8.87868 11.1213C8.31607 10.5587 8 9.79565 8 9C8 8.20435 8.31607 7.44129 8.87868 6.87868C9.44129 6.31607 10.2044 6 11 6C11.7956 6 12.5587 6.31607 13.1213 6.87868C13.6839 7.44129 14 8.20435 14 9Z"
        stroke="#005968"
        stroke-width="1.5"
      />
    </svg>
  );
};
export const DrawingIcon = () => {
  return (
    <svg
      width="12"
      height="18"
      viewBox="0 0 12 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M0.359375 15.78L0.609375 18L2.22937 16.46L4.99937 8.86C4.31937 8.69 3.71937 8.35 3.22937 7.88L0.359375 15.78ZM8.76937 7.88C8.27938 8.35 7.66937 8.69 6.99937 8.86L9.76937 16.46L11.3894 18L11.6494 15.78L8.76937 7.88ZM8.99937 5C8.99937 3.7 8.15937 2.6 6.99937 2.18V0H4.99937V2.18C3.83938 2.6 2.99937 3.7 2.99937 5C2.99937 6.66 4.33938 8 5.99937 8C7.65937 8 8.99937 6.66 8.99937 5ZM5.99937 6C5.44937 6 4.99937 5.55 4.99937 5C4.99937 4.45 5.44937 4 5.99937 4C6.54938 4 6.99937 4.45 6.99937 5C6.99937 5.55 6.54938 6 5.99937 6Z"
        fill="#005968"
      />
    </svg>
  );
};

export const CalendarSecondaryIcon = () => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M6 5V1M14 5V1M6.01 8H6M6.01 11H6M6.01 14H6M10.01 11H10M10.01 8H10M14.01 11H14M10.01 14H10M14.01 8H14M4 19H16C17.6569 19 19 17.6569 19 16V6C19 4.34315 17.6569 3 16 3H4C2.34315 3 1 4.34315 1 6V16C1 17.6569 2.34315 19 4 19Z"
        stroke="#E8B000"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  );
};
export const CalendarPrimaryIcon = () => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M6 5V1M14 5V1M6.01 8H6M6.01 11H6M6.01 14H6M10.01 11H10M10.01 8H10M14.01 11H14M10.01 14H10M14.01 8H14M4 19H16C17.6569 19 19 17.6569 19 16V6C19 4.34315 17.6569 3 16 3H4C2.34315 3 1 4.34315 1 6V16C1 17.6569 2.34315 19 4 19Z"
        stroke="#005968"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  );
};
export const AreaIcon = () => {
  return (
    <svg
      width="22"
      height="13"
      viewBox="0 0 22 13"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M5.072 12.144C4.58133 12.144 4.10133 12.1067 3.632 12.032C3.16267 11.9573 2.72 11.8453 2.304 11.696C1.888 11.536 1.51467 11.344 1.184 11.12C1.024 11.0027 0.912 10.8693 0.848 10.72C0.794667 10.5707 0.778667 10.4267 0.8 10.288C0.832 10.1387 0.896 10.0107 0.992 9.904C1.088 9.79733 1.20533 9.73333 1.344 9.712C1.48267 9.68 1.63733 9.72267 1.808 9.84C2.288 10.1493 2.79467 10.3787 3.328 10.528C3.86133 10.6667 4.44267 10.736 5.072 10.736C5.97867 10.736 6.64533 10.576 7.072 10.256C7.50933 9.92533 7.728 9.49867 7.728 8.976C7.728 8.54933 7.57333 8.21333 7.264 7.968C6.95467 7.712 6.43733 7.50933 5.712 7.36L4 7.008C2.98667 6.79467 2.22933 6.44267 1.728 5.952C1.23733 5.46133 0.992 4.8 0.992 3.968C0.992 3.46667 1.09333 3.008 1.296 2.592C1.50933 2.176 1.80267 1.81867 2.176 1.52C2.54933 1.21067 2.99733 0.976 3.52 0.815999C4.04267 0.655999 4.61333 0.575999 5.232 0.575999C5.89333 0.575999 6.512 0.661333 7.088 0.832C7.67467 1.00267 8.19733 1.264 8.656 1.616C8.79467 1.712 8.88533 1.83467 8.928 1.984C8.98133 2.12267 8.992 2.26133 8.96 2.4C8.93867 2.53867 8.88 2.656 8.784 2.752C8.688 2.848 8.56533 2.90667 8.416 2.928C8.26667 2.93867 8.10133 2.88533 7.92 2.768C7.504 2.49067 7.07733 2.29333 6.64 2.176C6.21333 2.048 5.73867 1.984 5.216 1.984C4.69333 1.984 4.23467 2.064 3.84 2.224C3.456 2.384 3.15733 2.608 2.944 2.896C2.74133 3.184 2.64 3.52533 2.64 3.92C2.64 4.368 2.784 4.72533 3.072 4.992C3.36 5.25867 3.84 5.46133 4.512 5.6L6.208 5.952C7.27467 6.176 8.06933 6.52267 8.592 6.992C9.11467 7.46133 9.376 8.08533 9.376 8.864C9.376 9.36533 9.27467 9.81867 9.072 10.224C8.86933 10.6187 8.58133 10.96 8.208 11.248C7.83467 11.536 7.38133 11.76 6.848 11.92C6.32533 12.0693 5.73333 12.144 5.072 12.144ZM12.3223 12.112C12.0663 12.112 11.8636 12.0427 11.7143 11.904C11.5756 11.7547 11.5063 11.552 11.5063 11.296V5.44H10.6103C10.4076 5.44 10.2476 5.38667 10.1303 5.28C10.0129 5.16267 9.95425 5.008 9.95425 4.816C9.95425 4.61333 10.0129 4.45867 10.1303 4.352C10.2476 4.24533 10.4076 4.192 10.6103 4.192H11.9703L11.5063 4.624V4.048C11.5063 2.992 11.7676 2.19733 12.2903 1.664C12.8129 1.12 13.5756 0.8 14.5783 0.704L15.0743 0.672C15.2556 0.650666 15.3996 0.677333 15.5063 0.752C15.6129 0.826666 15.6823 0.922666 15.7143 1.04C15.7463 1.15733 15.7463 1.28 15.7143 1.408C15.6929 1.52533 15.6396 1.632 15.5543 1.728C15.4689 1.824 15.3623 1.87733 15.2343 1.888L14.9303 1.904C14.3009 1.95733 13.8423 2.13867 13.5543 2.448C13.2663 2.74667 13.1223 3.20533 13.1223 3.824V4.448L12.8983 4.192H14.6743C14.8983 4.192 15.0636 4.24533 15.1703 4.352C15.2876 4.45867 15.3463 4.61333 15.3463 4.816C15.3463 5.008 15.2876 5.16267 15.1703 5.28C15.0636 5.38667 14.8983 5.44 14.6743 5.44H13.1223V11.296C13.1223 11.84 12.8556 12.112 12.3223 12.112ZM19.5109 12.144C18.8709 12.144 18.3322 12.032 17.8949 11.808C17.4575 11.584 17.1322 11.2533 16.9189 10.816C16.7055 10.3787 16.5989 9.84533 16.5989 9.216V5.44H15.6389C15.4255 5.44 15.2602 5.38667 15.1429 5.28C15.0255 5.16267 14.9669 5.008 14.9669 4.816C14.9669 4.61333 15.0255 4.45867 15.1429 4.352C15.2602 4.24533 15.4255 4.192 15.6389 4.192H16.5989V2.544C16.5989 2.27733 16.6682 2.07467 16.8069 1.936C16.9562 1.79733 17.1589 1.728 17.4149 1.728C17.6709 1.728 17.8682 1.79733 18.0069 1.936C18.1455 2.07467 18.2149 2.27733 18.2149 2.544V4.192H20.0869C20.3002 4.192 20.4655 4.24533 20.5829 4.352C20.7002 4.45867 20.7589 4.61333 20.7589 4.816C20.7589 5.008 20.7002 5.16267 20.5829 5.28C20.4655 5.38667 20.3002 5.44 20.0869 5.44H18.2149V9.088C18.2149 9.65333 18.3322 10.08 18.5669 10.368C18.8122 10.656 19.2069 10.8 19.7509 10.8C19.9429 10.8 20.1082 10.784 20.2469 10.752C20.3962 10.7093 20.5189 10.6827 20.6149 10.672C20.7322 10.672 20.8282 10.7147 20.9029 10.8C20.9775 10.8747 21.0149 11.024 21.0149 11.248C21.0149 11.408 20.9829 11.5573 20.9189 11.696C20.8655 11.8347 20.7642 11.9253 20.6149 11.968C20.4869 12.0107 20.3109 12.048 20.0869 12.08C19.8735 12.1227 19.6815 12.144 19.5109 12.144Z"
        fill="#005968"
      />
    </svg>
  );
};
export const BudgetIcon = () => {
  return (
    <svg
      width="14"
      height="19"
      viewBox="0 0 14 19"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M5.99805 2.92188H0.998047C0.73283 2.92188 0.478477 2.81652 0.29094 2.62898C0.103404 2.44145 -0.00195312 2.18709 -0.00195312 1.92188C-0.00195312 1.65666 0.103404 1.4023 0.29094 1.21477C0.478477 1.02723 0.73283 0.921875 0.998047 0.921875H12.998C13.2633 0.921875 13.5176 1.02723 13.7052 1.21477C13.8927 1.4023 13.998 1.65666 13.998 1.92188C13.998 2.18709 13.8927 2.44145 13.7052 2.62898C13.5176 2.81652 13.2633 2.92188 12.998 2.92188H9.99805C10.4432 3.51321 10.7507 4.19651 10.898 4.92188H12.998C13.2633 4.92188 13.5176 5.02723 13.7052 5.21477C13.8927 5.40231 13.998 5.65666 13.998 5.92188C13.998 6.18709 13.8927 6.44145 13.7052 6.62898C13.5176 6.81652 13.2633 6.92188 12.998 6.92188H10.898C10.6672 8.05107 10.0534 9.06588 9.16058 9.79473C8.26774 10.5236 7.1506 10.9217 5.99805 10.9219H5.41205L11.705 17.2149C11.8872 17.4035 11.988 17.6561 11.9857 17.9183C11.9834 18.1805 11.8783 18.4313 11.6929 18.6167C11.5075 18.8021 11.2566 18.9073 10.9944 18.9096C10.7323 18.9118 10.4797 18.811 10.291 18.6289L2.29105 10.6289C2.15124 10.489 2.05603 10.3109 2.01746 10.1169C1.9789 9.92296 1.9987 9.72192 2.07437 9.53922C2.15004 9.35652 2.27818 9.20036 2.44259 9.09048C2.607 8.98059 2.8003 8.92192 2.99805 8.92188H5.99805C6.61853 8.922 7.22378 8.72972 7.73044 8.37153C8.23709 8.01335 8.62022 7.50687 8.82705 6.92188H0.998047C0.73283 6.92188 0.478477 6.81652 0.29094 6.62898C0.103404 6.44145 -0.00195312 6.18709 -0.00195312 5.92188C-0.00195312 5.65666 0.103404 5.40231 0.29094 5.21477C0.478477 5.02723 0.73283 4.92188 0.998047 4.92188H8.82705C8.62022 4.33688 8.23709 3.83041 7.73044 3.47222C7.22378 3.11403 6.61853 2.92176 5.99805 2.92188Z"
        fill="#005968"
      />
    </svg>
  );
};

export const PhotoUpload = () => {
  return (
    <svg
      width="18"
      height="16"
      viewBox="0 0 18 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M3.16667 14.6663L6.26996 10.1976C6.97379 9.18409 8.49659 9.26021 9.09584 10.3389L9.33615 10.7714M6.5 14.6663L11.6506 7.58431C12.3619 6.6062 13.8455 6.68762 14.4455 7.7377L16.5 11.333M4 14.6663H14C15.3807 14.6663 16.5 13.5471 16.5 12.1663V3.83301C16.5 2.4523 15.3807 1.33301 14 1.33301H4C2.61929 1.33301 1.5 2.4523 1.5 3.83301V12.1663C1.5 13.5471 2.61929 14.6663 4 14.6663ZM6.08333 4.45801C6.08333 4.80319 5.80351 5.08301 5.45833 5.08301C5.11316 5.08301 4.83333 4.80319 4.83333 4.45801C4.83333 4.11283 5.11316 3.83301 5.45833 3.83301C5.80351 3.83301 6.08333 4.11283 6.08333 4.45801Z"
        stroke="#00596B"
        strokeWidth="1.5"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const CheckBox = () => {
  return (
    <svg
      width="18"
      height="18"
      viewBox="0 0 18 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M5.66667 9.20833L7.75 11.2917L12.3333 6.70833M4 16.5H14C15.3807 16.5 16.5 15.3807 16.5 14V4C16.5 2.61929 15.3807 1.5 14 1.5H4C2.61929 1.5 1.5 2.61929 1.5 4V14C1.5 15.3807 2.61929 16.5 4 16.5Z"
        stroke="#00596B"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const UndoArrow = () => {
  return (
    <svg
      width="10"
      height="10"
      viewBox="0 0 19 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M3.84028 7.00935L4.96695 6.24018C5.05648 6.17892 5.15732 6.13612 5.26357 6.11426C5.36983 6.0924 5.47937 6.09193 5.58581 6.11288C5.69225 6.13382 5.79345 6.17577 5.8835 6.23626C5.97355 6.29674 6.05064 6.37457 6.11028 6.46518C6.23195 6.64867 6.27689 6.87252 6.23545 7.08874C6.19401 7.30497 6.0695 7.49635 5.88862 7.62185L3.15778 9.48602C3.06826 9.54727 2.96742 9.59008 2.86117 9.61194C2.75491 9.6338 2.64536 9.63427 2.53893 9.61332C2.43249 9.59237 2.33129 9.55043 2.24124 9.48994C2.15119 9.42945 2.07409 9.35163 2.01445 9.26102L0.172784 6.49768C0.0502102 6.31433 0.00474734 6.09009 0.0462304 5.87347C0.0877134 5.65686 0.212807 5.46528 0.394451 5.34018C0.483979 5.27892 0.584815 5.23612 0.691071 5.21426C0.797326 5.1924 0.906871 5.19193 1.01331 5.21288C1.11975 5.23382 1.22095 5.27577 1.311 5.33626C1.40105 5.39674 1.47814 5.47457 1.53778 5.56518L2.23695 6.61435C3.25028 3.17435 6.40028 0.666016 10.1295 0.666016C14.6778 0.666016 18.3645 4.39685 18.3645 8.99935C18.3645 13.6018 14.6778 17.3327 10.1295 17.3327C10.0207 17.332 9.91309 17.31 9.81284 17.2677C9.71259 17.2255 9.62165 17.1639 9.54519 17.0865C9.46874 17.0092 9.40827 16.9175 9.36725 16.8167C9.32623 16.716 9.30546 16.6081 9.30612 16.4993C9.30612 16.0393 9.67528 15.666 10.1295 15.666C13.7678 15.666 16.7178 12.681 16.7178 8.99935C16.7178 5.31768 13.7678 2.33268 10.1295 2.33268C7.17612 2.33268 4.67695 4.29935 3.84028 7.00935Z"
        fill="#00596A"
      />
    </svg>
  );
};

export const CloudUpload = () => {
  return (
    <svg
      width="36"
      height="30"
      viewBox="0 0 36 30"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M18.832 28.0002V15.5002M18.832 15.5002L15.4987 18.8335M18.832 15.5002L22.1654 18.8335M9.66536 24.1837C4.84622 22.7495 1.33203 18.2852 1.33203 13.0002C1.33203 6.55684 6.55538 1.3335 12.9987 1.3335C17.8141 1.3335 21.9481 4.25087 23.7296 8.41456C24.5485 8.14563 25.4233 8.00016 26.332 8.00016C30.9344 8.00016 34.6654 11.7311 34.6654 16.3335C34.6654 20.3651 31.8024 23.728 27.9987 24.5001"
        stroke="#005968"
        stroke-width="2.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  );
};

export const EditBtn = () => {
  return (
    <svg
      width="18"
      height="19"
      viewBox="0 0 18 19"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M9.5 17.6946H15.5M3.50736 16.6946H2C1.44772 16.6946 1 16.2469 1 15.6946V14.1873C1 13.3916 1.31607 12.6285 1.87868 12.0659L11.75 2.19462C12.7855 1.15909 14.4645 1.15909 15.5 2.19462C16.5355 3.23015 16.5355 4.90909 15.5 5.94462L5.62868 15.8159C5.06607 16.3785 4.30301 16.6946 3.50736 16.6946Z"
        stroke="white"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
export const YellowWarning = () => {
  return (
    <svg
      width="28"
      height="27"
      viewBox="0 0 28 27"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M14 8.66669V16.6667M14.0133 20.6667H14M10.4223 3.82211L2.22775 20.2112C0.897943 22.8708 2.83193 26 5.80545 26H22.1945C25.168 26 27.102 22.8708 25.7722 20.2112L17.5777 3.82211C16.1036 0.873914 11.8964 0.873913 10.4223 3.82211Z"
        stroke="#E8B000"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const Danger = () => {
  return (
    <svg
      width="26"
      height="26"
      viewBox="0 0 26 26"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M15.8284 10.1716L13 13M13 13L10.1716 15.8284M13 13L10.1716 10.1716M13 13L15.8284 15.8284M25 13C25 6.37258 19.6274 1 13 1C6.37258 1 1 6.37258 1 13C1 19.6274 6.37258 25 13 25C19.6274 25 25 19.6274 25 13Z"
        stroke="#A80000"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const Success = ({ className }: { className?: string }) => {
  return (
    <svg
      width="26"
      height="26"
      viewBox="0 0 26 26"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      {/* Circle path */}
      <path
        d="M25 13C25 6.37258 19.6274 1 13 1C6.37258 1 1 6.37258 1 13C1 19.6274 6.37258 25 13 25C19.6274 25 25 19.6274 25 13Z"
        stroke="#FFFFFF"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeDasharray="80"
        strokeDashoffset="80"
        className={className}
      />
      {/* Checkmark path */}
      <path
        d="M7.66667 13.3333L11 16.6667L18.3333 9.33333"
        stroke="#FFFFFF"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeDasharray="40"
        strokeDashoffset="40"
        className={className}
      />
    </svg>
  );
};
export const AttachmentIcon = () => {
  return (
    <svg
      width="16"
      height="21"
      viewBox="0 0 16 21"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M3.58049 11.4874L2.16627 12.9016C0.604177 14.4637 0.604177 16.9964 2.16627 18.5585C3.72837 20.1206 6.26103 20.1206 7.82313 18.5585L10.6516 15.7301C12.2137 14.168 12.2137 11.6353 10.6516 10.0732C9.93423 9.3559 9.01225 8.96797 8.07357 8.90944M12.4193 9.01254L13.8336 7.59832C15.3957 6.03623 15.3957 3.50357 13.8336 1.94147C12.2715 0.379373 9.7388 0.379373 8.1767 1.94147L5.34827 4.7699C3.78618 6.33199 3.78618 8.86465 5.34827 10.4268C6.06559 11.1441 6.98758 11.532 7.92626 11.5905"
        stroke="#005968"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  );
};
export const ThreeDots = () => {
  return (
    <svg
      width="4"
      height="16"
      viewBox="0 0 4 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M2 0C0.895431 0 0 0.916254 0 2.04651C0 3.17677 0.895431 4.09302 2 4.09302C3.10457 4.09302 4 3.17677 4 2.04651C4 0.916254 3.10457 0 2 0ZM1.09091 2.04651C1.09091 1.53276 1.49792 1.11628 2 1.11628C2.50208 1.11628 2.90909 1.53276 2.90909 2.04651C2.90909 2.56026 2.50208 2.97674 2 2.97674C1.49792 2.97674 1.09091 2.56026 1.09091 2.04651Z"
        fill="#00596B"
      />
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M2 5.95349C0.895431 5.95349 0 6.86974 0 8C0 9.13026 0.895431 10.0465 2 10.0465C3.10457 10.0465 4 9.13026 4 8C4 6.86974 3.10457 5.95349 2 5.95349ZM1.09091 8C1.09091 7.48625 1.49792 7.06977 2 7.06977C2.50208 7.06977 2.90909 7.48625 2.90909 8C2.90909 8.51375 2.50208 8.93023 2 8.93023C1.49792 8.93023 1.09091 8.51375 1.09091 8Z"
        fill="#00596B"
      />
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M2 11.907C0.89543 11.907 0 12.8232 0 13.9535C0 15.0837 0.89543 16 2 16C3.10457 16 4 15.0837 4 13.9535C4 12.8232 3.10457 11.907 2 11.907ZM1.09091 13.9535C1.09091 13.4397 1.49792 13.0233 2 13.0233C2.50208 13.0233 2.90909 13.4397 2.90909 13.9535C2.90909 14.4672 2.50208 14.8837 2 14.8837C1.49792 14.8837 1.09091 14.4672 1.09091 13.9535Z"
        fill="#00596B"
      />
    </svg>
  );
};

export const CommentIcon = () => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M6.0002 8.5H14.0002M6.0002 12.5H10.0002M1.0002 10C1.0002 5.02944 5.02964 1 10.0002 1C14.9708 1 19.0002 5.02944 19.0002 10C19.0002 14.9706 14.9708 19 10.0002 19C9.30717 19 8.63244 18.9217 7.98437 18.7734C7.35838 18.6301 6.70618 18.6103 6.08126 18.7581C4.68573 19.0884 2.75953 19.3545 1.4417 18.9801C0.886389 18.8224 0.832027 18.1354 1.09965 17.6239C1.41097 17.0289 1.68178 16.2964 1.88646 15.6456C2.11568 14.9167 1.97026 14.1402 1.67854 13.434C1.24139 12.3758 1.0002 11.2161 1.0002 10Z"
        stroke="#005968"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  );
};
export const MivanProject = () => {
  return (
    <svg
      width="14"
      height="19"
      viewBox="0 0 14 19"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        width="2.26434"
        height="21.5191"
        rx="1.13217"
        transform="matrix(0.89206 0.451917 -0.549667 0.835384 11.8281 0)"
        fill="#E8B000"
      />
      <rect
        width="2.26434"
        height="10.3094"
        rx="1.13217"
        transform="matrix(0.89206 0.451917 -0.549667 0.835384 10.4531 8.875)"
        fill="#E8B000"
      />
      <rect
        width="2.26434"
        height="10.6083"
        rx="1.13217"
        transform="matrix(0.89206 0.451917 -0.549667 0.835384 6.95312 0.318359)"
        fill="#E8B000"
      />
    </svg>
  );
};

export const YellowEditPencil = () => {
  return (
    <svg
      width="15"
      height="16"
      viewBox="0 0 15 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M8.41732 15.2455H13.4173M3.42345 14.4122H2.16732C1.70708 14.4122 1.33398 14.0391 1.33398 13.5788V12.3227C1.33398 11.6597 1.59738 11.0238 2.06622 10.5549L10.2923 2.32885C11.1553 1.4659 12.5544 1.4659 13.4173 2.32885C14.2803 3.19179 14.2803 4.5909 13.4173 5.45385L5.19122 13.6799C4.72238 14.1488 4.08649 14.4122 3.42345 14.4122Z"
        stroke="#E8B000"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  );
};

export const MinusIcon = () => {
  return (
    <svg
      width="12"
      height="2"
      viewBox="0 0 12 2"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M1.33301 1H10.6663"
        stroke="white"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  );
};
export const DateIcon = () => {
  return (
    <svg
      width="18"
      height="18"
      viewBox="0 0 18 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M5.66667 4.83333L5.66667 1.5M12.3333 4.83333V1.5M5.675 7.33333H5.66667M5.675 9.83333H5.66667M5.675 12.3333H5.66667M9.00833 9.83333H9M9.00833 7.33333H9M12.3417 9.83333H12.3333M9.00833 12.3333H9M12.3417 7.33333H12.3333M4 16.5H14C15.3807 16.5 16.5 15.3807 16.5 14V5.66667C16.5 4.28595 15.3807 3.16667 14 3.16667L4 3.16667C2.61929 3.16667 1.5 4.28595 1.5 5.66667L1.5 14C1.5 15.3807 2.61929 16.5 4 16.5Z"
        stroke="#005968"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  );
};
export const ActionBtn = () => {
  return (
    <svg
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M6.56267 0.125H9.43733C10.3858 0.125 11.1512 0.125 11.7707 0.175167C12.4077 0.227667 12.9677 0.3385 13.4857 0.602167C14.3094 1.02143 14.9792 1.69083 15.399 2.51433C15.6627 3.03233 15.7723 3.59233 15.8248 4.22933C15.875 4.84883 15.875 5.61417 15.875 6.56267V9.43733C15.875 10.3858 15.875 11.1512 15.8248 11.7707C15.7723 12.4077 15.6615 12.9677 15.3978 13.4857C14.9786 14.3094 14.3092 14.9792 13.4857 15.399C12.9677 15.6627 12.4077 15.7723 11.7707 15.8248C11.1512 15.875 10.3858 15.875 9.43733 15.875H5.22567C2.8865 15.875 1.714 13.047 3.36833 11.3938L8.51333 6.25H6.6875C6.45544 6.25 6.23288 6.15781 6.06878 5.99372C5.90469 5.82962 5.8125 5.60706 5.8125 5.375C5.8125 5.14294 5.90469 4.92038 6.06878 4.75628C6.23288 4.59219 6.45544 4.5 6.6875 4.5H10.625C10.8571 4.5 11.0796 4.59219 11.2437 4.75628C11.4078 4.92038 11.5 5.14294 11.5 5.375V9.3125C11.5 9.54456 11.4078 9.76712 11.2437 9.93122C11.0796 10.0953 10.8571 10.1875 10.625 10.1875C10.3929 10.1875 10.1704 10.0953 10.0063 9.93122C9.84219 9.76712 9.75 9.54456 9.75 9.3125V7.48783L4.60617 12.6317C4.48395 12.754 4.40074 12.9099 4.36704 13.0795C4.33334 13.2491 4.35067 13.425 4.41683 13.5847C4.48299 13.7445 4.59503 13.8811 4.73878 13.9773C4.88253 14.0734 5.05155 14.1248 5.2245 14.125H9.4C10.3952 14.125 11.0882 14.125 11.6283 14.0807C12.1568 14.0363 12.4613 13.9558 12.6923 13.8392C13.1858 13.5872 13.5872 13.1858 13.8392 12.6923C13.9558 12.4613 14.0375 12.1568 14.0807 11.6272C14.1238 11.0882 14.125 10.3952 14.125 9.4V6.6C14.125 5.606 14.125 4.91183 14.0807 4.37167C14.0363 3.84317 13.9558 3.53867 13.8392 3.30883C13.5877 2.81465 13.1863 2.41277 12.6923 2.16083C12.4613 2.04417 12.1568 1.9625 11.6272 1.91933C11.0882 1.87617 10.3952 1.875 9.4 1.875H6.6C5.606 1.875 4.91183 1.875 4.37167 1.91933C3.84317 1.9625 3.53867 2.04417 3.30883 2.16083C2.81447 2.41258 2.41258 2.81447 2.16083 3.30883C2.04417 3.53867 1.9625 3.84317 1.91933 4.37283C1.87617 4.91183 1.875 5.606 1.875 6.6V9.75C1.875 9.98206 1.78281 10.2046 1.61872 10.3687C1.45462 10.5328 1.23206 10.625 1 10.625C0.767936 10.625 0.545376 10.5328 0.381281 10.3687C0.217187 10.2046 0.125 9.98206 0.125 9.75V6.56267C0.125 5.61417 0.125 4.84883 0.175167 4.22933C0.227667 3.59233 0.3385 3.03233 0.602167 2.51433C1.02143 1.69057 1.69083 1.02077 2.51433 0.601C3.03233 0.337333 3.59233 0.227667 4.22933 0.175167C4.84883 0.125 5.61417 0.125 6.56267 0.125Z"
        fill="#005968"
      />
    </svg>
  );
};
interface ColorProps {
  color: string;
}

export const TableViewBilling: FC<ColorProps> = ({ color }) => {
  return (
    <svg
      width="14"
      height="14"
      viewBox="0 0 14 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M2.83341 7.0013H11.1667M1.16675 1.16797H12.8334M1.16675 12.8346H12.8334"
        stroke={color}
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  );
};

export const FloorsIcon = () => {
  return (
    <svg
      width="22"
      height="18"
      viewBox="0 0 22 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M1 12.84L1 16.68C1 16.7649 1.03399 16.8463 1.09448 16.9063C1.15498 16.9663 1.23703 17 1.32258 17H13.2581C13.3436 17 13.4257 16.9663 13.4862 16.9063C13.5467 16.8463 13.5806 16.7649 13.5806 16.68V13.16L20.6774 13.16C20.763 13.16 20.845 13.1263 20.9055 13.0663C20.966 13.0063 21 12.9249 21 12.84V9C21 8.91513 20.966 8.83374 20.9055 8.77373C20.845 8.71371 20.763 8.68 20.6774 8.68L13.5806 8.68V5.48L20.6774 5.48C20.763 5.48 20.845 5.44629 20.9055 5.38627C20.966 5.32626 21 5.24487 21 5.16V1.32C21 1.23513 20.966 1.15374 20.9055 1.09373C20.845 1.03371 20.763 1 20.6774 1L8.74194 1C8.65638 1 8.57433 1.03371 8.51384 1.09373C8.45334 1.15374 8.41936 1.23513 8.41936 1.32V4.84H1.32258C1.23703 4.84 1.15498 4.87371 1.09448 4.93373C1.03399 4.99374 1 5.07513 1 5.16L1 9C1 9.08487 1.03399 9.16626 1.09448 9.22627C1.15498 9.28629 1.23703 9.32 1.32258 9.32H8.41936V12.52H1.32258C1.23703 12.52 1.15498 12.5537 1.09448 12.6137C1.03399 12.6737 1 12.7551 1 12.84ZM12.9355 16.36H1.64516L1.64516 13.16H12.9355V16.36ZM9.06452 1.64L20.3548 1.64V4.84L9.06452 4.84L9.06452 1.64ZM1.64516 8.68L1.64516 5.48H12.9355V8.68H1.64516ZM9.06452 9.32H20.3548V12.52H9.06452V9.32Z"
        fill="#005968"
        stroke="#005968"
        stroke-width="0.5"
      />
    </svg>
  );
};

export const DurationIcon = () => {
  return (
    <svg
      width="18"
      height="18"
      viewBox="0 0 18 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M9 4.41667V8.43347C9 8.78068 9.19617 9.09809 9.50672 9.25336L11.75 10.375M17.25 9C17.25 4.44365 13.5563 0.75 9 0.75C4.44365 0.75 0.75 4.44365 0.75 9C0.75 13.5563 4.44365 17.25 9 17.25C13.5563 17.25 17.25 13.5563 17.25 9Z"
        stroke="#005968"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  );
};

export const InfoIcon = () => {
  return (
    <svg
      width="26"
      height="26"
      viewBox="0 0 26 26"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M12.9867 6.33333L13 6.33333M13 18.3333L13 10.3333M13 0.999998C19.6274 0.999999 25 6.37258 25 13C25 19.6274 19.6274 25 13 25C6.37258 25 1 19.6274 1 13C1 6.37258 6.37258 0.999998 13 0.999998Z"
        stroke="black"
        strokeOpacity="0.87"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const TaskEditPencil = () => {
  return (
    <svg
      width="18"
      height="19"
      viewBox="0 0 18 19"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M9.5 17.6951H15.5M3.50736 16.6951H2C1.44772 16.6951 1 16.2474 1 15.6951V14.1877C1 13.3921 1.31607 12.629 1.87868 12.0664L11.75 2.19511C12.7855 1.15957 14.4645 1.15957 15.5 2.19511C16.5355 3.23064 16.5355 4.90957 15.5 5.94511L5.62868 15.8164C5.06607 16.379 4.30301 16.6951 3.50736 16.6951Z"
        stroke="#005968"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  );
};

export const ViewIcon = () => {
  return (
    <svg
      width="20"
      height="16"
      viewBox="0 0 20 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M2.729 10.7468C2.02067 9.82683 1.6665 9.366 1.6665 8.00016C1.6665 6.6335 2.02067 6.17433 2.729 5.2535C4.14317 3.41683 6.51484 1.3335 9.99984 1.3335C13.4848 1.3335 15.8565 3.41683 17.2707 5.2535C17.979 6.17516 18.3332 6.63433 18.3332 8.00016C18.3332 9.36683 17.979 9.826 17.2707 10.7468C15.8565 12.5835 13.4848 14.6668 9.99984 14.6668C6.51484 14.6668 4.14317 12.5835 2.729 10.7468Z"
        stroke="#005968"
        stroke-width="1.5"
      />
      <path
        d="M12.5 8C12.5 8.66304 12.2366 9.29893 11.7678 9.76777C11.2989 10.2366 10.663 10.5 10 10.5C9.33696 10.5 8.70107 10.2366 8.23223 9.76777C7.76339 9.29893 7.5 8.66304 7.5 8C7.5 7.33696 7.76339 6.70107 8.23223 6.23223C8.70107 5.76339 9.33696 5.5 10 5.5C10.663 5.5 11.2989 5.76339 11.7678 6.23223C12.2366 6.70107 12.5 7.33696 12.5 8Z"
        stroke="#005968"
        stroke-width="1.5"
      />
    </svg>
  );
};

export const Cross: FC<crossProps> = ({ height = 16, width = 13 }) => {
  return (
    <svg
      width={height}
      height={width}
      viewBox="0 0 12 12"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M11 1L6 6M6 6L1 11M6 6L1 1M6 6L11 11"
        stroke="black"
        strokeOpacity="0.87"
        strokeWidth="1.5"
        strokeLinecap="round"
        stroke-linejoin="round"
      />
    </svg>
  );
};

export const VideoIcon = () => {
  return (
    <svg
      width="14"
      height="10"
      viewBox="0 0 14 10"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M11 4.33341V2.66675C11 1.56218 10.1046 0.666748 9 0.666748H3C1.89543 0.666748 1 1.56218 1 2.66675V7.33341C1 8.43798 1.89543 9.33341 3 9.33341H9C10.1046 9.33341 11 8.43798 11 7.33341V5.66675M11 4.33341L13.1057 2.2277C13.1897 2.1437 13.3333 2.20319 13.3333 2.32198V7.67819C13.3333 7.79697 13.1897 7.85646 13.1057 7.77247L11 5.66675M11 4.33341V5.66675"
        stroke="#005968"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  );
};
export const AudioIcon = () => {
  return (
    <svg
      width="12"
      height="12"
      viewBox="0 0 12 12"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M6.0013 0.166748C6.13391 0.166748 6.26109 0.219426 6.35486 0.313195C6.44862 0.406963 6.5013 0.53414 6.5013 0.666748V11.3334C6.5013 11.466 6.44862 11.5932 6.35486 11.687C6.26109 11.7807 6.13391 11.8334 6.0013 11.8334C5.86869 11.8334 5.74152 11.7807 5.64775 11.687C5.55398 11.5932 5.5013 11.466 5.5013 11.3334V0.666748C5.5013 0.53414 5.55398 0.406963 5.64775 0.313195C5.74152 0.219426 5.86869 0.166748 6.0013 0.166748ZM3.33464 2.16675C3.46724 2.16675 3.59442 2.21943 3.68819 2.31319C3.78196 2.40696 3.83464 2.53414 3.83464 2.66675V9.33341C3.83464 9.46602 3.78196 9.5932 3.68819 9.68697C3.59442 9.78074 3.46724 9.83341 3.33464 9.83341C3.20203 9.83341 3.07485 9.78074 2.98108 9.68697C2.88731 9.5932 2.83464 9.46602 2.83464 9.33341V2.66675C2.83464 2.53414 2.88731 2.40696 2.98108 2.31319C3.07485 2.21943 3.20203 2.16675 3.33464 2.16675ZM8.66797 2.16675C8.80058 2.16675 8.92775 2.21943 9.02152 2.31319C9.11529 2.40696 9.16797 2.53414 9.16797 2.66675V9.33341C9.16797 9.46602 9.11529 9.5932 9.02152 9.68697C8.92775 9.78074 8.80058 9.83341 8.66797 9.83341C8.53536 9.83341 8.40818 9.78074 8.31441 9.68697C8.22065 9.5932 8.16797 9.46602 8.16797 9.33341V2.66675C8.16797 2.53414 8.22065 2.40696 8.31441 2.31319C8.40818 2.21943 8.53536 2.16675 8.66797 2.16675ZM0.667969 4.83341C0.800577 4.83341 0.927754 4.88609 1.02152 4.97986C1.11529 5.07363 1.16797 5.20081 1.16797 5.33341V6.66675C1.16797 6.79936 1.11529 6.92653 1.02152 7.0203C0.927754 7.11407 0.800577 7.16675 0.667969 7.16675C0.53536 7.16675 0.408183 7.11407 0.314415 7.0203C0.220647 6.92653 0.167969 6.79936 0.167969 6.66675V5.33341C0.167969 5.20081 0.220647 5.07363 0.314415 4.97986C0.408183 4.88609 0.53536 4.83341 0.667969 4.83341ZM11.3346 4.83341C11.4672 4.83341 11.5944 4.88609 11.6882 4.97986C11.782 5.07363 11.8346 5.20081 11.8346 5.33341V6.66675C11.8346 6.79936 11.782 6.92653 11.6882 7.0203C11.5944 7.11407 11.4672 7.16675 11.3346 7.16675C11.202 7.16675 11.0748 7.11407 10.9811 7.0203C10.8873 6.92653 10.8346 6.79936 10.8346 6.66675V5.33341C10.8346 5.20081 10.8873 5.07363 10.9811 4.97986C11.0748 4.88609 11.202 4.83341 11.3346 4.83341Z"
        fill="#005968"
      />
    </svg>
  );
};

export const PreviewIcon = () => {
  return (
    <svg
      width="13"
      height="15"
      viewBox="0 0 13 15"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M1.33203 3.73263V11.2671C1.33203 12.8289 3.04222 13.7881 4.37493 12.9736L10.5395 9.20641C11.8156 8.42655 11.8156 6.57315 10.5395 5.79329L4.37493 2.02607C3.04223 1.21164 1.33203 2.17077 1.33203 3.73263Z"
        stroke="black"
        stroke-opacity="0.6"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  );
};

export const BackIcon = () => {
  return (
    <svg
      width="7"
      height="13"
      viewBox="0 0 7 13"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M6.001 1.1665L2.08188 5.08563C1.30083 5.86667 1.30083 7.133 2.08188 7.91405L6.001 11.8332"
        stroke="black"
        stroke-opacity="0.6"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  );
};

// export const PercentageIcon = () => {
//   return (
//     <svg
//       width="28"
//       height="24"
//       viewBox="0 0 28 24"
//       fill="none"
//       xmlns="http://www.w3.org/2000/svg"
//     >
//       <rect width="28" height="24" rx="12" fill="#F0F6F6" />
//       <path
//         d="M11.756 15.964C11.692 16.084 11.608 16.164 11.504 16.204C11.408 16.244 11.312 16.252 11.216 16.228C11.12 16.204 11.04 16.156 10.976 16.084C10.912 16.012 10.872 15.924 10.856 15.82C10.848 15.716 10.876 15.604 10.94 15.484L15.476 7.6C15.54 7.472 15.62 7.388 15.716 7.348C15.82 7.308 15.916 7.3 16.004 7.324C16.1 7.348 16.18 7.396 16.244 7.468C16.316 7.54 16.36 7.628 16.376 7.732C16.392 7.836 16.364 7.948 16.292 8.068L11.756 15.964ZM10.688 12.604C10.032 12.604 9.52 12.376 9.152 11.92C8.784 11.456 8.6 10.82 8.6 10.012C8.6 9.468 8.684 9.004 8.852 8.62C9.02 8.236 9.26 7.944 9.572 7.744C9.884 7.536 10.256 7.432 10.688 7.432C11.336 7.432 11.844 7.66 12.212 8.116C12.588 8.572 12.776 9.204 12.776 10.012C12.776 10.548 12.692 11.012 12.524 11.404C12.364 11.788 12.128 12.084 11.816 12.292C11.504 12.5 11.128 12.604 10.688 12.604ZM10.688 11.752C10.912 11.752 11.104 11.688 11.264 11.56C11.424 11.432 11.544 11.24 11.624 10.984C11.712 10.728 11.756 10.404 11.756 10.012C11.756 9.428 11.66 8.992 11.468 8.704C11.284 8.416 11.024 8.272 10.688 8.272C10.464 8.272 10.272 8.336 10.112 8.464C9.952 8.592 9.828 8.784 9.74 9.04C9.66 9.296 9.62 9.62 9.62 10.012C9.62 10.604 9.712 11.044 9.896 11.332C10.088 11.612 10.352 11.752 10.688 11.752ZM16.556 16.108C15.908 16.108 15.4 15.88 15.032 15.424C14.664 14.968 14.48 14.332 14.48 13.516C14.48 12.98 14.56 12.52 14.72 12.136C14.888 11.752 15.128 11.456 15.44 11.248C15.752 11.04 16.124 10.936 16.556 10.936C17.212 10.936 17.724 11.168 18.092 11.632C18.46 12.088 18.644 12.716 18.644 13.516C18.644 14.06 18.56 14.528 18.392 14.92C18.232 15.304 17.996 15.6 17.684 15.808C17.372 16.008 16.996 16.108 16.556 16.108ZM16.556 15.268C16.78 15.268 16.972 15.204 17.132 15.076C17.292 14.948 17.412 14.756 17.492 14.5C17.58 14.236 17.624 13.908 17.624 13.516C17.624 12.94 17.528 12.508 17.336 12.22C17.152 11.932 16.892 11.788 16.556 11.788C16.34 11.788 16.148 11.852 15.98 11.98C15.82 12.108 15.696 12.3 15.608 12.556C15.528 12.812 15.488 13.132 15.488 13.516C15.488 14.108 15.584 14.548 15.776 14.836C15.968 15.124 16.228 15.268 16.556 15.268Z"
//         fill="#005968"
//       />
//     </svg>
//   );
// };
export const WieghtPercentageIcon = () => {
  return (
    <svg
      width="28"
      height="24"
      viewBox="0 0 28 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect width="28" height="24" rx="12" fill="#F0F6F6" />
      <path
        d="M11.756 15.964C11.692 16.084 11.608 16.164 11.504 16.204C11.408 16.244 11.312 16.252 11.216 16.228C11.12 16.204 11.04 16.156 10.976 16.084C10.912 16.012 10.872 15.924 10.856 15.82C10.848 15.716 10.876 15.604 10.94 15.484L15.476 7.6C15.54 7.472 15.62 7.388 15.716 7.348C15.82 7.308 15.916 7.3 16.004 7.324C16.1 7.348 16.18 7.396 16.244 7.468C16.316 7.54 16.36 7.628 16.376 7.732C16.392 7.836 16.364 7.948 16.292 8.068L11.756 15.964ZM10.688 12.604C10.032 12.604 9.52 12.376 9.152 11.92C8.784 11.456 8.6 10.82 8.6 10.012C8.6 9.468 8.684 9.004 8.852 8.62C9.02 8.236 9.26 7.944 9.572 7.744C9.884 7.536 10.256 7.432 10.688 7.432C11.336 7.432 11.844 7.66 12.212 8.116C12.588 8.572 12.776 9.204 12.776 10.012C12.776 10.548 12.692 11.012 12.524 11.404C12.364 11.788 12.128 12.084 11.816 12.292C11.504 12.5 11.128 12.604 10.688 12.604ZM10.688 11.752C10.912 11.752 11.104 11.688 11.264 11.56C11.424 11.432 11.544 11.24 11.624 10.984C11.712 10.728 11.756 10.404 11.756 10.012C11.756 9.428 11.66 8.992 11.468 8.704C11.284 8.416 11.024 8.272 10.688 8.272C10.464 8.272 10.272 8.336 10.112 8.464C9.952 8.592 9.828 8.784 9.74 9.04C9.66 9.296 9.62 9.62 9.62 10.012C9.62 10.604 9.712 11.044 9.896 11.332C10.088 11.612 10.352 11.752 10.688 11.752ZM16.556 16.108C15.908 16.108 15.4 15.88 15.032 15.424C14.664 14.968 14.48 14.332 14.48 13.516C14.48 12.98 14.56 12.52 14.72 12.136C14.888 11.752 15.128 11.456 15.44 11.248C15.752 11.04 16.124 10.936 16.556 10.936C17.212 10.936 17.724 11.168 18.092 11.632C18.46 12.088 18.644 12.716 18.644 13.516C18.644 14.06 18.56 14.528 18.392 14.92C18.232 15.304 17.996 15.6 17.684 15.808C17.372 16.008 16.996 16.108 16.556 16.108ZM16.556 15.268C16.78 15.268 16.972 15.204 17.132 15.076C17.292 14.948 17.412 14.756 17.492 14.5C17.58 14.236 17.624 13.908 17.624 13.516C17.624 12.94 17.528 12.508 17.336 12.22C17.152 11.932 16.892 11.788 16.556 11.788C16.34 11.788 16.148 11.852 15.98 11.98C15.82 12.108 15.696 12.3 15.608 12.556C15.528 12.812 15.488 13.132 15.488 13.516C15.488 14.108 15.584 14.548 15.776 14.836C15.968 15.124 16.228 15.268 16.556 15.268Z"
        fill="#005968"
      />
    </svg>
  );
};

export const SendIcon = () => {
  return (
    <svg
      width="18"
      height="16"
      viewBox="0 0 18 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M17.089 7.41066L10.4223 0.743989C10.3058 0.627481 10.1573 0.548143 9.9957 0.516003C9.83407 0.483863 9.66654 0.500366 9.51429 0.563424C9.36204 0.626483 9.23191 0.733265 9.14034 0.870274C9.04877 1.00728 8.99987 1.16836 8.99984 1.33316V4.28732C6.72341 4.49798 4.60753 5.55054 3.06625 7.23901C1.52497 8.92749 0.66923 11.1303 0.666504 13.4165V14.6665C0.666635 14.8395 0.720595 15.0081 0.820899 15.1491C0.921203 15.29 1.06288 15.3962 1.22628 15.453C1.38967 15.5098 1.56669 15.5143 1.73278 15.466C1.89887 15.4176 2.04579 15.3188 2.15317 15.1832C2.96971 14.2124 3.97145 13.4141 5.09992 12.8348C6.22838 12.2554 7.46096 11.9067 8.72567 11.809C8.76734 11.804 8.8715 11.7957 8.99984 11.7873V14.6665C8.99987 14.8313 9.04877 14.9924 9.14034 15.1294C9.23191 15.2664 9.36204 15.3732 9.51429 15.4362C9.66654 15.4993 9.83407 15.5158 9.9957 15.4836C10.1573 15.4515 10.3058 15.3722 10.4223 15.2557L17.089 8.58899C17.2452 8.43272 17.333 8.22079 17.333 7.99982C17.333 7.77885 17.2452 7.56693 17.089 7.41066ZM10.6665 12.6548V10.9165C10.6665 10.6955 10.5787 10.4835 10.4224 10.3272C10.2661 10.171 10.0542 10.0832 9.83317 10.0832C9.62067 10.0832 8.75317 10.1248 8.5315 10.154C6.2855 10.3607 4.14757 11.214 2.3765 12.6107C2.57754 10.7727 3.44945 9.07344 4.8253 7.83829C6.20114 6.60313 7.98424 5.91886 9.83317 5.91649C10.0542 5.91649 10.2661 5.82869 10.4224 5.67241C10.5787 5.51613 10.6665 5.30417 10.6665 5.08316V3.34482L15.3215 7.99982L10.6665 12.6548Z"
        fill="white"
      />
    </svg>
  );
};

export const RedCrossIcon = () => {
  return (
    <svg
      width="14"
      height="14"
      viewBox="0 0 14 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M13 1L7 7M7 7L1 13M7 7L1 1M7 7L13 13"
        stroke="#A80000"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  );
};

export const RupeesIcon: React.FC<{ height: string; width: string }> = ({
  height,
  width,
}) => {
  return (
    <svg
      width={height}
      height={width}
      viewBox="0 0 10 12"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M4.33594 1.33333H1.0026C0.825793 1.33333 0.656224 1.2631 0.5312 1.13807C0.406176 1.01305 0.335938 0.843478 0.335938 0.666667C0.335938 0.489856 0.406176 0.320286 0.5312 0.195262C0.656224 0.0702379 0.825793 0 1.0026 0H9.00261C9.17942 0 9.34899 0.0702379 9.47401 0.195262C9.59904 0.320286 9.66927 0.489856 9.66927 0.666667C9.66927 0.843478 9.59904 1.01305 9.47401 1.13807C9.34899 1.2631 9.17942 1.33333 9.00261 1.33333H7.00261C7.2994 1.72755 7.50439 2.18309 7.60261 2.66667H9.00261C9.17942 2.66667 9.34899 2.7369 9.47401 2.86193C9.59904 2.98695 9.66927 3.15652 9.66927 3.33333C9.66927 3.51014 9.59904 3.67971 9.47401 3.80474C9.34899 3.92976 9.17942 4 9.00261 4H7.60261C7.44869 4.75279 7.03952 5.42934 6.44429 5.91524C5.84907 6.40114 5.10431 6.66657 4.33594 6.66667H3.94527L8.14061 10.862C8.26204 10.9877 8.32924 11.1561 8.32772 11.3309C8.3262 11.5057 8.25609 11.6729 8.13248 11.7965C8.00888 11.9202 7.84167 11.9903 7.66687 11.9918C7.49207 11.9933 7.32367 11.9261 7.19794 11.8047L1.8646 6.47133C1.7714 6.3781 1.70793 6.25932 1.68222 6.13002C1.6565 6.00072 1.66971 5.8667 1.72015 5.7449C1.7706 5.6231 1.85603 5.51899 1.96563 5.44573C2.07524 5.37248 2.2041 5.33336 2.33594 5.33333H4.33594C4.74959 5.33341 5.15309 5.20523 5.49086 4.96644C5.82863 4.72765 6.08405 4.39 6.22194 4H1.0026C0.825793 4 0.656224 3.92976 0.5312 3.80474C0.406176 3.67971 0.335938 3.51014 0.335938 3.33333C0.335938 3.15652 0.406176 2.98695 0.5312 2.86193C0.656224 2.7369 0.825793 2.66667 1.0026 2.66667H6.22194C6.08405 2.27667 5.82863 1.93902 5.49086 1.70023C5.15309 1.46144 4.74959 1.33325 4.33594 1.33333Z"
        fill="#E8B000"
      />
    </svg>
  );
};

export const PercentageIcon2: React.FC<{
  height?: string;
  width?: string;
  color?: string;
}> = ({ height, width, color }) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 15 13"
      fill={"#fffff"}
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M5.008 12.452C4.92267 12.612 4.81067 12.7187 4.672 12.772C4.544 12.8253 4.416 12.836 4.288 12.804C4.16 12.772 4.05333 12.708 3.968 12.612C3.88267 12.516 3.82933 12.3987 3.808 12.26C3.79733 12.1213 3.83467 11.972 3.92 11.812L9.968 1.3C10.0533 1.12933 10.16 1.01733 10.288 0.964C10.4267 0.910666 10.5547 0.9 10.672 0.931999C10.8 0.963999 10.9067 1.028 10.992 1.124C11.088 1.22 11.1467 1.33733 11.168 1.476C11.1893 1.61467 11.152 1.764 11.056 1.924L5.008 12.452ZM3.584 7.972C2.70933 7.972 2.02667 7.668 1.536 7.06C1.04533 6.44133 0.8 5.59333 0.8 4.516C0.8 3.79067 0.912 3.172 1.136 2.66C1.36 2.148 1.68 1.75867 2.096 1.492C2.512 1.21467 3.008 1.076 3.584 1.076C4.448 1.076 5.12533 1.38 5.616 1.988C6.11733 2.596 6.368 3.43867 6.368 4.516C6.368 5.23067 6.256 5.84933 6.032 6.372C5.81867 6.884 5.504 7.27867 5.088 7.556C4.672 7.83333 4.17067 7.972 3.584 7.972ZM3.584 6.836C3.88267 6.836 4.13867 6.75067 4.352 6.58C4.56533 6.40933 4.72533 6.15333 4.832 5.812C4.94933 5.47067 5.008 5.03867 5.008 4.516C5.008 3.73733 4.88 3.156 4.624 2.772C4.37867 2.388 4.032 2.196 3.584 2.196C3.28533 2.196 3.02933 2.28133 2.816 2.452C2.60267 2.62267 2.43733 2.87867 2.32 3.22C2.21333 3.56133 2.16 3.99333 2.16 4.516C2.16 5.30533 2.28267 5.892 2.528 6.276C2.784 6.64933 3.136 6.836 3.584 6.836ZM11.408 12.644C10.544 12.644 9.86667 12.34 9.376 11.732C8.88533 11.124 8.64 10.276 8.64 9.188C8.64 8.47333 8.74667 7.86 8.96 7.348C9.184 6.836 9.504 6.44133 9.92 6.164C10.336 5.88667 10.832 5.748 11.408 5.748C12.2827 5.748 12.9653 6.05733 13.456 6.676C13.9467 7.284 14.192 8.12133 14.192 9.188C14.192 9.91333 14.08 10.5373 13.856 11.06C13.6427 11.572 13.328 11.9667 12.912 12.244C12.496 12.5107 11.9947 12.644 11.408 12.644ZM11.408 11.524C11.7067 11.524 11.9627 11.4387 12.176 11.268C12.3893 11.0973 12.5493 10.8413 12.656 10.5C12.7733 10.148 12.832 9.71067 12.832 9.188C12.832 8.42 12.704 7.844 12.448 7.46C12.2027 7.076 11.856 6.884 11.408 6.884C11.12 6.884 10.864 6.96933 10.64 7.14C10.4267 7.31067 10.2613 7.56667 10.144 7.908C10.0373 8.24933 9.984 8.676 9.984 9.188C9.984 9.97733 10.112 10.564 10.368 10.948C10.624 11.332 10.9707 11.524 11.408 11.524Z"
        fill={color ?? ""}
        fillOpacity="0.6"
      />
    </svg>
  );
};

export const SuccessIcon: React.FC = () => {
  return (
    <svg
      width="9"
      height="9"
      viewBox="0 0 9 9"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M1.5 4.875L2.57951 5.95451C3.01885 6.39385 3.73116 6.39384 4.1705 5.9545L7.5 2.625"
        stroke="#005968"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  );
};

export const ReasonIcon = () => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M5.99971 8.5H13.9997M5.99971 12.5H9.99971M0.999712 10C0.999712 5.02944 5.02915 1 9.99971 1C14.9703 1 18.9997 5.02944 18.9997 10C18.9997 14.9706 14.9703 19 9.99971 19C9.30668 19 8.63195 18.9217 7.98388 18.7734C7.35789 18.6301 6.70569 18.6103 6.08078 18.7581C4.68524 19.0884 2.75904 19.3545 1.44121 18.9801C0.885901 18.8224 0.831539 18.1354 1.09916 17.6239C1.41048 17.0289 1.68129 16.2964 1.88597 15.6456C2.1152 14.9167 1.96977 14.1402 1.67805 13.434C1.2409 12.3758 0.999712 11.2161 0.999712 10Z"
        stroke="#005968"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  );
};

export const ImageIcon = () => {
  return (
    <svg
      width="14"
      height="12"
      viewBox="0 0 14 12"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M2.33333 11.3334L4.81597 7.75842C5.37903 6.94761 6.59727 7.00851 7.07667 7.87142L7.26892 8.21747M5 11.3334L9.12046 5.66779C9.68954 4.8853 10.8764 4.95044 11.3564 5.7905L13 8.66675M3 11.3334H11C12.1046 11.3334 13 10.438 13 9.33341V2.66675C13 1.56218 12.1046 0.666748 11 0.666748H3C1.89543 0.666748 1 1.56218 1 2.66675V9.33341C1 10.438 1.89543 11.3334 3 11.3334ZM4.66667 3.16675C4.66667 3.44289 4.44281 3.66675 4.16667 3.66675C3.89052 3.66675 3.66667 3.44289 3.66667 3.16675C3.66667 2.89061 3.89052 2.66675 4.16667 2.66675C4.44281 2.66675 4.66667 2.89061 4.66667 3.16675Z"
        stroke="#005968"
        stroke-linejoin="round"
      />
    </svg>
  );
};
export const Deletelevel = () => {
  return (
    <svg
      width="14"
      height="18"
      viewBox="0 0 14 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M3.67094 4L4.1244 2.63962C4.35126 1.95905 4.98816 1.5 5.70554 1.5H8.30301C9.02039 1.5 9.65729 1.95905 9.88415 2.63962L10.3376 4M3.67094 4H1.68597C1.06649 4 0.663573 4.65193 0.940615 5.20601L1.34814 6.02106C1.506 6.33677 1.59521 6.68234 1.6099 7.03501L1.90445 14.1041C1.96024 15.4431 3.06205 16.5 4.40228 16.5H9.60627C10.9465 16.5 12.0483 15.4431 12.1041 14.1041L12.3987 7.03501C12.4133 6.68234 12.5026 6.33677 12.6604 6.02105L13.0679 5.20601C13.345 4.65193 12.9421 4 12.3226 4H10.3376M3.67094 4H10.3376"
        stroke="#A80000"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  );
};
export const DownArrow = () => {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M4 9L9.87868 14.8787C11.0503 16.0503 12.9497 16.0502 14.1213 14.8787L20 8.99999"
        stroke="#004350"
        stroke-opacity="0.87"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  );
};
interface CalendarIconProps {
  strokeColor?: string;
}

export const CalendarIcon: React.FC<CalendarIconProps> = ({
  strokeColor = "#005968",
}) => {
  return (
    <svg
      width="12"
      height="12"
      viewBox="0 0 12 12"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M4 3.5V1.5M8 3.5V1.5M4.005 5H4M4.005 6.5H4M4.005 8H4M6.005 6.5H6M6.005 5H6M8.005 6.5H8M6.005 8H6M8.005 5H8M3 10.5H9C9.82843 10.5 10.5 9.82843 10.5 9V4C10.5 3.17157 9.82843 2.5 9 2.5H3C2.17157 2.5 1.5 3.17157 1.5 4V9C1.5 9.82843 2.17157 10.5 3 10.5Z"
        stroke={strokeColor}
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  );
};
interface RightArrowProps {
  strokeColor?: string;
}

//right arrow icon
export const RightArrow: React.FC<RightArrowProps> = ({
  strokeColor = "#005968",
}) => {
  return (
    <svg
      width="22"
      height="22"
      viewBox="0 0 22 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M10.1875 6.125L12.7644 8.7019C14.0336 9.97111 14.0336 12.0289 12.7644 13.2981L10.1875 15.875M20.75 11C20.75 5.61522 16.3848 1.25 11 1.25C5.61522 1.25 1.25 5.61522 1.25 11C1.25 16.3848 5.61522 20.75 11 20.75C16.3848 20.75 20.75 16.3848 20.75 11Z"
        stroke={strokeColor}
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  );
};
interface LeftArrowProps {
  strokeColor: string;
}

// left icon

export const LeftArrow: React.FC<LeftArrowProps> = ({
  strokeColor = "#005968",
}) => {
  return (
    <svg
      width="22"
      height="22"
      viewBox="0 0 22 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M11.8125 15.875L9.23559 13.2981C7.96639 12.0289 7.96639 9.97111 9.2356 8.70191L11.8125 6.12501M11 20.75C16.3848 20.75 20.75 16.3848 20.75 11C20.75 5.61524 16.3848 1.25002 11 1.25002C5.61522 1.25002 1.25 5.61524 1.25 11C1.25 16.3848 5.61522 20.75 11 20.75Z"
        stroke={strokeColor}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

//updown arrow

export const Updownarrow = () => {
  return (
    <svg
      width="14"
      height="16"
      viewBox="0 0 14 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M3.66675 9.66671V1.33337M3.66675 1.33337L1.16675 3.83337M3.66675 1.33337L6.16675 3.83337M10.3334 6.33337V14.6667M10.3334 14.6667L7.83341 12.1667M10.3334 14.6667L12.8334 12.1667"
        stroke="#005968"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  );
};

// attachicon
export const AttachIcon = ({}) => {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M13.4876 16.4195L14.9018 17.8337C16.4639 19.3958 18.9965 19.3958 20.5586 17.8337C22.1207 16.2716 22.1207 13.739 20.5586 12.1769L17.7302 9.34844C16.1681 7.78635 13.6354 7.78635 12.0733 9.34845C11.356 10.0658 10.9681 10.9878 10.9096 11.9264M11.0127 7.58066L9.59845 6.16645C8.03635 4.60435 5.50369 4.60435 3.94159 6.16645C2.3795 7.72854 2.3795 10.2612 3.94159 11.8233L6.77002 14.6517C8.33212 16.2138 10.8648 16.2138 12.4269 14.6517C13.1442 13.9344 13.5321 13.0124 13.5906 12.0737"
        stroke="black"
        stroke-opacity="0.6"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  );
};
//send button icon
export const SendButtonIcon = ({}) => {
  return (
    <svg
      width="42"
      height="42"
      viewBox="0 0 42 42"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect width="42" height="42" rx="21" fill="#005968" />
      <path
        d="M22.6423 19.4705L17.6925 24.4202M25.884 12.3124L14.3579 17.0585C12.0169 18.0224 11.8488 21.2737 14.0778 22.4739L17.1642 24.1358C17.5088 24.3214 17.7913 24.6039 17.9769 24.9485L19.6388 28.0349C20.839 30.2639 24.0903 30.0958 25.0542 27.7548L29.8003 16.2287C30.8156 13.7629 28.3498 11.2971 25.884 12.3124Z"
        stroke="white"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  );
};
// active
export const ActiveTick = ({}) => {
  return (
    <svg
      width="16"
      height="17"
      viewBox="0 0 16 17"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M10.5 7.33336L7.92337 10.0104C7.59555 10.351 7.05038 10.351 6.72256 10.0104L5.91671 9.17319M5.28499 2.74221C5.86798 2.9873 6.54293 2.78912 6.90087 2.26775C7.43056 1.4962 8.56953 1.4962 9.09922 2.26775C9.45716 2.78912 10.1321 2.9873 10.7151 2.74221C11.5778 2.37952 12.536 2.99529 12.5645 3.93073C12.5837 4.56285 13.0444 5.09449 13.6673 5.20349C14.5892 5.36481 15.0623 6.40084 14.5806 7.20318C14.255 7.74536 14.3551 8.44165 14.8202 8.87014C15.5085 9.50425 15.3465 10.6316 14.5074 11.0461C13.9404 11.3262 13.6481 11.9661 13.8078 12.578C14.044 13.4836 13.2981 14.3444 12.3682 14.2394C11.7397 14.1685 11.148 14.5488 10.9514 15.1499C10.6605 16.0395 9.56771 16.3603 8.8421 15.7693C8.35177 15.3699 7.64832 15.3699 7.158 15.7693C6.43239 16.3603 5.33956 16.0395 5.04869 15.1499C4.85214 14.5488 4.26036 14.1685 3.63194 14.2394C2.70196 14.3444 1.9561 13.4836 2.19232 12.578C2.35195 11.9661 2.05972 11.3262 1.49272 11.0461C0.653643 10.6316 0.491552 9.50425 1.17986 8.87014C1.64499 8.44165 1.7451 7.74536 1.41954 7.20318C0.937753 6.40084 1.4109 5.36481 2.33276 5.20349C2.95571 5.09449 3.41637 4.56285 3.43562 3.93073C3.46409 2.99529 4.42225 2.37952 5.28499 2.74221Z"
        stroke="white"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  );
};

// quality
export const QualityIcon = ({}) => {
  return (
    <svg
      width="18"
      height="24"
      viewBox="0 0 18 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M12.3926 18L12.5659 21.5156L9.00987 19.0444L5.43392 21.518L5.6074 18H4.10557L3.84673 23.25H5.56667L9.0079 20.8697L12.4332 23.25H14.1532L13.8944 18H12.3926ZM16.4723 7.26163L16.3694 5.0091L14.4703 3.79377L13.2547 1.89444L11.0022 1.79132L8.99998 0.754395L6.99781 1.79155L4.74523 1.89468L3.52967 3.79377L1.63053 5.0091L1.52768 7.26163L0.490479 9.26385L1.52768 11.2661L1.63081 13.5186L3.52967 14.7339L4.74504 16.6333L6.99762 16.7362L8.99998 17.7733L11.0022 16.7362L13.2547 16.6333L14.4701 14.7339L16.3694 13.5188L16.4723 11.2663L17.5095 9.26404L16.4723 7.26163ZM14.9889 10.8686L14.9062 12.674L13.384 13.648L12.4099 15.1703L10.6045 15.2528L8.99998 16.084L7.39526 15.2528L5.58987 15.1703L4.61581 13.648L3.09373 12.674L3.01128 10.8686L2.17967 9.26385L3.01109 7.65941L3.09373 5.85379L4.61581 4.87972L5.58987 3.3575L7.39526 3.275L8.99998 2.44372L10.6047 3.27496L12.4101 3.35746L13.3842 4.87968L14.9062 5.85379L14.9887 7.65918L15.8203 9.26385L14.9889 10.8686Z"
        fill="#A80000"
      />
    </svg>
  );
};

// calendar
export const CalenderNew = () => {
  return (
    <svg
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M5 4.25V1.25M11 4.25V1.25M5.0075 6.5H5M5.0075 8.75H5M5.0075 11H5M8.0075 8.75H8M8.0075 6.5H8M11.0075 8.75H11M8.0075 11H8M11.0075 6.5H11M3.5 14H12C13.7426 14 14 13.7426 14 12V5C14 3.75736 13.7426 2.75 12.5 2.75H3.5C2.25736 2.75 1.25 3.75736 1.25 5V12.5C1.25 13.7426 2.25736 14.75 3.5 14.75Z"
        stroke="#005968"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  );
};

export const Notification = () => {
  return (
    <svg
      width="22"
      height="26"
      viewBox="0 0 22 26"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M8.33339 21.2353H2.25922C1.08875 21.2353 0.521039 19.8037 1.37338 19.0015C2.41146 18.0245 3.00006 16.6622 3.00006 15.2367V11.6666C3.00006 7.24835 6.58178 3.66663 11.0001 3.66663M8.33339 21.2353V22.3333C8.33339 23.8061 9.5273 25 11.0001 25C12.4728 25 13.6667 23.8061 13.6667 22.3333V21.2353M8.33339 21.2353H13.6667M13.6667 21.2353H19.7409C20.9114 21.2353 21.4791 19.8037 20.6267 19.0015C19.5887 18.0245 19.0001 16.6622 19.0001 15.2367V11.6666C19.0001 7.24835 15.4183 3.66663 11.0001 3.66663M11.0001 3.66663V1.66663"
        stroke="black"
        strokeOpacity="0.87"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const Attachment = () => {
  return (
    <svg
      width="20"
      height="16"
      viewBox="0 0 20 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M11.3635 12.0507L12.6598 13.3471C14.0918 14.779 16.4134 14.779 17.8453 13.3471C19.2772 11.9152 19.2772 9.59355 17.8453 8.16162L15.2526 5.5689C13.8206 4.13698 11.499 4.13698 10.0671 5.5689C9.40956 6.22644 9.05396 7.0716 9.00031 7.93205M9.09482 3.94843L7.79845 2.65207C6.36653 1.22014 4.04493 1.22014 2.613 2.65207C1.18108 4.08399 1.18108 6.40559 2.613 7.83752L5.20573 10.4302C6.63765 11.8622 8.95926 11.8622 10.3912 10.4302C11.0487 9.7727 11.4043 8.92754 11.458 8.06709"
        stroke="#005968"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  );
};

export const closeEye = () => {
  return (
    <svg
      width="20"
      height="16"
      viewBox="0 0 20 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M11.3635 12.0507L12.6598 13.3471C14.0918 14.779 16.4134 14.779 17.8453 13.3471C19.2772 11.9152 19.2772 9.59355 17.8453 8.16162L15.2526 5.5689C13.8206 4.13698 11.499 4.13698 10.0671 5.5689C9.40956 6.22644 9.05396 7.0716 9.00031 7.93205M9.09482 3.94843L7.79845 2.65207C6.36653 1.22014 4.04493 1.22014 2.613 2.65207C1.18108 4.08399 1.18108 6.40559 2.613 7.83752L5.20573 10.4302C6.63765 11.8622 8.95926 11.8622 10.3912 10.4302C11.0487 9.7727 11.4043 8.92754 11.458 8.06709"
        stroke="#005968"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  );
};
export const openVersion = () => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M2.5 2.5L6.66667 6.625M2.5 2.5V6.25M2.5 2.5H6.25M2.5 17.4583L6.66667 13.3333M17.5 17.4583L13.375 13.3333M17.4583 2.5L13.3333 6.625M13.75 2.5H17.5V6.25M17.5 13.75V17.5H13.75M6.25 17.5H2.5V13.75"
        stroke="#005968"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  );
};
export const calendar = () => {
  return (
    <svg
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M5.33333 4.66667V2M10.6667 4.66667V2M5.34 6.66667H5.33333M5.34 8.66667H5.33333M5.34 10.6667H5.33333M8.00667 8.66667H8M8.00667 6.66667H8M10.6733 8.66667H10.6667M8.00667 10.6667H8M10.6733 6.66667H10.6667M4 14H12C13.1046 14 14 13.1046 14 12V5.33333C14 4.22876 13.1046 3.33333 12 3.33333H4C2.89543 3.33333 2 4.22876 2 5.33333V12C2 13.1046 2.89543 14 4 14Z"
        stroke="#005968"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  );
};

export const EyeHideIcon = ({
  width = "16",
  height = "14",
  color = "",
}: {
  width?: string;
  height?: string;
  color?: string;
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 20 19"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        id="Vector"
        d="M10.3174 6.51659C11.6923 6.66116 12.7905 7.73442 12.9732 9.09759M7.76389 7.5C7.28885 8.03076 7 8.73164 7 9.5C7 11.1569 8.34315 12.5 10 12.5C10.7684 12.5 11.4692 12.2111 12 11.7361M4.5 4.22189C2.37097 5.65996 1 7.7501 1 9.5C1 12.5 5.02944 16.5 10 16.5C11.9572 16.5 13.7684 15.8798 15.2452 14.9448M6.97011 3C7.91688 2.68297 8.93691 2.5 10 2.5C14.9706 2.5 19 6.5 19 9.5C19 10.6141 18.4443 11.8661 17.491 13M18 17.5L2 1.5"
        stroke="#005968"
        stroke-width="1.25"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  );
};

export const EyeVisibleIcon = ({
  width = "24",
  height = "25",
  color = "",
}: {
  width?: string;
  height?: string;
  color?: string;
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 24 25"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g id="Icon set 1">
        <g id="Group">
          <path
            id="Vector"
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M12 8.75C11.0054 8.75 10.0516 9.14509 9.34835 9.84835C8.64509 10.5516 8.25 11.5054 8.25 12.5C8.25 13.4946 8.64509 14.4484 9.34835 15.1517C10.0516 15.8549 11.0054 16.25 12 16.25C12.9946 16.25 13.9484 15.8549 14.6517 15.1517C15.3549 14.4484 15.75 13.4946 15.75 12.5C15.75 11.5054 15.3549 10.5516 14.6517 9.84835C13.9484 9.14509 12.9946 8.75 12 8.75ZM9.75 12.5C9.75 11.9033 9.98705 11.331 10.409 10.909C10.831 10.4871 11.4033 10.25 12 10.25C12.5967 10.25 13.169 10.4871 13.591 10.909C14.0129 11.331 14.25 11.9033 14.25 12.5C14.25 13.0967 14.0129 13.669 13.591 14.091C13.169 14.5129 12.5967 14.75 12 14.75C11.4033 14.75 10.831 14.5129 10.409 14.091C9.98705 13.669 9.75 13.0967 9.75 12.5Z"
            fill="#005968"
          />
          <path
            id="Vector_2"
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M12 3.75C7.486 3.75 4.445 6.454 2.68 8.747L2.649 8.788C2.249 9.307 1.882 9.784 1.633 10.348C1.366 10.953 1.25 11.612 1.25 12.5C1.25 13.388 1.366 14.047 1.633 14.652C1.883 15.216 2.25 15.694 2.649 16.212L2.681 16.253C4.445 18.546 7.486 21.25 12 21.25C16.514 21.25 19.555 18.546 21.32 16.253L21.351 16.212C21.751 15.694 22.118 15.216 22.367 14.652C22.634 14.047 22.75 13.388 22.75 12.5C22.75 11.612 22.634 10.953 22.367 10.348C22.117 9.784 21.75 9.307 21.351 8.788L21.319 8.747C19.555 6.454 16.514 3.75 12 3.75ZM3.87 9.662C5.498 7.545 8.15 5.25 12 5.25C15.85 5.25 18.501 7.545 20.13 9.662C20.57 10.232 20.826 10.572 20.995 10.954C21.153 11.312 21.25 11.749 21.25 12.5C21.25 13.251 21.153 13.688 20.995 14.046C20.826 14.428 20.569 14.768 20.131 15.338C18.5 17.455 15.85 19.75 12 19.75C8.15 19.75 5.499 17.455 3.87 15.338C3.43 14.768 3.174 14.428 3.005 14.046C2.847 13.688 2.75 13.251 2.75 12.5C2.75 11.749 2.847 11.312 3.005 10.954C3.174 10.572 3.432 10.232 3.87 9.662Z"
            fill="#005968"
          />
        </g>
      </g>
    </svg>
  );
};

export const DepartmentCardLogo = ({
  width = "32",
  height = "32",
  color = "",
}: {
  width?: string;
  height?: string;
  color?: string;
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      xmlnsXlink="http://www.w3.org/1999/xlink"
    >
      <rect
        id="department 1"
        width="32"
        height="32"
        fill="url(#pattern0_6322_40853)"
      />
      <defs>
        <pattern
          id="pattern0_6322_40853"
          patternContentUnits="objectBoundingBox"
          width="1"
          height="1"
        >
          <use xlinkHref="#image0_6322_40853" transform="scale(0.00195312)" />
        </pattern>
        <image
          id="image0_6322_40853"
          data-name="department.png"
          width="512"
          height="512"
          preserveAspectRatio="none"
          xlinkHref="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAgAAAAIACAYAAAD0eNT6AAAACXBIWXMAAA7DAAAOwwHHb6hkAAAAGXRFWHRTb2Z0d2FyZQB3d3cuaW5rc2NhcGUub3Jnm+48GgAAIABJREFUeJzs3XmcW3X1//HXuZNpoZRN2yK77LuyuyGy6VeEfhUwdpK2iAhjJ8nQhbJIWcK+05aZTEvFUijNTBnlp4Iiyo76RVYXBIpsioC0ZS8tdJJ7fn+kQ1k67XRybz43yXn+40Og57675XNyP5tgjKkN8XgDDSP2RXRPhD1R2R10E5ANgA0BAd5CeRthIcrjwN9okL+w3vIHmTWrx+1PwBhTSeI6gDGmDM3NjbwT+ybIUcBIYNgAK70B+mvwfsGbciu3tb0fYEpjTARZA2BMNTq8dTAbF0ehnAmyQ8DVFyIyg0GxacyZ9mbAtY0xEWENgDHV5KBsjM0WjkPlbGB4yE97E+ES3vCm2RsBY2qPNQDGVItE5hDQacAeFX7yi4ieSb7jhgo/1xgTImsAjIm6MantKcpFQNxxkrvxvYnMb/ur4xzGmABYA2BMVMVTQ2n0JqN6OjDYdZwVfIR5NBQnM3fmQtdhjDEDZw2AMVGTzXo8vXAMKpcBm7iO0wdbH2BMlbMGwJgoSbR8DbxpwJ6uo/SP/hPxppBv73adxBizdqwBMCYKmlJb4smFKGOoyr+Xege+P5H5Mx93ncQY0z9V+EFjTA0Z2TyE9QediuppwDqu45SpgDIb6ZlC56zFrsMYY1bPGgBj3BCSme+CXoGyleswAXsdkfN4eViOe7IF12GMMatmDYAxlTYqtR+eTAO+7DpKyJ5COJl87jeugxhjPskaAGMqJT5ucxobLq7eef6B0jvwGsYzr+0J10mMMSvV0YeQMY7EJ65L4/KTUKYA67uO40gPwgx6es6me9ZbrsMYY6wBMCZcidRIkKuBz7qOEhGvIXI+Pa+2091ddB3GmHpmDYAxYWjK7As6DeErjpO8j/Iwos8joihbA/sB6zpNJTwKTCSfu89pDmPqmDUAxgRpTPOmFBsvBL4PeA6TvIVwMfizyM944yP/ZnTrBvj6fdCzgWFu4n3gJoRTyef+5TiHMXXHGgBjghDPDqJxcQuq5wEbOExSOqtfY6fQOf3V1f6Xx03YiPd6TkeYgNu7BpYjzGSdZWcye/Y7DnMYU1esATCmXKV5/mnAto6T/BllPF25P6/VjxqV3hGPC3B/2+DLiJzLjsOuJZv1HWcxpuZZA2DMQI1K7YInVwHfdJzkP4hOId8xF9ABV0m2HobqVNDdg4s2IA/jyQTmtf/RcQ5japo1AMasrfjET9G4/ByUNNDgLIewFORyBi25hDlz3guk5kHZGJstPh7VC3G7PkCBnyGcYusDjAmHNQDG9FdzcyNLBqVQzQIbOUxSGhwLscl0T/93KE9Y2eSkgFgoz+gPYSk+bRT1Aro7ljjLYUwNsgbAmP5Ith6G+tOA3ZzmUB4CnUBXx58q8rxEZmfEvwqVwyvyvL4FM81hjPmANQDGrM6Ylp0oNlwJeoTjJC8hcp6zBXKlBuhqYJeKP/ujHsTzxzNvxgOOcxhT9awBMGZVki0b43unIUwEBjlMsgzlaoYsu9D5FrmVUyDnAhs6TKIIN9Kjp9Ld8V+HOYypatYAGPNhKxfBXQAMd5pFuRWKrXTNfMFpjo87Nv1pCpztfBEkvIvIFYEugjSmjlgDYEyvROYQ0GnAHk5zCI/iexPoarvfaY41aWrdC/GnAQc6TvIiomeS77jBcQ5jqoo1AMaMSW1PUS7C/UE4ixG5oOouykmkRqIyHWEbx0nuxvcmMr/tr45zGFMVrAEw9SueGkqjNxnV03F7FG7pqlzxzmJe29sOcwzcyqOQz8ftlcelo5AbipOZO3OhwxzGRJ41AKb+ZLMeTy8cg8plwCZOsyi3ot4E5rc96zRHUJoym4Geg3ACbi9DehPhEt7wpnFb2/sOcxgTWdYAmPqSaPkaeNOAPR0neRJ0Ep0dv3WcIxzRuQ75aUTOJN/e7TiHMZFjDYCpD02pLfHkQpQxuP1z/zoi51XdPP/ACMnMd1G9HNjabRS9A9+fyPyZj7vNYUx0WANgatvI5iGsP+hUVE8D1nGYpAflOqRnCp2zFjvMUXkjm4ewfmMrypnAUIdJCiiz6/L3wJhVsAbA1KrSt0/0CpSt3EbRO0Am0Jn7h9scjo1u3QL1L4rMW5iXh+W4J1twmMMYp6wBMLVnVGo/PJkGfNlxkqdRfxJdM37tOEe0JFv3xy9OR+SLjpM8hS+TmN9+m+McxjhhDYCpHfFxm9PYcHEEvmG+gXApPcOn0p1d7jBHlAnJ1FhULgU+4zaK3oHXMJ55bU+4zWFMZVkDYKpffOK6NC4/CWUKbvegl+aYi96ZdLctcpijeoydvB7F906JxBoNYQY9PWfTPesthzmMqRhrAEx1S6RGglwNfNZxkrvAn0jnjL85zlGd4uO3orFwAcpYx0leQ+T8OtmlYeqcNQCmOo1K742n00C+6jSH8gyenGH7zAOSTB2MyjTgc26D6GOITCCfu89tDmPCYw2AqS5jmjel0JhF+CFub6JbgsiVvCEX20lzAVt5UuPlwAinWZRbaZSTmNv+vNMcxoTAGgBTHVaeNX8esIHDJKWz5jV2Cp3TX3WYo/YdN2Ej3us5HWECbu9qWI4wk3WWncns2e84zGFMoKwBMNFXmuefBmzrNojci/gTyHf8xW2OOjMqvSMeF+D+tsaXETmXHYddSzbrO85iTNmsATDRNSq1C55cBXzTcZL/IDqFfMdcQB1nqV/J1sNQnQq6u+MkD+PJBOa1/9FxDmPKYg2AiZ74xE/RuPwclDQu5/mFpSCXM2jJJcyZ856zHGalg7IxNlt8PKoXAsMcJlHgZwinkM/9y2EOYwbMGgATHc3NjSwZlEI1C2zkMEnpw70Qm0z39H87zGH6srJJTAExZzmEpfi0UdQL6O5Y4iyHMQNgDYCJhmTrYag/DdjNaQ7lIdAJdHX8yWkO0z+JzM6IfxUqhztOYtNEpupYA2DcGtOyE8WGK0GPcJzkJUTPsA/wKlVqIK8GdnGc5EE8fzzzZjzgOIcxa2QNgHEj2bIxvncawkRgkMMky1CuZsiyC22LV5VbOYV0LrChwySKcCM9eirdHf91mMOY1bIGwFTWykVcFwDDnWZRboViK10zX3CawwTr2PSnKXC280Wk8C4iV9giUhNV1gCYyklkDgGdiutjXoVH8b0JdLXd7zSHCVdT616IPw040GkO4d+gZ5HvuMFpDmM+xhoAE74xqe0pykU4P8hFXkHI0vPqT+2ilzqSSI1EZTrCNo6T3I3vTWR+218d5zAGsAbAhCmeGkqjNxnV03F7lGvpqlfxzmJe29sOcxhXVh4lfT5ur4wuHSXdUJzM3JkLHeYwxhoAE4KVl7lcBmziNItyK+pNYH7bs05zmGhoymwGeg7CCYDnMMmbCJfwhjfNLpMyrlgDYIKVaPkaeNOAPR0neRJ0Ep0dv3Wcw0RRU2Zf0GkIX3Gc5GlEzrTrpI0L1gCYYDSltsSTC1HG4PbP1euInEfPq+02z2/WQEhmvovq5cDWbqPoHfj+RObPfNxtDlNPrAEw5RnZPIT1B52K6mnAOg6TlOb5BzWew5xpbzrMYarNyOYhrN/YinImMNRhkgLKbKRnCp2zFjvMYeqENQBmoIRE+ljgImAzx1F+TUPxZG6cscBtjn6IZwcx+NVt8L3tQNbD9zfC82rx7+EbiC4D7zle59mqmOdOprdGuQz4nuMkixHO5uXhP+GebMFxFlPDavGDx4Qtkf4SynSE/RwneRKRieTbb3eco29jJ69Hz9LD8eQglIOAnXB5eY0bReCfwL0I9yDebyK9G6Op9at4/jSUvR0n+TvKRLpydzrOYWqUNQCm/0a3boH6l6IkcD3Pr5zLBj0zmDWrx2GOvjW1fhUpNoMcBaznOk7ELANuBX5CZ+73rsOsUjbr8fTiH6y4dtjtThb0FzRwCjd2POM2h6k11gCYNRvZPIShjZMRTkMZ4jBJAeEaYpzDDbnXHObo26jUNxA5OwKry6uEPIJyHl3tv3KdZJVGt26AX5wCMh63Z1m8Dzodr+HCSL89MVXFGgCzOkIyPQq4FGUrx1l+D0ykM/cPxzlWrTR/PBU4ynWUKnU7PicxP/e06yCrNCa1PUUuB/mO4ySvIjKFHYddRzbrO85iqpw1AGbVRqX3Rrja/TdZ/SfqTY7sN0SAZLoJ5RpgA9dRqpqwFGU8nblrXUfpU1P6UISpwB5OcwiPojqezo4/OM1hqpo1AOaj4hPXJbb8XGAibhervYXKBRSHXU13drnDHH2LxxtoHDF9xa1zJijCXHqGnxDx3/dmlPOAYQ6TKMpMGrzTbVrADIQ1AGalZOv++P48hO0dpvBBfkqscGakz0o/vHUwG2oe0aNdR6lRv6egR9PdscR1kD4lWzZGvbOBNNDoMMmLiH6ffMfdDjOYKmQNgClpSh+Fx41uF/nJvYg/gXzHX9xl6Id4vIHYiG5svj9cqg/wbuFQbpm11HWU1RqV3hGPC3B722UBNENnxzUOM5gq0+A6gImARHoCwk+BQY4SvIDqiXTlTuXvD/3XUYb+Ej538E8REq6D1DyRLRjUsCvfPaKbe+5R13H69I+HXuPxh7rZ7QsPIuyDm2kBD+QIdt8PHn/oXgfPN1XIGoB6l8z8EOjAxdsgYSkiFzH43QQ3/uRvFX/+QCQyrQg/dh2jjuzC6+8W+ftD97kOskb/ePAZhh1xDRssewn4ElT8bZogchB77L+Uxx/6U4WfbaqQTQHUs6aWIxDvF1R+sZ+CzEX5MV3tL1f42QOXTO2JygO43Q9ej4oIh5DPRb8J6JVoHoY2nofQTOW/aClCknyuq8LPNVXGGoB6lTxpB7T4GJU/pe7/EG8C+bYHK/zc8mSzHgsW/RnY13WUOvU0heF7RHZnQF9GZ/ZAdSrKoRV+8vuo9yW62h6r8HNNFfFcBzAOZLMefvE6Kjv4/weV0XTmvlJ1gz/AgoUnYoO/SzsSW3Sy6xBrbV7738nnDgM5CqWSR/kORvw5xLOu1vWYKmANQD1asOjkCh7wswzlUtZdtitd7Xkguou5+nJ462CQs13HMJzOcRM2ch1iQDrbf8EGPbsiMgF4q0JP/RwNi86p0LNMFbJFgPUm0TwMGv4flVjxr9wKxSPpmvEzHnusul7dfth++54AYqv+3RtMsfgWjz9UnaffPfKIz98f/DN77/8TlHUovVEK90uY8EU+t+88/v7wG6E+x1QlewNQbyR2GjA05Kc8jCcH0JUbSdfMF0J+VgXISa4TmF7SSjxe3V9cbsi9Rj43Hp/9Qe8P+WmN+HJGyM8wVcoWAdaTseNGUGh4jvDm/v8LcgY7Dbu+Zi4qGZXeG49HQqr+PrAIqN63I6vWSGkv/LqhVBfv6+Tb7giltguJ9PeAy4CtQ3pCDzHZibntz4dU31Qpl2e9m0orNpxAOIP/+yBTWXfpRcye/U4I9d3xaAq44jsoHaj+nF1GPFIzjdInCU2te+Lpt1FtBT4VWGX1E0DtNACduZuIT7yFhuUnI5xO8H9HGyloC3BqwHVNlbM3APUkkf4bQd9ipnIzXvEU8jOeC7RuVCTSjwF7BlJL9Da0cCydsxYHUq9axJs3JNb4E4I6Klf4N/lcWN+W3YqP25yYdwnIaIL8fC79mn2WalyEa0JjDUC9SKR3Ax4PsOLLKMfSlbszwJrRUrrsZTHBrJXpprAwQXd3MYBa1UhIZK4BPTGQajHZtqZfaY9u+SK+Nw/YNrCanhzAvPY/BlbPVD1bBFg//jewSspDqOxX04M/ALF9COLviPIMsXV/UMeDP4BSGJYBgrnoqYf9A6kTVfNmPEAj+wP3BFbT128HVsvUBGsA6oYG84Ep/JtBHF5VR/gOlBZ3DKjSecy94t2AalWv7uxyPM4KppjuEEydCLsh9xoFHQk8GUxBqe2myaw1awDqhgRxip2P+KO4IfdaALWiT7zty6/BUtZ5tzuANLVhh+G/obTzoTxC+b831aC7Ywme911KO0bKpHuTzdpnvvmA/WGoB2PHjQC2KLuOaDfzZjxQfqAqoXy6/CLyB+bMea/8OjUim/URvav8QuLiyl035rU9gTI7gErr88yrtf/mxPSbNQD1YHnjpoHUKXrXB1KnWmgAByb5/osBJKktKv8OoErYh1lFS4N/QyB1Cg3BfBaYmmANQD0Q3TiAKkpM6msFsWj597mLvB5AktoiBDCFpJW+xdKtlzZ5GFhWdh3PD+48BlP1rAGoB8H8pX+DeW1vB1CnmgSwTVbqeeX/qikBHH4k9bWF+Z5sAeWlsuuoWANgPmANQD1Qr/xvsrA0gBrGmIESyt9JIlJfb07MalkDUA9U6+vbkjG1qfxT/OyzwHyINQDGGGNMHbIGwBhjjKlD1gAYY4wxdcgaAGOMMaYOWQNgjDHG1CFrAIwxxpg6ZA2AMcYYU4esATDGGGPqkDUAxhhjTB2yBsAYY4ypQ9YAGGOMMXUo5jqAMXXp+OPXZ9m6WwGbIbopeOvg6zI8eQ94m6I+jz/8Bbqzy11H/Yim1JaItz/olsDWwGeABgBU3wZ5FeF5lOeRnr/SOWuxy7jGmL5ZA2BMJRx//PosG/Jt0K8BX2IZu9D7Bk4F0NLlw7rivhcP8Bb5JFLPgtyP6L343E1Xx4sVz55IHQAyBvQQkB36vJPmwzf0CkCjkkg/BfIH0DtY0nMrt8yyWyWNiQhrAIwJ1x4kUnN5T44GXdtrmb3SgMsOqByPoCRSf0CZi6c/Iz/jjTACAxCPNxDb5FjQk4A9S/9wrS+SE2AX0F2AExnauIRk+leofe4YEwX2F9GYUOkRIEFc5AogIF9F+CoqbTSlr6dYPI/umS8FUr1XouVz4P0EdP9A68JQlGTANY0xA2SLAI2pToMRmok1PENTpo3E+E3Krphs2ZhE+krwHgGCHvyNMRFjbwCMqW7rIJqBwg9oSrfT4F3EvLa316pCPDuIxsXHoXoBMDycmMaYqLE3AMbUhvUQTsP3nyWZGc9B2f4090IyEye26ElUr8EGf2PqijUAxtSWYahOY9NFj5PMxOlr5V6y9TCS6YdRvQnYtqIJjTGRYFMAxtSmnVC9iUT6z6h3Cl1t9wMwunVXfD+L+nHH+YwxjlkDYExt+wLi30sifTPIu/j+GOzNnzEGawCMqQcCHBPUXkRjTG2wbwLGGGNMHbIGwBhjjKlD1gAYY4wxdcgaAGOMMaYOWQNgjDHG1CFrAIwxxpg6ZNsAjXHrNZDfoTyD+P9FvSHg74xw4IqrgKPuVZDbUX0EeANPfdT7LOjnEY5AWdsrkI0xFWINgDFOyCP4nMWrw37PPdnCKv+TUan98LwTQZuA9Subb42eBDmLzvab6euAgXhqKDHvB6DnARtVNJ0xZo2sATCmst5HtZWu3LWs6WSe+R0PAQ9x/PEns2zdLHAS7v/Ovo1yFsWFObq7i6v9L7s7lgBtJMbfBD3dIF+tTERjTH/YGgBjKuc1VA+hq+MnrM2xfLNnv0Nn7mR8b19UHwgv3hr9DJVd6MpdvcbB/8M6p7/KksI3Ee4MMZsxZi1ZA2BMZfQgGqer408DrjC/7a90dXwZ0e8DrwUXbQ2U5xGOoDMXp6v95QHVuGXWUrSnCVgYbDhjzEBZA2BMJaieRr7j7iAqke+4AWK7IeQDqLc6PcDFvNuzO/ncb8qu1jlrMXBq2XWMMYGwBsCY8D3NBoX2QCt2Tn+VfG40+AcBTwZau+QPwF505s7glllLA6v6yvB5CP8OrJ4xZsCsATAmfBcxa1ZPKJU7Z9zL4Hf3RuRc4P0AKr6G8kM6cwfSmftHAPU+6p5sAZVc4HWNMWvNGgBjwrWcwY2/DPUJc+a8R749i8/ngLsGWEVR5kDPznTlZhPm3cEFuQ7wQ6tvjOkXawCMCdeDzJn2ZkWeND/3NJ25w0DGsnaL7Z5AOIiu3A9WzNOHq7ttEfB46M8xxqyWNQDGhEpfqPQD6Wy/kcGNOyFcDaxuu94yRM7lTW9v8rn7KhVwhWcr/DxjzMdYA2BMmEQ2JZHZueLPnTPtTfK58cBXgb99Mpfehvi7k2/PcltbEGsH1pKE/6bBGLNa1gAYEyblUNR3dwJeZ+7/eGX4PohMAN4BeQXR75Pv+Bb5Gc85y6W66uOPjTEV4/pYUWNM2Ep3DUynKXUzDd5bzOt423UkY4x71gAYUy+6Ol50HcEYEx02BWCMMcbUIWsAjDHGmDpkDYAxxhhTh6wBMMYYY+qQNQDGGGNMHbIGwBhjjKlD1gAYY4wxdcgaAGOMMaYOWQNgjDHG1CFrAIwxxpg6ZA2AMcYYU4esATDGGGPqkDUAxhhjTB2yBsAYY4ypQ9YAGGOMMXXIGgBj+qauA9Qssc8eY1yzv4TG9G1ZMGW8hmDq1JRhZf1oVT+gHMbULWsAjOmLsCSYQlreYFebtivrRwvvBpTDmLplDYAxfVEWBVJH2DmQOrWiKbUl8Pkyq7wTRBRj6pk1AMb0SZ8NqNC3iGcHBVSr+nlyGiBl1VBeCSaMMfXLGgBj+iINTwdUaWNii8YHVKu6JdMHovyo7DqiQf3eGFO3rAEwpi897z8EFAOqdjbJ1J4B1apOiczOKDcDsbJriTxZfiBj6ps1AMb0pXvWW6B/C6jaUFRuZWxmm4DqVZdEZmfgLuDTAVQr0tPzxwDqGFPXrAEwZrW82wMstjkFvbvumoAPBn/dNKCKj5SaM2NMOawBMGZ1PJkbcMWtKei9jGotbxtctRiV3hH0zgAHf4CfBVjLmLplDYAxqzOv7QngLwFX3RLPv7Pm3wQkMjvjyT3AZgFWLaIyL8B6xtQtawCMWRPRK0KounVNTwcE/9q/ROWXdLW/HGhNY+qUNQDGrMnLI+YDQZ0J8GG12QSENfiD4vnnB1zTmLplDYAxa3JPtoDKpJCq11YTEN7gD9BNviPo6Rhj6pY1AMb0R1f7r1C5OaTqW1PUe0i2bBtS/coIZ8Ffr3coFMNqwoypS9YAGNNfRRkHvBBKbWUr1LuLpnGfDaV+2MJZ8PchejrdM18Kp7Yx9ckaAGP6q7ttEaJHQWg30W2NNNxTdU1AuK/9Abrp7OgIqbYxdcsaAGPWRr7jL6gcCUFdFfwJ1dUEhD/4P8G6y34YUm1j6po1AMasra72e/D8rwNvhvSE6mgCwh/8X0T1m8yebVf/GhMCawCMGYh5Mx7A129Qr01A+IP/Inz9H7o6XgypvjF1zxoAYwZqfsdD+BwKvB7SE7bGa7g3crsDxrTsFOJqf4BFeHIo8zvsxj9jQmQNgDEDFW/eEJGdgedCe0bUdgckMjtTbLib0Fb7rxj857X/PaT6xpgVyr+X25h6kmgehgz6NqpHA4eCDq7AU3unAw6ia+YLFXjeqiUyO4PeDXwmtGcIy/D9UYxKNzI/92hozzHGWANgzBolmochsW/hSxz4H1QbHaTonQ44mPyM8N449GVMy04U9U7CHPyh9MYDmYLHFBLpfyH8EtVudhrxJ7JZP9RnG1NnrAEwZlWS6a1BvoOvceBLKB7iOJOyFeLdzajWQ5jfFsbdBKs2pmUnit5dhPfavy9bo5wEchILFi0mmb4N1W7WL/yWWbN6KpzFmJpjDYAxvZIt20LDSHyNo3wZVJwP+h+nbIXn382o1oMr0gS4G/w/bhjKWJCxvNP4Osn0r1Ht5s2G33Fb2/uOsxlTlawBMPUtkd4NkTiqcZRdQYncoP9JW1akCYjO4P9xn/qgGdjYX0pT+i487Wad9/6fnRlgTP9ZA2Dqz8pBPwHsiKrrRAMRbhMQ3cH/o5QhCEeiciTL1r2GpvQdeNpNT+GXdM96y3U8Y6LMGgBT++LxBmKbfA3Ro1GOAjar0kH/47bE8+9kbOZg5rY/H1jVUa2fp+j/lrAX/AVvnQ+agVjjcpKpO8H7Obr8l3TOWuw6nDFRYw2AqU2Htw5mY/9QfD0akW+DDqMmxvxP2JqC3h1YE5BIHQD+LcBG5UdzahAqh4MeDo3XkEjfh+jN9Pj/z24VNKbEGgBTWxKpA0B+AP5RKBsj0Z/QD0AwTUBTyxGI3IQyJMBsUdAAHIzKwcQariaRfgCVm6HwM6fnKhjjmDUAphYIiczRwI9B93EdxpHymoBkajQq16G4OOOgkgT4EqJfgobLSKQfAL2eJYW53DJrqetwxlSSHQVsqltT+gs0pf8M+rM6Hvx79TYB26zVj0pkWlG5AWp+8P+4UjOAzGRo44skM1mOP35916GMqRRrAEx1Orx1MMnMVQh/RNjPdZwIWbsmIJE+F/Rq7LPgU6iew7J1nyWR+QHVsBnUmDLV+196U43GZrZhI/0jqhMpze9WkyUocxD9ARDWNrU1NwHZrEcy3Q6cHVKGZYicAFxOmJclBW846GwSqd/RlIn2FkhjymQNgKkuyZZ9KOj/VdnrfgXuAY6joJvSlfsB+Y45iPcNwm4CVnWLYHNzIwsW34iSDunZy1BGkm//KZ25U+nMbYd6e4NeCDwV0jMDJoch+ihNmYNcJzEmLNYAmOqRbN0f9e4CNnEdpV+U51HNEpPt6MwdTGfuero7lnzw7/NtD4beBJRuEfzsB/9kZPMQlsR+CZoI6Zmlwb8rd+dH/mlX22N0dpxJZ24XYHdEzwb+GlKGoGyC6O0kUmH9WhnjlO0CMNUhkd4N9X8LbOA6yhosQfkZ4s+ha8Z9sIbTB/JtD5Js/Qbq/w7YMIQ8K68S9uQt1Lu1dM9BKFY9+H9cZ+4fwD+A8xmT2h5fjsHnGIR9id7c+yCQG0lkGuhsv9F1GGOCZG8ATPQlWzZG+QWwsesoffjkK/7OGfeypsG/V6XeBKh3Lzge/D/uxo5nyOcupSu3P8I2qE5C+SMQpat/PdDrGJ0+0nUQY4JkbwBM9PkN1yK6vesYq/AcqjfQ6N1Q9il8lXgTEJ6BDf4fl8/9C5gKTKUpsxnifwfkGOBruF/sGcNnHonMF+hsr5J1DMasnjUAJtqSqdGoHu06xoesfMUEecMEAAAgAElEQVTf2Y9X/Gsj/CYgDMEM/h/X1f4y0AF0kGgeBo3fAY4BDgEGBfqs/tsAtIt4dn+6s8sdZTAmMNYAmOg6/vj1WeZdHuQYO0AK3AvMoaA//8hCvqBVVxMQzuD/caWLfK4FriXZsjHISFSOAb4BrBPqsz/p8zQsPpPwtk8aUzHWAJjoem/IyaCbOkxQesWPf31Fz4yvjiagMoP/x+VnvAHcANzA8cevz3vrHoFyDHA4sF5FMoieQtO42XaPgKl21gCYaIqnhqLa6uDJ4b3iXxvRbgLcDP4fN3v2O0AX0EV84ro0vn84KkcDRxLur9k6eA3nAceG+AxjQme7AEw0NcpY4FMVetrKVfyxdT+z1qv4wxL+7oCBiMbg/3HdU5eR77iZztwY3vQ2Qf0jgeuA10N5ntJEMh3mwkpjQmdvAEw0+XpsBa7ydfOKf21E601ANAf/j7ut7X3g18CvOSgbY9NFBwPfA+IE92vYiPIj4IyA6hlTcfYGwERPU2pLRL4YUvUVZ/HzNTpz29PVcW5kB/9e0XgTUB2D/8fdky3Qmfs9nbkTaWQ7YHaA1ceSzdpnqKla9ofXRI/I/4RQ9U+IjvngFX8+525+fyDcNgHVOfh/3A251+jM/RCVUwKquAVPLbKbKE3VsgbARJB8NcBi7wMn0pk7gHzHPOZe8W6AtSvLTRNQG4P/h3W1X4EyI5BaQhjNqjEVYQ2AiaK9A6rTg8i36cxdSzV921+dyjYBtTf49yrqqcB/A6gU1tHKxoTOGgATLdmsB7pDMMXkTPLttwdTK0LybQ+Cjg39OaKpmhz8Abo7liDaEUCloJpVYyrOGgATLc+8vAkwuOw6yjMUXr2y/EARlBi/CcgloT9HJcvYzDahP8cV1V8GUGU4x03YKIA6xlScNQAmWnTQsGAKyTV0dxeDqRUhifGbQOEuYNcKPG1rCnp3zTYB6xefJIipofff36r8MMZUnjUAJlpU1g2ozu8DqRMllR38e9VuEzBrVg8QxKLQqF5TbcxqWQNgokW0MZA66zb8K5A6UeFm8O9Vu01AEG8A/IYhAeQwpuKsATC16d2NlrqOEJix40ZA8Q7cDP69tqag9zKqdTuHGaJJCKZpNabCrAEwJsrGjhtBIXYn6O6uowBb4vl3k2zZ1nUQY0z5rAEwJqqiNfj32hI8awKMqQHWABgTRdEc/EuUrawJMKb62W2AxkRNlAf/XspWiHc3yZaDyc94LvD6icz1oGU2GDKNzvafBxPImNpjDYAxUVINg3+vUJsA3QfYrcwiNwWRxJhaZVMAxkRFNQ3+vWw6wJiqZQ2AMVEQbx1OoeHuEAf/ZYR1gZCyFerdRXy8nYhnTBWxBsAY1447bh1i/i8Ib5//MsT7X3wOAV4P6RlbEyv8kvjEYE5yNMaEzhoAY9wSlq/3U8K7VrY0+Ofb7mB+7lF8vk54TcCeNC6/JqTaxpiAWQNgjEvJzDkoyZCqrxz8e83PPYp4hwNvhvJEZSxNqRNDqW2MCZQ1AMa4Mmrc7qieEVL1Tw7+vfJtD+JzKGG9CRC5kvi4zUOpbYwJjDUAxriQzXp4DddAKOfI9z349wp3OmB9YrHLQ6hrjAmQNQDGuPDUwh8Szrz/mgf/XqFOB2iCpvShwdc1xgTFGgBjKi0eb0DktBAq93/w7xXudMDFIdQ0xgTEGgBjKq1h+FFA0Nfqrv3g3yus6QBhP5pSYe1uMMaUyRoA0z/Cp0hm4oC4jlL1RCYGXHHgg3+vsKYDgv+5mvJ8i9GtW7gOYaLBGoBal0ztiTCu7DrKEFRvIpG+j1Gp/QJIVp9GtW5HsHP/5Q/+vfJtDyLe/xBsE3AUY5o3DbBe/WlubqQpfRKwQwDVvo7vP0UydZYd2mSsAahVieZhJNPTUXmYYAecA/DkzyTSN5FMbx1g3fog/hEBVgtu8O8V/JqABoqNQf6c60uy9TDeGfQownRgvYCqrofKecSW/5Nk6ljsrV7dsgag1hx33Dok0j+GxudQTgIaQniKAHHgCZKZLGMnB/XBVPuEbwVUaXngg3+vldMBwdwdoBwZSJ16MjqzB4n071D/9yHeD7E5KteTSN3LqPTeIT3DRJg1ALVDSKS/x/vrPQlcBKwf+hNL0wLnUFi2gGTqWLJZ+/O0Ooe3DgYOCqSWMDOUwb9Xvu1BlGOAQtm1hMM47rh1yg9VB8aOG0EiPRNfHwO+XpmHylfxeIim1LUkxm9SmWeaKIi5DmACMCq1HyJTga84SlD6JrFgUStNrZPoarvfUY5o28jfHhgcQKV36PEuCKDO6nXl7iSRuhRkSpmV1mPZersCj/b7R6i0IzqirKf6/gNl/fhKOrx1MBvpeAo6BdjAQQIPkR9CIU4ifQGF4dPpzi53kMNUkDUA1awpsxnoOQgnEI23Ofsi/n00pW9FvQnMb3vWdaBokZ1BAyjDL+huW1R+oX6IDbmYwrJxwKfLquPpTqxNA9DVPrOs51WTRGok+FMJfmvoQGwAXEbDomaSmTPIt3e7DmTCE4VBw6ytkc1DSKZPQ/QphGai9vsoHInnP0kyPZ1484au40SG6E4BFbolmDr9MPeKd0FuLruOeAH93GvIqPTeJNL3gvyKaAz+Kwnbr9j1cyeJls+5jmPCEa2Bw6yJkMiMYWjjApRLqMQ8/8A1opxErPFpEqkfEY+HsRixuvgEs//a54+B1Ok3Lf956kdrgHMpPm5zEpnr8XgIONB1nDU4BLxHSKbbOTZd3lsgEznWAFSLptSXSaQfAJ0LAQ0klTECZCaxEY+RSFdoUVNESSAX/xQpvvpqAHX6TwhgukFsEeDI5iE0pc4h1rAA9Fiq5/M3hpKmh6dJZFo5KGtTxzWiWv4A1q9kemuSqS5E/gDs7zpOGfYAfkcicyuJzM6uwzihgay5eZ3u7mIAdfpPGRpAkXoeNISm9FiGNi5AJEtw+/kr7VOgV7Ppor8yKvUN12FM+er5L2W0xVNDaZAfo0yqrW9PegTwDRLpmTRyLjfkXnOdqGI8GgJYA+jg9DbZq+zFiyr1+VkzOvMVijoVoZZOz9wVT24vLfblZObnnnYdyAyMvQGImmzWI5n5ITHvaYQzgBoa/D/QCLTSwz9JpCfQ3BzEq/Ho00CO2B1KPPWZAOr0l4AeHUCVJQFkqR5N4z5LIj0fX++vscF/JeFIPP5OIn2FLfatTtYARElT5iAWLHwY1WtB6+H89I2BqbzT+DhNmf91HaYCXgikSqP31UDq9EdT5mvAjgFUej6AGtE3unUDEpmLkYYnge9R+8fsDgJOJtb4NE2pE+0wsOpiv1lRMKp1OxLpmxG9u/S6te7siOgvSabvQLV2txyJvhBIHV+PDaROv+j3gymjtd0AxOMNNKVOxPefBj2d2nxztzojEJnFgkWPkGj5muswpn+sAXAp3rwhydTleP4TwFGO07zr+PmgHIrS4TpGiJ4LpIrwzYoc2Tp28noIxwRSS7zaPRRK9EBiIx5FZBbg7ihdYamzZ6+0J3j3kEjfRNO4z7oOY1bPGgAXslmPZOpYYo0LUJlM6TWaK0+h/pEU9DOInAu85zBLbetZ9HeCuWUvhvYkA6izesWlxxDMWRPLKfgPBlAnqk4GXL658hHmorFtEe8LwJ8cZukVRxoWkExPZ3Sri6ONTT9YA1BpTelDWbDoMVSux+W3BXgdkQm8MnwPumb8mu6OJeTbsxRiOyHMdZirdpW27wVzgY94xwdSZ3VUgnn9j95Hd0d9LQKsFOFO8PcinzuWzumvkm97kM7cAYh8D/iX43SDUE7C958kmWm29QHRY78hlTIqvSOJ9E0Id+D220IPwtUMbtyOfPt07sl+9La37un/Jp87FpWDQR9zlLF2KbcHVGh3RrV+PphaqzC6dQsgmLlc9QL6OZsPeRqR75HPHUbnjL997N8p+fZulvTsinA6ON+BsRmq17Bg0Z8ZnXF1YZlZBWsAwhaf+CmS6el4PA7EHaf5fzToruRz45kzbfVb0rra72GnEfsicgLw38rEqwMS+zVBXLEL4PnHBVJnVXz9PhDM8c2qvwqkjgF4HeQk1u/ZfY0X9dwyayn53KWo7grMI5CbqMqyL77eTzJ9A/FxmzvOYqj9LSruNDc3smTQD1C9ABjuOM0ToCfT2fHbAf3osZPXo/jeKaieipODaAagMHxwZK8zTaR/BYwMoNJC1u/ZglmzegKo9VGJ9FNA+Rf4KH+kK3dA+YFCkki/TbTv1OjVg3IdRe/MAd8E2ZTZF9GpgPvfD2EpyOX0NF5K99RlruPUK3sDEIZk62G8M+hRVK/B7eC/GJEJFBZ+bsCDP5RuhMu3Z/G8HVesD3D9TaK6KdcFVGkE78S+GVCtlZpSXyaIwR/Ak6B+rnVM78Av7k1X7kdlXQPd1f4wnbmvgv4v6vhcBmUIqucQW/40ydSx2JdRJ6wBCFIiszOJ9K9R//eguztMshzhajyvNM8f1Nnx89r+Qz53LOJ9kWisNK5O/x1+C4FNqwS1UC+Umu8iYvfJD9xTCEfQ2fF15s98PLCqnR23UBy+MyITgLcDqzswW6ByPYn0/zG65YuOs9Qd67qCcGz60xQ4GyWF6/sVlFvx/PHkZwSz57xvQjLzXVQvAz4b8rPWXpSnAACSmatQnRhApeXQszmdsxYHUAuOO24d3l/vFWCjAKpdT2fuuADqhCeaUwCvIXI+Lw/LfWKRbtDGNG9KoTGL8EOCWvMxcD7CPDR2Cp3TK3vjZZ2yNwDlaG5uJJkZTw/PopyE08FfHkG9A+nKjazA4A8rVxrvtmKl8TsVeGbtKBZmB1RpENo4KqBasHy97xDM4A/49vp/7ZR26BR6Vr1DJww3znqFrtyP8Nkf5N7Qn7d6HspYKDxLMpPl8NbBjvPUPHsDMFDJ1NGoXAZs5zjJf1DOoCt3Iy7n5ke3boH6F6GMIQp/rkR+xI7DriWb9V1H6VNT+sFALopRHqIrF8xV0cnUb1A5PIBKz9KZ24HorhcREqkmkBtw/dYOQOVmVE5lfpvbExOj87kG6D+Bk+nsuMV1klrl/oO62jS17lVaSatuz7sWluLTxpBlFzJ7dnS+fSdb9kFlKkjlLqzpkz4GOpHOGa6/2axaIpUCyQVSyy/uUfY8cVNmM0T/TRCvgpWz6MpdUHadMNif0dUr7WBKoXouEIVb/u7CkwnMa/+76yC1xhqA/ko0D0Maz0JJ43auTIGfIZxCPuf6pK++JVIjQaYB27qOUsF1EWsn3rwhscZXCGJrpXIpXbnTy6qRyJwKemnZWcCnENuG7un/DqBWcCI13y2vIGTpefWngS3SDVqU1jZBAWV2WdsgzSdYA7Am8ewgGhe3oHoe4PpM6wdRnUhXR3WswI/Wr91yhJmIdxbz2lyvfF4pkcmDJgKo9DKFhVuVNZgk0n8D9gggy+105oLfnjhQ9uewPInMzqBXAt9yHQV4A+FSeoZPjfQi3yphDcDqJFIjUZmOsI3jJP9BdAr5jurcgx+dtydE7ptXIv114HeB1BL5Jvn2gR27Wzok5qFAcqg00dU+P5Ba5Yram6hGOYm57dV5NXKy9TDUnwbs5joKsABhEvncb1wHqWbWAKxKU2ZfxL8qAnOE7yJyRc2cljW6dVd8/0rA/bdD4VF8bwJdbfc7zZHNeixY9BywdfnFpJPO9oHdEtiUaUM0U34G3qQwaDPnf16bWvdC/GnAgU5zlDyMepOc/1kLQrROOAX0DryG8cxre8J1kmpkDcCHxcdtTkPD2Qgn4HaLZGk/bI+eSndH7Z3DX/omMR3Y1XUUlFuh2ErXzBecZWjKnIfoWQFUeo/BjZuu8Z6Hj4tnBxFb9BIwrOwEQo58LohGYmBWzltH4G0TLyFyXuR3owxEsmVjfO80hIm4vc4cStsnZ9DTczbds95ynKWqWAMAMLJ5COs3tqJMwf2hIPes+LZQ2zfxrVxpnCWwfecDtgzlamc7KsZmtqGgzxBE06naTFfHT9bqx5S2fv287GcDiL8v+RmPBFJrbURp5XrvDp2iXlDz1yCPSu+IxwW4v+gMeg9Q6nm1PRLTe1Wg3huA3tPsLieQV7BlJfk36FnkO25wmqPS4hM/RePycyKy0vhlRM518o0tkb4LOLjsOgO5fCeR/iXwv2U/G3mczvYgFhGundI8/1Tc712vjh06YWhKH4pwFW6vOl9BH0NkAvncfa6TRF39NgDJ1v1RfyrwZcdJliByJW/IxdzW9r7jLO6MadmJYsOVoEe4jgI8vGLf8R8r9sSm9FiEYJq/Bn9nbpyxoF//bbx1ODH/JaAxgCdPpDM3LYA6/ZPI7Iz4VwV0cFG5qmuHThiyWY+nF45ZcZDQJq7jVP2iywqovwagKbUlnlwYgRPr7NzrVUm2HobqVMeXKUGlv83FJ65LbPnLBDEdonI+Xe1n9+u/TaQnAFPLfiYsp+BtUZE92tF6a/QiomdW7Q6dMMRTQ2n0JqN6GrCO4zRup/cirn4agJV32rv/QyncifqT6JzxN6c5ouqgbIzNFh+P6vnACKdZKjmfm8jMAj0xgEr/Yqfh2/ZrGiORfgzYs+wnqtxMV/sxZddZnZUr0C8kiAWL5Snt0Bm05BLmzHnPcZZoio/fisbCBaXz/Z1zN70XYbXfAKx8LXUp8BnHaZ5G5Ezy7XZFan+sXGk8AXB9MUj4ZzEk0l8iqGuWRQ8h33H3av+bUeN2x2sI5nhVj5HMy90aSK1Vic4e9NreoROGpsxBK7ZV7+U6CvAwquPreqrmQ2q7AUimDkblKoL4hlMeO72qHMmTdkCLFxKNlcYPAhPozP1fKNUT6SeAXQKotOareBPpK4FJATzrVV4ZvkUot9dFa23I3YhOIt/xF9dBqs4HX8S8S0A3dZymNL1XiE2O3HHVFVabDcCY1PYU5SLcDxg9KNfZ+dUBSWQOAb0K+LzjJOF9gAR3Hv+7rLts0z7nPQ/Kxth00YsE8VYsiHsIPi5K+8yVZ/DkDHtzF4CVU7GnEsQdGOUQloJcXs/TOLXVABw3YSPe6zk9Gq+M9Q6QCXTm/uE2R42J1krj4OeBE+M3gcKLBLMq/zg6c9ev8t+MTh+JTzDXrPq6K/M7ngyk1sr1H1E4ae5NhEt4w5tW1zt0whCt68Or+6j1Mrj+hQ/GykHhclwvGoOnEE62M6pDFqVFnUGvBE+kfwWMLLsO3E1n7pA+ntENfDeAZ/yJztxXAqjTu5d8KsFcSFSO0s1zjcWzmDtzoeMstS0627EBuRfxJ9TTFE/1NwClxUFX4v4AitIpVC8Py4UyF2pWLTrbOgnsA6QpfRTCzQEEUmKy3Sf2QSdbNka9VwjmLdmJdOauLatCpNZ46B143iS7e76ionMgW51tz67eBmBMy04UvfNx/6Fh51BHQVP6Cyu+PX7JcZLyP0CCnJ8XPZt8x/kf+WeJVAokV3ZtWMbgxs3W+u6BXpGasmMBImfZPL9D0TqSvS4OaKu+BiA+8VM0LD81IouDbkW9Ccxve9ZpDtOr9E0CvQJlK8dZSusDBvoBksxcherEslMoz9CV25EPT00k0n8G9i+7NnIDne3fX+sfFq0pu9cRLrMdOhHSlNkM9JwIXMoG6D8Rb0qtNobV0wBE6RpK4dEVB/nc6zSHWbWV3yTOBIY6zTLQFeSB7tGXAz441rh0eUv/jgleE5WD6Wq/Z61+TGlr7jTcT9mVduhIzxQ6Zy12nMWsSrJlH1SmRuBa9tLhbcXiBObPfNx1lCBVRwMQmUNA5BWELD2v/tRum6oC0VppfDf4E9bq9MdE+iFg3/IfLT+hs725VDNzMWj5W/aU5+nKbUd/Fz1GZ2sutkOnypQue5oGbOs4SWlxaA1t63b9obh6o1K74MkVwLccJymdJ93gXcS8trcdZzFra1RqP0SmIgSzWn3gSusDGoqT+7W6PLi5+rdZ0rMp+2z2HgsWvQBsWXbFVa0tWJVo7dZ4EmGy7dCpQvHsIBoXt6B6HrCB4zSvI3JeLSz4jmYDcGz60xQ4GyUNNDjNYjdK1YrelcaXAZ91nKV/+8vjzRsSa3yFQA5M0SQ+r+HJ7eXXwkfYdrWXJEXrCG67J75WJJqHIY1nRWJsgKfwZRLz229znGPAotUAHN46mI10POgU3Hd5f6Z0vWk4R75WSjw7iNjibRHZAt/fFNERqAwHWQ/RdYANP/Yj3gF9B+Qt4C2Qhaj8C6/wEm/EXqr6FbEjm4ewfuxkVE4D1nOcZgHCpNV+I01k8qCJAJ51O7AYGB1Ard/TmftGn/820fI1kKkROPv9fdDpeA0X2pu7GpNM7VlaH8BBrqOA/gK/YXI1LgaPTgPQlPouIpfifp7nRUR/TL4jT7WdCjUqvSMie4HuhbAnyo4IWxFcp1xEeR7hCZQnQR6Dwp/pmvlCQPUrpymzGaIXAWNxvtKY3+LrpFWeplda//L7AJ5RBJYT1NuEzo7OT/zjsZltKOhlBHPAULl+RkxOtTd3Na4pfRRwGcL2jpO8jzKt2q4ddt8AJFv2Qb2rgAMdJ3kX0Ut5p3Alt8xa6jhL/5QOUDmC0q/dAbjbHbEQeADlToQ7q2pxVXRWGq84T2LQuXRPff2Df5rNeixY9BzuD0jp9QaD393sI0cfx1NDickZwEScz/PLI6hMpKvtfrc5TMXEs4OILWwFORPYyHGa/6JMYefhc6rh2mF3DUBpr+eFCMfi9huYD8ylUJxC98yXHOboD6Epsw/oUQjfxvmuiD69DNyCrzezYeFuZs3qcR1ojZpS3wW5DGEbx0leQzTLyyNmfrDAKJE+FzjbbawVlBl05VJA7zz/sah3UQRueHsZ0SnsOOKGavjgNSGItw6nwT8X4UQg5jaMPFLa9dPxB7c5Vq/yDUB84ro0LD8Z4TRc79FG70e9SXS1P+w2xxokW/bBbzgO0W8TxAruynoTuBnhevK5+4nytMrhrYPZ2J+Acgbu16A8ga8Tmd/xuxWv1p8lCm/sfN2f+R0PMTrzFXydRiDbFMuyDJUraFznUuZe8a7jLCYKEundELkK1b7XqVSGAvMpxE6L6rXDlfxAERKpJkQucX5Km/I8npwW6dOdDm8dzIb+9xDSwBdcxwmE8jwwG4n9JNLnbCfGbwLF80GPx/VKY+VWlJPxmAkc7DQL8jiFhiOIFS4FRuG2IVGETnw9na6OFx3mMFGVTH8L5UpgZ6c5eq8dfmf5ZVGbXq7MX+DRLV+kKFMR+WJFnte3txEuYtC70yN7/3MyvTUq40B/iOsTD8PzPsJNFJnG/NyjrsP0KdHyOcS7CuVQx0l6gKdxP+WzANgK1/e4qz5Ag05k3owHnOYw0dfc3Mg7g8aBngN82nGaF0FPo7Oji4i8CQ23AYiP25yGhrMjcKZz6QCWHj2V7o7/OszRt9GtW1D0z0I4HufzVxWk/BE4h67cna6j9CkyJ1HWvZcQOY8dh11r8/xmrSRbNsb3TovExVPKQ6AT6Or4k9MchNUAxFNDafBORXQyrr8twF3gT1yrI1graey4ERQafgy04PoPplt34evpzO94yHWQVWpubuTtWAaRs4CNXcepM0tQLqU46Eq6py5zHWaVslmPfy7eDd/fHWQHlG0Q1gM2AnW/dqPqyEN05s4IvOyYlp0oeFcgHBl47bXjo9xArOcMbpz1iqsQwf7B/GBVsFwIbBZo7bWm/0TkFPK5X7rN0Yd484Y0DDoZ0Yk4XwwZGYroTaBnkJ/xnOswq1Q6pfJclB9RT29q3Ch9SCJT6Gp/2XWYT4inhhLzvgP+MSAHAp9yHalmiN5GviO8I+AT6a8DVwJ7hPaM/nkH9CLebJjq4pC14BqAZPpAVK4C3SewmgPzBqrnUxyRi+z1nk3poxDJRWDrVFS9B1xGYdAlkf3GNyq1Cw1cicrhrqPUqPsQfxL5GY+4DvIJpRsVTwESuD9NsjaF3QAAxOMNxIafAHIe7q+lfg7RU8h33FzJh5bfACRbtkW9S3F/+lcB4RpinMMNudccZ1m1eOozxKQN979W1eI5fMlE+qztROqbIFcCu7qOUiOeAzmVzvafuw7yCaNbt8DXy0BH4f70yNpWiQagV+nOjSnASbifhq3olPXAG4DRrRvgF6eAjMf9L9pv8byTmdf2hOMcfRGSqe+X3pDY/PEAXEehZyLds95yHWSVDsrG2HRhM8i5wDDXcarU2yAX8qZMj9x9E9msx1OLJyGaxb7xV0YlG4Beo1q3w/MvBY6p6HM/qYjoT9DCWXTOWhzmgwbWACTS3wcuw/1rkydAT6az47eOc/Qt0TwMGucC33Qdpcq9iPKDSO8WOG7CRrzfcxaQAQa5jlMliig/pbF4Vr+uSK60eOozxJgLcpjrKHXFRQPQK9HytRXbf/d28vyV3kQ5k52Hzwhr18vaNQBN4z6LF7smAicsvYZyDv8dfk2k72NOtu4Pfrfzg49qh4/KhRRfPTfS17omT9oBv3j5iuOaTV+EO1F/UmR36CTSuwG/BbZwHaXuuGwAYMUdHIu/D1wYgbVaf6DBP4EbZywIunD/G4BE5hjQObhdsb4ckRyDYucxZ9qbDnOsWVNmHKLTcD89UoPkXgp+U2TPdOiVyBwCehXweddRIuZpVE6hq/1XroP0aXTLF/G9X2Mr+91w3QD0iqeGEuN0kEm43dL+HqIt5DvmBFm0fw1AMjMe1atwuvBF7wCZEPmb5o47bh3eG9KOyA9dR6lxLyPeUeTbHnQdZLVKW2PHoHIp8BnXcRx7A+FS3vCmRW6e/8NK3/zvwwZ/d6LSAPQa3boF6l+EMgaXR2ALV7Pj8IlBTQms+SeSSF8BnBzEwwboryCT6Gy/y2GG/om3Difm/wb3F6TUi2WonEBXe951kDUa3boBvv4YdALOr8ytuAIqMxmk2cju0OnVlNkM0QeBzV1HqWtRawB6JdJfAqbi9n6W2XTmTiCA44RX3wAk0qdQWuznwquonkVx0exIz+F2EtkAACAASURBVPf2io/filjhdlxfPNG3N4CnUF7D443S/5cV/6vLgQ1AB+HLenisi6/rId5nQLei9GEY1W9DCnoGnR2XuA7SL6Wb/S4F4q6jVITobRQ5mfkdT7qOskbxeAOxEb/H+aVLJrINQImQTCVRuRh3t7NeHMRJiX03AIlUAmTeav+bcLwHMg1PLmZe29sVfvbAJDI7I3p7hBb7PY3q/cA/EHmcQvEJume+VFbF+MR1GVTYBi3uhbIfKvvisRfKkGAilyngV2OhS6QOAJlK7b4tegKRSeTbb3cdpN8S6R8DF7mOYYh6A1AysnkIQxsnA6fiYnuokCKfm1FeiVVJnrQDFP/i4MO9m5icxtz25yv83IFLtuyDerfh9ua+N4E7EfkdfuF3dM18oSJPPSgbY5OFe+HJkaAjQfaqyHP7IsylZ+EPquKNEaxYabxoLHAhtfPKeTHoObwyYlakd+h8XNO4zyINT+D+7hID1dEA9IqP25xYw4XAWCq7Tq4H9b5AV9tjAy3wyQYgm/V4atF9CF8pK9raeRj1JtHVdn8Fn1m+0krh24ENHDz9feBX+HId/qu/i8SgFx+/FQ2FkaBjHF79PI/Cwu9H4tejv8ZOXo/CslMRJkfmjcraWw60Mbjxgsjv0FmVpszPET3adQzz/9u78zi3yqqB479zk5nuyNKWRUQFpKyylE3WisheVuMk6fJW1JEmGdqyI6BRQdnbMpNpGQV5gWZmGIooO7K9KggUkF1ANkWB0rJJ9yT3vH9kSvd2ZvLc3Jvk+X4+/bwfXzrnnrYzec59lvN0q6QCYLloam/EvRrk4DI+9QXyw/bua9v7NQuAWHIyxU0O5fAf4AJGDLupYqZulxvTtDOu+yfKf8f084hcjy6b5XWXqJLEE3ugchowhnIfHRWy7DBsXMV9T0UTX0LkV0AcP3ca95rejoTPIXvNP/zOpE/GpHbD1eeoqL/zKleJBcBy8VQEVy9D+GpZnidcTDZzUd++dGVjmjbCdd/C6w1fwiKUKwkPuJybrlzo6bO8UPygfoxyNghRfRzlp3S23l+2Z5owpmkjCu4ERM4ra0MNZQYdmUTZnmdSNLkfwlTgG36nsgHPITqFbOvDfidSkljiJpCxfqdhraSSCwAoHgdfOnAyyPl4PUMsLCKn2/WlL0polf+1yz5nIxxrLLE1KXArWjiJjhm38/xjOQ+f5Y3xyc1w5SFguzI98TlETqc9cwYvzXmjTM8054Unl/LinCf40n4tDOAdYF/KMSMg7MNu+zq8OOcRz59l2otz/sOLc65jt32eQWV/JHD3R3yIyI/Jf/ADOv83mNc291R84ibgXI+92jlYhNd5Yc4sv9Pos2efzfPinL+w176/RulPcbOvV/sD6nDE4cU5vX45XDEDcOqpQ1g84G28e/v/K8oUOjJPeBTfe8W12gcpzxnQt1GZFOhuaX1x6qlDWNT/DETOpTwbrn5Ie+Y3ZXiONyJTBhDOnQF6Hv524YTlJ3QGLPol11//mc+5mBFPNaJ6rQeRFXgB+BswH6VyNkSaIJwE7ND3r6/wGYDVNTTtjuNeDRzmSXxhEaHCV3t7n8aKAiCaHIdwoweJ/Qs4l2ymEwONC3wkxJK34P1VvnlEpxEamK7I5ZGeGpvYHldmonzL4yflUDmCjpZHPH6Ot8Y2bkmh7mJgAuXvyKlAF1o4t2wnTMollrwf+LbBiDlUrqUQuoKu6f8yGLeylLqpstoKgOXiyRNQvQLka8ZjK5PoyFzTmy9Z8UHiaMxwOgtALiRXvyPZTAeVPfhDLDkJ7wf/p1BnX7KtZ1f14A9wc+vrZDPfRvR7gJfd4eoQvZWGpnIt2Xjj5rb3aM98H5d9QP6vbM9V5oAeQnumoeoG/8bGOuAAgxE/AD2Mjpammh78rXXLZn5PfviuFLvrGj4t0/sxvFgAxBqHokavu3wWYVfaWy6ha+pig3H9UWz/6GVHREW4mPwH+5dyprMCafFyi/AugJetnjfDcW9lwoTKb8HbmXmG9pZRiB4GdFA8DmpaHvR2XDmGHYftT3vrXzx4hv8+q9sbcw1cluDqcVX7d2WZ05VeRnvmavK6E/CYsbgi+zEu1auTB90zAPWHAnWG0riX8ICDyGb+aSiev2KNQ4FOzP39rO4THI4nm7moos6um9Q+fS75D44AvQTvZor2YMmg6R7FLr9s68O0Z2LknS+hcjbFI7WlmodyESpfpr31JDpb7qm4o5S9IbKbwWAX09k6x1w8q+p1tb7PJ85hQJehiEJee7WkunwJwFQ70jfpVxermunrdNqBupvxrt/zc7jO3szK3OlR/MrR1VWgvfVChOOAjzx5htBIPBn1JLZfuprn0dFyJYqJzaIP05G5mI6Wdw3EqgCuqXXYTxmwqFdrr5YFwD3NS1mQm0Bxw6gJvRrLuwsA10wBoDquIruArctr85uAIz2K/jsW5A6gs7nyjvZ5KZu5Gwntj+JNO2illTFN5evfYAWXiqlGLX+smlMRVvnd0bYIiAEGZoClLwWAkamwh+hoNbee4bd48suoXuxNcLmR94Z9t/sf3lpd9pp/gByEuap4ZZugeh2265tlrkHLXw3FsWpVe+YlVH5feiDdlV58tjndv9nA2X+dXXqMINEZeHHuWqWF9pYJFXVRih86Wt5F3ENRHjUeW/UIYolG43GtSmPm51t0rpE4Vm0T91YDUfpx6qk9/r4Oc+qpg1lsYIObo8+UHCMooqk4qkebD6yX0JG50HzcHohEQtQN2w1kR1wdgSM7oDocZAjIINABIJ8AC4AFiL4N+goqrxIuPNfbBhNGZGd8zOjGIxhcfzfooWaDy6+ITbqd9un2w7tWCf2MbDl1HTv9b5UupM9QMDAxuWTApkCPvifDfDZkE8IGXkaV8g8QXhif3Iycmr8MSbiGbGt5B//oaV/BCZ+A6mHAoShfKOYi3Xvtl3+z6Wr/l1X/ez6kxFIvIfoQrns/GxXup62tPG2c72hbRKTxBMJ1jwB7GIy8CeSvAmwPeMuy/BfuN5eCiY9V3QTo0Sk8h365egNPBFcqr6//2uTkV8Bws0H1dnYYNsVszHWIJAYTTZ5KLPUIEnoT1WnA8dA9+PeNgO6Kcjri3Mlnde8SS15DfOJIQ1mvX1fbp+T1aMD0hskxxCYanlmwLMvqgw8X9elK3zW40q+nv7XcLUWDrSG5A+j3DEd9ggX5MZ6fp45P3IRY8ieE5W2E67qnzL3a6DYUaEKdp4glHySW8qa/9cq6Wt9H3CMAw1P2oauKxz0ty7Jqi/3gW1lIf47ZW8HeJFw43tPd/kc39SOWugB13gZ+Bmzm2bPW7jDQB4km/+L5jEB2xpuoRMHkxSo6ktc+MN0G27IsK/BsAbBcPLEHKhGDEZch7nc93TwXbzqcjd3nQS/G6zunN0Q4EHWeIJpqZsLkjT17TkfLI6AXGY2pcgmRtJmlMMuyrAphC4Dl1LkYo38fchHZGU+bi7eSCRP6E0tlUPePlHLlpnkhRFMszb1ItOlgz57S3noZisnuiV+mbv4Eg/Esy7ICzxYAAGNSB4Ieayye8CAjhl5pLN7K4qd/jaUDHwNNeBLfjC8i7kPEkufjzT4ExXHHA+8Yi+jqed23w1mWZdUEWwAAFPQ8g9E+JFf4H082/cUSB6GFJ0H2NB7bvDDwS2LJDk+m17MzPgZNGosnfJUF4THG4llWOY1Kh4kldyFy2hf9TsWqHCY3vFWmcamvktdjjMVTptA108TNbKuKJUaDdAIDjMf21ncJf7App556svF+6e2tdxBLdgFm9m6oTAZuMBLLsrwyKh1my7k7Q2gkoiNxdSQyb3dgAOEQxJLvgzwN+jToU6jzdO1c8GT1hi0A8joRUzMhqo/T0XqzkVgriya+A9JOxf57yeEsHvBHIonD6WpdYDR0KDeJQt23ARMbD3cnmhpV3GhoWQEwKh3mi/N3Qt2RuM5IcPcuDvbOANBiwy5ZY5Vti+4lzWNBQBRiqfdQfRpHnkbdp21RYEHFDiiGRKYMgGXfNxTNxQlNwvR99rHUYaA3U/n/VvtR58wmkh5NV9pMwwuAm9veI5o6H9EZRuKJng48YiSWZfXG6oO96EiYtwcuAz4fyPu8pUa3RDgO1eNsUWAtV+mDSmlCy2IYuQgJgJvINj9pKFZRtGlPcG8HetzZKdBUj6Bu3m8ptt81Vyi9P/Q3bDHvTITtDUQbTSSxBV2t7xuIZVlr16PB3mu2KKh1tV0AOCQNDUMLCeXONxJpuQmTN2ZJbjYwxGhcvylxYsk5tGemGYv5SDpPPJFGxcTyS5gwY4CrDMSyrLUP9jJvd1wGlm+w7ylbFNSS2i0AYsldUPYyFO3X3Nz2nqFYRUvy1yF81WjM4LicWPIJ2jPm7lHfYXg7r847B/h66cHke9gCwOqLSCREePiOxTd6GYnLSJx5e64x2AdpzN+gtRUFyY9RXsahuNlQ5WnaMy/5nanVO7VbAIiejBo5op5HMPc2CxBL/Aj0ZKMxg6UOZRbjztqNm65caCRiOu0SS/0c1MSd2rvQ0LQ7nc3PGYhlVT09imjq28U3e3ZHGfj5Z4tQYYN9j21S7P7JgZ/vS1hlpkC38Tc9qydqtwBQ+Y6hSJ1kMz26erFHYpM2h/yvjMULKuGr5JdcCJhbOhkx9He8Mu8tIzMnIT0JsAWAtWHCxM9H+eoc7Hto5ZkCqxLUZiOghqbtMDJVDIia7fgn+SuATYzGXJMLPA/8L6I/QUkh8iPQs7r/PHcDH3mcA6BnMKZpZ2Ph0mkX4ddGYimnGIljWZYVULU5A+ComQ934UGyrc8aiQXQkNgHZayxeGt6EpHr0GW30d42f72/M512eO3DvXF1HKLj8eayoXq0cCVgrhET4eshnwZK7D6ouzI2sT03t75uIivLsqygqc0ZAGPr62q26Y/IBXjSO1+eRvQw2jP7kW1p2+DgD8W36Wzzk3S0NNGv7svAz4HFxlNTOZpoam9j8dqnz0X0d0ZiFTjCSBzLsqwAqr0CIDJlU2AfA5GWUl9/u4E4RWNSuyEcbyxe0TLgTPJz9yPb+nCfo9ww7RPaMz/FdXYDzO3cX07cC4zGU1PtfOVwM3Esy7KCp/YKgNCSb2Diz63cyw3TPik9oW6uTsHs2/88lENoz1xNV1fBSMTO5jcYkjsUuNZIvM/JCYxNmGjiU/RJ6GHAxL0D3yQSCRmIY1mWFTi1VwCIc6CZONppJA7AuLMGAaZOJQDyHi4H0ZF5wlzMbm1tOdozp4FcajCqUJBxxqLd07wUuM9ApI2pG7abgTiWZVmBU3sFAHKAgSCLyXOHgThF+SUnYa7j33+hcBSdmdcMxVu79pbzgd8YjDgWkzMgyh8MxdnPSBzLsqyAqa0CIJKuB93XQKTHDd9qFzUWSfgR7TOeNxZvfT5xUsVrR43YljETzQ22hfq7gHzpgcQWAJZlVaXaKgBC8/YEBpQcR+UvpSfTrbGxDvRQQ9FuIZvpMBRrw+5pXooj44GckXgFx9ymu66pHwEvGohkomC0LMsKnNoqAETMfJiL/tlIHICF9fsCgw1EWkK+cIaBOL0zq/llRFqMxBIOMxJnRcA5BoLsUJw5sqzAeRuYDfwYV4+E3DBcRoDGKd5l8QjwXx/zswKuxhoB6dcMBMmTV3NH4Qr6TSMr38J1dM38j4FIvZdzLycsE4H+JUb6BhMm9OeGG5aYSAvhKZQflhiljvoPtwdeNpGSZfXR28DTwNO4+jT95GluzHy4lt83H3gNaO/+30JD8ms43ZcTwUhgL7xp7GVVmNoqAES3L/kCIOVvRtf/xVBL4kJhppE4fdHV+j7x5G0o8RIj9WfpgB0otik2wH3SyL5CV3fCFgBW2ciq1++G3Se4aeYHfQym3RuCVy4KIJraCnFHIs5IXB2J8A1gMwPJWxWktgoApfSz5iLmWv8WjSg5gvAMnTNNrHf3netmEafUAgBwdsRUATA4/xKf1S2h5JkJ3cFIPpa1prdZ482+ZW1v9mZ1tLwLvAufn2ayMwU1qHYKgFHpMMz7SslxRN8oPZlu6bTDq/NKX5Zw+aOBbEqzsPAwg52lQL+S4oiWXhAt19aWI5Z4B6S0v2OxV5taJhh9szfNzhTUoNopADb/8MtAXemB5M3SY3R79eNhmDiVIPJ46cmU6I62RcSSLwCl9fVX50tmElpO3gFKKwCM52TVgLfx483eNDtTUNVqpwAI6fZm7uoumCsAQrkhFIz0vnnFRJDS6SsgpRUA4pr+IHmn9BBqCwBrff4DPM6GN+hVg7XNFKxZFAj7ogz0L02rJ2qnAICtDcUxVwDkQ0MQt/Q4UphbehADhPdLLrJUTHVELBLeMVD4bWEgE6taqSToaDHTebIyrVkURFOzEVO3rlpeqZ0+AGpk3eoTsjM+NhCnSAqDjMTJbb7QSJxSqRi4gEfNFgCq/zYQZVPS6dr5WbF6R9wDGdNkp8CtilNDH2o61ECQ9w3EWIkaumnuJTO3/ZVKKX06Q8Xs96Qwz0CUMC99urGBOFZVknNwXbtMZFWcGioAKL0AUKp1Xa+KyXwjYZxlJgpIy7KswLAFQO+YGUys8jFVtImRGSTLsqzAqJ0CQHWYgSC2AKg4YUP/Zo4tACzLqiq1UwAgBj7ADU0nW+Xz3iYfgokDoHYGwLKs6lI7BYAYWAIQuweg4jySzgOflhzHwcAMkmVZVnDURgHQ2FgHfKHkOHYTYGVSA3s3zBwjtSzLCozaKAA+Dm+GkWvh7BJAZTKyd8MuAViWVVVqowBwXEMf3q4tACqROCZmbmwBYFlWVamNAkDCZj68w2qXACqSnQGwLMtaXW0UAKY2cDmOnQGoTAb+3ewpAMuyqkttFABmPrxdth1m7h4Aq5wMzNyYOEZqWZYVHLYA6LmPSKcNXN1nlZ2IiaWbjbtPk1iWZVWF2igAzBzhstP/FcvI5k3pPk1iWZZVFWqjADAxfWt7AFQwUxcCmTpNYlmW5b8aKQDsRUA1zdiFQIZOk1iWZQVAjRQABmYAPGkD7CwzE2frejNxSiWl5yEsNZDIakxdCGRnACzLqh61UQCIgZsAlXkGMlmVW/jMTKD8YDNxSiTuRiXHUPnEQCarMnYhkL0R0LKs6lEbBYCJTYAiHxnIZFWhkJkCoC6/jZE4JZPdSw+h5gsAUxcC2V4AlmVVkeovACJTBgCDSo7jxRJArv4DoFByHDd0QunJlCieOBkYVXIc4Y2SY6yNiQuBxBYAlmVVj+ovAEJLDX1oG2knu6quqYvBwIAn+iNGNw4sPaG+Z4DKT41EUnnZSJw1Axv497PNgKqKYq6vhzoB2YcTEEK/kr5eHdtzpQyqvwAgZOZDWzy7CfAlAzGGM7juNANx+mZM8ljg60ZiufKCkTirsxcCWWvQxcZCOW5AluEC48ulfbkuMpOGtT7VXwCIoZ3brmd9AP5kKM45vs0CuJxvKNI/6Wz2ZgnAzgBYq1MxsC+kmyuHG4tV6cY2bgm6S2lBPNgMbK2h+gsAFTMXAdWp+VMAAOL+wVCkzX2ZBYhNPBQ4wFC0ew3FWRt7IZC1uneMRRKNE0kE4zSO3wr1pwJSUgzVf5lJxlqf6i8AxEgbYJfFH3hTkWZnvAnyoqFoPswCOKbe/gG9y1ysNdglAGt1rxiMtSkhOdVgvMo0YUJ/0FTJcRx51UA21gZUfwFg5q3tY7q6St+tv07uDEOByjsL0JDcCzjSULR/897wewzFWpMa2cQ5qPtUiVUNHPdRo/GEM4oDYA1bOvBUYIuS4+Tyj5WejLUh1V8AKCaWALyZ/l9uQf4GwFSfgfLNAjjG1v5BtK37vL43HEN7OIydKrF89+7mzwGGmnEB8GWWDLrIYLzKEklsAXJxyXGUt+ia+R8DGVkbUP0FgJklAPNNgFZ2R9silJmGopVnFmDsxBHAyUZiCYvQujYjsdb9EEOnOAydKrH890g6j/Ck0ZjC2TQ0ld4QqxKFpRnYpOQ4gn37L5PqLwAwMAPgRRvg1dVzNebeRryfBSg452Dq+0eZSfv0uUZirfsZhi4EsvcBVBe5w3DAOsT9NZFIyHDcYItOPBb4jqFopjZGWxtQCwWAgQ9sD5oAre7GzIdAi6Fo3s4CjGnaGhhrKNoSVK4yFGs9DF0IZOpUiRUMYb0ZDF9AJexDaNjpRmMG2ZimjRDH1Azmp+TrTRdl1jrUQgFg4APbsyZAq6rjKiphFsAtnAWY6nzWSkfLu4ZirZuxC4HsUcCqUiy87zYe15GLiU/c1njcINLCZcDWZoLJrO4OqVYZVHsBIMCmpUfx4CKgtamEWYBY41CQHxiJJSwir1cYibUhpi4EMrOnxAoSh+uNx1QGQsjU6Z7giiUOQqXRWDwpmP+3sNapuguACZO/gIk3VTNHyHom6LMAUjcJE5crQXHtv6v1fSOxevY82w3QWtOszF1geDMggOoRRJPjjMcNiqOb+oFci7FxRO4iO+NpM7GsnqjuAiC/zNCHdZmWAKB7FkAyhqKZnQUYd9YglImGopVp7X9lJgo5uwRQhRRX0p5EFqYy7rThnsT22xf0AmBnQ9EUldo9QumT6i4A1NSHtVu+AgCgTq8kiLMA+cUJMDYFXp61/5XZC4GsdelsuQd4xIPIm5EPXe1BXH81nLYroucajHgrHc1/MxjP6oHqLgAKhgYrccqzB2C5IM4CFKf7phjIp7xr/6swspRjTwFUK5czAS+aUY2hIXW0B3H9kU47iPNrzG0EXkhYTBYTVg9VdwHgBPwioPU/09wsgLBHyTE2ZRjolgayKf/a/womCgC7CbBadWaeAfXmbT2kM6vmsqDX5jchsr+xeKoXcVPLW8biWT1W3QWAmTPbLtsO+9hAnN4xOwsQJD6s/X/O1BJAaTedWcGV75dGed14XGUbwvIL43HLLTJpG1TN/TmUORTmXWMsntUrVV4AGGoDnE67BuL0ntm9AEFR/rX/5URMFAD1RBo3MhDHCqKuqYsR9wcY6RmxhtOJJkxdne2PcL4FGGIoWh6cH3l70Zq1PtVdAJg5s22mhWxfFPsCVFdbTLfwWx8fbmYzp9PPbgSsZu0z/g+4zoPIDiIzaWys8yC296KpODDaWDzhUrvxz1/VXQCYuAnQyNnxkphtU+q3UL2Pfx5DxzntfQDVL587C/DiRrrd+G9d5W14G5/cDNGpBiO+Sv3CSwzGs/qgugsAM0e2/C4ALFNMXQiELQCqXlfbpyCTPIktXEhDYidPYnslJ1cDpvoZuKA/4IYblhiKZ/VRdRcAUiEXAVllYuhCIHFsAVAL2ltmo3KbB5H74chMKmUzaSx1GKjBjoYyk/bWv5iLZ/VVdRcAJs5si497ACyz7IVAVm8V3CTgxSmgQ4glzPXQ98roxoGgbZgrVt6lX/gCQ7GsElVvAVC8j3vj0gMZ6R5nBYGpC4EQ2wugVnS1vo8a7Xi3Erm8+2rt4Bpc9wtgO2PxhAQ3TPvEWDyrJNVbAPTfbDNM/PnKeRGQ5T0zmzptN8Ba0tH6G4QHPYi8EQU3uDcGxhN7AKcbjNhBNvN7g/GsElVvAeDWVd5FQFYZ2AuBrF5TCE0EzN9TLxxHPHGy8bilGpUOo1wPhA1F/BDCkw3Fsgyp3gLA1D0AIbsHoKoYuRDIXglcMyKJwcSS30ULF3v2DJUMscZgfU9tOe8ykD0NRqyDwuVEU8czYUJ/g3GtEpiq7gLI0FEt1xYA1cXIkk6wPqwts+ITNwEZjSsnIxwJeD1gbQHhdkY3nsAdbYs8ftaGxVPfR/UMw1E3Ah2PMJ6lgxYQT9yFym3k9W66WhcYfpbVQ1VcAOhQMxtXl9klgOpirwS21hRpGkaocCIip6AcBtSV95CeHM7g8L1EppxI19Ty3j66sljyu6jO9Pgpg1FpABoIy2KiyfuB2fSvu8NuECyv6i0AxMg0rUv+4/JfBLSqUMkRVEo/+rYsp4RLTwUn5/eyk4mCblMikZDtYV7hxjRtjasngZ4M7sEgBr7BSyEHE172GPGJx5Cd8WbZHx9NjgOj6/49MQDhBOAEluaWEUs+hOptFEK309Vc/ltYa0z1FgBm3tI+DsCHvIF2xrqw5BiD+i9kaa7kMGhoKPBq6YH6+nydj5T8aucQ3mQTbJfIyhOfuC1u6GRwT8F19yN4zXhGoM5fiaYa6Gh5pCxPjERChIf9Ejgbf/8+6oGjEDmKsDuDWPLPiM4m5/6OrpletGWueVVcAMhQAz1f/P6AF2Dv0qNo6TcKLvzPZ4SHK6V+QLi6D/Boyfn0lcOHZu55qx+K/98fVk80JHZC5BRET0ZlT6T0b2OPDUf0AWLJn5P/4BJPX0IamrbDca8HDvHsGX0TAkahMopw6BqiiSfAmY1TuM2X2ZEqVcUFgJGjWv5uAIwnRqEm+m87/y05RFdXgVhyITC4xEjfBaaVnE+fmTrWae8DCLRo05447skopwDdffcDPeivLgT8jPDwk4glmoy3zh131iDySyYj7o9RBhqNbZ4gsj/o/qhzBbHE38CZjeveRmfr3/1OrpJVcQFQDRcBiZmWmeK+bSQOvA7sUWKMbxBPfJNs68MmEuo1YxcC2fsAAkYYM3E/VE5B5WRwtzUz0+O7PUD+RCx5B+pcSUfzn0uKFmscitR9n/ziKcDmlfl3JHuC7okjFxNL/h1hNq5zm71auPdsAbB+/hUAseT/oHzLSCzXMbXm/hKlFwCgMoNIYm9/jv+E50PeQBzbDMh3kUiI0OYHI3oycDIuX/Q7JY8IcDziHk8s+QLKLWjhdnba4mXSaXeDXx1rHAr1R4EeD4xGPT/WWE47oVyIuBcSS74JzMZxb2PWjCcwcu9HdbMFwPr5swQwZuL+uJhqEaoMXGSmABBeMPQjNYKQ3Myo9He6+/OXz3ubfMiW8wwsAtsCwDfx5CGojAU9EbTW2jLvhrAbEvoFr877lHjyKZS3gHcQlnb/R4vfogAAIABJREFUnhDKMGBrYC9g2xoZC7cFzsZ1ziaW/DfwO9BbaG99lBr5C+it6iwAjm7qB+5GpQfyoQ1wNPktXG4DBhiK+CLXX1/6JkAAnIdhwy8cPSKcwJYfdBFJjCvrTMAj6Tyx5KeUfFGU7QZYVpFIiNDwOMJ5KDvbz3MAvrDKLKH9K1nZ1kATSBPx5L9w9Xqkbibt0+f6nViQ+H0m2xuDc2Y+nMt5FfDoxoFEk5cg3AcYKF4+90djkXbY7CmMzorIiYRlDrHEQeZi9oCZC4FsAVAu0dQowsOfQ7gR2NnvdKwKo2yDSBry/ySWvIZxpxnYWF0dqnMGwKkbauZN1eObAE89dQiLB34LcY9E5XhgK/MPUXMFQDrtEk/ehxI3FhN2LG5ySv0J0XsoOPfS2fw8nr7P6HyQ7UsMUmtTz+UXmTKA8LKpoI1U2BZ+K5D6AU3kQxOIJi9kx2EtPdpDUcWqswAQQ0e0xPgSgBBP7I5yFMiRLOZA0DrUq882eY8hObPXmLpuFnFMFgAAAnooyqE47qXEUu+B3ovKfRTq/mi8Nao4HxqoL+wMgJcip32R8LK7ga/7nYpVdYYgTOfVeRGiiTgdre/4nZBfqrMAMNMEyMxFQOOTm5Hn27gciXAUyhalJ9ZDym9oazPQvm8l729+H1vOmwtsbjTuKnRL4HuIfo/wsgLRxBxE7kG5jx2HzSm9arcXAgXa2IkjKDj3AV/2OxWrqh2EyDPEE9/17Viyz6q0ADC1Q7sPFwFFIiHqh+6Dho5C9Shy7A2EfJnAdAq/Nx6zuInu/yg29CmHULEJCPsj/IxX531ILPlH4F7yeh9dre/3Iaa9ECio4hNHUnDuofKXWN7pXmoyeaVu0LgID6McgLlNy+U2FJV7iCXH0565xe9kyq06NwE6YuLDw2XEVj2beh532nDiqQjR5LWEh/8b1/krqj8F9sPEZT59pc4soimz+wpiyR8A3zEas3c2A6LADYTlPWLJN4gnpxNvOpxIur6HMUzMAGxUPG1iGTO2cUvU+QOVO/j/E+Ea0INpz3yZ/PD9EbJ+J+WRpajEyWYOJ1+/GejxCDcBpXcdLb9+QJZoqsHvRMqtOmcAXDYz8Mb98Tqnmhsb61gQPghXitP6eb4OKgHcpjQC0QeIJA7r45vyqqKp00BbCdaGrG1RTgf3dMLz/kss+SDofYjcSzbzz7V+hZkLgeALuhnwbumBLCZM6M/SutvxZCOsp14BnY2GZq/Ria4rvYx0ehyvzZ+L6hSf8vPCJ+CeSMeM/wOga+pi4A7gjuLGzdyR4J4CchwlH7ctmxCiNxJPvkc28ye/kymX6iwAxMgSwKpvieNSXyXHkaBH8RmHUdxIUgl2IiwPlVwERFOnIYEb/Fe3EXASyEkoFNuEyr0U3HsZsOhP3HDDEsDchUBSGIotAMxYOqgV2NfvNHroOeA2YDbtmZfW+zuLLxFnEE+8icqVFN82K9kruIUInTNfXOt/LRYDtwO3E0nX48z/FuKegsiJFGfvgqwepYtoYu9a2RhYnQUAOrT0cUoWEEscBRwFchR5HRHooW/9SisCKmPwX5udUN0JR6awdNBi4olHcOVeXBaY+ZOE7D4AE+JNh6Pu9/xOYz0U5SkcZuPobG5ufb3XEbKtLUSbHkXcdmCE+RTL4nrCA07npit7dr14V3oZcA9wD6PSp7HFvEOBUxBOgjJuhu6d4TjyW+Db1EBrpSotAEx0adORIPeUHicw+lYEVO7gv7oBqByNcLS5kLYdcMkaG+v4zJ3udxpr4aL8FXQ2jtxG+zqWk3qjo/lvRBJ7E2IaIqdSOT9THyEkyWY6+hyh2PL7QeBB0ukUr3x4IFI4BeRk4EumEjVC+Rbx1A/JtrT5nYrXqrQAqNhNRF7bibA8QjR1GB0tG566jiZ+iGiGyvmgKjNbAJTss7omgtPdrzjoO9JFLn8rXTP/Y/wJxbbXP6AhcS3CNd0nXILKRZhFzjmTruZ5xqIWl0X+3P1rMrHkLohEUI0SlNkR1YuZMPkWbpj2id+peKlaC4CgrzX5qWcbA6vnzd9D9j6AkhTv7Djf5yyWAQ8BsyF3Ox1t5bn/o7N1Dun0gbzywfeLbWqDtvlRHwA9k+yM5z1/VHEfxUtAmnhiD5BTUE4BdvL82es2jGX5yUDaxxw8V30FQCQxGKrquksvrH85wA7+PWULgFJ8QU/Bn7/DJcD9iM6mvv4Pvr3lFd+Ef00k/b+E548BPQt/Z0MKwGxUrqAj85QvGWRbnwWeBS5iTNPOFNyTEU7BxDXkvaWaYtxZV/R4z0MFqr4CoL8zjHzV790wYe1FgB38e8HOAJREaCzj0xYCd6Mym4GL7jZ3Q6YBxc1yvwVuIJ48GmUcMBoYVKYM3gQ6cZ3r6Gx+o0zP3LBZzS8DLwMX09C0HY6eAnoKsA/l+XzajMKiCHBDGZ7li+orAHJGegDUimIREJv0TdqnzyWenIjaNf+es3sA+qwhuQPoIR4/5VPgTpTZFOrv7T6iFmRKNnM3cDfjzhpEYfFolJOAQzC7a94FXgAeQJxbyDY/aTC2N4qFyeXA5UQmbUM4fzJwCnAAXja0UxmPLQAqiDKsCoevj4GBeHOGeCfIP0wseRPKJXg3+C+mctuFroMtAPrM0dEY6ca0ho+A21H3Nj4NP8A9zUs9eIb3itPOHd2/igWTcBCwN8L2KNsjbMOGO40uBF4HXkd5FdzH6N/v0Yre3NY1/V/ANGAaYxu3xK07ETgFZRTmO68ezITJG1f039d6VF8B4LjbVMELbA54FOUxhDsZMewJXp0/CtE7UAZ68LydgF96EHe5++hXF2XpsgZwRoJuCuxNxV/2IlsRSdd3T+FavSLfNhpOeR1Hfkxu6O+r8t+jM/Ma8Bpw/ef/v0i6ntD8oTjOIMhthBsaAu4yNLSQUP4zws6n3Jgxce9FcN3c9h4wA5hBJLEFYWcS6GTM7QMLs2zZYRQbP1Wd6ioAGpp2B/dSv9Poo7nA3ajeTSh0P7OaV++p/RCx1GgPiwCv3Ee/hSd2d+G7dpX/0nDaroRCx6IcQ3Eqr9K+HzcnPO8PjDvrlGreKOSRfQzG+h11A8bV3L9BsdCxnSiXK+5lOp9oajbCH7pvFS2dsh9VWgBUz2VA8Ynb4rj3Ujm9p12UOaimUdmHEcO2oj1zKh2tt65l8C9qb3kIldEIi8qca1+tPPivqXPmi2Qzl9GeORRxh6MS7b5QxNyZY+8dSX7xvUSmVNnyhocik7YBNjUSS+R+huQaam7wt9ato+UpHI7E2MVE1XujY6W9ca1dZMoAdNltBLe95HKfUjx+dDdadw8d0+f2OkJ7S6XMBKx/8F9ddsbHQCfQSTrt8I+5+6LOscAxKHsS7HWdgwgvu5F0umGdF0hZK4QK2xqK9BE5939oa8sZimdVi1ktLxBPnY3qtRv+zRtk6vs1cKqjAAgtawV29zuNdfg7oncBdzM4/xcjH1bBLwJ6N/ivrjiIPt796yKiqa0QPQY4hmKP7sHGMjXnO7w2/ydUeeMQI4StjcRRvcbILZdWdcrNvY7w8HMpfQDfmuILSNWdL6/8JYB44mSECX6nsZIlwL2INiHudrRndibbejbZ1oeNvqkEdzmgtMF/bTpa3qU98xvaMyfziTMUOAKYBvoPY88wQfUC4k2VcqOdf0SHGIlT59xoJI5Vnbq6CqjMMhCpH5EpVdlcrrJnAOITN0GdlgAUZu8Ad4PexYL8g9zRVp5BOXgzAeYH/9UVj3X9sfvXFOKnfw03fxzCMSCHAPWePXvDwqh7A42Nu9tp6fVQE8dB5T1uanmr9DhWVRP3MUOrhwMoHmWuKpVdAOCkje307J0C8Ffgblznbjqbn/Mhh6LgFAHeD/5rk73mH8BUYCqnnjqEJf2/jcsxiHOMT98bO/FZ+IdAqw/PrhQGzmpr7/fPWLVHeN/M+2HedH+BQKjcAiAyaRs0/6MyPvFDhPtQvZN8v/vomvpRGZ+9fv4XAf4M/qsrtne9rfuX0JDcE+EYhOMoHjsr05KXXMS4s/7X7kz3VN7vBKwKUHDzOFU5dhtRuQVAKH8B3nTGW9mzaPfUfmHeE3R1FTx+Xt/5VwQEY/Bfk9KZeQZ4BriYSNMw6gpHA8egciTeHhfdgvyisaze98CyLCtAKrMAmDB5Y5blxnqw9L8Y9FHEuRPXvY2O1neMP8FL7S0PEW06CnHvphw75UXuJ1d3Eu2ZoA3+ayreZ34jcCORSIi6oXtAaDTKcaB7YfyYoUzEFgCWZQVYZRYAS3MTwOhb7lJUf8XC/BVl28DnlY7mPxNLnQB6F95ei3wf9QtOJBu4N/8NK87kPN39K83YxPbk5Xs4TDY4e7I7DU27+7o/xKou45ObsVjrSoqxJP/fkj/jjm7qx+DCJiXF6IkhSxYG6tbGKlSZBQDEDMb6Jy4n09n6jMGY/hIdgXq6PBLUaf++ubn1deACxqV+Q15/D+xmJK7jHgPYAsAyI8eDhKW0fieD66ZQvEin7zZxj0Ll9pJi9MSigS1Ak+fPqWGV1wcgktiC4kUyJrxNPnxI91pxdYgnJ6J4eaVvdQ3+K7up5S3y9aMM9hc4ylAcy7Is4yqvAAjJtzGT92Jc58TuqyWrgx38S9c19SNEYxSPepZqP0alK3WWzbKsKld5BYDoSDNxuLiq1mft4G9OdsbTLL+HvTT92HrudgbiWJZlGVd5BQDydQNBPuKzXGnrYEFiB38PuL82Eibv7GwkjmVZlmEVWADw1ZIjKLdW/G7/5ezg740hhceM3LPgBP6GSsuyalQlFgBfMBDjLwZi+M8O/t5pa8uhvGYg0kYGYliWZfWM4/S4Q45DLmSmnU5Yy9V9rvQPVKHyrxC1g385lN7uWQ3dfGdZVnXbeCMzn+Xq9rgFeRiX/xqZB8jJl4FXDURaH8HERSIqlX1TWyUP/tHED3FkU+NxV7fDsCtIp90Soxj4PrGNyC3L6oEli4YQNvCRHsr1+MUlDO9/BMOVUgeTkBwM3F9SDGvDKnnwB0DOQdnem9greYmpwDLPn2NZlmVC2NDnYnjZxz39rU53W9RPSn6oyxh75tljFT/4W5ZlWWslfNNAlI968/m9fPL/lZIfK3yVreYnS45jrZ0d/C3LsqrTqHQYlQmlB9JedbUtFgAij5f+YED1MuJN+xqJZa1gB39rXUY3DiSW/AnCeAPRTiCWuIxIo4mTNpZl9dSW888Ati09kMzpze8uFgAuT5T+YAD6oe7vGTNxf0PxLDv4W2snxFJjGVz3KvAzYJCBmP1AziFc9xqxxI+IROwGRsvyWrTpYNBLjMRS+lAAyLIHMbLjGYAtcJ2HiaZOI52uxD4DweH94H+vHfwr0JjUgUSTT4DeBGztwROGg8wkvPmzxJLf9iC+ZVkA8dSRiPsHTNzMKyyioH/szZcUB+j2tvnAvSUnsEJ/RGfw6rxniCa/ZTBu7SjP4H+SHfwrSPS0rxBLduLqnxH28f6BuitwP7HUncRSO3r/PMuqEZHEYGLJn6F6F7CxmaB6B12tC3rzFSuqDpGbUB1tJpHP7Y7wALHUXbju2XS2/t1w/OpkB39rZWOaNsLV80EnA/3Ln4AeCxxBLDmTOn7GjZkPy5+DZVWBCRP6s2TgRETOB4YZja1Ory8wW1EAfCx/YGN9B/iSyZyK9FgcOZJYqo28pOlqnmf+GVXCDv7WcpFIiNCwU3HdXwCb+5xNHdBEjrHEkj9nSC5DW1tlN9SqNGE5iWVuaQVgSEvvgprTB3Hw/pIrJ1x6J86gaGys47PwqSyVixC+6MET3uC9oXf29otWFAD3NC8lnroY1WuNprXKszRBWMcTT11F/YJLfRuEpGCm/bFp0cQPUVrwavAXuZ/6BXbwrwSx1GGgVwO7+53KajYBpvJZOEE8dQHZli6/E6oZN7W85XcKAN3TzHY2tyfSaYfX5p/CZ+4lIF/z7DnKL3kkne/tl626SW/wst8Cb5jKaR0Go/pTlg76O/FkFO/edNdNQ+V/5obEkxMRuRbvLmi6l/oFJ9jBP+AakjsQS/4B9EGCN/ivRL6G6i3Ekw/Q0BTgPC3LF0IsdQqvznsB1Vs8HvzfYqPcTX350lUHm7a2HMLpQDnekL+C0k408RjRxAFleF5w2Wl/KzJlU2KJaTi8CJjei+Md5Vs47tPEUm3EJvm9TGFZ/mtIHU0sNQf0VijDUkmI0/u6HLfm22Y2czfKr0tOqqdE9kfkUaLJO2ho2q48zwzQEkA5pv3t4B9co9Jh4qlGwsteAZlEca290oRAfwj5N4in0kyY4MNGRcvyWTRxALHkQzh6N+jIMj31emZler32v9zap5sLeibK631OqS+E43Dcl4gnrmDCZEPHIgIu2rSnnfavYWOSx7HlvBe6992Y3RHsj0GfL+/Fkt/Fj+U9yyq3hsQ+xFP3IfIoGOnn31P/JJ87o5QAax94uloXENajgQ9KCd4H/VA5i6W5N4gnz+Xopn5lfn55SX4j7Jt/7YmldiSWuhOXO4BqPF//FaCTWPLxml/es6rXmKadiSVvwZEnUD2izE//L65zAl1tn5YSZN1vnje3vo6rxwELS3lAH22Kcikbu88TT0V8eH5lK+72t2/+QTM+uRnx5HTQF7rP1le7fRH5C7HkLURP+4rfyViWEdHTvkI0eS2u+zwQofwzXTnEOYXO5udKDbT+9oOdrXOIJU8CbgU2KvVhfbADqrcQSz6MOmfS0fysDzlUmnsDfdRPeA6lx/dV99k8XM+f0VNHN/VjY51ETi/An58jPwkQwQkdSyx5JeEBl5Nf7HdOltV70cSXEOci0O9honVvXwiLgDjZ5gdMhNvwH6I980fGpA7C1bvwpElQj3wTcZ8iluzTUYcaEvzd/u2Z7/idQnnpAWysL2Pkpq8KpgwEfkJ+8feBl/1Ox7J6JexeBhIH9XNZei44x5NtftJUwJ5tPpvV8gIq+wMPmXpwHzjA//j4/KAL/uBfmw4hGIP/s8Bf/U4C+CJgLxiyKs33AD8H/78Slm+YHPyhN7vPO1repT1zOKL/A1RPi8ZqYDf8Wes2H5HJ5D/Ym/bMAaDHowSjo5xlWRuyGOE88h8c7EUnyN4eP1OyrTeSL3wdmEV5GgZZG5Krs1f6WqtbAvyKAYu3Jdsyna6uAgDtrXdQqN8F5EKgVzeHWZZVTno74u5KNnPZ5z+/hvXt/HnXzP/QnhmLq/sBj5lNyeq1Xb6w1O8UrABR7kTcXWjP/Jjrr/9sjf/eNXUx7S2XEMrtgNIGePLhYllWn7yCK8fQ3noS2Rlvevmg0hrQdLbOoT1zECLfRfiXoZwsy+oTeRp1DqEjM7pHHxw3t71HR+ZHqLMPyP+VIUHLstbtI0Qm896w3ehsuaccDzTRgU7JtnTxWW4nRH4G2DM+llVe7yLyI0YM3ZeO5j/3+qs7mv9Ge8so0OPx/jIwy7JWlSvOxOVGkG2Z3pdb/frKXAvaO9oWkW1J4zg7INyE3R9gWd4SFqFcxoDFO5JtaSOdLq33QXvrHQzJ7YTIZKCkDmOWZfWEPoBb2IuOzI9ob5tf7qebb2Ywq/nfwHiiyQzCdGA/48+wrNqmwK24hXPomPm20cjFW8WmMz55M3l+gpLAr6Yn1qpiyfPREnuxONpFtvVhQxlZffcaDmcyq7XPF/mY4N0PdkfmCdLpA3jtg7GoXAZs4dmzLKtWKHNAJ9PR6u3m2xszHwKTiKVmgF4FHOPp86yeaEDYvaQIKq8BtgDwzycIl/KxM417mn3fvO1tZV+ckryRcWfNprDkbFTPw99mCpZVqf6N6AW0t5Z3ea295RXgWOJNh6M6FXTXsj3bsqqHizCLUOEsbppZ7kv21smra2hXddOVC8m2pAnprkBXWZ65Xs61xJP2jcZau0hiMPFUGhjlcyYACxH5Gfn6Hci23ohfe2uyzQ8wZNleiPwImOdLDqvai3jyRsadNtzvRKyAijftixNq8zsN4GFw9ySbGR+kwR/8uq87ljoMdCrwdV+e/zl9ACc0iVnN/vQmj008FJxHSo4zYlio5A1gFqTTTveS1eXA5j5nU3xjcHLncnPbez7nsqr4xE1wnXMRpgD1PmcTqClVT8WSz0KJSwAwhfbMNBPpBNaYpq1R95coY/FrjANQXseRH5NtCcBL79qVZwZgde0tDzFi2J7dbYV9rIjkcFz3WeLJ6UQav+BfHpbv4olv8uq8Z1D5X/wf/B9Bnb3JZsYHbvAHyM74mI7Mebjshv8zehsXrw4vvGCvDq9xoxsHEk+lcd3XUMbh3+BfnLXrv3C3IA/+4Gd1tFxw3iY+ROQX5Oa2eNV2cQ12BsB/kUnbUJe/uPsDw1/Cv0Av6p7qrxzR5LcQrsb3GT1AeBCRKcxqecHvVIyzMwDrIsRT3wG9EmUbH/Moztrl9By6Wt/3MY8e82cGYGUrv00ofh6J2AzVaYSHv0A8daSPeVjlMO6sQcRTacL5VwMw+C9A5GfULxxRcYM/QEfmwZVm9Ob6movyLVx9hmjyWiJNw3zNxfJeQ2IfYsm/oHqLz4P/EzjugWQz4ytl8IcgzACsLt50OOpOA3bxNQ/lThx3kqe9mO0MQPmtWOcPwtHU4huDhs+mfbq/A6cpkcRg6pyzUD0X6O9zNh8jXEZu2FS60st8zqV0dgZghchpX6Qu9Cvf1/mXn87Jlvl0jiH+zwCsLtv8AENye3bvNi57Z6TPCcehzt+JJ6czpmkj3/KwzIkm9+PVeY91r/P7O/gLD36+M7haBn+ArtYFZFvS5MMjujuC+mkTlEsJz3uBMcnjfM7FMmF040DiyXMJh17xdZ1fWBSI0zklCt4MwMoiUzalbtlP/e9GJu8hpMnNvc7o/gA7A1Ae0cSXcOSSALwtALyGyIVB3xxkTDQ1CnGvBtnT71SKbVfdKXTOfNHvTPqktmcAiuv8qpcDX/Exj2IXTuFsspl/+piHEcGbAVhZ19SPyGYmgewG3OtfIrolqtcS3vwJYomD/MvD6pXl6/wifu8KhuJ09HnkhwV+Z7BRHS2PMGL43sX9AeLziQY5HCf0N7s/oMJEU3sTTf4Z1Vvwd/B/CkcOpj3z3WoY/CHoBcBy7S2v0J452v/bynQkyJ+JJu8getpX/MvD2gAhnopQWPwyqj/F37Xo4k1feWcE2cxlVbEW3VvptEu29UbC/b8WgBtDwwiNhN1XiKcmMSpt7zkIqmhqK6LJaxF9AuFAHzPpvm1z2H7MannUxzyM83s6tPcaG+tYUJ9A9eeAf2vzwiJcminoxXS1LuhTDLsEYF68ad/uTaTf8DuVip9y9kpQGrUUvYpwBtnM3T7nsWG1sgQQmTKAumWno1wADPExk8Uo1zBw8SVcf/1nPubhmcqYAVhZW1uObMt0yG2HcA1QnjP7q1MGIpxLWF4hnhiP/x9ktW1M09bEkzei7uP4P/i/gnAs7a3ftoP/Wsxq/jfZzHhc3Q/w9lKjDRuBchexxB+JJf09eWRBLDGa0LKXUC7Fz8FfuZOw7EJH5rxqHfyhGgathuReODoN5GBf81DmIEyiPfPXHn+NnQEo3ejGgQypa0K5EBjsczbFZlLvDs3wSDrvcy6VIiibuwByCDOor/spN0z7xOdc1lTNMwBB+RwXngGmkM38ydc8yqTyZgBW15l5hvbWQ0CPR3nLtzyEfYBHiSVvITLJz4YUtaK4zj+47uXutwU/B/8cwjXkc9uRbZluB/9eUbItXSzI7YJwHuDn21Ydyukszb1h9weUydjGLYkmr8XhSZ8H/w8RmUzug31rZfCHapgBWFlw1o4WInIl9Qsu5YYblqzzd9kZgL5pSOyDI9OAA/xOBeVO1JlMZ7OPm1OrSDS1FehPEX6A/y8or+DKGXS23ONzHkWx5OXAV0uKIfw2EPsdIul66uZP9H0v1/JZn1zuJ3S1fepjHr6orgJgueB8iLyD6IXr7BJlC4DeiZz2RUKhnwTg37U4VajuGbTP+D9f86hW8YkjUZnq+5Qw2CLPtFhiNMg0YFtf87D/rlVaACwXnDfFRxCdQrb12VX+v7YA6JkV6/x+z+zgWVMoa+2CMljU+JuiEdGmPRGdCnqoz5kEa2bHR9VdABQF66aolfu+2wJgw4oDwDX4v0FsGcJMxLmIWc3/9TmX2hKc6WLw49bQSjc+uRl5foKSBEI+ZvIRIj+3m3RXqIUCoGh040CG1J+D6jnAAB8zWYDIVXwsv2Lj/P62AFiH+MSRqDMN8L/zonIndXI6N7X4t8nUgljjUKTuogAMJAAvI3IG2Zb7fM4juFb0bPkZ8AUfM8mh/BbJXUB7m3/3ywRQ7RQAy0VP+woSugL4js+ZvAbMBs4vOVI1FQDR1FYIvwL1u3UvwBMUj031/Gin5b14Yg9Urga+6XcqwK2E5RxbHK4mmjwJ4QpgO1/zEL0Hdc6gveUVX/MIKL8/YP0TTe4HOg2R/f1OpWTVUACsmOb9Bb6v8/MfRH7ODkN/U/F/r9WseHX4dGBnnzOxy0PLNSR2wpGrgaN8zuQ1HM5kVuZOn/MItNotACBod8P3XaUXALHEaFSmIyUecSqVifbOVnmtmGZOAxv7nM18RC6uyf0BK25u9Xt55hOES8kNm1qT9270Um0XAMuNO2sQhSVno3oe0M/vdHqtUguAhuReOEwFDvE5k6q64rMmBebqcGqrm1xwCrDiJutQ4SxumvmBj3lUFFsArGxsYnsK8ksg4ncqvVJpBUCwNnM9CUy26/xVYuzEEeSdKxGO8zuVqt88GpwlmIfBnUz7jOd9zqPi2AJgbWKpw0CnAl/3O5UeqZQCIFjHudbfpMmqbPGmw1GdCrqrz5kU9wf0X3xh1VwqE0vtCHoVcIyveSiv48iPybZ0+ZpHBbMFwLqs2B9wBTDc73TWqxIKgOA0dOlZm2ar8o1Kh9lq/qndG0v9/hl+F5GfVfTG0uAss9ifYUNzXcLaAAAGo0lEQVRsAbAh8Ymb4DrnIkwB6v1OZ62CXAAEp/tXcY0wp+fQ1fq+z7lY5bTiZ3gyvu/xkaeL09Wtf/E3j14orvN/D9VLgKE+ZmJ/hg2zBUBPNSR3QLgqEGuLqwtiARCc7l8ADyN6xhqtmK3aEj/9a2jhEoKwx0e5EwpNdMx82+9U1is4SylP4LiTmTXjcZ/zqCq2AOit4saXacAufqfyuSAVAMHp/gXoPxDnArtGaK2iuMfnamB3nzNZjHINAxdfErj9AcF54fk3ohfYvTre8PvNrPK88OSbHLDnr1nqfIzIfkB/v1Piw0Vv8eIc/3fAxlInstT5PRDH37+XT4ALyA+fQOflL/iYhxVELz75Fjt95dc4A98F2RcY5FMmdQgHka8bx677zeXFJ1/0KY8VIlM2ZY+RlyL8FmEnHzNZiPILCvVxOpuf9jGPqmZnAEoRnE0xoMxBNUln65yyPzuW2hFxr0bl6LI/e1V5lOupK1xkzwJbPbKiB8i5+F3MK3MIyRRmtTxa9mev2DB5MTCs7M9fwfbkKCNbAJhQPBYzFf/bXxY3yYSZwo2ZDz1/WpAKIPQBHOcMZrXYN36r96KJL+HIJShj8fdzsfwDYDT5LYSpwG5led66PYUjk30pgGqULQBMKh51m4rfF2Asv7J0h6HNnuwNCE73L4BXEbnIrvNbRkST+3UPht/wNY9ytKUOzqbIyj8iWaFsAWDaisHR/2Y3XiwLBGcT5EcIl9ue35YHhHjqO6BXomzjcy7mN8FNmLwxS3LnBeBYZHA3QdYIWwB4JTjtbs0sC4ydOIKCczV+d/+yd3tb5TK6cSBD6ppQLgQG+5xN6S2rg9TcrNrbJFcIWwB4rSG5F45OAznY50z6tiwQtHV+ZDLtmZf8zcOqKWOatkbdXwZmf0A+fBZd0//Vq68MSnvzWrooqQLYAqBcgnLlbU+XBYKzKxjg77hyJp0t9/ich1XLGhL7IDIV4UCfM+l5K9zgXHBWfAGpxauSA8wWAOUUmTKA8LKzEc5FGehjJgVEf00hdCWdzW+s8l+KexhO7N7DsKM/6X1uLqoXUZh3vf3QsAJCiKZiiF4KfMnnXN5G+Qn9F3atUQhEE1/CcaagmsTfFuZLQafjhC5hVvN/fczDWgtbAPhhTNPWuO6lFBvm+DylKC+B/h1YBGwB7If/O/uXokwj5PzSfmhYgTS6cSBDwmeici7+NRJa7jPgceBdipv6dqJ4pM/xMynQ23FDZ63xkmEFhi0A/NSQ2AdHpgEH+J1KYCh34riTyM540+9ULGuDoqmtQH+K8H1sZ9XlXsGVM+ySXfDZAsB/QTpy5B+7OciqZA3JvXCYChzidyo++giRn/Pu0AyPpPN+J2NtmC0AgmJ040CG1J+D6jnAAL/TKaNiE5Dc3OvsOr9V8YrNwKYB2/qdShnZo7kVyhYAQROcI0des01ArOoUSddTN39iMG7E9Jo+gOtOoXOm/xcZWb1WzQNMZYsm9wOdhsj+fqdiWPEssxbOCfxd6JZVivHJzcjzkwA0A/PCazicyazMnX4nYvWdLQCCbEXnrsso7tCvbMocRM+gvfUvfqdiWWXTkNiJEFcF4LZMEz5BuNS24K4OtgCoBCuuLD0Pf3t395X5fuaWVWmCc49GXxRbiocKZ9mrtquHLQAqSXC6evVMOW40s6xKUmy09T1ULwGG+p1ODz0M7mTaZzzvdyKWWbYAqERB6eu9bn3vWW5ZtSAyZVNCy85BmIK/nfrWTXkdR35sr9quXrYAqFRButlrVU8AU0q6tcyyakXxls1fEKxZvZ7fNWBVNFsAVLr4xE1wnXMD8CbxDqIX2nV+y+qD4v6Aqym28PVLcZ0/p+fQ1fq+j3lYZWILgGrRkNwB4SqE48r8ZPu2YFkmrLiB8xeUf1bvCRx3MrNmPF7m51o+sgVAtYlOPBZxLgd29vhJOeB6woWf2F3BlmXQhMkbszR3EZAA+nv8tDeB82nP3OLxc6wAsgVANUqnHV794FiQ84FvGI6+EOE6cuGr7AY/y/JQpGkYdZpE9XRgE8PRn0f0Kt4dnrV9+2uXLQCq3ZiJ++M6JwMnA9v1McpSkAdAb0fc2WRnfGwwQ8uy1mdM00Zo4USUkxA5AmVgHyP9s/gzLLeRzfwZu1en5tkCoJaMadqZgjsSYTdgD2Arir3KN6Z4AdEnwKfAPNB/gDyPq88xaMlfbb9+ywqA0Y0DGVT3DYTdga+jOgKRYaz4OV5K8Wf4E4T3QJ4H93lUn7Hn+K3V/T85XrqYsi6wFAAAAABJRU5ErkJggg=="
        />
      </defs>
    </svg>
  );
};

export const MailIcon = ({
  width = "16",
  height = "14",
  color = "",
}: {
  width?: string;
  height?: string;
  color?: string;
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 16 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        id="Vector"
        d="M3 3.66406L6.61325 6.0729C7.453 6.63273 8.547 6.63273 9.38675 6.0729L13 3.66406M3 12.8307H13C14.3807 12.8307 15.5 11.7114 15.5 10.3307V3.66406C15.5 2.28335 14.3807 1.16406 13 1.16406H3C1.61929 1.16406 0.5 2.28335 0.5 3.66406V10.3307C0.5 11.7114 1.61929 12.8307 3 12.8307Z"
        stroke="#005968"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  );
};

export const DesiginationCardLogo = ({
  width = "32",
  height = "32",
  color = "",
}: {
  width?: string;
  height?: string;
  color?: string;
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      xmlnsXlink="http://www.w3.org/1999/xlink"
    >
      <rect
        id="department 1"
        width="32"
        height="32"
        fill="url(#pattern0_6322_40853)"
      />
      <defs>
        <pattern
          id="pattern0_6322_40853"
          patternContentUnits="objectBoundingBox"
          width="1"
          height="1"
        >
          <use xlinkHref="#image0_6322_40853" transform="scale(0.00195312)" />
        </pattern>
        <image
          id="image0_6322_40853"
          data-name="department.png"
          width="512"
          height="512"
          preserveAspectRatio="none"
          xlinkHref="data:image/png;base64,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"
        />
      </defs>
    </svg>
  );
};

export const InOfficeIcon = ({
  width = "17",
  height = "18",
  color = "",
}: {
  width?: string;
  height?: string;
  color?: string;
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 17 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        id="Vector"
        d="M6.66634 14H3.33301C1.9523 14 0.833008 12.8807 0.833008 11.5V4C0.833008 2.61929 1.9523 1.5 3.33301 1.5H13.333C14.7137 1.5 15.833 2.61929 15.833 4V11.5C15.833 12.8807 14.7137 14 13.333 14H9.99967M6.66634 14V16.5M6.66634 14H9.99967M6.66634 16.5H9.99967M6.66634 16.5H5.83301M9.99967 16.5V14M9.99967 16.5H10.833"
        stroke="#005968"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  );
};

// need To change this icon when ui-ux provide
export const OnSiteIcon = ({
  width = "17",
  height = "18",
  color = "",
}: {
  width?: string;
  height?: string;
  color?: string;
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 128 128"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      xmlnsXlink="http://www.w3.org/1999/xlink"
    >
      <rect
        id="engineer (1) 1"
        width="128"
        height="128"
        fill="url(#pattern0_6962_41416)"
      />
      <defs>
        <pattern
          id="pattern0_6962_41416"
          patternContentUnits="objectBoundingBox"
          width="1"
          height="1"
        >
          <use xlinkHref="#image0_6962_41416" transform="scale(0.0078125)" />
        </pattern>
        <image
          id="image0_6962_41416"
          data-name="engineer (1).png"
          width="128"
          height="128"
          preserveAspectRatio="none"
          xlinkHref="data:image/png;base64,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"
        />
      </defs>
    </svg>
  );
};

export const QualificationLogo = ({
  width = "29",
  height = "26",
  color = "",
}: {
  width?: string;
  height?: string;
  color?: string;
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 29 26"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g id="elements">
        <path
          id="Vector"
          d="M1 7.66667C1 9.45571 11.8082 14.3333 14.3333 14.3333C16.8584 14.3333 27.6667 9.45571 27.6667 7.66667C27.6667 5.87763 16.8584 1 14.3333 1C11.8082 1 1 5.87763 1 7.66667Z"
          stroke="#005968"
          stroke-width="1.5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path
          id="Vector_2"
          d="M6.33398 11.6641L6.66371 19.2271C6.66863 19.3398 6.68141 19.4524 6.71187 19.5611C6.8426 20.0275 7.09563 20.4539 7.49007 20.7366C10.4561 22.8621 18.2118 22.8621 21.1779 20.7366C21.5723 20.4539 21.8254 20.0275 21.9561 19.5611C21.9866 19.4524 21.9993 19.3398 22.0043 19.2271L22.334 11.6641"
          stroke="#005968"
          stroke-width="1.5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path
          id="Vector_3"
          d="M26.417 9.66406C26.417 9.24985 26.0812 8.91406 25.667 8.91406C25.2528 8.91406 24.917 9.24985 24.917 9.66406H26.417ZM23.7622 23.3486L24.4919 23.522L23.7622 23.3486ZM27.5718 23.3486L28.3014 23.1753L27.5718 23.3486ZM25.667 9.66406H24.917V19.007H25.667H26.417V9.66406H25.667ZM23.7622 23.3486L24.4919 23.522C24.6918 22.6804 25.0344 21.9169 25.3935 21.2141C25.7303 20.555 26.1419 19.8463 26.3646 19.2825L25.667 19.007L24.9694 18.7315C24.7811 19.2082 24.4605 19.7434 24.0578 20.5315C23.6774 21.276 23.273 22.1629 23.0325 23.1753L23.7622 23.3486ZM27.5718 23.3486L28.3014 23.1753C28.061 22.1629 27.6566 21.276 27.2762 20.5315C26.8734 19.7434 26.5528 19.2082 26.3646 18.7315L25.667 19.007L24.9694 19.2825C25.1921 19.8463 25.6037 20.555 25.9405 21.2141C26.2996 21.9169 26.6421 22.6804 26.8421 23.522L27.5718 23.3486ZM26.0876 24.9974V24.2474H25.2464V24.9974V25.7474H26.0876V24.9974ZM23.7622 23.3486L23.0325 23.1753C22.8695 23.8617 22.805 24.6517 23.3553 25.2036C23.615 25.464 23.9409 25.5922 24.2463 25.6599C24.5509 25.7274 24.8927 25.7474 25.2464 25.7474V24.9974V24.2474C24.935 24.2474 24.7191 24.2283 24.5711 24.1955C24.4238 24.1628 24.4024 24.1293 24.4176 24.1445C24.4447 24.1717 24.4278 24.1846 24.4199 24.1C24.4104 23.9988 24.4213 23.8191 24.4919 23.522L23.7622 23.3486ZM27.5718 23.3486L26.8421 23.522C26.9127 23.8191 26.9236 23.9988 26.9141 24.1C26.9062 24.1846 26.8893 24.1717 26.9164 24.1445C26.9316 24.1293 26.9102 24.1628 26.7629 24.1955C26.6149 24.2283 26.399 24.2474 26.0876 24.2474V24.9974V25.7474C26.4413 25.7474 26.7831 25.7274 27.0877 25.6599C27.393 25.5922 27.719 25.464 27.9787 25.2036C28.5289 24.6517 28.4645 23.8617 28.3014 23.1753L27.5718 23.3486Z"
          fill="#005968"
        />
      </g>
    </svg>
  );
};

export const BillingPageNav = ({ color = "rgba(0, 0, 0, 0.87)" }) => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M15.6734 7.47316C15.8558 7.10127 15.7022 6.65193 15.3303 6.46951C14.9584 6.28709 14.5091 6.44068 14.3266 6.81256L12.8663 9.78961C12.4137 10.7124 11.0833 10.6673 10.6942 9.71603C9.815 7.56698 6.80955 7.46517 5.78698 9.5498L4.32665 12.5268C4.14423 12.8987 4.29782 13.3481 4.6697 13.5305C5.04158 13.7129 5.49093 13.5593 5.67335 13.1874L7.13369 10.2104C7.58632 9.28764 8.91668 9.3327 9.30584 10.284C10.185 12.433 13.1904 12.5348 14.213 10.4502L15.6734 7.47316Z"
        fill={color}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M14.4635 0.373728C13.3214 0.249985 11.8818 0.249992 10.0452 0.25H9.95475C8.11821 0.249992 6.67861 0.249985 5.53648 0.373728C4.37094 0.500006 3.42656 0.762324 2.62024 1.34815C2.13209 1.70281 1.70281 2.13209 1.34815 2.62024C0.762324 3.42656 0.500006 4.37094 0.373728 5.53648C0.249985 6.67861 0.249992 8.11821 0.25 9.95475V10.0452C0.249992 11.8818 0.249985 13.3214 0.373728 14.4635C0.500006 15.6291 0.762324 16.5734 1.34815 17.3798C1.70281 17.8679 2.13209 18.2972 2.62024 18.6518C3.42656 19.2377 4.37094 19.5 5.53648 19.6263C6.67859 19.75 8.11817 19.75 9.95465 19.75H10.0453C11.8818 19.75 13.3214 19.75 14.4635 19.6263C15.6291 19.5 16.5734 19.2377 17.3798 18.6518C17.8679 18.2972 18.2972 17.8679 18.6518 17.3798C19.2377 16.5734 19.5 15.6291 19.6263 14.4635C19.75 13.3214 19.75 11.8818 19.75 10.0453V9.95473C19.75 8.11824 19.75 6.67859 19.6263 5.53648C19.5 4.37094 19.2377 3.42656 18.6518 2.62024C18.2972 2.13209 17.8679 1.70281 17.3798 1.34815C16.5734 0.762324 15.6291 0.500006 14.4635 0.373728ZM3.50191 2.56168C4.00992 2.19259 4.66013 1.97745 5.69804 1.865C6.74999 1.75103 8.10843 1.75 10 1.75C11.8916 1.75 13.25 1.75103 14.302 1.865C15.3399 1.97745 15.9901 2.19259 16.4981 2.56168C16.8589 2.82382 17.1762 3.14111 17.4383 3.50191C17.8074 4.00992 18.0225 4.66013 18.135 5.69804C18.249 6.74999 18.25 8.10843 18.25 10C18.25 11.8916 18.249 13.25 18.135 14.302C18.0225 15.3399 17.8074 15.9901 17.4383 16.4981C17.1762 16.8589 16.8589 17.1762 16.4981 17.4383C15.9901 17.8074 15.3399 18.0225 14.302 18.135C13.25 18.249 11.8916 18.25 10 18.25C8.10843 18.25 6.74999 18.249 5.69804 18.135C4.66013 18.0225 4.00992 17.8074 3.50191 17.4383C3.14111 17.1762 2.82382 16.8589 2.56168 16.4981C2.19259 15.9901 1.97745 15.3399 1.865 14.302C1.75103 13.25 1.75 11.8916 1.75 10C1.75 8.10843 1.75103 6.74999 1.865 5.69804C1.97745 4.66013 2.19259 4.00992 2.56168 3.50191C2.82382 3.14111 3.14111 2.82382 3.50191 2.56168Z"
        fill={color}
      />
    </svg>
  );
};

export const SendApproval = ({ color = "rgba(0, 0, 0, 0.87)" }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      height="24px"
      viewBox="0 -960 960 960"
      width="24px"
      fill="#e3e3e3"
    >
      <path d="m438-437.38-64.92-64.31q-8.31-8.31-20.58-8.5-12.27-.19-21.58 9.11-8.69 8.7-8.69 21.08 0 12.38 8.69 21.08l81.77 81.77q10.85 10.84 25.31 10.84 14.46 0 25.31-10.84l165.77-165.77q8.92-8.93 8.8-20.89-.11-11.96-8.8-21.27-9.31-9.3-21.39-9.61-12.07-.31-21.38 9L438-437.38ZM336.39-112.69l-55.31-93-104.62-22.47q-13.46-2.61-21.5-14.15-8.04-11.54-6.42-25l10.23-107.61-71.15-81.39q-9.23-9.84-9.23-23.69 0-13.85 9.23-23.69l71.15-81.39-10.23-107.61q-1.62-13.46 6.42-25t21.5-14.15l104.62-22.47 55.31-93q7.23-11.84 19.69-16.15 12.46-4.31 25.31 1.31L480-820.46l98.61-41.69q12.85-5.62 25.31-1.31 12.46 4.31 19.69 16.15l55.31 93 104.62 22.47q13.46 2.61 21.5 14.15 8.04 11.54 6.42 25l-10.23 107.61 71.15 81.39q9.23 9.84 9.23 23.69 0 13.85-9.23 23.69l-71.15 81.39 10.23 107.61q1.62 13.46-6.42 25t-21.5 14.15l-104.62 22.47-55.31 93q-7.23 11.84-19.69 16.15-12.46 4.31-25.31-1.31L480-139.54l-98.61 41.69q-12.85 5.62-25.31 1.31-12.46-4.31-19.69-16.15ZM378-162l102-43.23L583.23-162 640-258l110-25.23L740-396l74-84-74-85.23L750-678l-110-24-58-96-102 43.23L376.77-798 320-702l-110 24 10 112.77L146-480l74 84-10 114 110 24 58 96Zm102-318Z" />
    </svg>
  );
};
