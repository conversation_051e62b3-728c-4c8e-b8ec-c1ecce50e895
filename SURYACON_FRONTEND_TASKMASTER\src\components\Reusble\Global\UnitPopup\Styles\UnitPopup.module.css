.unitpopup_container {
  /* width: 100%; */
  min-width: 14rem;
  padding: 10px;
  max-height: 12rem;
  border-radius: 24px;
  overflow-y: auto;
  /* backdrop-filter: blur(100px); */
  box-shadow: 0px 0px 4px 0px #91a1a1bf;
  border: 1px solid;
  border-image-source: linear-gradient(
    130.72deg,
    rgba(237, 231, 231, 0.07) -16.06%,
    rgba(251, 251, 251, 0.05) 82.03%
  );
  z-index: 1;
  position: fixed;
  /* right: 33px; */
  background: var(--main_background);
}

.customWidth {
  width: 100%;
  position: relative;
  /* Customize width here */
}

.unitpopup_list {
  list-style-type: none;
  width: 100%;
  position: relative;
  line-height: 18px;
  color: var(--text-black-60);
  background: var(--main_background);
  max-height: 9.5rem;
  overflow: auto;
  padding: 4px;
}

/* Style for the scrollbar itself */
.unitpopup_list::-webkit-scrollbar {
  width: 4px;
  height: 4px;
  /* Width of the scrollbar */
}

/* Style for the scrollbar track (the background area) */
.unitpopup_list::-webkit-scrollbar-track {
  background-color: transparent;
  /* Light grey track */
  border-radius: 10px;
  /* Rounded corners for the track */
}

/* Style for the scrollbar thumb (the draggable part) */
.unitpopup_list::-webkit-scrollbar-thumb {
  background-color: var(--primary_color);
  /* Darker thumb */
  border-radius: 10px;
}

/* Hover effect for the scrollbar thumb */
/* .unitpopup_list::-webkit-scrollbar-thumb:hover {
  background-color: #555; 
} */

.unitpopup_list li {
  padding: 0.5rem;
}

.unit {
  cursor: pointer;

  box-sizing: border-box;
  border: 1px solid transparent;
}

.unit:hover {
  border: 1px solid;
  border-image-source: linear-gradient(
    130.72deg,
    rgba(237, 231, 231, 0.07) -16.06%,
    rgba(251, 251, 251, 0.05) 82.03%
  );
  /* color: var(--primary_color); */
  /* backdrop-filter: blur(100px); */
  box-shadow: 0px 0px 4px 0px #91a1a1bf;
  border-radius: 24px;
  color: var(--primary_color);
  box-sizing: border-box;
}

.unitselected {
  border: 1px solid;
  color: var(--primary_color);
  /* backdrop-filter: blur(100px); */
  box-shadow: 0px 0px 4px 0px #91a1a1bf;
  border-radius: 24px;
  border-color: var(--primary_color);
}

/* 🍁. popup css written by abhishek opening--> */

.unitpopup_container.unit_popup_class {
  right: 1.45rem;
  left: 1.45rem;
  max-height: 15.4rem;
}

.unitpopup_list.unit_popup_class {
  max-height: 13.875rem;
  backdrop-filter: blur(100px);
  padding: 10px;
  background-color: var(--white-50-background);
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.unit_popup_class.unitpopup_list li {
  padding: 0.5rem 1rem;
  margin: 0;
}

/* 🍁. popup css written by abhishek closing-->*/
/* popup for add project form start by rattandeep singh */
/* .addprojecttypePopup {
  width: 99%;

  padding: 10px;
  max-height: 12rem;
  border-radius: 24px;
  overflow-y: auto;
  backdrop-filter: blur(100px);
  box-shadow: 0px 0px 4px 0px #91a1a1bf;
  border: 1px solid;
  border-image-source: linear-gradient(
    130.72deg,
    rgba(237, 231, 231, 0.07) -16.06%,
    rgba(251, 251, 251, 0.05) 82.03%
  );
  z-index: 1;
  position: absolute;
  top: 4rem;
  right: 0;
  left: 0.1rem;
  background: var(--main_background);
} */
/* popup for add project form end by rattandeep singh */