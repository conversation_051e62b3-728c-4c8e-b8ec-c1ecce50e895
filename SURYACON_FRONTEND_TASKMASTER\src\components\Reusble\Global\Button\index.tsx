// ---------------------------------- Author: <PERSON><PERSON> --------------------------------
import React from "react";
import styles from "./Styles/Button.module.css";
import { ButtonProps } from "../GlobalInterfaces/GlobalInterface";

const Button: React.FC<ButtonProps> = ({
  type,
  Content,
  Callback,
  width,
  height = "2.6rem",
  property = "",
}) => {
  const buttonClass =
    {
      Accept: styles.button_next_button,
      Approve: styles.button_next_button,
      Decline: styles.button_decline_button,
      Navigate: styles.button_navigate_button,
      Navigate2: styles.button_navigate_button2,
      Normal: styles.button_normal_button,
      Next: styles.button_next_button,
      Cancel: styles.button_cancel_button,
      Disable: styles.button_disable_button,
      Delete: styles.button_delete_button,
      Decline2: styles.button_decline_button2,
      EditRequest : styles.editRequest_button,
      Reason : styles.reason_button,
      ApproveMonthlyTarget: styles.monthly_target_approve_button,
      DeclineMonthlyTarget: styles.monthly_target_decline_button,
      Decline2: styles.button_decline_button2,
      EditRequest: styles.editRequest_button,
      Reason: styles.reason_button,
    }[type] || "";

  const handleClick = (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
    if (Callback && type !== "Normal") {
      Callback(e);
    }
  };
  return (
    <div
      className={`${buttonClass} ${styles.button_Main} ${styles[property]}`}
      onClick={(e) => {
        e.stopPropagation();
        handleClick(e);
      }}
      // style={{ width , height: "2.6rem"}}
      style={{ width, height }}
    >
      {Content}
    </div>
  );
};

export default Button;