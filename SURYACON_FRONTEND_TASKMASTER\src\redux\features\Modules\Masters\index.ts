import { createSlice, PayloadAction } from "@reduxjs/toolkit";

interface Masters {
  _id: string;
  isDeleted?: boolean;
  [key: string]: any;
}
interface ToolData {
  data: Masters[];
  consumableCount?: number;
  returnableCount?: number;
  [key: string]: any;
}

interface MastersState {
  fetchedData: Masters[];
  fetchedToolCatData: Masters[];
  fetchedManpower: Masters[];
  fetchedMaterial: Masters[];
  fetchedMachinary: Masters[];
  fetchedTaskCat: Masters[];
  searchedData: Masters[];
  categoryId: string;
  fetchedToolData: ToolData;
  newlyAddedIds: string[];
  fetchedDepartment: Masters[];
  // fetchedDepartmentDes: Masters[];
  searchedToolData: any;
}

const initialState: MastersState = {
  fetchedData: [],
  fetchedToolCatData: [],
  fetchedManpower: [],
  fetchedMaterial: [],
  fetchedMachinary: [],
  fetchedTaskCat: [],
  categoryId: "",
  fetchedToolData: { data: [] },
  newlyAddedIds: [],
  searchedData: [],
  fetchedDepartment: [],
  searchedToolData: { data: [] },
};

// ===============================
// Slice
// ===============================
const masterSlice = createSlice({
  name: "masters",
  initialState,
  reducers: {
    setFetchedMasters(
      state,
      action: PayloadAction<{ data: Masters[]; page: number; type: string }>
    ) {
      let usableState: string = "";

      switch (action.payload.type) {
        case "manpower":
          usableState = "fetchedManpower";
          break;
        case "material":
          usableState = "fetchedMaterial";
          break;
        case "tool":
          usableState = "fetchedToolCatData";
          break;
        case "machinary":
          usableState = "fetchedMachinary";
          break;
        case "taskcategory":
          usableState = "fetchedTaskCat";
          break;
        case "masterDepartment":
          usableState = "fetchedDepartment";
          break;
        default:
          usableState = "";
          break;
      }

      const { data: newData, page } = action.payload;

      const isPageOne = page === 1;
      const addedIdsSet = new Set(state.newlyAddedIds);

      if (isPageOne) {
        const newItems = newData.filter((item) => addedIdsSet.has(item._id));
        const rest = newData.filter((item) => !addedIdsSet.has(item._id));
        (state as any)[usableState] = [...newItems, ...rest];
      } else {
        const existingIds = new Set(
          (state as any)[usableState].map((item: any) => item._id)
        );
        const filteredNewData = newData.filter(
          (item) => !existingIds.has(item._id)
        );
        (state as any)[usableState] = [
          ...(state as any)[usableState],
          ...filteredNewData,
        ];
      }
      console.log((state as any)[usableState], "data fetched of metr");
    },

    setFetchedMastersDesignation(
      state,
      action: PayloadAction<{ data: Masters[]; page: number }>
    ) {
      const { data: newData, page } = action.payload;
      const isPageOne = page === 1;
      const addedIdsSet = new Set(state.newlyAddedIds);

      if (isPageOne) {
        const newItems = newData.filter((item) => addedIdsSet.has(item._id));
        const rest = newData.filter((item) => !addedIdsSet.has(item._id));
        state.fetchedData = [...newItems, ...rest];
      } else {
        const existingIds = new Set(
          state.fetchedData.map((item: any) => item._id)
        );
        const filteredNewData = newData.filter(
          (item) => !existingIds.has(item._id)
        );
        state.fetchedData = [...state.fetchedData, ...filteredNewData];
      }
      console.log(state.fetchedData, "data fetched of metr");
    },

    //  ==============================================================================
    //         clear MasterSlice STate
    //  ==============================================================================
    clearFetchedMasters(state) {
      state.fetchedData = [];
      state.categoryId = "";
      state.fetchedToolData = { data: [] };
      state.fetchedToolCatData = [];
      state.fetchedMaterial = [];
      state.fetchedTaskCat = [];
      state.newlyAddedIds = [];
      state.fetchedManpower = [];
      state.fetchedMachinary = [];
      state.searchedData = [];
      state.fetchedDepartment = [];
      state.searchedToolData = [];
    },

    SetCategoryId(state, action: PayloadAction<string>) {
      state.categoryId = action.payload;
    },

    setToolData(state, action: PayloadAction<ToolData>) {
      state.fetchedToolData = action.payload;
    },

    setSearchData(state, action: PayloadAction<Masters[]>) {
      state.searchedData = action.payload;
    },

    setSearchToolData(state, action: PayloadAction<any>) {
      state.searchedToolData = action.payload;
    },

    //  ==============================================================================
    //      update and delete searched data
    //  ==============================================================================

    deleteOrUpdateFetchedMastersSearch(
      state,
      action: PayloadAction<Masters[]>
    ) {
      const updatedData = [...state.searchedData];
      action.payload?.forEach((element) => {
        const index = updatedData.findIndex((item) => item._id === element._id);
        if (element?.isDeleted) {
          if (index !== -1) updatedData.splice(index, 1);
        } else {
          if (index !== -1) {
            updatedData[index] = element;
          }
        }
      });
      state.searchedData = updatedData;
      console.log(JSON.parse(JSON.stringify(updatedData)), "fasfjaslk");
      console.log(JSON.parse(JSON.stringify(state.searchedData)), "adsfjasf");
    },

    //  ==============================================================================
    //      Add, update and delete categories data
    //  ==============================================================================

    addOrUpdateFetchedMastersCategories(
      state,
      action: PayloadAction<{ data: Masters[]; type: string }>
    ) {
      let usableState: string = "";

      switch (action.payload.type) {
        case "manpower":
          usableState = "fetchedManpower";
          break;
        case "material":
          usableState = "fetchedMaterial";
          break;
        case "tool":
          usableState = "fetchedToolCatData";
          break;
        case "machinary":
          usableState = "fetchedMachinary";
          break;
        case "taskcategory":
          usableState = "fetchedTaskCat";
          break;
        case "masterDepartment":
          usableState = "fetchedDepartment";
          break;
        default:
          usableState = "";
          break;
      }
      console.log(action.payload, "asdfjkjafd");

      const updatedData = usableState ? [...(state as any)[usableState]] : [];
      action.payload?.data?.forEach((element) => {
        const index = updatedData.findIndex((item) => item._id === element._id);

        if (element?.isDeleted) {
          if (index !== -1) updatedData.splice(index, 1);
          state.newlyAddedIds = state.newlyAddedIds.filter(
            (id) => id !== element._id
          );
        } else {
          if (index !== -1) {
            updatedData[index] = element;
          } else {
            updatedData.unshift(element);
            state.newlyAddedIds.unshift(element._id);
          }
        }
      });
      console.log(updatedData, "asadf");
      (state as any)[usableState] = updatedData;
    },
    //  ================================================================================================================
    //       add update and delete designation  by category id
    //  ================================================================================================================
    addOrUpdateFetchedMasters(state, action: PayloadAction<Masters[]>) {
      const updatedData = [...state.fetchedData];
      const fields = [
        "machineryCategoryId",
        "materialCategoryId",
        "manpowerCategoryId",
        "categoryId",
        "DepartmentId",
      ];

      console.log(action.payload, "asdfasdfasfsd");
      action.payload.forEach((element) => {
        console.log("matched2>>", element);
        const index = updatedData.findIndex((item) => item._id === element._id);
        console.log("matched2", state.categoryId, fields);
        const matchesCategory = fields.some(
          (field) => element?.[field] === state.categoryId
        );
        console.log(matchesCategory, "matchedsCategory");
        if (!matchesCategory) return;

        if (element?.isDeleted) {
          if (index !== -1) updatedData.splice(index, 1);
        } else {
          if (index !== -1) {
            updatedData[index] = element;
          } else {
            console.log(element, "yess");
            updatedData.unshift(element);
          }
        }
      });

      state.fetchedData = updatedData;
    },

    //  ==============================================================================
    //       add update and delete only Tools
    //  ==============================================================================

    addOrUpdateFetchedTools(state, action: PayloadAction<any>) {
      const incomingData = Array.isArray(action.payload.data)
        ? action.payload.data
        : action.payload.data
        ? [action.payload.data]
        : [];

      const currentData = Array.isArray(state.fetchedToolData.data)
        ? [...state.fetchedToolData.data]
        : [];

      incomingData.forEach((element: Masters) => {
        const index = currentData.findIndex((tool) => tool._id === element._id);
        const isMatch = action.payload.categoryId === state.categoryId;

        if (!isMatch) return;

        state.fetchedToolData.consumableCount = action.payload.consumableCount;
        state.fetchedToolData.returnableCount = action.payload.returnableCount;

        if (element?.isDeleted) {
          if (index !== -1) currentData.splice(index, 1);
        } else {
          index !== -1
            ? (currentData[index] = element)
            : currentData.unshift(element);
        }
      });

      state.fetchedToolData.data = currentData;
    },

    //  ==============================================================================
    //       update and delete only Tools searched
    //  ==============================================================================

    deleteOrUpdateFetchedMastersSearchTool(state, action: PayloadAction<any>) {
      const incomingData = Array.isArray(action.payload.data)
        ? action.payload.data
        : action.payload.data
        ? [action.payload.data]
        : [];
      const currentData = [...state.searchedToolData];

      incomingData.forEach((element: Masters) => {
        const index = currentData.findIndex((tool) => tool._id === element._id);
        const isMatch = action.payload.categoryId === state.categoryId;

        if (!isMatch) return;
        console.log(isMatch, "matcjhed");

        if (element?.isDeleted) {
          if (index !== -1) currentData.splice(index, 1);
        } else {
          index !== -1 ? (currentData[index] = element) : null;
        }
      });
      console.log(
        JSON.parse(JSON.stringify(state.searchedToolData)),
        "fsdfsdf"
      );
      state.searchedToolData = currentData;
    },
  },
});

export const {
  setFetchedMasters,
  clearFetchedMasters,
  SetCategoryId,
  setToolData,
  addOrUpdateFetchedMasters,
  addOrUpdateFetchedTools,
  addOrUpdateFetchedMastersCategories,
  deleteOrUpdateFetchedMastersSearch,
  deleteOrUpdateFetchedMastersSearchTool,
  setFetchedMastersDesignation,
  setSearchData,
  setSearchToolData,
} = masterSlice.actions;

export const selectFetchedMaterials = (state: any) =>
  state.masterSlice.fetchedMaterial;
export const selectFetchedManpower = (state: any) =>
  state.masterSlice.fetchedManpower;
export const selectFetchedMachinary = (state: any) =>
  state.masterSlice.fetchedMachinary;
export const selectFetchedToolCat = (state: any) =>
  state.masterSlice.fetchedToolCatData;
export const selectFetchedTaskCat = (state: any) =>
  state.masterSlice.fetchedTaskCat;
export const selectFetchedTools = (state: any) =>
  state.masterSlice.fetchedToolData;
export const selectFetchedSearch = (state: any) =>
  state.masterSlice.searchedData;
export const selectFetchedDepartment = (state: any) =>
  state.masterSlice.fetchedDepartment;
export const selectFetchedSearchTool = (state: any) =>
  state.masterSlice.searchedToolData;

export default masterSlice.reducer;

//  ==============================================================================
//    Created by Aman 🐱‍👤
//  ==============================================================================
