import { useDispatch, useSelector } from "react-redux";
import { RootState, store } from "../../../../../../redux/store";
import { useEffect, useState } from "react";
import {
  settaskChangeAPiFlag,
  taskFormReducer,
  updateTaskData,
} from "../../../../../../redux/features/Modules/TaskMaster/Slices/TaskMasterSlice";
import {
  closePopup,
  openPopup,
} from "../../../../../../redux/features/Modules/Reusble/popupSlice";
import TaskWorkInstructionsPopup, {
  requiredthings,
} from "../TaskWorkInstructionPopup/TaskWorkinstructionsPopup";
import WorkInstructionsPopup, {
  PhotoSection,
} from "../../../../../../components/Reusble/TaskMaster/WorkInstructionsPopup";
import { DeleteIcon, SuryconLogo } from "../../../../../../assets/icons";
import TCRpopup from "../../../../../../components/Reusble/Global/TCRpopup";
import AddToolTip from "../../../../../../components/Reusble/Global/AddToolTip";
import TaskTcrPopup from "../TaskCreationTcrPopup/TaskTcrPopup";
import ControlPlanpopup from "../../../../../../components/Reusble/TaskMaster/ControlPlanpopup";
import FMEApopup from "../../../../../../components/Reusble/TaskMaster/FMEApopup";
import { DeletePopup } from "../../../../../../components/Reusble/Global/DeletePopup";
import styles from "../../Styles/TaskCreationForm.module.css";
import {
  ControlPlanDetails,
  FailureModeDetails,
} from "../../../../../../interfaces/Modules/TaskMaster/TaskMasterInterface/TaskMaster";
import { settaskWorkInstructionDelete } from "../../../../../../redux/features/Modules/TaskMaster/Slices/WorkInstructionSlice";
import { useAppSelector } from "../../../../../../redux/hooks/Modules/Reduxhooks/ReduxHooks";
import { useGetSubTaskRouteByTaskIdQuery } from "../../../../../../redux/api/Modules/TaskMaster/TaskMasterapi";
import { useParams } from "react-router-dom";
import { saveSyncData } from "../../../../../../Backup/BackupFunctions/BackupFunctions";
import {
  fileTypeMapper,
  getFileName,
  isBase64,
} from "../../../../../../functions/functions";
import { useToast } from "../../../../../../hooks/ToastHook";
import { isEqual } from "lodash";

const TaskCreationMethod: React.FC = ({ isEdit = false }) => {
  const dispatch = useDispatch();
  const { popups } = useSelector((state: RootState) => state.popup);
  const params = useParams();
  const { taskId } = params;
  const [popupIdParent, setPopupIdParent] = useState<string | null>(null);
  const [categortDataParent, setCategoryDataParent] = useState<any>(null);
  const [deleteIdParent, setDeleteIdParent] = useState<string>();
  const handleSetPopupId = (id: string) => {
    setPopupIdParent(id);
  };

  const showToast = useToast();

  const TaskData = useSelector(
    (state: RootState) => state.taskForm.currentSubtaskData
  );

  const isDeletedNext = useAppSelector(
    (state) => state.isDeletedSLice.isDeletedNext
  );

  const [controlPlanInitialState, setControlPlanInitialState] = useState<
    ControlPlanDetails | undefined
  >({
    _id: "",
    description: "",
  });

  const failureModeData = TaskData?.MethodId?.Failuremode;

  const [fmeaInitialState, setFmeaInitialState] =
    useState<FailureModeDetails>();

  // interface TCRDataType {
  //   id: string;
  //   data: {
  //     description: string;
  //     file: { name: string; type: string } | null;
  //     category: string;
  //     photoDetails?: {
  //       id: number;
  //       photo: string | null;
  //       referenceDetail: string;
  //     }[];
  //   };
  // }

  const handleFmeaDataChange = (data: {
    _id: number | string;
    Description: string;
    solution: string;
    severity: string;
  }) => {
    if (fmeaInitialState) {
      dispatch(
        updateTaskData({
          ...TaskData,
          MethodId: {
            ...TaskData?.MethodId,
            Failuremode: failureModeData?.map((item) => {
              return item._id === data._id ? data : item;
            }),
          },
        })
      );

      saveSyncData(
        {
          ...TaskData,
          MethodId: {
            ...TaskData?.MethodId,
            Failuremode: failureModeData?.map((item) => {
              return item._id === data._id ? data : item;
            }),
          },
        },
        "time",
        "TaskForm"
      );

      setFmeaInitialState(undefined);
      showToast({
        messageContent: "Quality Ensuring Measures Updated Successfully!",
        type: "success",
      });

      dispatch(settaskChangeAPiFlag(true));
      return;
    }
    dispatch(
      updateTaskData({
        ...TaskData,

        MethodId: {
          ...TaskData?.MethodId,
          Failuremode: [...(failureModeData || []), data],
        },
      })
    );

    saveSyncData(
      {
        ...TaskData,
        _id: TaskData?._id || taskId,
        MethodId: {
          ...TaskData?.MethodId,
          Failuremode: [...(failureModeData || []), data],
        },
      },
      "time",
      "TaskForm"
    );

    showToast({
      messageContent: "Quality Ensuring Measures Added Successfully!",
      type: "success",
    });
    dispatch(settaskChangeAPiFlag(true));
  };

  const handleToggleDropdown = (name: string) => {
    dispatch(openPopup(name));
  };

  const workInstructionData = useAppSelector(
    (state) =>
      state.taskForm.currentSubtaskData?.MethodId?.work_instruction_id ?? []
  );
  const taskClosingRequirementData =
    TaskData?.MethodId?.task_closing_requirement;

  //formating work instruction data
  const formattedWorkInstructionData = workInstructionData?.map(
    (data: any) => ({
      id: data?._id,
      data: {
        description: data?.Description,
        file: data?.file,
        category: data?.optionselected,
        photoDetails: data?.photoref?.photos?.map(
          (photo: any, index: number) => ({
            id: index + 1,
            fileName: photo.fileName,
            photo: photo.photo,
            referenceDetail: photo.details,
          })
        ),
      },
    })
  );

  //this is for the these for mentioned properties inside the work instructions as they are entered after adding work instruction's main body
  const [popupCategoryData, setPopupCategoryData] = useState<{
    [key: string]: {
      Manpower: requiredthings[];
      Machinery: requiredthings[];
      Tools: requiredthings[];
      Materials: requiredthings[];
    };
  }>({});

  //setting the work instructions material, machinary, tools, manpower state
  useEffect(() => {
    if (!Array.isArray(workInstructionData) || workInstructionData.length === 0)
      return;

    console.log("sync check", workInstructionData);

    // Prepare new popup data
    const newPopupCategoryData = workInstructionData.reduce(
      (acc: any, data) => {
        acc[`popup-${data._id}`] = {
          Manpower: data?.manpowerId || [],
          Machinery: data?.machinaryId || [],
          Tools: data?.toolsId || [],
          Materials: data?.materialId || [],
        };
        return acc;
      },
      {}
    );

    // Use current popupCategoryData directly, not via callback
    if (!isEqual(popupCategoryData, newPopupCategoryData)) {
      console.log("Updating popupCategoryData23");
      setPopupCategoryData(newPopupCategoryData);
    } else {
      console.log("No change in popupCategoryData");
    }
  }, [workInstructionData]);

  //formatting task closin  g requirement data
  const formattedTaskClosingRequirementData =
    taskClosingRequirementData?.map((data) => ({
      id: data?._id,
      data: {
        description: data?.Description,
        file: data?.file,
        category: data?.optionselected,
        photoDetails: data?.photoref?.photos?.map((photo, index) => ({
          id: index + 1,
          fileName: photo.fileName,
          photo: photo.photo,
          referenceDetail: photo.details,
        })),
      },
    })) || [];

  console.log(formattedTaskClosingRequirementData, "this is taskclosign reui");

  const controlPlandata = TaskData?.MethodId?.Controlplan ?? [];

  //will remove when working on this
  // const [tcrData, setTcrData] = useState<TCRDataType[]>(
  //   formattedTaskClosingRequirementData || []
  // );

  //this runs whenever work instructions are added
  const handleWorkInstructionsSubmit = (data: {
    description: string;
    file: { name: string; type: string } | null;
    category: string;
    photoDetails?: {
      id: number;
      photo: string | null;
      fileName: string;
      referenceDetail: string;
    }[];
  }) => {
    const newId = String(Date.now());

    // Ensure work_instruction_id is always an array
    const workInstructionIdArray = Array.isArray(
      TaskData?.MethodId?.work_instruction_id
    )
      ? TaskData?.MethodId?.work_instruction_id
      : [];

    dispatch(
      updateTaskData({
        ...TaskData,
        MethodId: {
          ...TaskData?.MethodId,
          work_instruction_id: [
            ...workInstructionIdArray,
            {
              photoref: {
                photos:
                  data.photoDetails && Array.isArray(data.photoDetails)
                    ? data.photoDetails.map((photo) => ({
                        id: photo.id.toString(),
                        fileName: photo.fileName,
                        details: photo.referenceDetail,
                        photo: photo.photo as string,
                      }))
                    : [],
              },
              file: data.file,
              _id: newId,
              Description: data.description,
              optionselected: data.category,
              materialId: [],
              manpowerId: [],
              toolsId: [],
              machinaryId: [],
            },
          ],
        },
      })
    );

    saveSyncData(
      {
        ...TaskData,
        _id: TaskData?._id || taskId,
        MethodId: {
          ...TaskData?.MethodId,
          work_instruction_id: [
            ...workInstructionIdArray,
            {
              photoref: {
                photos:
                  data.photoDetails && Array.isArray(data.photoDetails)
                    ? data.photoDetails.map((photo) => ({
                        id: photo.id.toString(),
                        fileName: photo.fileName,
                        details: photo.referenceDetail,
                        photo: photo.photo as string,
                      }))
                    : [],
              },
              file: data.file,
              _id: newId,
              Description: data.description,
              optionselected: data.category,
              materialId: [],
              manpowerId: [],
              toolsId: [],
              machinaryId: [],
            },
          ],
        },
      },
      "time",
      "TaskForm"
    );

    dispatch(settaskChangeAPiFlag(true));
    showToast({
      messageContent: "Work Instruction Added Successfully!",
      type: "success",
    });
  };

  const handleTcrSubmit = (data: {
    description: string;
    file: { name: string; type: string } | null;
    category: string;
    photoDetails?: {
      id: number;
      fileName: string;
      photo: string | null;
      referenceDetail: string;
    }[];
  }) => {
    const newId = String(Date.now());

    // Ensure work_instruction_id is always an array
    const taskClosingRequirementsArray = Array.isArray(
      TaskData?.MethodId?.task_closing_requirement
    )
      ? TaskData?.MethodId?.task_closing_requirement
      : [];

    dispatch(
      updateTaskData({
        ...TaskData,
        MethodId: {
          ...TaskData?.MethodId,
          task_closing_requirement: [
            ...taskClosingRequirementsArray,
            {
              photoref: {
                photos:
                  data.photoDetails && Array.isArray(data.photoDetails)
                    ? data.photoDetails.map((photo) => ({
                        id: photo.id.toString(),
                        fileName: photo.fileName,
                        details: photo.referenceDetail,
                        photo: photo.photo as string,
                      }))
                    : [],
              },
              file: data.file,
              _id: newId,
              Description: data.description,
              optionselected: data.category,
            },
          ],
        },
      })
    );

    saveSyncData(
      {
        ...TaskData,
        _id: TaskData?._id || taskId,
        MethodId: {
          ...TaskData?.MethodId,
          task_closing_requirement: [
            ...taskClosingRequirementsArray,
            {
              photoref: {
                photos:
                  data.photoDetails && Array.isArray(data.photoDetails)
                    ? data.photoDetails.map((photo) => ({
                        id: photo.id.toString(),
                        fileName: photo.fileName,
                        details: photo.referenceDetail,
                        photo: photo.photo as string,
                      }))
                    : [],
              },
              file: data.file,
              _id: newId,
              Description: data.description,
              optionselected: data.category,
            },
          ],
        },
      },
      "time",
      "TaskForm"
    );

    showToast({
      messageContent: "Task Closing Requirement Added Successfully!",
      type: "success",
    });
    dispatch(settaskChangeAPiFlag(true));
  };

  const [requiredThingsDelete, setRequiredThingsDelete] = useState<
    FailureModeDetails | undefined
  >();
  console.log(requiredThingsDelete, "this is data for deletetion");
  const [requiredThingsDeleteName, setRequiredThingsDeleteName] =
    useState<string>();
  const [deleteId, setDeleteId] = useState<string>();

  console.log("requiredThingsDelete--", requiredThingsDelete);
  //deep comparison
  const deepEqual = (a: any, b: any): boolean => {
    if (a === b) return true;

    if (
      typeof a !== "object" ||
      typeof b !== "object" ||
      a === null ||
      b === null
    )
      return false;

    if (Array.isArray(a) !== Array.isArray(b)) return false;

    if (Array.isArray(a)) {
      if (a.length !== b.length) return false;
      return a.every((item, index) => deepEqual(item, b[index]));
    }

    const keysA = Object.keys(a);
    const keysB = Object.keys(b);
    if (keysA.length !== keysB.length) return false;

    return keysA.every((key) => deepEqual(a[key], b[key]));
  };

  console.log("taskkkkkkk dataaaaa34", TaskData);

  //this useEffect is for updating the changed properties mentioned in the above state
  // useEffect(() => {
  //   const taskLatest = store.getState().taskForm.currentSubtaskData;
  //   if (
  //     !taskLatest ||
  //     !taskLatest.MethodId ||
  //     !taskLatest._id ||
  //     Object.keys(popupCategoryData).length !== workInstructionData.length
  //   )
  //     return;

  //   const updatedWorkInstructions =
  //     taskLatest.MethodId?.work_instruction_id?.map((item) => {
  //       const newItem = workInstructionData.find(
  //         (data: any) => data._id === item._id
  //       );

  //       if (newItem) {
  //         return {
  //           ...item,
  //           manpowerId:
  //             popupCategoryData[`popup-${newItem._id}`]?.Manpower ??
  //             item.manpowerId ??
  //             [],
  //           machinaryId:
  //             popupCategoryData[`popup-${newItem._id}`]?.Machinery ??
  //             item.machinaryId ??
  //             [],
  //           toolsId:
  //             popupCategoryData[`popup-${newItem._id}`]?.Tools ??
  //             item.toolsId ??
  //             [],
  //           materialId:
  //             popupCategoryData[`popup-${newItem._id}`]?.Materials ??
  //             item.materialId ??
  //             [],
  //         };
  //       }
  //       return item;
  //     });

  //   if (
  //     !deepEqual(
  //       updatedWorkInstructions,
  //       taskLatest.MethodId.work_instruction_id
  //     )
  //   ) {
  //     const updatedTaskData = {
  //       ...taskLatest,
  //       MethodId: {
  //         ...taskLatest.MethodId,
  //         work_instruction_id: updatedWorkInstructions,
  //       },
  //     };

  //     dispatch(updateTaskData(updatedTaskData));

  //     //saving data in localdb
  //     saveSyncData(updatedTaskData, "time", "TaskForm");

  //     //to call the api
  //     // dispatch(settaskChangeAPiFlag(true));
  //   }
  // }, [popupCategoryData, TaskData, workInstructionData]);

  //this is updating the popupcategory state / not sure
  const handleUpdateCategoryData = (
    popupId: string,
    category: string,
    newData: requiredthings[]
  ) => {
    console.log(popupId, category, newData, "check all three things here");
    setPopupCategoryData((prev) => ({
      ...prev,
      [popupId]: {
        ...(prev[popupId] || {
          Manpower: [],
          Machinery: [],
          Tools: [],
          Materials: [],
        }),
        [category]: newData,
      },
    }));
    dispatch(settaskChangeAPiFlag(true));
  };

  interface WorkInstructionsData {
    id?: string;
    description: string;
    file: { name: string; type: string } | null;
    category: string;
    photoDetails: PhotoSection[];
  }

  const handlePhotoPopupClose = () => {
    dispatch(closePopup(getPopupId("workinstructionsphoto")));
  };
  const [initaldata, setInitalData] = useState<
    WorkInstructionsData | undefined
  >();

  const getPopupId = (category: string) => `${initaldata?.id}-${category}`;

  return (
    <div className={styles.subtask_creation_method_container}>
      <div className={styles.subtask_creation_designation_header}>
        <SuryconLogo />
        <h3>Method</h3>
      </div>
      <div style={{ display: "flex" }}>
        <div className={styles.taskcreation_column}>
          <div style={{ minWidth: "50px", position: "relative" }}>
            <AddToolTip
              label="Work Instructions"
              isEdit={isEdit}
              onClick={(event) => {
                event.stopPropagation();
                handleToggleDropdown("WorkInstructions");
              }}
              className={`${styles.subtask_creation_add_tooltip} ${styles.three_cols}`}
              className2={
                TaskData?.MethodId?.work_instruction_id?.length > 0
                  ? "widthfull_1rem"
                  : ""
              }
            />

            {/* Render Work Instructions Popups Dynamically */}
            {(formattedWorkInstructionData || [])?.map((popup, idx) => (
              <div style={{ position: "relative" }}>
                {/* <>{popupCategoryData[`popup-${popup.id}`]}</> */}

                <TaskWorkInstructionsPopup
                  key={popup.id}
                  workId={popup.id}
                  id={String(idx)}
                  isEdit={isEdit}
                  handleDelete={() => {
                    const workinstruction =
                      TaskData?.MethodId?.work_instruction_id?.find(
                        (item) => item?._id === popup.id
                      );

                    setDeleteId("work_instruction_id");
                    setRequiredThingsDeleteName("Work Instruction");
                    setRequiredThingsDelete(workinstruction as any);
                    setTimeout(() => {
                      dispatch(openPopup("DeleteMethodId"));
                    }, 400);
                  }}
                  initaldata={
                    initaldata || {
                      id: "asdf",
                      description: "",
                      file: null,
                      category: "",
                      photoDetails: [],
                    }
                  }
                  onClick={(e: any) => {
                    e.stopPropagation();

                    if (!isDeletedNext) {
                      setInitalData({
                        id: popup.id,
                        ...popup.data,
                        photoDetails: popup.data.photoDetails || [],
                      });
                      dispatch(
                        openPopup(`popup-${popup.id}-workinstructionsphoto`)
                      );
                    }
                  }}
                  popupId={`popup-${popup.id}`}
                  popupIdParent={popupIdParent}
                  setCategoryDataParent={setCategoryDataParent}
                  categortDataParent={categortDataParent}
                  setPopupIdParent={handleSetPopupId}
                  setDeleteIdParent={setDeleteIdParent}
                  deleteIdParent={deleteIdParent}
                  data={{
                    ...popup.data,
                    photoDetails: popup.data.photoDetails || [],
                  }}
                  categoryData={
                    popupCategoryData[`popup-${popup.id}`] || {
                      Manpower: [],
                      Machinery: [],
                      Tools: [],
                      Materials: [],
                    }
                  }
                  onUpdateCategoryData={handleUpdateCategoryData}
                  //this is updating the work instructions
                  onUpdateWorkInstructions={(updatedData) => {
                    const updatedWorkInstructions =
                      formattedWorkInstructionData.map((e) => {
                        if (e.id === updatedData.id) {
                          return {
                            ...e,
                            data: {
                              ...updatedData,
                              photoDetails: updatedData.photoDetails || [],
                            },
                          };
                        }
                        return e;
                      });

                    dispatch(
                      updateTaskData({
                        ...TaskData,
                        MethodId: {
                          ...TaskData?.MethodId,
                          work_instruction_id: updatedWorkInstructions.map(
                            (e) => ({
                              photoref: {
                                photos:
                                  e?.data.photoDetails
                                    ?.filter(
                                      (photo: { photo: string | null }) =>
                                        photo.photo !== null
                                    )
                                    .map(
                                      (photo: {
                                        id: number;
                                        fileName: string;
                                        referenceDetail: string;
                                        photo: string | null;
                                      }) => ({
                                        id: photo.id.toString(),
                                        fileName: photo.fileName,
                                        details: photo.referenceDetail,
                                        photo: photo.photo as string,
                                      })
                                    ) || [],
                              },
                              file: e?.data.file,
                              _id: e?.id,
                              materialId:
                                popupCategoryData[`popup-${e?.id}`]
                                  ?.Materials || [], //this might a bit confusing ask me directly
                              optionselected: e?.data.category,
                              Description: e?.data.description,
                              machinaryId:
                                popupCategoryData[`popup-${e?.id}`]
                                  ?.Machinery || [],
                              toolsId:
                                popupCategoryData[`popup-${e?.id}`]?.Tools ||
                                [],
                              manpowerId:
                                popupCategoryData[`popup-${e?.id}`]?.Manpower ||
                                [],
                            })
                          ),
                        },
                      })
                    );

                    saveSyncData(
                      {
                        ...TaskData,
                        _id: TaskData?._id || taskId,
                        MethodId: {
                          ...TaskData?.MethodId,
                          work_instruction_id: updatedWorkInstructions.map(
                            (e) => ({
                              photoref: {
                                photos:
                                  e?.data.photoDetails
                                    ?.filter(
                                      (photo: { photo: string | null }) =>
                                        photo.photo !== null
                                    )
                                    .map(
                                      (photo: {
                                        id: number;
                                        fileName: string;
                                        referenceDetail: string;
                                        photo: string | null;
                                      }) => ({
                                        id: photo.id.toString(),
                                        fileName: photo.fileName,
                                        details: photo.referenceDetail,
                                        photo: photo.photo as string,
                                      })
                                    ) || [],
                              },
                              file: e?.data.file,
                              _id: e?.id,
                              materialId:
                                popupCategoryData[`popup-${e?.id}`]
                                  ?.Materials || [], //this might a bit confusing ask me directly
                              optionselected: e?.data.category,
                              Description: e?.data.description,
                              machinaryId:
                                popupCategoryData[`popup-${e?.id}`]
                                  ?.Machinery || [],
                              toolsId:
                                popupCategoryData[`popup-${e?.id}`]?.Tools ||
                                [],
                              manpowerId:
                                popupCategoryData[`popup-${e?.id}`]?.Manpower ||
                                [],
                            })
                          ),
                        },
                      },
                      "time",
                      "TaskForm"
                    );

                    showToast({
                      messageContent: "Work Instruction Updated Successfully!",
                      type: "success",
                    });
                    dispatch(settaskChangeAPiFlag(true));
                    handlePhotoPopupClose();
                  }}
                />
              </div>
            ))}

            {/* Main Popup for Submitting Data */}
            {popups["WorkInstructions"] && (
              <WorkInstructionsPopup
                onCancel={() => dispatch(closePopup("WorkInstructions"))}
                onSubmit={(data) => {
                  handleWorkInstructionsSubmit(data);
                  setTimeout(() => {
                    dispatch(closePopup("WorkInstructions"));
                  }, 400);
                }}
              />
            )}

            {popups["Tcrphoto"] && (
              <TCRpopup
                onCancel={() => dispatch(closePopup("Tcrphoto"))}
                onSubmit={(data) => {
                  handleTcrSubmit(data);
                  setTimeout(() => {
                    dispatch(closePopup("Tcrphoto"));
                  }, 400);
                }}
              />
            )}
          </div>

          <div style={{ minWidth: "50px", position: "relative" }}>
            <AddToolTip
              label="Quality Ensuring Measures"
              onClick={() => {
                setFmeaInitialState(undefined);
                handleToggleDropdown("FMEA");
              }}
              isEdit={isEdit}
              className={`${styles.subtask_creation_add_tooltip} ${styles.three_cols}`}
              className2={failureModeData?.length! > 0 ? "widthfull_1rem" : ""}
            />
            <div
              style={{ display: "flex", flexDirection: "column", gap: "1rem" }}
            >
              {" "}
              {[...(failureModeData ?? [])]
                .sort(
                  (a, b) => Number(b.severity || 0) - Number(a.severity || 0)
                )
                .map((item, index) => (
                  <div>
                    <div
                      key={index}
                      className={styles.fmea_data_container}
                      onClick={
                        isEdit
                          ? () => {
                              if (!isDeletedNext) {
                                setFmeaInitialState(item);
                                dispatch(openPopup("FMEA"));
                              }
                            }
                          : () => {}
                      }
                      style={{ position: "relative" }}
                    >
                      <div className={styles.fmea_tooltip_solution}>
                        <div className={styles.fmea_tooltip_solution_container}>
                          <h4>
                            <span className={styles.fmea_tooltip_description}>
                              Description:{" "}
                            </span>
                            {item.Description || "No description provided"}
                          </h4>
                        </div>
                        <div className={styles.fmea_tooltip_severity_container}>
                          <p>Severity</p>
                          <p>{item.severity || "(1-10)"}</p>
                        </div>
                      </div>
                      <div className={styles.fmea_tooltip}>
                        <h4>
                          <span className={styles.fmea_tooltip_Solution}>
                            Solution:{" "}
                          </span>
                          {item.solution || "No solution provided"}
                        </h4>
                      </div>

                      {isEdit && (
                        <div
                          className={styles.delete_icon_tooltip}
                          onClick={(e) => {
                            e.stopPropagation();
                            setDeleteId("Failuremode");
                            setRequiredThingsDeleteName("Failure Mode");
                            setRequiredThingsDelete(item);
                            setTimeout(() => {
                              dispatch(openPopup("DeleteMethodId"));
                            }, 400);
                          }}
                        >
                          <DeleteIcon />
                        </div>
                      )}
                    </div>
                  </div>
                ))}
            </div>

            {popups["FMEA"] && (
              <div className={styles.fmea_container}>
                <FMEApopup
                  onCancel={() => dispatch(closePopup("FMEA"))}
                  onSubmit={handleFmeaDataChange}
                  initialData={fmeaInitialState}
                />
              </div>
            )}
            {popups["DeleteMethodId"] && (
              <DeletePopup
                isOpen={isEdit}
                width="35rem"
                height="calc(100% - 9rem)"
                heightupperlimit={
                  requiredThingsDeleteName === "work instruction"
                    ? "2rem"
                    : "0.25rem"
                }
                header={`Are you sure you want to delete this ${
                  requiredThingsDeleteName == "Failure Mode"
                    ? "Quality Ensuring Measures"
                    : requiredThingsDeleteName == "Control Plan"
                    ? "Quality Control Plan "
                    : requiredThingsDeleteName
                } ?`}
                callbackDelete={async () => {
                  if (deleteId && deleteId === "Failuremode") {
                    const newfailureModeData = failureModeData?.filter(
                      (e) => e?._id !== requiredThingsDelete?._id
                    );

                    dispatch(
                      updateTaskData({
                        ...TaskData,
                        MethodId: {
                          ...TaskData?.MethodId,
                          Failuremode: newfailureModeData,
                        },
                      })
                    );

                    saveSyncData(
                      {
                        ...TaskData,
                        _id: TaskData?._id || taskId,
                        MethodId: {
                          ...TaskData?.MethodId,
                          Failuremode: newfailureModeData,
                        },
                      },
                      "time",
                      "TaskForm"
                    );

                    showToast({
                      messageContent: `Quality Ensuring Measures Deleted Successfully!`,
                      type: "success",
                    });
                    dispatch(settaskChangeAPiFlag(true));
                  }
                  if (deleteId && deleteId === "Controlplan") {
                    const newControlPlanData = controlPlandata?.filter(
                      (e) => e?._id !== requiredThingsDelete?._id
                    );

                    dispatch(
                      updateTaskData({
                        ...TaskData,
                        MethodId: {
                          ...TaskData?.MethodId,
                          Controlplan: newControlPlanData,
                        },
                      })
                    );

                    saveSyncData(
                      {
                        ...TaskData,
                        _id: TaskData?._id || taskId,
                        MethodId: {
                          ...TaskData?.MethodId,
                          Controlplan: newControlPlanData,
                        },
                      },
                      "time",
                      "TaskForm"
                    );

                    showToast({
                      messageContent:
                        requiredThingsDeleteName === "Control Plan"
                          ? "Quality Control Plan Deleted Successfully!"
                          : `${requiredThingsDeleteName} Deleted Successfully!`,
                      type: "success",
                    });
                    dispatch(settaskChangeAPiFlag(true));
                  }
                  if (deleteId && deleteId == "task_closing_requirement") {
                    console.log(requiredThingsDelete, "ook");
                    const newTaskClosingRequirementData =
                      taskClosingRequirementData?.filter(
                        (e) => e?._id !== requiredThingsDelete?.id
                      );

                    dispatch(
                      updateTaskData({
                        ...TaskData,
                        MethodId: {
                          ...TaskData?.MethodId,
                          task_closing_requirement:
                            newTaskClosingRequirementData,
                        },
                      })
                    );

                    saveSyncData(
                      {
                        ...TaskData,
                        _id: TaskData?._id || taskId,
                        MethodId: {
                          ...TaskData?.MethodId,
                          task_closing_requirement:
                            newTaskClosingRequirementData,
                        },
                      },
                      "time",
                      "TaskForm"
                    );

                    showToast({
                      messageContent: `${requiredThingsDeleteName} Deleted Successfully!`,
                      type: "success",
                    });
                    dispatch(settaskChangeAPiFlag(true));
                  }
                  if (deleteId && deleteId == "work_instruction_id") {
                    console.log(requiredThingsDelete, "ook");
                    dispatch(
                      settaskWorkInstructionDelete(requiredThingsDelete?._id)
                    );
                    const newwork_instruction_id = workInstructionData?.filter(
                      (e) => e?._id !== requiredThingsDelete?._id
                    );
                    const uniqueArray = (arr: any) => [...new Set(arr)];

                    dispatch(
                      updateTaskData({
                        ...TaskData,
                        MethodId: {
                          ...TaskData?.MethodId,
                          work_instruction_id: newwork_instruction_id,
                        },
                        Tobedeleted: {
                          workinstruction: uniqueArray([
                            ...(TaskData?.Tobedeleted?.workinstruction || []),
                            ...(!/^\d{13}$/.test(
                              String(requiredThingsDelete?._id)
                            )
                              ? [requiredThingsDelete!?._id]
                              : []),
                          ]),
                        },
                      })
                    );

                    saveSyncData(
                      {
                        ...TaskData,
                        _id: TaskData?._id || taskId,
                        MethodId: {
                          ...TaskData?.MethodId,
                          work_instruction_id: newwork_instruction_id,
                        },
                        Tobedeleted: {
                          workinstruction: uniqueArray([
                            ...(TaskData?.Tobedeleted?.workinstruction || []),
                            ...(!/^\d{13}$/.test(
                              String(requiredThingsDelete?._id)
                            )
                              ? [requiredThingsDelete!?._id]
                              : []),
                          ]),
                        },
                      },
                      "time",
                      "TaskForm"
                    );
                    const workInstructionToDelete = workInstructionData?.filter(
                      (e: any) => e?._id === requiredThingsDelete?._id
                    )[0];
                    console.log(
                      workInstructionToDelete,
                      "thi is work instruction dataasdfasdfasdf"
                    );
                    const machinaryId =
                      workInstructionToDelete?.machinaryId || [];
                    const manpowerId =
                      workInstructionToDelete?.manpowerId || [];
                    const toolsId = workInstructionToDelete?.toolsId || [];
                    const materialId =
                      workInstructionToDelete?.materialId || [];
                    console.log(
                      machinaryId,
                      manpowerId,
                      toolsId,
                      materialId,
                      "these are al.lisc checkout aheras"
                    );
                    const ItemsToDelete = [
                      ...machinaryId,
                      ...manpowerId,
                      ...toolsId,
                      ...materialId,
                    ];
                    const latestTaskData =
                      store.getState().taskForm.currentSubtaskData;
                    const allMachinaryItems =
                      latestTaskData?.MethodId?.work_instruction_id?.flatMap(
                        (el: any) => el.machinaryId || []
                      );
                    const allManpowerItems =
                      latestTaskData?.MethodId?.work_instruction_id?.flatMap(
                        (el: any) => el.manpowerId || []
                      );
                    const allToolsItems =
                      latestTaskData?.MethodId?.work_instruction_id?.flatMap(
                        (el: any) => el.toolsId || []
                      );
                    const allMaterialItems =
                      latestTaskData?.MethodId?.work_instruction_id?.flatMap(
                        (el: any) => el.materialId || []
                      );
                    const allitems = [
                      ...(allMachinaryItems || []),
                      ...(allManpowerItems || []),
                      ...(allToolsItems || []),
                      ...(allMaterialItems || []),
                    ];
                    const notpresentInAnotherWI = ItemsToDelete?.filter(
                      (el: any) =>
                        !allitems?.some((item: any) => item._id === el?._id)
                    );
                    if (notpresentInAnotherWI?.length > 0) {
                      const TaskDataLatest =
                        store.getState().taskForm.currentSubtaskData;
                      console.log(
                        TaskDataLatest,
                        "this is task data latestsasdf"
                      );
                      const updatedTaskData = {
                        ...TaskDataLatest,
                        MachinaryId: (
                          (TaskDataLatest as any).MachinaryId || []
                        ).filter(
                          (el: any) =>
                            !notpresentInAnotherWI?.some(
                              (item: any) => item._id === el?._id
                            )
                        ),
                        ManpowerId: (
                          (TaskDataLatest as any).ManpowerId || []
                        ).filter(
                          (el: any) =>
                            !notpresentInAnotherWI?.some(
                              (item: any) => item._id === el?._id
                            )
                        ),
                        MaterialId: (
                          (TaskDataLatest as any).MaterialId || []
                        ).filter(
                          (el: any) =>
                            !notpresentInAnotherWI?.some(
                              (item: any) => item._id === el?._id
                            )
                        ),
                        ToolId: ((TaskDataLatest as any).ToolId || []).filter(
                          (el: any) =>
                            !notpresentInAnotherWI?.some(
                              (item: any) => item._id === el?._id
                            )
                        ),
                      };
                      saveSyncData(updatedTaskData, "time", "TaskForm");
                      dispatch(updateTaskData(updatedTaskData as any));
                    }
                    showToast({
                      messageContent: `${requiredThingsDeleteName} Deleted Successfully!`,
                      type: "success",
                    });
                    dispatch(settaskChangeAPiFlag(true));
                  }
                  // dispatch(closePopup("DeleteMethodId"));
                }}
                onClose={() => {
                  dispatch(closePopup("DeleteMethodId"));
                }}
              >
                {deleteId == "work_instruction_id" && (
                  <>
                    <div className={styles.summaryDivData}>
                      <div className={styles.summaryDataContent}>
                        <p
                          style={{ color: "var(--text-black-60)" }}
                          className="p_tag_14px"
                        >
                          Description
                        </p>
                        <div
                          className=""
                          style={{
                            display: "flex",
                            gap: "1rem",
                            flexWrap: "wrap",
                          }}
                        >
                          <h4 style={{ color: "var(--text-black-87)" }}>
                            {requiredThingsDelete?.Description}
                          </h4>
                        </div>
                      </div>
                    </div>
                    {requiredThingsDelete?.file?.name && (
                      <div className={styles.summaryDivData}>
                        <div className={styles.summaryDataContent}>
                          <p
                            style={{ color: "var(--text-black-60)" }}
                            className="p_tag_14px"
                          >
                            {fileTypeMapper(requiredThingsDelete?.file)}
                          </p>
                          <div
                            className=""
                            style={{
                              display: "flex",
                              gap: "1rem",
                              flexWrap: "wrap",
                            }}
                          >
                            <h4 style={{ color: "var(--text-black-87)" }}>
                              {requiredThingsDelete?.file?.name}
                            </h4>
                          </div>
                        </div>
                      </div>
                    )}

                    <div className={styles.summaryDivData}>
                      <div className={styles.summaryDataContent}>
                        <p
                          style={{ color: "var(--text-black-60)" }}
                          className="p_tag_14px"
                        >
                          Action
                        </p>
                        <div
                          className=""
                          style={{
                            display: "flex",
                            gap: "1rem",
                            flexWrap: "wrap",
                            textTransform: "capitalize",
                          }}
                        >
                          <h4 style={{ color: "var(--text-black-87)" }}>
                            {requiredThingsDelete?.optionselected}
                          </h4>
                        </div>
                      </div>
                    </div>
                    {requiredThingsDelete?.optionselected == "photo" && (
                      <div className={styles.summaryDivData}>
                        <div className={styles.summaryDataContent}>
                          {requiredThingsDelete?.photoref?.photos?.map((e) => (
                            <>
                              <p>
                                {isBase64(e?.photo)
                                  ? e?.fileName
                                  : getFileName(e?.photo)}
                              </p>
                              {e?.details && (
                                <p
                                  style={{ color: "var(--text-black-60)" }}
                                  className="p_tag_14px"
                                >
                                  Reference Detail
                                </p>
                              )}
                              <div
                                className=""
                                style={{
                                  display: "flex",
                                  gap: "1rem",
                                  flexWrap: "wrap",
                                }}
                              >
                                <h4 style={{ color: "var(--text-black-87)" }}>
                                  {e?.details}
                                </h4>
                              </div>
                            </>
                          ))}
                        </div>
                      </div>
                    )}
                    {(requiredThingsDelete?.manpowerId?.length ?? 0) > 0 && (
                      <div className={styles.summaryDivData}>
                        <div className={styles.summaryDataContent}>
                          <p
                            style={{ color: "var(--text-black-60)" }}
                            className="p_tag_14px"
                          >
                            Manpower
                          </p>
                          <div
                            className=""
                            style={{
                              display: "flex",
                              gap: "1rem",
                              flexWrap: "wrap",
                            }}
                          >
                            {requiredThingsDelete?.manpowerId.map((e) => (
                              <h4
                                style={{
                                  marginTop: "0.3rem",
                                  color: "var(--text-black-87)",
                                }}
                              >
                                {e?.name}
                              </h4>
                            )) || "No description"}
                          </div>
                        </div>
                      </div>
                    )}
                    {(requiredThingsDelete?.machinaryId?.length ?? 0) > 0 && (
                      <div className={styles.summaryDivData}>
                        <div className={styles.summaryDataContent}>
                          <p
                            style={{ color: "var(--text-black-60)" }}
                            className="p_tag_14px"
                          >
                            Machinery
                          </p>
                          <div
                            className=""
                            style={{
                              display: "flex",
                              gap: "1rem",
                              flexWrap: "wrap",
                            }}
                          >
                            {requiredThingsDelete?.machinaryId.map((e) => (
                              <h4
                                style={{
                                  marginTop: "0.3rem",
                                  color: "var(--text-black-87)",
                                }}
                              >
                                {e?.name}
                              </h4>
                            )) || "No description"}
                          </div>
                        </div>
                      </div>
                    )}
                    {(requiredThingsDelete?.materialId?.length ?? 0) > 0 && (
                      <div className={styles.summaryDivData}>
                        <div className={styles.summaryDataContent}>
                          <p
                            style={{ color: "var(--text-black-60)" }}
                            className="p_tag_14px"
                          >
                            Materials
                          </p>
                          <div
                            className=""
                            style={{
                              display: "flex",
                              gap: "1rem",
                              flexWrap: "wrap",
                            }}
                          >
                            {requiredThingsDelete?.materialId.map((e) => (
                              <h4
                                style={{
                                  marginTop: "0.3rem",
                                  color: "var(--text-black-87)",
                                }}
                              >
                                {e?.name}
                              </h4>
                            )) || "No description"}
                          </div>
                        </div>
                      </div>
                    )}
                    {(requiredThingsDelete?.toolsId?.length ?? 0) > 0 && (
                      <div className={styles.summaryDivData}>
                        <div className={styles.summaryDataContent}>
                          <p
                            style={{ color: "var(--text-black-60)" }}
                            className="p_tag_14px"
                          >
                            Tools
                          </p>
                          <div
                            className=""
                            style={{
                              display: "flex",
                              gap: "1rem",
                              flexWrap: "wrap",
                            }}
                          >
                            {requiredThingsDelete?.toolsId.map((e) => (
                              <h4
                                style={{
                                  marginTop: "0.3rem",
                                  color: "var(--text-black-87)",
                                }}
                              >
                                {e?.name}
                              </h4>
                            )) || "No description"}
                          </div>
                        </div>
                      </div>
                    )}
                  </>
                )}
                {deleteId == "Controlplan" && (
                  <>
                    <div className={styles.summaryDivData}>
                      <div className={styles.summaryDataContent}>
                        <p
                          style={{ color: "var(--text-black-60)" }}
                          className="p_tag_14px"
                        >
                          Description
                        </p>
                        <div
                          className=""
                          style={{
                            display: "flex",
                            gap: "1rem",
                            flexWrap: "wrap",
                          }}
                        >
                          <h4 style={{ color: "var(--text-black-87)" }}>
                            {requiredThingsDelete?.name}
                          </h4>
                        </div>
                      </div>
                    </div>
                  </>
                )}
                {deleteId == "Failuremode" && (
                  <>
                    <div className={styles.summaryDivData}>
                      <div className={styles.summaryDataContent}>
                        <p
                          style={{ color: "var(--text-black-60)" }}
                          className="p_tag_14px"
                        >
                          Description
                        </p>
                        <div
                          className=""
                          style={{
                            display: "flex",
                            gap: "1rem",
                            flexWrap: "wrap",
                          }}
                        >
                          <h4 style={{ color: "var(--text-black-87)" }}>
                            {requiredThingsDelete?.Description}
                          </h4>
                        </div>
                      </div>
                    </div>
                    <div className={styles.summaryDivData}>
                      <div className={styles.summaryDataContent}>
                        <p
                          style={{ color: "var(--text-black-60)" }}
                          className="p_tag_14px"
                        >
                          Solution
                        </p>
                        <div
                          className=""
                          style={{
                            display: "flex",
                            gap: "1rem",
                            flexWrap: "wrap",
                          }}
                        >
                          <h4 style={{ color: "var(--text-black-87)" }}>
                            {requiredThingsDelete?.solution}
                          </h4>
                        </div>
                      </div>
                    </div>
                    <div className={styles.summaryDivData}>
                      <div className={styles.summaryDataContent}>
                        <p
                          style={{ color: "var(--text-black-60)" }}
                          className="p_tag_14px"
                        >
                          Severity
                        </p>
                        <div
                          className=""
                          style={{
                            display: "flex",
                            gap: "1rem",
                            flexWrap: "wrap",
                          }}
                        >
                          <h4 style={{ color: "var(--text-black-87)" }}>
                            {requiredThingsDelete?.severity}
                          </h4>
                        </div>
                      </div>
                    </div>
                  </>
                )}
                {deleteId == "task_closing_requirement" && (
                  <>
                    <div className={styles.summaryDivData}>
                      <div className={styles.summaryDataContent}>
                        <p
                          style={{ color: "var(--text-black-60)" }}
                          className="p_tag_14px"
                        >
                          Description
                        </p>
                        <div
                          className=""
                          style={{
                            display: "flex",
                            gap: "1rem",
                            flexWrap: "wrap",
                          }}
                        >
                          <h4 style={{ color: "var(--text-black-87)" }}>
                            {requiredThingsDelete?.data?.description}
                          </h4>
                        </div>
                      </div>
                    </div>
                    {requiredThingsDelete?.data?.file?.name && (
                      <div className={styles.summaryDivData}>
                        <div className={styles.summaryDataContent}>
                          <p
                            style={{ color: "var(--text-black-60)" }}
                            className="p_tag_14px"
                          >
                            {fileTypeMapper(requiredThingsDelete?.data?.file)}
                          </p>
                          <div
                            className=""
                            style={{
                              display: "flex",
                              gap: "1rem",
                              flexWrap: "wrap",
                            }}
                          >
                            <h4 style={{ color: "var(--text-black-87)" }}>
                              {requiredThingsDelete?.data?.file?.name}
                            </h4>
                          </div>
                        </div>
                      </div>
                    )}
                    <div className={styles.summaryDivData}>
                      <div className={styles.summaryDataContent}>
                        <p
                          style={{ color: "var(--text-black-60)" }}
                          className="p_tag_14px"
                        >
                          Action
                        </p>
                        <div
                          className=""
                          style={{
                            display: "flex",
                            gap: "1rem",
                            flexWrap: "wrap",
                            textTransform: "capitalize",
                          }}
                        >
                          <h4 style={{ color: "var(--text-black-87)" }}>
                            {requiredThingsDelete?.data?.category}
                          </h4>
                        </div>
                      </div>
                    </div>
                    {requiredThingsDelete?.data?.category == "photo" && (
                      <div className={styles.summaryDivData}>
                        <div className={styles.summaryDataContent}>
                          {requiredThingsDelete?.data?.photoDetails?.map(
                            (e) => (
                              <>
                                <p>
                                  {" "}
                                  {isBase64(e?.photo)
                                    ? e?.fileName
                                    : getFileName(e?.photo)}
                                </p>
                                {e?.referenceDetail && (
                                  <p
                                    style={{ color: "var(--text-black-60)" }}
                                    className="p_tag_14px"
                                  >
                                    Reference Detail
                                  </p>
                                )}
                                <div
                                  className=""
                                  style={{
                                    display: "flex",
                                    gap: "1rem",
                                    flexWrap: "wrap",
                                  }}
                                >
                                  <h4 style={{ color: "var(--text-black-87)" }}>
                                    {e?.referenceDetail}
                                  </h4>
                                </div>
                              </>
                            )
                          )}
                        </div>
                      </div>
                    )}
                  </>
                )}
              </DeletePopup>
            )}
          </div>
        </div>
        <div
          className={styles.taskcreation_column}
          style={{ marginRight: "1.5rem" }}
        >
          <div style={{ minWidth: "50px", position: "relative" }}>
            <AddToolTip
              label="Task Closing Requirements"
              isEdit={isEdit}
              className={`${styles.subtask_creation_add_tooltip} ${styles.three_cols}`}
              onClick={() => handleToggleDropdown("Tcrphoto")}
              className2={
                formattedTaskClosingRequirementData.length > 0
                  ? "widthfull_1rem"
                  : ""
              }
            />
            {(formattedTaskClosingRequirementData || [])?.map((popup) => (
              <div style={{ position: "relative" }}>
                <TaskTcrPopup
                  key={popup.id}
                  isEdit={isEdit}
                  handleDelete={() => {
                    setDeleteId("task_closing_requirement");
                    setRequiredThingsDeleteName("Task Closing Requirement");
                    setRequiredThingsDelete(popup);
                    setTimeout(() => {
                      dispatch(openPopup("DeleteMethodId"));
                    }, 400);
                  }}
                  initaldata={
                    initaldata || {
                      id: "asdf",
                      description: "",
                      file: null,
                      category: "",
                      photoDetails: [],
                    }
                  }
                  onClick={(e) => {
                    e.stopPropagation();
                    if (!isDeletedNext) {
                      console.log(
                        "photo details hein",
                        popup.data.photoDetails
                      );
                      setInitalData({
                        id: popup.id,
                        ...popup.data,
                        photoDetails: popup.data.photoDetails || [],
                      });
                      dispatch(openPopup(`popup-${popup.id}-Tcrphoto`));
                    }
                  }}
                  popupId={`popup-${popup.id}`}
                  data={{
                    ...popup.data,
                    photoDetails: popup.data.photoDetails || [],
                  }}
                  OnUpdateTcrData={(updatedData) => {
                    const updatedTaskClosingRequirements =
                      formattedTaskClosingRequirementData.map((e) => {
                        if (e.id === updatedData.id) {
                          return {
                            ...e,
                            data: {
                              ...updatedData,
                              photoDetails: updatedData.photoDetails || [],
                            },
                          };
                        }
                        return e;
                      });

                    dispatch(
                      updateTaskData({
                        ...TaskData,
                        MethodId: {
                          ...TaskData?.MethodId,
                          task_closing_requirement:
                            updatedTaskClosingRequirements.map((e) => ({
                              photoref: {
                                photos:
                                  e?.data.photoDetails
                                    ?.filter(
                                      (photo: { photo: string | null }) =>
                                        photo.photo !== null
                                    )
                                    .map(
                                      (photo: {
                                        id: number;
                                        fileName: string;
                                        referenceDetail: string;
                                        photo: string | null;
                                      }) => ({
                                        id: photo.id.toString(),
                                        fileName: photo.fileName,
                                        details: photo.referenceDetail,
                                        photo: photo.photo as string,
                                      })
                                    ) || [],
                              },
                              file: e?.data.file,
                              _id: e?.id,
                              optionselected: e?.data.category,
                              Description: e?.data.description,
                            })),
                        },
                      })
                    );

                    saveSyncData(
                      {
                        ...TaskData,
                        _id: TaskData?._id || taskId,
                        MethodId: {
                          ...TaskData?.MethodId,
                          task_closing_requirement:
                            updatedTaskClosingRequirements.map((e) => ({
                              photoref: {
                                photos:
                                  e?.data.photoDetails
                                    ?.filter(
                                      (photo: { photo: string | null }) =>
                                        photo.photo !== null
                                    )
                                    .map(
                                      (photo: {
                                        id: number;
                                        fileName: string;
                                        referenceDetail: string;
                                        photo: string | null;
                                      }) => ({
                                        id: photo.id.toString(),
                                        fileName: photo.fileName,
                                        details: photo.referenceDetail,
                                        photo: photo.photo as string,
                                      })
                                    ) || [],
                              },
                              file: e?.data.file,
                              _id: e?.id,
                              optionselected: e?.data.category,
                              Description: e?.data.description,
                            })),
                        },
                      },
                      "time",
                      "TaskForm"
                    );

                    showToast({
                      messageContent:
                        "Task Closing Requirement Updated Successfully!",
                      type: "success",
                    });
                    console.log(updatedData, "updateddata");
                    dispatch(settaskChangeAPiFlag(true));
                    dispatch(closePopup(getPopupId("Tcrphoto")));
                  }}
                />
              </div>
            ))}
          </div>
          <div style={{ minWidth: "50px", position: "relative" }}>
            <AddToolTip
              label="Quality Control Plan"
              onClick={() => {
                setControlPlanInitialState(undefined);
                handleToggleDropdown("Control Plan");
              }}
              isEdit={isEdit}
              handleDelete={(item) => {
                setDeleteId("Controlplan");
                setRequiredThingsDeleteName("Control Plan");
                setRequiredThingsDelete(item);
                setTimeout(() => {
                  dispatch(openPopup("DeleteMethodId"));
                }, 400);
              }}
              className={`${styles.subtask_creation_add_tooltip} ${styles.three_cols}`}
              // className2={controlPlandata.length > 0 ? "" : ""}
              data={controlPlandata?.map((data) => ({
                _id: data?._id,
                name: data?.Description,
              }))}
              onTooltipClick={
                isEdit
                  ? (item) => {
                      console.log(item, "rat");

                      if (!isDeletedNext) {
                        setControlPlanInitialState({
                          _id: item?._id,
                          description: item?.name,
                        });
                        handleToggleDropdown("Control Plan");
                      }
                    }
                  : () => {}
              }
            />
            {popups["Control Plan"] && (
              <ControlPlanpopup
                onSubmit={(data) => {
                  if (controlPlanInitialState) {
                    const updatedControlPlanData = controlPlandata.map((p) => {
                      console.log("data", p);
                      return p._id === data._id
                        ? { ...p, Description: data.description }
                        : p;
                    });

                    dispatch(
                      updateTaskData({
                        ...TaskData,
                        MethodId: {
                          ...TaskData?.MethodId,
                          Controlplan: updatedControlPlanData,
                        },
                      })
                    );

                    saveSyncData(
                      {
                        ...TaskData,
                        _id: TaskData?._id || taskId,
                        MethodId: {
                          ...TaskData?.MethodId,
                          Controlplan: updatedControlPlanData,
                        },
                      },
                      "time",
                      "TaskForm"
                    );

                    dispatch(closePopup("Control Plan"));
                    showToast({
                      messageContent:
                        "Quality Control Plan Updated Successfully!",
                      type: "success",
                    });
                    dispatch(settaskChangeAPiFlag(true));
                    return;
                  }
                  dispatch(
                    updateTaskData({
                      ...TaskData,
                      MethodId: {
                        ...TaskData?.MethodId,
                        Controlplan: [
                          ...controlPlandata,
                          {
                            _id: String(Date.now()),
                            Description: data.description,
                          },
                        ],
                      },
                    })
                  );

                  saveSyncData(
                    {
                      ...TaskData,
                      _id: TaskData?._id || taskId,
                      MethodId: {
                        ...TaskData?.MethodId,
                        Controlplan: [
                          ...controlPlandata,
                          {
                            _id: String(Date.now()),
                            Description: data.description,
                          },
                        ],
                      },
                    },
                    "time",
                    "TaskForm"
                  );

                  // dispatch(closePopup("Control Plan"));
                  showToast({
                    messageContent: "Quality Control Plan Added Successfully!",
                    type: "success",
                  });
                  dispatch(settaskChangeAPiFlag(true));
                }}
                initialData={controlPlanInitialState && controlPlanInitialState}
                isEdit={controlPlanInitialState ? true : false}
                onCancel={() => dispatch(closePopup("Control Plan"))}
              />
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default TaskCreationMethod;
