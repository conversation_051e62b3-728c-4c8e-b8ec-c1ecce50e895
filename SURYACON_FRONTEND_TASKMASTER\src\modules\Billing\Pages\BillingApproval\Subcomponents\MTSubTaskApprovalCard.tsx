import React from 'react'
import styles from '../Styles/BillingApprovalSubcomponent.module.css'
import { RootState } from '../../../../../redux/store';
import { useDispatch, useSelector } from 'react-redux';
import MTSubTaskApprovalSubCard from './MTTASubCard';
import Button from '../../../../../components/Reusble/Global/Button';
import { 
  AreaIcon, 
  BudgetIcon, 
  CalendarPrimaryIcon, 
  CalendarSecondaryIcon, 
  ExpandIcon, 
  ReasonIcon, 
  RemarkIcon, 
  TaskDescriptionIcon 
} from '../../../../../assets/icons';
import { formatDateMT, numberWithCommas } from '../../../../../functions/functions';
import { 
  openDeclineDialog, 
  openSubTaskApproveDialog,  
} from '../../../../../redux/features/Modules/Billing/BillingApproval/Slices/BillingApprovalSlice';

interface MTSubTaskApprovalCardProps {
  floorNumber: number | string;
  taskCount: number;
  taskDescription?: string;
  budget?: number;
  area?: string | number;
  startDate?: string;
  endDate?: string;
  remark?: string;
}

const MTSubTaskApprovalCard: React.FC<MTSubTaskApprovalCardProps> = ({
  floorNumber,
  taskCount,
  taskDescription = "",
  budget = 0,
  area = "",
  startDate = "",
  endDate = "",
  remark = ""
}) => {

  const dispatch = useDispatch();
  
  // Get data from Redux store
  const subTaskData = useSelector((state: RootState) => state.monthlyTarget.monthlyTargetSubTaskData);
  

  // If no data is available, use the props
  const dataToUse = subTaskData || {
    _id: `floor-${floorNumber}`,
    taskDescription,
    Budget: budget,
    area,
    startDate,
    endDate,
    remarks: remark,
    decline: false,
    reason: ""
  };

  const handleApprove = () => {
    dispatch(openSubTaskApproveDialog(dataToUse._id)); 
  };

  const handleDecline = () => {
    dispatch(openDeclineDialog(dataToUse._id)); 
  };

  // Helper for ordinal suffix
  function getOrdinalSuffix(n: number | string) {
    const num = typeof n === "string" ? parseInt(n) : n;
    if (isNaN(num)) return "";
    const j = num % 10, k = num % 100;
    if (j === 1 && k !== 11) return "st";
    if (j === 2 && k !== 12) return "nd";
    if (j === 3 && k !== 13) return "rd";
    return "th";
  }

  return (
    <div className={`${styles.mt_task_approval_card}`}>
      <div className={`${styles.mt_task_approval_card_top}`}>
        <div className={`${styles.mt_task_approval_card_top_left}`}>
          <p className={`${styles.mt_task_approval_card_top_left_text}`}>
            {floorNumber}{getOrdinalSuffix(floorNumber)} Floor
          </p>
          <p className={`${styles.mt_task_approval_card_top_left_text2}`}>{taskCount}</p>
        </div>
        <div className={`${styles.mt_task_approval_card_top_right}`}>
          <div className={styles.expand_icon_wrapper}>
            <ExpandIcon />
          </div>
        </div>
      </div>
      <div className={`${styles.mt_task_approval_card_bottom} ${styles.expanded}`}>
        <MTSubTaskApprovalSubCard 
          keyProp={"Task Description"} 
          valueProp={dataToUse.taskDescription || taskDescription} 
          icon={TaskDescriptionIcon} 
        />
        <MTSubTaskApprovalSubCard 
          keyProp={"Budget"} 
          valueProp={numberWithCommas(dataToUse.Budget || budget)} 
          icon={BudgetIcon} 
        />
        <MTSubTaskApprovalSubCard 
          keyProp={"Area"} 
          valueProp={dataToUse.area || area} 
          icon={AreaIcon} 
        />
        <MTSubTaskApprovalSubCard 
          keyProp={"Start Date"} 
          valueProp={formatDateMT(dataToUse.startDate || startDate)} 
          icon={CalendarPrimaryIcon} 
        />
        <MTSubTaskApprovalSubCard 
          keyProp={"End Date"} 
          valueProp={formatDateMT(dataToUse.endDate || endDate)} 
          property={"secondaryBg"} 
          icon={CalendarSecondaryIcon} 
        />
        <MTSubTaskApprovalSubCard 
          keyProp={"Remarks"} 
          valueProp={dataToUse.remarks || remark} 
          icon={RemarkIcon} 
        />
        
        {dataToUse.decline !== false ? (
          <MTSubTaskApprovalSubCard
            keyProp={"Reason"}
            valueProp={dataToUse.reason || ""}
            valueClassName={"decline_reason_text"}
            icon={ReasonIcon}
          />
        ) : (
          ""
        )}
        
        {dataToUse.decline === false && (
          <div className={`${styles.mt_task_approval_card_bottom_buttons}`}>
            <Button
              type="Decline"
              Content="Decline"
              Callback={handleDecline}
              property="monthly_target_decline_button"
              height="32px"
            />
            <Button
              type="Approve"
              Content="Approve"
              Callback={handleApprove}
              property="monthly_target_approve_button"
              height="32px"
            />
          </div>
        )}
      </div>
    </div>
  );
}

export default MTSubTaskApprovalCard;
