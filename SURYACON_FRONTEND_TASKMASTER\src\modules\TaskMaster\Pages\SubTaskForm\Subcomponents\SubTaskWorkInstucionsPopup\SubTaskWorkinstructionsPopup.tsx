import { useDispatch, useSelector } from "react-redux";
import WorkInstructionsPopup from "../../../../../../components/Reusble/TaskMaster/WorkInstructionsPopup";
import { useCallback, useEffect, useState } from "react";
import { useGetTaskBuildingBlocksQuery } from "../../../../../../redux/api/Modules/TaskMaster/TaskMasterapi";
import {
  closePopup,
  openPopup,
} from "../../../../../../redux/features/Modules/Reusble/popupSlice";
import {
  CheckBox,
  DeleteIcon,
  ImageIcon,
  SuryconLogo,
} from "../../../../../../assets/icons";
import AddToolTip from "../../../../../../components/Reusble/Global/AddToolTip";
import AddCategoryType from "../../../../../../components/Reusble/Global/AddCategoryType";
import { setRequiredThings } from "../../../../../../redux/features/Modules/TaskMaster/Slices/WorkInstructionSlice";
import { DeletePopup } from "../../../../../../components/Reusble/Global/DeletePopup";
import styles from "../../Styles/SubtaskCreationForm.module.css";
import { RootState, store } from "../../../../../../redux/store";
import {
  requiredthings,
  SubTaskWorkInstructionsPopupProps,
  TaskDataType,
  WorkInstructionsData,
} from "../../../../../../interfaces/Modules/TaskMaster/TaskMasterInterface/TaskMaster";
import {
  getFileName,
  initializeDatabase,
  isBase64,
} from "../../../../../../functions/functions";
import {
  setChangeAPiFlag,
  settaskChangeAPiFlag,
  updateSubtaskData,
  updateTaskData,
} from "../../../../../../redux/features/Modules/TaskMaster/Slices/TaskMasterSlice";
import { saveSyncData } from "../../../../../../Backup/BackupFunctions/BackupFunctions";
import { useParams } from "react-router-dom";
import { useToast } from "../../../../../../hooks/ToastHook";

const SubTaskWorkInstructionsPopup: React.FC<
  SubTaskWorkInstructionsPopupProps
> = ({
  data,
  workId,
  isEdit,
  onUpdateWorkInstructions,
  popupId,
  handleDelete,
  onClick,
  initaldata,
  categoryData,
  onUpdateCategoryData,
  setPopupIdParent,
  popupIdParent,
  setCategoryDataParent,
  categortDataParent,
  setDeleteIdParent,
  deleteIdParent,
  id,
}) => {
  if (!data) return null;
  const { taskId, subtaskId } = useParams<{
    taskId: string;
    subtaskId: string;
  }>();
  const showToast = useToast();
  const TaskData = useSelector(
    (state: RootState) =>
      state.taskForm.currentSubtaskData || {
        _id: taskId,
        name: "",
        Unit: "",
        description: "",
        subtaskWeighatages: 0,
        Tracking: "",
        MaterialId: [],
        ToolId: [],
        MachinaryId: [],
        ManpowerId: [],
        AdminId: [],
        AssigneeId: [],
        Reporter: [],
        Subtaskdetails: [],
        MethodId: {
          work_instruction_id: [],
          task_closing_requirement: [],
          Controlplan: [],
          Failuremode: [],
        },
      }
  );
  // const [setSecondPrimaryLabel, setSecondPrimaryLabel] = useState("");
  const dispatch = useDispatch();
  const { popups } = useSelector((state: RootState) => state.popup);
  const { requiredThingsDeleteName } = useSelector(
    (state: RootState) => state.WorkInstructionReducer
  );
  const [selectedOption, setSelectedOption] = useState("");
  const [primaryLabelForAddCategoryType, SetprimaryLabelForAddCategoryType] =
    useState("");
  const [selectedOptionApidata, setSelectedOptionApidata] = useState([]);
  const [deleteId, setDeleteId] = useState<string>();
  console.log(
    "requiredThingsDeleteName",
    requiredThingsDeleteName,
    deleteId,
    "deleteId"
  );
  const [isPhotoCheckboxPage, setIsPhotoCheckboxPage] = useState(false);
  const [allSubtasks, SetallSubtasks] = useState<any[]>([]);
  useEffect(() => {
    async function fetchData() {
      const dbName = await initializeDatabase("SubTaskForm");
      const response = await window.electron.getSubtasksByTaskId({
        dbName,
        taskId,
      });
      SetallSubtasks(response || []);
      // Update Redux state here with response
    }
    fetchData();
  }, [taskId]);
  // const [requiredThingsDelete, setRequiredThingsDelete] =
  //   useState<requiredthings>();

  type taskBuildingBlocks = {
    [key: string]: requiredthings[];
  };

  const subTaskData = useSelector(
    (state: RootState) => state.taskMaster.currentSubtaskData
  );

  const handleSelect = useCallback(
    async (categoryName: keyof typeof categoryData, selectedItems: any) => {
      if (!selectedItems?.length) return;

      const newSelectedItems = selectedItems.map((item: any) => ({
        _id: item.id,
        name: item.category,
        DeparmentId: item?.parentId || "",
      }));

      onUpdateCategoryData(popupId, categoryName, [
        ...categoryData[categoryName],
        ...newSelectedItems,
      ]);

      const catMap = {
        Manpower: "manpowerId",
        Machinery: "machinaryId",
        Tools: "toolsId",
        Materials: "materialId",
      } as const;

      const newCatMap = {
        Manpower: "ManpowerId",
        Machinery: "MachinaryId",
        Tools: "ToolId",
        Materials: "MaterialId",
      } as const;

      const catName = catMap[categoryName];
      const newCateName = newCatMap[categoryName];

      if (!catName || !newCateName) return;

      // --- Step 1: Update MethodId nested structure
      const updatedMethodSubtask = {
        ...subTaskData,
        MethodId: {
          ...subTaskData?.MethodId,
          work_instruction_id: subTaskData?.MethodId?.work_instruction_id?.map(
            (item: any) => {
              if (item?._id === workId) {
                return {
                  ...item,
                  [catName]: [...(item?.[catName] || []), ...newSelectedItems],
                };
              }
              return item;
            }
          ),
        },
      };
      dispatch(updateSubtaskData(updatedMethodSubtask as any));
      await saveSyncData(updatedMethodSubtask, "time", "SubTaskForm");

      const subtaskLatest = store.getState().taskMaster.currentSubtaskData;
      const TaskDataLatest = store.getState().taskForm.currentSubtaskData;
      // --- Step 2: Top-level subtask update
      const uniqueItemsSubtask = newSelectedItems.filter(
        (newItem: any) =>
          !(subtaskLatest?.[newCateName] || []).some(
            (existingItem: any) => existingItem._id === newItem._id
          )
      );
      const updatedSubtask = {
        ...subtaskLatest,
        [newCateName]: [
          ...(subtaskLatest?.[newCateName] || []),
          ...uniqueItemsSubtask,
        ],
      };
      dispatch(updateSubtaskData(updatedSubtask as any));
      await saveSyncData(updatedSubtask, "time", "SubTaskForm");

      // --- Step 3: Task update
      const uniqueItemsTask = newSelectedItems.filter(
        (newItem: any) =>
          !(TaskDataLatest?.[newCateName] || []).some(
            (existingItem: any) => existingItem._id === newItem._id
          )
      );
      const updatedTask = {
        ...TaskDataLatest,
        [newCateName]: [
          ...(TaskDataLatest?.[newCateName] || []),
          ...uniqueItemsTask,
        ],
      };
      dispatch(updateTaskData(updatedTask as any));
      await saveSyncData(updatedTask, "time", "TaskForm");

      // Toast and flags
      showToast({
        messageContent: `${
          selectedItems?.length > 1 ? categoryName : selectedItems[0]?.category
        } added Successfully!`,
        type: "success",
      });
      dispatch(setChangeAPiFlag(true));
      dispatch(settaskChangeAPiFlag(true));
    },
    [
      popupId,
      categoryData,
      onUpdateCategoryData,
      subTaskData,
      TaskData,
      workId,
      dispatch,
    ]
  );

  const getCategoryName = (category: string): string => {
    switch (category) {
      case "Materials":
        return "Material";
      case "Tools":
        return "Tool";
      case "Manpower":
        return "Manpower";
      case "Machinery":
        return "Machine";
      default:
        return ""; // Return empty string or fallback value
    }
  };

  const getPopupId = (category: string) => {
    console.log(category, "this is where the error is comignasd");
    return `${popupId}-${category}`;
  };
  const getCategories = async (tablename: string) => {
    console.log("fetheced category called");

    let response;
    let dbName;
    switch (tablename) {
      case "MaterialCategory":
        dbName = await initializeDatabase("MaterialCategory");
        response = await window.electron.getCategoryData({ dbName });
        break;
      case "Manpowercategory":
        dbName = await initializeDatabase("Manpowercategory");
        response = await window.electron.getCategoryData({ dbName });
        break;
      case "ToolCategory":
        dbName = await initializeDatabase("ToolCategory");
        response = await window.electron.getCategoryData({ dbName });
        break;
      case "machinaryCategory":
        dbName = await initializeDatabase("MachinaryCategory");
        response = await window.electron.getCategoryData({ dbName });
        break;
      case "departmentdetails":
        dbName = await initializeDatabase("Departments");
        response = await window.electron.getCategoryData({ dbName });
        break;
      case "designationdetails":
        dbName = await initializeDatabase("MaterialCategory");
        response = await window.electron.getCategoryData({ dbName });
        break;
      default:
        console.log("Invalid table name");
        response = null;
    }
    console.log(response, "this is response for selecte option");
    return (
      response?.map((e: { _id: string; name: string }) => ({
        id: e._id,
        category: e.name,
      })) || []
    );
  };
  const handleToggleDropdown = async (name: string, modelname?: string) => {
    if (modelname) {
      const data = await getCategories(modelname);
      setSelectedOptionApidata(data);
      (() => {
        switch (modelname) {
          case "MaterialCategory":
            SetprimaryLabelForAddCategoryType("Category");

            return;
          case "machinaryCategory":
            SetprimaryLabelForAddCategoryType("Category");
            return;
          case "ToolCategory":
            SetprimaryLabelForAddCategoryType("Category");
            return;
          case "Manpowercategory":
            SetprimaryLabelForAddCategoryType("Category");
            return;
          case "departmentdetails":
            SetprimaryLabelForAddCategoryType("Department");
            return;
          default:
            return <>Unknown </>;
        }
      })();
      // (() => {
      //   switch (modelname) {
      //     case "MaterialCategory":
      //       return <>Add Materials </>;
      //     case "machinaryCategory":
      //       return <>Add Machinery </>;
      //     case "ToolCategory":
      //       return <>Add Tools </>;
      //     case "Manpowercategory":
      //       return <> Add Manpower </>;
      //     case "departmentdetails":
      //       return <> Add Designation</>;
      //     case "designationdetails":
      //       return <> Add Designation</>;
      //     case "taskManagerDataDetail":
      //       return <> Add Task Manager</>;
      //     case "reporterDataDetail":
      //       return <> Add Reporters</>;
      //     case "assigneeDataDetail":
      //       return <> Add Assignees</>;
      //     default:
      //       return <>Unknown </>;
      //   }
      // })();
    }
    const namespacedPopupId = getPopupId(name);
    console.log(namespacedPopupId, "theseare name sapisdf");
    dispatch(openPopup(namespacedPopupId));
  };

  const handlePhotoPopupClose = () => {
    setIsPhotoCheckboxPage(false);
    dispatch(closePopup(getPopupId("workinstructionsphoto")));
  };
  const handlePhotoPopupSubmit = (newData: WorkInstructionsData) => {
    console.log(newData, "newData");
    if (data && onUpdateWorkInstructions && !initaldata) {
      const updatedData = {
        ...data,
        photoDetails: [
          ...(data.photoDetails || []),
          ...(newData.photoDetails || []),
        ],
      };
      // console.log(updatedData, "jaggadon");
      onUpdateWorkInstructions(updatedData);
    } else {
      if (onUpdateWorkInstructions) {
        onUpdateWorkInstructions(newData);
      }
    }

    handlePhotoPopupClose();
  };

  const handleDeleteWorkInstructionItem = useCallback(async () => {
    console.log(
      deleteIdParent,
      subTaskData,
      id,
      categoryData,
      categortDataParent,
      "chal ya nhi"
    );

    if (!deleteIdParent || !requiredThingsDeleteName?._id) return;

    // 1. Remove from categoryDataParent
    const newCategories = categortDataParent?.[deleteIdParent]?.filter(
      (e: { _id: string }) => e?._id !== requiredThingsDeleteName?._id
    );

    onUpdateCategoryData(popupIdParent, deleteIdParent, newCategories);

    // 2. Determine newKey for MethodId
    let newKey: string | undefined;
    switch (deleteIdParent) {
      case "Tools":
        newKey = "toolsId";
        break;
      case "Materials":
        newKey = "materialId";
        break;
      case "Machinery":
        newKey = "machinaryId";
        break;
      case "Manpower":
        newKey = "manpowerId";
        break;
      default:
        console.error("Invalid deleteIdParent value:", deleteIdParent);
        return;
    }

    // 3. Helper to remove the item from MethodId.work_instruction_id
    const updatedSubtask = {
      ...subTaskData,
      MethodId: {
        ...subTaskData?.MethodId,
        work_instruction_id: subTaskData?.MethodId?.work_instruction_id?.map(
          (item: any) => {
            if (item?._id === workId) {
              return {
                ...item,
                [newKey!]: (item[newKey!] || []).filter(
                  (el: any) => el?._id !== requiredThingsDeleteName._id
                ),
              };
            }
            return item;
          }
        ),
      },
    };

    // 4. Update Redux & Local DB for Subtask
    dispatch(updateSubtaskData(updatedSubtask as any));
    try {
      await saveSyncData(updatedSubtask, "time", "SubTaskForm");
    } catch (error) {
      console.error("Failed to save subtask data to local DB:", error);
    }

    // 5. Check if item is present in any other Work Instruction
    const subtaskLatest = store.getState().taskMaster.currentSubtaskData;
    const allItems = subtaskLatest?.MethodId?.work_instruction_id?.flatMap(
      (el: any) => el[newKey!] || []
    );
    const presentInAnotherWI = allItems?.some(
      (el: any) => el?._id === requiredThingsDeleteName._id
    );

    // 6. Determine newKey2 for top-level arrays
    let newKey2: string | undefined;
    switch (deleteIdParent) {
      case "Tools":
        newKey2 = "ToolId";
        break;
      case "Materials":
        newKey2 = "MaterialId";
        break;
      case "Machinery":
        newKey2 = "MachinaryId";
        break;
      case "Manpower":
        newKey2 = "ManpowerId";
        break;
      default:
        console.error("Invalid deleteIdParent for newKey2:", deleteIdParent);
        return;
    }

    if (!presentInAnotherWI && newKey2) {
      const updatedSubtask2 = {
        ...subtaskLatest,
        [newKey2]: ((subtaskLatest as any)[newKey2] || []).filter(
          (el: any) => el?._id !== requiredThingsDeleteName._id
        ),
      };
      dispatch(updateSubtaskData(updatedSubtask2 as any));
      try {
        await saveSyncData(updatedSubtask2, "time", "SubTaskForm");
      } catch (error) {
        console.error("Failed to save subtask (secondary) to local DB:", error);
      }
    }

    // 7. Remove from Task if not present in any other subtask

    // Remove current subtask from allSubtasks and append updated one from redux state
    const currentSubtaskId = subTaskData?._id;
    const updatedSubtaskFromRedux =
      store.getState().taskMaster.currentSubtaskData;
    const filteredSubtasks = allSubtasks.filter(
      (el: any) => el._id !== currentSubtaskId
    );
    const allSubtasksWithLatest = [
      ...filteredSubtasks,
      updatedSubtaskFromRedux,
    ];
    const subtaskThings = allSubtasksWithLatest.flatMap(
      (el: any) => el[newKey2!] || []
    );
    const presentInAnotherSubtask = subtaskThings?.some(
      (el: any) => el?._id === requiredThingsDeleteName._id
    );
    console.log(
      subtaskThings,
      presentInAnotherSubtask,
      allSubtasks,
      "these are al subtasdkfasdf"
    );
    const TaskDataLatest = store.getState().taskForm.currentSubtaskData;

    if (!presentInAnotherSubtask && newKey2) {
      const updatedTask = {
        ...TaskDataLatest,
        [newKey2]: ((TaskDataLatest as any)[newKey2] || []).filter(
          (el: any) => el?._id !== requiredThingsDeleteName._id
        ),
      };
      dispatch(updateTaskData(updatedTask as any));
      try {
        await saveSyncData(updatedTask, "time", "TaskForm");
      } catch (error) {
        console.error("Failed to save task to local DB:", error);
      }
    }

    // 8. Final state updates
    dispatch(setChangeAPiFlag(true));
    dispatch(settaskChangeAPiFlag(true));
    dispatch(closePopup("DeleteWipRequiredThingsName"));
    showToast({
      messageContent: `${requiredThingsDeleteName?.name} deleted Successfully!`,
      type: "success",
    });
  }, [
    categoryData,
    deleteIdParent,
    requiredThingsDeleteName,
    dispatch,
    onUpdateCategoryData,
    popupIdParent,
    categortDataParent,
    subTaskData,
    workId,
    subtaskId,
    allSubtasks,
    TaskData,
  ]);

  return (
    <div
      className={styles.subtask_method_popup_container}
      style={{ position: "relative" }}
    >
      <div
        onClick={(e) => (isEdit ? onClick(e) : () => {})}
        className={styles.subtask_method_popup_header}
      >
        <div className={styles.tcr_desc_details}>
          <div className={styles.tcr_tooltip}>
            <h4>{data.description}</h4>
          </div>

          {data.file && (
            <div className={styles.tcr_photoname}>
              <p className="small_text_p_400">{data?.file?.name}</p>
            </div>
          )}
        </div>

        {/* Photo sections */}
        {data.category === "photo" && data.photoDetails && (
          <>
            {data.photoDetails.map((photo: any, index: any) => (
              <div key={index} className={styles.tcr_tooltip_photo_container}>
                <div className={styles.tcr_tooltip_photo}>
                  <ImageIcon />{" "}
                  <h4>
                    {isBase64(photo?.photo)
                      ? photo?.fileName
                      : getFileName(photo?.photo)}
                  </h4>
                </div>
              </div>
            ))}
          </>
        )}

        {/* Checkbox section */}
        {data.category === "checkbox" && (
          <div className={styles.tcr_tooltip_photo}>
            <CheckBox /> <h4>Checkbox</h4>
          </div>
        )}
      </div>

      {/* Photo popup */}
      {popups[getPopupId("workinstructionsphoto")] && (
        <WorkInstructionsPopup
          onCancel={handlePhotoPopupClose}
          onSubmit={handlePhotoPopupSubmit}
          initialData={initaldata}
          startWithPhotoCheckboxPage={isPhotoCheckboxPage}
          isEdit={initaldata ? true : false}
        />
      )}

      <div
        className={styles.subtaskcreation_line_container}
        style={{ marginBlock: "0" }}
      >
        <span className={styles.dottedline_wrapper}></span>
        <SuryconLogo />
        <span className={styles.dottedline_wrapper}></span>
      </div>

      {/* Category Selection Section */}
      <div key={popupId} className={styles.subtask_method_popup_row}>
        <div style={{ minWidth: "50px", position: "relative" }}>
          <AddToolTip
            label="Manpower"
            onClick={(e) => {
              e.stopPropagation();
              handleToggleDropdown("Manpower", "Manpowercategory");
              setSelectedOption("Manpowercategory");
            }}
            className={`${styles.subtask_creation_add_tooltip} ${styles.three_cols}`}
            data={categoryData?.Manpower}
            isEdit={isEdit}
            handleDelete={(item) => {
              setPopupIdParent(popupId);
              setCategoryDataParent(categoryData);
              setDeleteIdParent("Manpower");
              dispatch(setRequiredThings(item));
              setTimeout(() => {
      dispatch(closePopup("DeleteWipRequiredThingsName" + `${id}`));
    }, 400);
    setTimeout(() => {
      dispatch(openPopup("DeleteWipRequiredThingsName" + `${id}`));
    }, 400);
            }}
          />
          {popups[getPopupId("Manpower")] && (
            <AddCategoryType
              primaryLabel2="Add Manpower"
              modelname={selectedOption}
              isStepForm={true}
              primaryLabel={primaryLabelForAddCategoryType}
              title="Add Manpower"
              key={popupId}
              label="Manpower"
              data={selectedOptionApidata as TaskDataType[]}
              placeholder="Search"
              buttonLabel="Add Category"
              initialSelected={categoryData?.Manpower}
              onSelect={(item: any) => handleSelect("Manpower", item)}
              onClose={() => dispatch(closePopup(getPopupId("Manpower")))}
            />
          )}
        </div>
        <div style={{ minWidth: "50px", position: "relative" }}>
          <AddToolTip
            label="Machinery"
            onClick={(event: any) => {
              event.stopPropagation();
              handleToggleDropdown("Machinery", "machinaryCategory");
              setSelectedOption("machinaryCategory");
            }}
            isEdit={isEdit}
            handleDelete={(item) => {
              setPopupIdParent(popupId);
              setCategoryDataParent(categoryData);
              setDeleteIdParent("Machinery");
              dispatch(setRequiredThings(item));
               setTimeout(() => {
    dispatch(closePopup("DeleteWipRequiredThingsName" + `${id}`));
  }, 400);
  setTimeout(() => {
    dispatch(openPopup("DeleteWipRequiredThingsName" + `${id}`));
  }, 400);
            }}
            className={`${styles.subtask_creation_add_tooltip} ${styles.three_cols}`}
            data={categoryData?.Machinery}
          />
          {popups[getPopupId("Machinery")] && (
            <AddCategoryType
              primaryLabel2="Add Machinery"
              primaryLabel={primaryLabelForAddCategoryType}
              modelname={selectedOption}
              isStepForm={true}
              title="Add Machinery"
              label="Machinery"
              key={popupId}
              data={selectedOptionApidata as TaskDataType[]}
              initialSelected={categoryData?.Machinery}
              placeholder="Search"
              buttonLabel="Add Category"
              onSelect={(item: any) => handleSelect("Machinery", item)}
              onClose={() => dispatch(closePopup(getPopupId("Machinery")))}
            />
          )}
        </div>
      </div>

      <div
        className={styles.subtask_method_popup_row}
        style={{ paddingBottom: "1rem" }}
      >
        <div style={{ minWidth: "50px", position: "relative" }}>
          <AddToolTip
            label="Tools"
            onClick={(e) => {
              e.stopPropagation();
              handleToggleDropdown("Tools", "ToolCategory");
              setSelectedOption("ToolCategory");
            }}
            className={`${styles.subtask_creation_add_tooltip} ${styles.three_cols}`}
            data={categoryData?.Tools}
            isEdit={isEdit}
            handleDelete={(item) => {
              setPopupIdParent(popupId);
              setCategoryDataParent(categoryData);
              setDeleteIdParent("Tools");
              dispatch(setRequiredThings(item));
              setTimeout(() => {
    dispatch(closePopup("DeleteWipRequiredThingsName" + `${id}`));
  }, 400);
  setTimeout(() => {
    dispatch(openPopup("DeleteWipRequiredThingsName" + `${id}`));
  }, 400);
            }}
          />
          {popups[getPopupId("Tools")] && (
            <AddCategoryType
              primaryLabel2="Add Tools"
              primaryLabel={primaryLabelForAddCategoryType}
              modelname={selectedOption}
              isStepForm={true}
              title="Add Tools"
              label="Tool"
              key={popupId}
              data={selectedOptionApidata as TaskDataType[]}
              placeholder="Search"
              initialSelected={categoryData?.Tools}
              buttonLabel="Add Category"
              onSelect={(item: any) => handleSelect("Tools", item)}
              onClose={() => dispatch(closePopup(getPopupId("Tools")))}
            />
          )}
        </div>
        <div style={{ minWidth: "50px", position: "relative" }}>
          <AddToolTip
            label="Materials"
            onClick={(e) => {
              e.stopPropagation();
              handleToggleDropdown("Materials", "MaterialCategory");
              setSelectedOption("MaterialCategory");
            }}
            className={`${styles.subtask_creation_add_tooltip} ${styles.three_cols}`}
            data={categoryData?.Materials}
            isEdit={isEdit}
            handleDelete={(item) => {
              setPopupIdParent(popupId);
              setCategoryDataParent(categoryData);
              setDeleteIdParent("Materials");
              dispatch(setRequiredThings(item));
               setTimeout(() => {
    dispatch(closePopup("DeleteWipRequiredThingsName" + `${id}`));
  }, 400);
  setTimeout(() => {
    dispatch(openPopup("DeleteWipRequiredThingsName" + `${id}`));
  }, 400);
            }}
          />
          {popups[getPopupId("Materials")] && (
            <AddCategoryType
              primaryLabel2="Add Materials"
              primaryLabel={primaryLabelForAddCategoryType}
              modelname={selectedOption}
              isStepForm={true}
              title="Add Materials"
              data={selectedOptionApidata as any}
              key={popupId}
              placeholder="Search"
              label="Material"
              initialSelected={categoryData?.Materials}
              buttonLabel="Add Category"
              onSelect={(item: any) => handleSelect("Materials", item)}
              onClose={() => dispatch(closePopup(getPopupId("Materials")))}
            />
          )}
        </div>
      </div>

      {popups["DeleteWipRequiredThingsName" + `${id}`] && (
        <DeletePopup
          width="23rem"
          height="calc(100% - 9rem)"
          heightupperlimit="0rem"
          header={`Are you sure you want to delete this ${getCategoryName(
            deleteIdParent
          )} ?`}
          callbackDelete={handleDeleteWorkInstructionItem}
          onClose={() => {
            dispatch(closePopup("DeleteWipRequiredThingsName" + `${id}`));
          }}
        >
          <div className={styles.summaryDivData}>
            <div className={styles.summaryDataContent}>
              <p
                style={{ color: "var(--text-black-60)" }}
                className="p_tag_14px"
              >
                {getCategoryName(deleteIdParent)}
              </p>
              <div
                className=""
                style={{ display: "flex", gap: "1rem", flexWrap: "wrap" }}
              >
                <h4 style={{ color: "var(--text-black-87)" }}>
                  {requiredThingsDeleteName.name}
                </h4>
              </div>
            </div>
          </div>
        </DeletePopup>
      )}
      {isEdit && handleDelete && (
        <div className={styles.delete_icon_tooltip} onClick={handleDelete}>
          <DeleteIcon />
        </div>
      )}
    </div>
  );
};

export default SubTaskWorkInstructionsPopup;
