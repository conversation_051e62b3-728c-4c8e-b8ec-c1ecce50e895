import { getImageUrl } from "./../../../../Backup/TaskMasterBackup/index";
import {
  CheckSubtaskWeightageRequest,
  CheckSubtaskWeightageResponse,
  WorkInstruction,
} from "./../../../../interfaces/Modules/TaskMaster/TaskMasterInterface/TaskMaster.d";

import { baseApi } from "../..";
import { taskmaster, url } from "../../../../config/urls";
import {
  base64ToFile,
  compressImage,
  isBase64,
} from "../../../../functions/functions";
import {
  ApiResponse,
  GetTaskResponse,
  Responses,
  SubTaskDetail,
  SubTaskRouteInterface,
  TaskBuildingBlocksData,
  TaskMasterTableResponse,
} from "../../../../interfaces/Modules/TaskMaster/TaskMasterInterface/TaskMaster";

// Root type for the data structure
export const TaskMasterApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    //

    addTaskCategory: builder.mutation<
      ApiResponse<null>,
      { Categoryname: string; CategoryDescription: string }
    >({
      query: (credentials) => ({
        url: `${taskmaster}/addCategory`,
        method: "POST",
        body: credentials,
        // credentials: "include",
      }),

      invalidatesTags: ["UpdateCategory"],
    }),

    //
    // restore category start
    restoreCategory: builder.mutation<
      ApiResponse<null>,
      { categoryId: string }
    >({
      query: (categoryId) => ({
        url: `${taskmaster}/restoreCategory`,
        method: "POST",
        body: categoryId,
      }),

      invalidatesTags: ["UpdateCategory"],
    }),
    // restore category end
    restoreTask: builder.mutation<ApiResponse<null>, { taskId: string }>({
      query: (taskId) => ({
        url: `${taskmaster}/restoreTask`,
        method: "POST",
        body: taskId,
      }),

      invalidatesTags: ["UpdateCategory"],
    }),
    editTaskCategory: builder.mutation<
      ApiResponse<null>,
      { categoryId: string; name: string; Description: string }
    >({
      query: (credentials) => ({
        url: `${taskmaster}/editCategory`,
        method: "PUT",
        body: credentials,
      }),
      // invalidatesTags: ["EditCategory"],
    }),

    //

    deleteTaskCategory: builder.mutation<
      ApiResponse<null>,
      { categoryId: string }
    >({
      query: ({ categoryId }) => ({
        url: `${taskmaster}/deleteCategory/${categoryId}`,
        method: "DELETE",
      }),
      invalidatesTags: ["DeleteCategory"],
    }),

    //

    deleteTask: builder.mutation<ApiResponse<null>, { taskId: string }>({
      query: ({ taskId }) => ({
        url: `${taskmaster}/deleteTaskByTaskId/${taskId}`,
        method: "DELETE",
      }),
      // invalidatesTags: ["DeleteTask"],
    }),

    //

    deleteSubTask: builder.mutation<
      ApiResponse<null>,
      { subTaskId: string; taskId: string }
    >({
      query: ({ subTaskId, taskId }) => ({
        url: `${taskmaster}/subtask/deletesubtask?SubtaskId=${subTaskId}&TaskId=${taskId}`,
        method: "DELETE",
      }),
      invalidatesTags: ["DeleteSubtask"],
    }),

    //

    getTaskCategories: builder.query<Responses, string>({
      query: (time: string) => ({
        url: `${taskmaster}/getCategoriesData/${time ? time : ""}`,
        method: "GET",
      }),
      extraOptions: {
        abortOnUnmount: true, // Automatically cancels previous requests
      },
      keepUnusedDataFor: 0,
      // providesTags: ["EditCategory", "DeleteCategory", "UpdateCategory"],
    }),

    approveOrRejectCategory: builder.mutation<
      ApiResponse<string>, // adjust if your API returns something else
      {
        categoryId: string;
        isApprove: boolean;
        comment: string;
      }
    >({
      query: (body) => ({
        url: `mdpanel/actiononcategoryrequests`,
        method: "POST",
        body,
      }),
      invalidatesTags: ["UpdateCategory"], // ensures fresh data on refetch
    }),

    addTaskByCategoryId: builder.mutation<
      ApiResponse<null>,
      {
        Taskname: string;
        CategoryId: string;
        Unit: string;
        Description: string;
        Quantity: number;
      }
    >({
      query: (credentials) => ({
        url: `${taskmaster}/addTasks`,
        method: "POST",
        body: credentials,
      }),
      invalidatesTags: ["NewTask", "TaskCategory"],
    }),

    //
    getAllTaskByCategoryId: builder.query<
      GetTaskResponse,
      { id: string | undefined; time: string }
    >({
      query: ({ id, time }) => ({
        url: `${taskmaster}/getAllTaskByCategoryId/false/${id}/${
          time ? time : ""
        }`,
        method: "GET",
      }),
      extraOptions: {
        abortOnUnmount: true, // Automatically cancels previous requests
      },
      keepUnusedDataFor: 0,
    }),

    //
    addSubTasksById: builder.mutation<ApiResponse<null>, SubTaskDetail>({
      query: (credentials) => ({
        url: `${taskmaster}/subtask/addsubTask?TaskId=${credentials.taskId}`,
        method: "POST",
        body: credentials,
      }),
      // invalidatesTags: ["SubTasks"],
    }),

    getTaskBuildingBlocks: builder.query<
      ApiResponse<TaskBuildingBlocksData>,
      void
    >({
      query: () => ({
        url: `${taskmaster}/getTaskBuildingBlocks`,
        method: "GET",
      }),
    }),
    getAllCategoryByTableName: builder.query<
      ApiResponse<TaskBuildingBlocksData>,
      { tablename: string }
    >({
      query: ({ tablename }) => ({
        url: `${taskmaster}/getAllcatageorybyTablename?tableName=${tablename}`,
        method: "GET",
      }),
    }),
    getdatabycatgoryname: builder.query<
      ApiResponse<TaskBuildingBlocksData>,
      { _id: string; modelname: string }
    >({
      query: ({ _id, modelname }) => ({
        url: `${taskmaster}/getAlldesiginationsbycategoryid?_id=${_id}&modelname=${modelname}`,
        method: "GET",
      }),
    }),

    updateTaskById: builder.mutation<
      ApiResponse<null>,
      {
        taskId: string;
        name: string;
        Unit: string;
        Description: string;
        Quantity: number;
        departmentId: string[];
        designationId: string[];
        machinaryId: string[];
        toolId: string[];
        manpowerId: string[];
        materialId: string[];
        Adminid: string[];
        AssigneeId: string[];
        Reporters: {
          _id?: string;
          Level: string;
          designationId: string[];
        }[];
        WorkInstructions: any;
        TaskClosing: any;
        Failuremode: any;
        ControlPlan: any;
        Tobedeleted: string[];
      }
    >({
      query: ({ taskId, ...payload }) => {
        const formData = new FormData();
        const appendImage = async (
          images: { photos: string; Decription: string }[],
          Index: number,
          name: string,
          descName: string,
          formData: FormData
        ) => {
          images?.forEach(async (image: any, imageIndex: number) => {
            const file = await base64ToFile(
              image?.photos,
              `${name}[${Index}]image[${imageIndex}]`
            );
            // const compressedFile = await compressImage(file!, 0.7);

            // console.log("compressedFile", compressedFile);

            formData.append(`${name}[${Index}]image[${imageIndex}]`, file);
            formData.append(
              `${descName}[${Index}]desc[${imageIndex}]`,
              JSON.stringify(image?.Decription)
            );
          });
        };

        const filterWorkInstructions = payload?.WorkInstructions?.map(
          ({ photoRef, ...rest }: any) => {
            if (!Array.isArray(photoRef)) return { ...rest, photoRef: [] };

            const filteredPhotos = photoRef.filter(
              (photo: any) => !isBase64(photo?.photos)
            );

            return {
              ...rest,
              photoRef: filteredPhotos,
            };
          }
        );

        const filterTaskClosing = payload?.TaskClosing?.map(
          ({ photoRef, ...rest }: any) => {
            if (!Array.isArray(photoRef)) return { ...rest, photoRef: [] };

            const filteredPhotos = photoRef.filter(
              (photo: any) => !isBase64(photo?.photos)
            );

            return {
              ...rest,
              photoRef: filteredPhotos,
            };
          }
        );

        formData.append("name", payload.name);
        formData.append("Unit", payload.Unit);
        formData.append("Description", payload.Description);
        formData.append("Quantity", String(payload.Quantity)); // convert number to string
        formData.append("DepartmentId", JSON.stringify(payload.departmentId));
        formData.append("DesignationId", JSON.stringify(payload.designationId));
        formData.append("ManpowerId", JSON.stringify(payload.manpowerId));
        formData.append("MachinaryId", JSON.stringify(payload.machinaryId));
        formData.append("ToolId", JSON.stringify(payload.toolId));
        formData.append("MaterialId", JSON.stringify(payload.materialId));
        formData.append("Assignee", JSON.stringify(payload.AssigneeId));
        formData.append("Reporters", JSON.stringify(payload.Reporters));
        formData.append("Taskclosingreq", JSON.stringify(filterTaskClosing));

        filterWorkInstructions.length > 0
          ? formData.append(
              "WorkInstructions",
              JSON.stringify(filterWorkInstructions)
            )
          : formData.append("WorkInstructions", JSON.stringify([]));
        formData.append("FMEA", JSON.stringify(payload.Failuremode));
        formData.append("Controlplan", JSON.stringify(payload.ControlPlan));
        formData.append("Tobedeleted", JSON.stringify(payload.Tobedeleted));
        formData.append("Adminid", JSON.stringify(payload.Adminid));

        payload.WorkInstructions &&
          payload.WorkInstructions.length > 0 &&
          payload?.WorkInstructions?.forEach(
            async (workInst: any, Index: number) => {
              if (workInst?.photoRef?.length) {
                await appendImage(
                  workInst.photoRef,
                  Index,
                  "work",
                  "work_desc",
                  formData
                );
              }
            }
          );

        payload?.TaskClosing?.forEach((Task: any, Index: number) => {
          if (Task?.photoRef?.length) {
            appendImage(
              Task.photoRef,
              Index,
              "taskclosingreq",
              "taskclosingreq_desc",
              formData
            );
          }
        });

        console.log("form dataaaaa>>>>", formData);

        return {
          url: `${taskmaster}/taskupdate?_id=${taskId}`,
          method: "PUT",
          body: formData,
        };
      },
    }),

    //
    getTaskDetailsByTaskId: builder.query<TaskMasterTableResponse, any>({
      query: (taskid) => ({
        url: `${taskmaster}/taskdetails/getDetails?TaskId=${taskid}`,
        method: "GET",
      }),
      keepUnusedDataFor: 0,
      // providesTags: ["NewTask"],
    }),

    //
    getSubTaskRouteByTaskId: builder.query<SubTaskRouteInterface, string>({
      query: (id) => ({
        url: `${taskmaster}/getSubtasksRoute/${id}`,
        method: "GET",
      }),
      providesTags: ["DeleteSubtask", "BasicDetail"],
      keepUnusedDataFor: 0,
    }),

    editTask: builder.mutation<
      ApiResponse<null>,
      {
        taskId: string;
        name: string;
        Description: string;
        Unit: string;
        Quantity: number;
      }
    >({
      query: (task) => ({
        url: `taskmaster/updateTask`,
        method: "PUT",
        body: task,
      }),
      // invalidatesTags: ["Task"],
    }),

    //
    getTaskBasicsDetails: builder.query({
      query: (taskId) => ({
        url: `${taskmaster}/getTaskBasicsDetails/${taskId}`,
        method: "GET",
      }),
      // providesTags: ["Task"],
      keepUnusedDataFor: 0,
    }),

    //
    updateBasicSubTaskById: builder.mutation<
      ApiResponse<null>,
      {
        subtaskId: string;
        name: string;
        Description: string;
        Unit: string;
        subtaskWeighatages: string;
        Tracking: string;
      }
    >({
      query: ({ ...payload }) => ({
        url: `${taskmaster}/subtask/updatebasicdetails`,
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          subtaskId: payload.subtaskId,
          name: payload.name,
          Description: payload.Description,
          Unit: payload.Unit,
          subtaskWeighatages: payload.subtaskWeighatages,
          Tracking: payload.Tracking,
        }),
        invalidatesTags: ["BasicDetail"],
      }),
    }),

    getsubTaskDetails: builder.query({
      query: (subTaskId) => ({
        url: `${taskmaster}/subtask/subtaskdetails?subtaskId=${subTaskId}`,
        method: "GET",
      }),
      providesTags: ["BasicDetail"],
    }),

    getSubtaskForTriggerEvent: builder.query({
      query: ({ TaskId }) => {
        return {
          url: `${taskmaster}/getSubtaskForTriggerEvent?taskId=${TaskId}`,
          method: "GET",
        };
      },
    }),
    getAllBrands: builder.query({
      query: () => {
        return {
          url: `${taskmaster}/getAllBrands`,
          method: "GET",
        };
      },
    }),
    // this is update sub task
    updateSubTask: builder.mutation<
      ApiResponse<null>,
      {
        SubtaskId: string;
        name: string;
        Unit: string;
        Description: string;
        subtaskWeighatages: number;
        Tracking: string;
        machinaryId: string[];
        toolId: string[];
        manpowerId: string[];
        materialId: string[];
        AdminId: string[];
        AssigneeId: string[];
        Reporters: { _id?: string; Level: string; designationId: string[] }[];
        WorkInstructions: any[];
        TaskClosing: any[];
        Failuremode: any[];
        ControlPlan: any[];
        TriggerEvent: {};
        Tobedeleted: { workinstruction: string[] };
      }
    >({
      query: ({ SubtaskId, ...payload }) => {
        const appendImage = async (
          images: { photos: string; Decription: string }[],
          Index: number,
          name: string,
          descName: string,
          formData: FormData
        ) => {
          images?.forEach(async (image: any, imageIndex: number) => {
            const file = await base64ToFile(
              image?.photos,
              `${name}[${Index}]image[${imageIndex}]`
            );
            // const compressedFile = await compressImage(file!, 0.7);

            // console.log("compressedFile", compressedFile);

            formData.append(`${name}[${Index}]image[${imageIndex}]`, file);
            formData.append(
              `${descName}[${Index}]desc[${imageIndex}]`,
              JSON.stringify(image?.Decription)
            );
          });
        };

        const filterWorkInstructions = payload?.WorkInstructions?.map(
          ({ photoRef, ...rest }: any) => {
            if (!Array.isArray(photoRef)) return { ...rest, photoRef: [] };

            const filteredPhotos = photoRef.filter(
              (photo: any) => !isBase64(photo?.photos)
            );

            return {
              ...rest,
              photoRef: filteredPhotos,
            };
          }
        );

        const filterTaskClosing = payload?.TaskClosing?.map(
          ({ photoRef, ...rest }: any) => {
            if (!Array.isArray(photoRef)) return { ...rest, photoRef: [] };

            const filteredPhotos = photoRef.filter(
              (photo: any) => !isBase64(photo?.photos)
            );

            return {
              ...rest,
              photoRef: filteredPhotos,
            };
          }
        );

        const formData = new FormData();

        const serializeIds = (arr?: unknown[]) =>
          JSON.stringify((arr ?? []).filter(Boolean));

        formData.append("name", payload.name);
        formData.append("Unit", payload.Unit);
        formData.append("Description", payload.Description);
        formData.append(
          "subtaskWeighatages",
          String(payload.subtaskWeighatages)
        ); // convert number to string
        formData.append("Tracking", payload.Tracking);
        formData.append("ManpowerId", serializeIds(payload.manpowerId));
        formData.append("MachinaryId", serializeIds(payload.machinaryId));
        formData.append("ToolId", serializeIds(payload.toolId));
        formData.append("MaterialId", serializeIds(payload.materialId));
        formData.append("AssigneeId", JSON.stringify(payload.AssigneeId));
        formData.append("Reporters", JSON.stringify(payload.Reporters));
        formData.append("Taskclosingreq", JSON.stringify(filterTaskClosing));
        formData.append(
          "WorkInstructions",
          JSON.stringify(filterWorkInstructions)
        );
        formData.append("FMEA", JSON.stringify(payload.Failuremode));
        formData.append("Controlplan", JSON.stringify(payload.ControlPlan));
        formData.append("Tobedeleted", JSON.stringify(payload.Tobedeleted));
        formData.append("AdminId", JSON.stringify(payload.AdminId));
        formData.append("TriggerEvent", JSON.stringify(payload.TriggerEvent));
        payload?.WorkInstructions?.forEach((workInst: any, Index: number) => {
          if (workInst?.photoRef?.length) {
            appendImage(
              workInst.photoRef,
              Index,
              "work",
              "work_desc",
              formData
            );
          }
        });

        payload?.TaskClosing?.forEach((Task: any, Index) => {
          if (Task?.photoRef?.length > 0) {
            appendImage(
              Task.photoRef,
              Index,
              "taskclosingreq",
              "taskclosingreq_desc",
              formData
            );
          }
        });

        return {
          url: `${taskmaster}/subtask/update?SubtaskId=${SubtaskId}`,
          method: "PUT",
          body: formData,
        };
      },
      // invalidatesTags: ["SubtaskUpdate"],
    }),

    //
    getsubTaskDetailsLazy: builder.query({
      query: (subTaskId) => ({
        url: `${taskmaster}/subtask/subtaskdetails?subtaskId=${subTaskId}`,
        method: "GET",
      }),
      // providesTags: ["Task"],
      keepUnusedDataFor: 0,
    }),

    syncToBackChanges: builder.query<any, void>({
      query: () => ({
        url: `${url}/downlaoddata/getthelatestdata?tablelist=TaskCategory&sincetime=2024-12-13T07:21:29.749Z`,
        method: "GET",
        // credentials: "include",
      }),
    }),

    checkSubtaskWeightage: builder.mutation<
      CheckSubtaskWeightageResponse,
      CheckSubtaskWeightageRequest
    >({
      query: ({ ...payload }) => ({
        url: `${taskmaster}/checkSubtaskWeightage`,
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          taskId: payload.taskId,
          weightage: payload.weightage,
        }),
      }),
    }),

    getImageUrlApi: builder.query<any, string>({
      query: (img) => ({
        url: `downloadimage/getimage?key=${img}`,
        method: "GET",
      }),
    }),
  }),

  overrideExisting: false,
});

export const {
  useAddTaskCategoryMutation,
  useGetTaskCategoriesQuery,
  useApproveOrRejectCategoryMutation,
  useAddTaskByCategoryIdMutation,
  useGetAllTaskByCategoryIdQuery,
  useGetTaskDetailsByTaskIdQuery,
  useLazyGetTaskDetailsByTaskIdQuery,
  useAddSubTasksByIdMutation,
  useGetTaskBuildingBlocksQuery,
  useUpdateTaskByIdMutation,
  useGetSubTaskRouteByTaskIdQuery,
  useEditTaskCategoryMutation,
  useDeleteTaskCategoryMutation,
  useDeleteTaskMutation,
  useDeleteSubTaskMutation,
  useLazySyncToBackChangesQuery,
  useEditTaskMutation,
  useGetTaskBasicsDetailsQuery,
  useGetsubTaskDetailsQuery,
  useLazyGetsubTaskDetailsLazyQuery,
  useUpdateSubTaskMutation,
  useRestoreCategoryMutation,
  useUpdateBasicSubTaskByIdMutation,
  useRestoreTaskMutation,
  useGetSubtaskForTriggerEventQuery,
  useCheckSubtaskWeightageMutation,
  useGetImageUrlApiQuery,
  useGetAllBrandsQuery,
  useLazyGetAllCategoryByTableNameQuery,
  useLazyGetdatabycatgorynameQuery,
} = TaskMasterApi;
