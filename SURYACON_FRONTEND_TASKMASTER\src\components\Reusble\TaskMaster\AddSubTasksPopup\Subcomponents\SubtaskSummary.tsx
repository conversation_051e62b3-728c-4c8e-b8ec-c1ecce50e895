import React from "react";
import styles from "../Styles/AddSubTasksPopup.module.css";
import { WieghtPercentageIcon } from "../../../../../assets/icons";
import { SubtaskSummaryProps } from "../../TaskMasterInterfaces/TaskMasterInterface";
import { isValidValue } from "../../../../../functions/functions";

const SubtaskSummary: React.FC<SubtaskSummaryProps> = ({
  isEdit,
  tracking,
  initialState,
  inputValues,
  height
}) => {
  return (
    <div
     className={styles.summary_subtask_main_content}
     style={{
              height: height ? `calc(100% - ${height}px - 4.25rem)` : undefined
            }} 
     >
      <div className={styles.flexContainer}>
        {inputValues.name && isValidValue(inputValues.name) && (
          <div className={styles.summaryDivData}>
            <div className={styles.summaryDataContent}>
              <p
                style={{ color: "var(--text-black-60)" }}
                className="p_tag_14px"
              >
                Name
              </p>
              <h4
                style={{
                  color:
                    initialState?.name !== inputValues?.name && isEdit
                      ? "var(--secondary_color)"
                      : "var(--text-black-87)",
                }}
              >
                {inputValues.name || "No name provided"}
              </h4>
            </div>
          </div>
        )}
        {inputValues.Unit && isValidValue(inputValues.Unit) && (
          <div className={styles.summaryDivData}>
            <div className={styles.summaryDataContent}>
              <p
                style={{ color: "var(--text-black-60)" }}
                className="p_tag_14px"
              >
                Unit
              </p>
              <h4
                style={{
                  color:
                    initialState?.Unit !== inputValues?.Unit && isEdit
                      ? "var(--secondary_color)"
                      : "var(--text-black-87)",
                }}
              >
                {inputValues.Unit || "No unit"}
              </h4>
            </div>
          </div>
        )}
      </div>
      {inputValues.Description && isValidValue(inputValues.Description) && (
        <div className={styles.summaryDivData}>
          <div className={styles.summaryDataContent}>
            <p style={{ color: "var(--text-black-60)" }} className="p_tag_14px">
              Description
            </p>
            <h4
              style={{
                color:
                  initialState?.Description !== inputValues?.Description &&
                  isEdit
                    ? "var(--secondary_color)"
                    : "var(--text-black-87)",
              }}
            >
              {inputValues.Description || "No description"}
            </h4>
          </div>
        </div>
      )}
      <div className={styles.flexContainer}>
        {inputValues.subtaskWeighatages &&
          isValidValue(inputValues.subtaskWeighatages) && (
            <div className={styles.summaryDivData}>
              <div className={styles.summaryDataContent_weightage}>
                <div>
                  <p
                    style={{ color: "var(--text-black-60)" }}
                    className="p_tag_14px"
                  >
                    Weightage
                  </p>
                  <h4
                    style={{
                      color:
                        String(inputValues?.subtaskWeighatages) !==
                          String(initialState?.subtaskWeighatages) && isEdit
                          ? "var(--secondary_color)"
                          : "var(--text-black-87)",
                    }}
                  >
                    {inputValues.subtaskWeighatages || "No weightage"}
                  </h4>
                </div>
                <WieghtPercentageIcon />
              </div>
            </div>
          )}
        {tracking && isValidValue(tracking) && (
          <div className={styles.summaryDivData}>
            <div className={styles.summaryDataContent}>
              <p
                style={{ color: "var(--text-black-60)" }}
                className="p_tag_14px"
              >
                Tracking
              </p>
              <h4
                style={{
                  color:
                    initialState?.Tracking?.toLowerCase() !==
                      inputValues?.Tracking?.toLowerCase() && isEdit
                      ? "var(--secondary_color)"
                      : "var(--text-black-87)",
                }}
              >
                {tracking || "No tracking"}
              </h4>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default SubtaskSummary;
