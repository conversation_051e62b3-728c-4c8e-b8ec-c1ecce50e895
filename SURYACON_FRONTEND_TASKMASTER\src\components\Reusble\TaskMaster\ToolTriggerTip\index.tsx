import React from "react";
import styles from "./Styles/Tooltip.module.css";
import { TooltipProps } from "../TaskMasterInterfaces/TaskMasterInterface";
import { RootState } from "../../../../redux/store";
import { useSelector } from "react-redux";
import { isValidValue } from "../../../../functions/functions";


const ToolTriggerTip: React.FC<TooltipProps> = ({
  content,
  activeTip,
  handleClick,
  isTimeInterval = false,
}) => {
  const triggerData = useSelector(
    (state: RootState) => state.taskMaster.currentSubtaskData?.AutoId
  );
  console.log("first", triggerData?.TriggerAction?.ActionTime)

  return (
    <>
      <div
        onClick={(e) => handleClick && handleClick(e)}
        className={`${styles.tooltip} ${
          isTimeInterval && triggerData?.TriggerAction?.ActionTime ? styles.time_interval : ""
        }`}
      >
        <div>
          <h4>{content ?? ""} {activeTip == "ResponseTime" ? "hours" : ""}</h4>
        </div>
        {isTimeInterval && isValidValue(triggerData?.TriggerAction?.ActionTime) && triggerData?.TriggerAction?.ActionTime !=0 && (
          <div>
            <h4>Time Interval</h4>
            <p className={styles.time_interval_p}>{triggerData?.TriggerAction?.ActionTime ?? ""} Hours</p>
          </div>
        )}
      </div>
    </>
  );
};

export default ToolTriggerTip;
