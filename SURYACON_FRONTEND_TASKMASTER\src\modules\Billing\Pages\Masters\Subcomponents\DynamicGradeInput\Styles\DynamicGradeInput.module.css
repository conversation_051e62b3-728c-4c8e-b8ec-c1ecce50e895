.datafield_box {
    inline-size: 100%;
    border-radius: 24px;
    border: 1px solid var(--text-black-28);
    padding-inline: 15px;
    padding-block: 14px;
    position: relative;
    /* min-block-size: 55px; */
    flex-wrap: wrap;
    max-width: 550px;
    color: var(--text-black-60);
}

.delete_icon_tooltip {
    position: absolute;
    top: -0.5rem;
    right: 0rem;
    cursor: pointer;
    height: 24px;
    width: 24px;
    background-color: var(--secondary_warning_background);
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 100%;
}

.datafield_header {
    width: 100%;
    display: flex;
    justify-content: space-between;
    /* Space between label/grades and icon */
    align-items: center;
}

.tooltip_container {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    width: 100%;
    /* Ensure it takes full width */
}

.gradeItem {
    background-color: #f0f0f0;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 14px;
}

.datafield_addIcon {
    cursor: pointer !important;
    display: flex;
    align-items: center;
    justify-content: center;
}

.gradeinputbox_container {
    min-height: 2.6rem;
    min-width: 4rem;
    /* Start small */
    cursor: pointer;
    backdrop-filter: blur(40px);
    border-radius: 25px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding-inline: 0.6rem;
    box-shadow: 0px 0px 4px 0px #91a1a180;
    border: 1px solid var(--primary_color);
    width: fit-content;
    position: relative;
    max-width: 27rem
}

.gradeinputbox {
    min-height: 2.6rem;
    max-height: 8rem;
    background: transparent;
    font-size: 16px;
    border: none;
    outline: none;
    resize: none;
    overflow: hidden;
    padding: 0.6rem;
    font-family: inherit;
    /* margin-right: 0.5rem; */
    min-width: 4rem;
    /* Ensures minimum width */
    width: 4rem;
    /* Small initial size */
    white-space: nowrap;
}

.delete_icon_tooltip {
    opacity: 0;
    visibility: hidden;
}

.tooltip_box:hover .delete_icon_tooltip {
    opacity: 1;
    visibility: visible;
}