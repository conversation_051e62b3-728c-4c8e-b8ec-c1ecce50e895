Montserrat Variable Font
========================

This download contains <PERSON><PERSON><PERSON> as both variable fonts and static fonts.

Montserrat is a variable font with this axis:
  wght

This means all the styles are contained in these files:
  Montserrat/Montserrat-VariableFont_wght.ttf
  Montserrat/Montserrat-Italic-VariableFont_wght.ttf

If your app fully supports variable fonts, you can now pick intermediate styles
that aren’t available as static fonts. Not all apps support variable fonts, and
in those cases you can use the static font files for Montserrat:
  Montserrat/static/Montserrat-Thin.ttf
  Montserrat/static/Montserrat-ExtraLight.ttf
  Montserrat/static/Montserrat-Light.ttf
  Montserrat/static/Montserrat-Regular.ttf
  Montserrat/static/Montserrat-Medium.ttf
  Montserrat/static/Montserrat-SemiBold.ttf
  Montserrat/static/Montserrat-Bold.ttf
  Montserrat/static/Montserrat-ExtraBold.ttf
  Montserrat/static/Montserrat-Black.ttf
  Montserrat/static/Montserrat-ThinItalic.ttf
  Montserrat/static/Montserrat-ExtraLightItalic.ttf
  Montserrat/static/Montserrat-LightItalic.ttf
  Montserrat/static/Montserrat-Italic.ttf
  Montserrat/static/Montserrat-MediumItalic.ttf
  Montserrat/static/Montserrat-SemiBoldItalic.ttf
  Montserrat/static/Montserrat-BoldItalic.ttf
  Montserrat/static/Montserrat-ExtraBoldItalic.ttf
  Montserrat/static/Montserrat-BlackItalic.ttf

Get started
-----------

1. Install the font files you want to use

2. Use your app's font picker to view the font family and all the
available styles

Learn more about variable fonts
-------------------------------

  https://developers.google.com/web/fundamentals/design-and-ux/typography/variable-fonts
  https://variablefonts.typenetwork.com
  https://medium.com/variable-fonts

In desktop apps

  https://theblog.adobe.com/can-variable-fonts-illustrator-cc
  https://helpx.adobe.com/nz/photoshop/using/fonts.html#variable_fonts

Online

  https://developers.google.com/fonts/docs/getting_started
  https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_Fonts/Variable_Fonts_Guide
  https://developer.microsoft.com/en-us/microsoft-edge/testdrive/demos/variable-fonts

Installing fonts

  MacOS: https://support.apple.com/en-us/HT201749
  Linux: https://www.google.com/search?q=how+to+install+a+font+on+gnu%2Blinux
  Windows: https://support.microsoft.com/en-us/help/314960/how-to-install-or-remove-a-font-in-windows

Android Apps

  https://developers.google.com/fonts/docs/android
  https://developer.android.com/guide/topics/ui/look-and-feel/downloadable-fonts

License
-------
Please read the full license text (OFL.txt) to understand the permissions,
restrictions and requirements for usage, redistribution, and modification.

You can use them in your products & projects – print or digital,
commercial or otherwise.

This isn't legal advice, please consider consulting a lawyer and see the full
license for all details.
