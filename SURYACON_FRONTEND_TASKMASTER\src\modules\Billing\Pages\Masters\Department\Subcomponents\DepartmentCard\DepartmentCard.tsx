import React, { memo, useEffect, useMemo, useRef, useState } from "react";
import { useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import {
  DesiginationCardLogo,
  EyeHideIcon,
  EyeVisibleIcon,
  InOfficeIcon,
  MailIcon,
  OnSiteIcon,
  QualificationLogo,
  Twodots,
  User as UserIcon,
} from "../../../../../../../assets/icons";
import { useAuth } from "../../../../../../../AuthProvider";
import Button from "../../../../../../../components/Reusble/Global/Button";
import CardDotPopup from "../../../../../../../components/Reusble/Global/MainCardPopup";
import { getValue } from "../../../../../../../functions/functions";
import { IDepartmentCardProps } from "../../../../../../../interfaces/Modules/Billing/DepartmentInterfaces/DepartmentInterfaces";
import { setNavigate } from "../../../../../../../redux/features/Modules/Reusble/navigationSlice";
import { openPopup } from "../../../../../../../redux/features/Modules/Reusble/popupSlice";
import {
  setDepartmentFormData,
  setDesignationFormData,
  setFormMode,
  setInitialDepartmentFormData,
  setInitialDesignationFormData,
  setMdStateData,
} from "../../../../../../../redux/features/Modules/TaskMaster/Slices/MastersSlice";
import styles from "./DepartmentCard.module.css";

const DepartmentCard = ({
  data,
  type = "Department",
  cardHandler,
  isSoftDeleted = false,
}: IDepartmentCardProps) => {
  const [visible, setVisible] = useState(false);
  const roleRef = useRef<HTMLDivElement>(null);
  const cardRef = useRef<HTMLDivElement>(null);
  const [isButtonVisible, setIsButtonVisible] = useState(false);
  const [isPopupOpen, setIsPopupOpen] = useState(false);
  const dispatch = useDispatch();
  const navigate = useNavigate();

  // add by maherswar
  const { user } = useAuth();
  const isMD = user?.designationId?.roleId === "SURYAMD";
  const isHOD = user?.designationId?.roleId === "SURYAHOD";

  // These variables extract and memoize important state from the data prop for rendering and logic control.
  // This approach is best because it keeps the component logic clear, avoids repeated property access, and makes the code more maintainable and readable.
  const isApproved = data?.isApproved;
  const isMdDeclined = data?.MDDenied;
  const declinedComment = data?.MDdeniedComment;
  const isDeleted = data?.updatedData && data?.updatedData?.isDeleted;
  const desiginationCount = isMD
    ? getValue(data, "desigination")?.length
    : data?.desigination?.length || 0;
  const requiredQualificationsCount = isMD
    ? getValue(data, "RequiredQualification")?.length
    : data?.RequiredQualification?.length || 0;
  // const isUpdatedForMd = data?.updatedData ? true : false;

  // console.log("data in card", data);

  useEffect(() => {
    const el = roleRef.current;
    const card = cardRef.current;
    if (!el || !card) return;

    const handleTransitionEnd = () => {
      if (visible) {
        // Scroll fully into view after expansion completes
        el.scrollIntoView({
          behavior: "smooth",
          block: "nearest", // or "start" if you want it at the top
        });
      } else {
        el.style.display = "none";
        el.style.overflowY = "hidden";
        card.style.zIndex = "0";
      }
      el.removeEventListener("transitionend", handleTransitionEnd);
    };

    if (visible) {
      el.style.display = "flex";
      el.style.overflowY = el.scrollHeight > 311 ?  "scroll" : "hidden"
      el.style.transition = "max-height 0.3s ease, opacity 0.3s ease";
      el.style.maxHeight =  `${el.scrollHeight > 311 ?  312 : el.scrollHeight}px`;
      el.style.opacity = "1";
      card.style.zIndex = "5";
    } else {
      el.style.transition = "max-height 0.3s ease, opacity 0.3s ease";
      el.style.maxHeight = ` ${el.scrollHeight}px`; // set initial height
      el.style.opacity = "0";

      // Force reflow then collapse
      void el.offsetHeight;
      el.style.maxHeight = "0px";
    }

    el.addEventListener("transitionend", handleTransitionEnd);

    return () => {
      el.removeEventListener("transitionend", handleTransitionEnd);
    };
  }, [visible]);


  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // console.log("Clicked outside");
      if (cardRef.current && !cardRef.current.contains(event.target as Node)) {
        setVisible(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Prepares form data for edit popup based on card type (Department/Designation)
  const modifyFormData = () => {
    const formData = {
      _id: data._id || "",
      ...(type === "Department"
        ? {
            departmentName: getValue(data, "name"),
            departmentHead: getValue(data, "DepartmentHead"),
            description: getValue(data, "Description"),
            email: getValue(data, "Email"),
          }
        : {
            designationName: getValue(data, "name"),
            jobDescription: getValue(data, "Description"),
            requiredExperience: getValue(data, "Experience"),
            location: getValue(data, "location"),
            qualifications: getValue(data, "RequiredQualification"),
            supervisor: getValue(data, "ReporterId") || null,
          }),
    };
    return formData;
  };

  // Handles card click for navigation and state update (only for Department type)
  const handler = (e: React.MouseEvent) => {
    if (
      isSoftDeleted ||
      !isApproved ||
      isDeleted ||
      isMdDeclined ||
      type === "Desigination"
    )
      return;
    // e.stopPropagation();
    // console.log("Card Handler", data);
    cardHandler(null);
    if (type === "Department") {
      navigate(`/department/${data._id}`);
      dispatch(
        setNavigate({
          title: data?.name ?? "",
          route: `/department/${data?._id}`,
        })
      );
    }
  };

  // Handles edit button click, opens the appropriate popup and sets form data for editing
  const editHandler = (
    e: React.MouseEvent<HTMLDivElement> | React.KeyboardEvent<HTMLDivElement>
  ) => {
    e.stopPropagation();
    // console.log("Edit Handler");
    let popupType = "";
    const formData = modifyFormData();

    // Implement the edit logic here
    if (type === "Department") {
      popupType = "AddDepartmentForm";
      dispatch(setInitialDepartmentFormData(formData));
      dispatch(setDepartmentFormData(formData));
    } else {
      popupType = "AddDesignationForm";
      dispatch(setInitialDesignationFormData(formData));
      dispatch(setDesignationFormData(formData));
    }
    dispatch(setFormMode("Edit"));
    dispatch(openPopup(popupType));
  };

  // Handles reason button click, opens the reason popup with declined comment
  const reasonHandler = (
    e: React.MouseEvent<HTMLDivElement> | React.KeyboardEvent<HTMLDivElement>
  ) => {
    e.stopPropagation();
    if (type === "Desigination") {
      const popupType = "AddDesignationForm";
      console.log("Reason Handler");
      dispatch(setDesignationFormData({ reason: `${declinedComment}` }));
      dispatch(setFormMode("Reason"));
      dispatch(openPopup(popupType));
    } else {
      const popupType = "AddDepartmentForm";
      console.log("Reason Handler");
      console.log("DepartmentForm rendered with formMode:", data);
      dispatch(setDepartmentFormData({ reason: `${declinedComment}` }));
      dispatch(setFormMode("Reason"));
      dispatch(openPopup(popupType));
    }

    // Implement the reason logic here
  };

  // Renders designation or qualification items for the card
  const renderDesignationOrQualification = useMemo(() => {
    const items =
      type === "Department"
        ? isMD
          ? getValue(data, "desigination")
          : data?.desigination
        : isMD
        ? getValue(data, "RequiredQualification")
        : data?.RequiredQualification;
    console.log("Expand item", items);
    return items?.map((item: string, index: number) =>
      typeof item === "string" ? (
        <p key={index} className={styles.desination_role_label}>
          {item}
        </p>
      ) : null
    );
  }, [data]);

  const handleTakeAction = () => {
    dispatch(openPopup("ApprovalForm"));
    dispatch(setFormMode("INITIAL"));
    dispatch(setMdStateData(data));
  };

  return (
    <div
      ref={cardRef}
      onMouseEnter={() => setIsButtonVisible(true)}
      onMouseLeave={() => setIsButtonVisible(false)}
      className={`${styles.card_wrap}
        ${
          (isSoftDeleted || !isApproved || isMdDeclined || isDeleted) &&
          styles.faded_shadow
        }
         ${
           isMD && isApproved === false && isMdDeclined === false
             ? styles.mdDepartmentHoverEffect
             : ""
         }
        ${(isMdDeclined || isDeleted) && styles.declined} 
        ${visible && styles.expannded}`}
      onClick={handler}
      // ${isMD && isUpdatedForMd && styles.updated}
    >
      {!isSoftDeleted &&
        isMD &&
        isApproved === false &&
        isMdDeclined === false && (
          <div className={styles.mdDepartmentActionBtnWrapper}>
            <Button
              type="Approve"
              Content="Take Action"
              Callback={handleTakeAction}
            />
          </div>
        )}

      {isDeleted && <div className={styles.deleted_label}></div>}

      {!isSoftDeleted &&
        !isMD &&
        isHOD &&
        (!isApproved || isMdDeclined) &&
        !isDeleted && (
          <div
            className={`${styles.button_container} ${
              isButtonVisible ? styles.visible : ""
            }`}
          >
            {isMdDeclined && (
              <Button Content="Reason" type="Reason" Callback={reasonHandler} />
            )}
            {
              <Button
                Content={isMdDeclined ? "Edit" : "Edit Request"}
                type="EditRequest"
                Callback={editHandler}
                property="centerButton"
              />
            }
          </div>
        )}

      {!isSoftDeleted && !isHOD && isMD && isMdDeclined && (
        <div
          className={`${styles.button_container} ${
            isButtonVisible ? styles.visible : ""
          }`}
        >
          <Button Content="Reason" type="Reason" Callback={reasonHandler} />
        </div>
      )}

      <div
        className={`${
          isSoftDeleted || !isApproved || isMdDeclined || isDeleted
            ? styles.faded_content
            : styles.card_container
        }`}
      >
        {/* Card header with department/designation name and action icons (eye for MD, menu for others). */}
        <div className={styles.card_header}>
          <h5>{isMD ? getValue(data, "name") : data?.name}</h5>
          <div className={styles.header_right_ctrl_box}>
            {/* MD can toggle visibility of the designation/qualification list */}
            {/* {isMD && isApproved && ( */}
            <span
              onClick={(e) => {
                e.stopPropagation();
                setVisible(!visible);
              }}
            >
              {visible ? (
                <EyeVisibleIcon width="24" height="24" />
              ) : (
                <EyeHideIcon width="23" height="24" />
              )}
            </span>
            {/* )} */}

            {/* Three-dot menu for edit options (not visible for MD) */}
            <div onClick={(e) => e.stopPropagation()}>
              {isPopupOpen && (
                <div
                  style={{
                    height: "2rem",
                    width: "100%",
                    cursor: "pointer",
                    position: "absolute",
                    zIndex: 2,
                  }}
                ></div>
              )}

              {!isMD && (
                <div
                  onClick={() => setIsPopupOpen((prev) => !prev)}
                  style={{ cursor: "pointer", position: "relative" }}
                >
                  <Twodots />
                  {isPopupOpen && (
                    <CardDotPopup
                      editData={modifyFormData()}
                      setIsPopupOpen={setIsPopupOpen}
                      varient={type}
                    />
                  )}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Shows department head or experience, and email/location info with icons. */}
        <div className={styles.card_info}>
          {(data?.DepartmentHead?._id || data?.Experience) && (
            <div className={styles.card_info_label}>
              <p className={styles.icon_box}>
                <UserIcon />
              </p>
              <p className={styles.card_info_label}>
                {type === "Department"
                  ? isMD
                    ? getValue(data, "DepartmentHead")?.name
                    : data?.DepartmentHead?.name
                  : `${
                      isMD ? getValue(data, "Experience") : data?.Experience
                    } ${
                      Number(
                        isMD ? getValue(data, "Experience") : data?.Experience
                      ) > 1
                        ? "years"
                        : "year"
                    }`}
              </p>
            </div>
          )}
          {((type === "Department" && data?.Email) ||
            (type === "Desigination" && data?.location)) && (
            <div>
              <p className={styles.icon_box}>
                {type === "Department" ? (
                  <MailIcon />
                ) : (isMD ? getValue(data, "location") : data.location) ===
                  "headoffice" ? (
                  <InOfficeIcon />
                ) : (
                  <OnSiteIcon width="20" height="20" />
                )}
              </p>
              <p className={styles.card_info_label}>
                {type === "Department"
                  ? isMD
                    ? getValue(data, "Email")
                    : data.Email
                  : (isMD ? getValue(data, "location") : data.location) ===
                    "headoffice"
                  ? "In Office"
                  : "On Site"}
              </p>
            </div>
          )}
        </div>

        {/* Renders the designation/qualification section with icon, label, count (with leading zero if <10), and expands to show the list when visible. */}
        <div
          className={`${styles.card_desgination_section} ${
            visible &&
            (desiginationCount > 0 || requiredQualificationsCount > 0) &&
            styles.visible
          }`}
        >
          <div className={styles.card_designation_info}>
            <p className={styles.card_des_icon_box}>
              {type === "Department" ? (
                <DesiginationCardLogo />
              ) : (
                <QualificationLogo />
              )}
            </p>
            <div className={styles.card_designation_content}>
              <p>{type === "Department" ? "Designations" : "Qualifications"}</p>
              <p>
                {type === "Department"
                  ? desiginationCount < 10 && desiginationCount > 0
                    ? `0${desiginationCount}`
                    : desiginationCount
                  : requiredQualificationsCount < 10
                  ? `0${requiredQualificationsCount}`
                  : requiredQualificationsCount}
              </p>
            </div>
          </div>
          {/* Expandable list of designations or qualifications */}
          <div
            ref={roleRef}
            className={`${styles.designations_role_box} ${
              visible ? styles.visible : ""
            }`}
          >
            {renderDesignationOrQualification}
          </div>
        </div>
      </div>
    </div>
  );
};

export default memo(DepartmentCard);
