import React, { useEffect, useRef, useState } from "react";
import styles from "./Styles/TargetBadge.module.css";
import { TargetBadgeProps } from "../GlobalInterfaces/GlobalInterface";
import { setCurrentCardId } from "../../../../redux/features/Modules/Billing/BillingApproval/Slices/BillingApprovalSlice";
import { useAppDispatch } from "../../../../redux/hooks/Modules/Reduxhooks/ReduxHooks";
import { useSelector } from "react-redux";
import { RootState } from "../../../../redux/store";

const TargetBadge: React.FC<TargetBadgeProps> = ({
  maxlength,
  id = "",
  edit = false,
  handleValuechange,
  value,
  order,
  outerContainerClassName,
  bubbleValue,
  bubbleTextTagName = "span",
  bubbletextClassName,
  valueTextTagName = "p",
  valueTextClassName,
  secondValueTextTagName = "div",
  secondvalue,
  secondValueTextClassName,
  bubbleClassname,
  active,
  bubbleBackgroundColor,
  backgroundColor,
  frontIcon,
  input = false,
  icon,
  isArray = false,
  disabled = false,
  onClick,
}) => {
  const dispatch = useAppDispatch();
  const inputRef = useRef<HTMLInputElement>(null);
  const currentEditId = useSelector(
    (state: RootState) => state.monthlyTarget.CurrentEditcardId
  );

  const [inputValue, setInputValue] = useState(secondvalue?.toString() || "");
  const [isEditValue, setIsEditValue] = useState(false);
  const [isChanged, setIsChanged] = useState(false);
  const [length, setLength] = useState<number | null>(0);

  const combineClassNames = (classNames?: string | string[]) => {
    if (!classNames) return "";
    if (Array.isArray(classNames)) {
      return classNames
        .map((className) => styles[className] || className)
        .join(" ");
    }
    return styles[classNames] || classNames;
  };

  // 🔥 This is the fix to sync prop changes
  useEffect(() => {
    setInputValue(secondvalue?.toString() || "");
  }, [secondvalue]);

  useEffect(() => {
    if (isEditValue) {
      inputRef.current?.focus();
    }
  }, [isChanged, id]);

  useEffect(() => {
    if (isArray) {
      setLength(value?.length || 0);
    }
  }, [value]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const val = e.target.value;
    const regex =
      id === "weightage"
        ? /^(?:\d{0,2}(?:\.\d{0,2})?|100(?:\.0{0,2})?)$/
        : /^\d{0,10}(\.\d{0,10})?$/;

    if (regex.test(val)) {
      setInputValue(val);
      handleValuechange && handleValuechange(e);
    }
  };

  return (
    <div>
      <div
        className={`${styles.target_badge_outer_container} ${
          active && styles.border
        } ${order && styles.order} ${combineClassNames(
          outerContainerClassName
        )} ${disabled ? styles.disabled : ""}`}
        style={{ backgroundColor }}
        onClick={(e) => {
          if (disabled) return;
          e.stopPropagation();
          onClick && onClick();

          if (input) {
            setIsChanged(!isChanged);
            if (edit) {
              dispatch(setCurrentCardId(id));
            }
            setIsEditValue(true);
          }
        }}
      >
        {frontIcon && <div className={styles.iconContainer}>{frontIcon}</div>}

        <div style={{ width: "100%" }}>
          {isArray ? (
            <div
              className={styles.target_badge_array}
              style={{
                display: "flex",
                alignItems: "center",
                gap: "0.25rem",
                width: "80%",
                marginRight: "1rem",
              }}
            >
              {value?.map((item: string, index: any) => (
                <div
                  key={index}
                  className={styles.target_badge_array_item}
                  style={{ width: !item ? "0px" : "auto" }}
                >
                  {index < 2 &&
                    React.createElement(
                      valueTextTagName,
                      {
                        className: `${
                          inputValue.length > 0 ||
                          (currentEditId == id && input)
                            ? "small_text_p_400"
                            : combineClassNames(valueTextClassName)
                        }`,
                      },
                      item || ""
                    )}
                </div>
              ))}
            </div>
          ) : (
            React.createElement(
              valueTextTagName,
              {
                className: `${
                  inputValue.length > 0 || (currentEditId == id && input)
                    ? "small_text_p_400"
                    : combineClassNames(valueTextClassName)
                }`,
              },
              value || ""
            )
          )}

          {secondvalue &&
            !input &&
            React.createElement(
              secondValueTextTagName,
              { className: combineClassNames(secondValueTextClassName) },
              secondvalue
            )}

          {input &&
            (inputValue.length > 0 || currentEditId == id) && (
              <input
                ref={inputRef}
                onChange={handleInputChange}
                value={inputValue}
                readOnly={!(edit && isEditValue)}
                className={`${styles.mt_card_input} input_16px`}
                type="text"
                style={{
                  width: `${Math.max(50, inputValue.length * 10)}px`,
                }}
                maxLength={maxlength}
              />
            )}
        </div>

        {icon && <div className={styles.iconContainer}>{icon}</div>}

        {bubbleValue && (
          <div
            className={`${styles.targetBubble} ${combineClassNames(
              bubbleClassname
            )}`}
            style={{ backgroundColor: bubbleBackgroundColor }}
          >
            {React.createElement(
              bubbleTextTagName,
              { className: combineClassNames(bubbletextClassName) },
              bubbleValue
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default TargetBadge;
