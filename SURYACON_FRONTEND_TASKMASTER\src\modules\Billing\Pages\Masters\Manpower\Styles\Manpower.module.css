.loader_loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.loader_loading_image {
  width: 500px;
  height: 500px;
}

.addmanpowerform_container {
  position: fixed;
  top: 7rem;
  right: 1.5rem;
  transform: translate(0%, 0%);
  padding: 1.25rem 0.9rem;
  z-index: 89;
  width: 34.5rem;
  max-width: 90%;
  height: calc(100% - 8.5rem);
  animation: slideIn 0.5s ease-out;
  backdrop-filter: blur(100px);
  border-radius: 2.6rem;
  border: 1px solid;
  border-image-source: linear-gradient(
    130.72deg,
    rgba(237, 231, 231, 0.07) -22.43%,
    rgba(251, 251, 251, 0.05) 75.66%
  );
  backdrop-filter: blur(150px);
  box-shadow: 0px 4px 40px 0px #00000080;
  display: flex;
  flex-direction: column;
}

.inner_cardview {
  display: grid;
  gap: 1.5rem;
  grid-template-columns: repeat(4, 1fr);
  padding-left: 0.5rem;
  padding-right: 0.25rem;
  padding-top: 0.5rem;
}

.inner_cardview2 {
  display: grid;
  gap: 2rem;
  grid-template-columns: repeat(3, 1fr);
}

@media screen and (max-width: 1536px) {
  .inner_cardview {
    grid-template-columns: repeat(3, 1fr);
  }
}
@media screen and (max-width: 1280px) {
  .inner_cardview {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media screen and (max-width: 1200px) {
  .inner_cardview {
    grid-template-columns: repeat(3, 1fr);
  }

  .main_content_wrapper {
    width: fit-content;
    min-width: calc(100% + 10.5rem);
  }

  .cardview {
    width: fit-content;
  }
}

.cardview {
  height: 80dvh;
  padding-bottom: 2rem;
  padding-right: 1rem;
  overflow: scroll !important;
}

.cardview::-webkit-scrollbar {
  width: 4px; /* Width of the scrollbar */
}

/* Style for the scrollbar track (the background area) */
.cardview::-webkit-scrollbar-track {
  background-color: transparent; /* Light grey track */
  border-radius: 10px;
}

/* Style for the scrollbar thumb (the draggable part) */
.cardview::-webkit-scrollbar-thumb {
  background-color: var(--primary_color); /* Darker thumb */
  border-radius: 10px;
}

.summaryDivData {
  display: flex;
  align-items: center;
}

.summaryDataContent {
  display: flex;
  flex-direction: column;
  background: #ffffff99;
  border-radius: 0.75rem;
  width: 30.8rem;
  width: 100%;
  /* max-width: 28.5rem; */
  min-height: 3.188rem;
  padding: 1rem;
  white-space: normal;
  margin: 0.6rem 0.6rem 0.6rem 0.6rem;
  gap: 0.2rem;
  line-height: 1.363rem;
  text-align: left;
}

@keyframes slideIn {
  from {
    transform: translate(100%, 0%);
  }

  to {
    transform: translate(0%, 0%);
  }
}

@keyframes slideOut {
  from {
    transform: translate(0%, 0%);
  }

  to {
    transform: translate(100%, 0%);
  }
}

.addmanpowerform_container.closing {
  animation: slideOut 0.5s ease-out;
}

.closeButton {
  position: absolute;
  top: 1.5em;
  right: 0.625rem;
  padding: 1rem;
  background: transparent;
  border: none;
  cursor: pointer;
}
.summary_container {
  height: calc(100% - 7rem);
  overflow: auto;
}
.addmanpowerform_header {
  color: var(--primary_color);
  display: flex;
  justify-content: center;
  padding: 0.6rem;
  padding-bottom: 1rem;
}

.addmanpowerform_datainputs {
  /* flex: 1; */
  /* Take up remaining space inside the container */
  height: calc(100% - 6.5rem);
  padding: 0.6rem;
  overflow-y: auto;
  scrollbar-width: none;
  /* Enable vertical scrolling */
}

.addmanpowerform_btngroup {
  width: 100%;
  padding: 1.5rem 1rem;
  display: flex;
  justify-content: center;
  gap: 1rem;
  border-radius: 2.6rem;
  /* backdrop-filter: blur(60px); */
  /* padding-bottom: 0; */
  position: fixed;
  bottom: 0rem;
  left: 50%;
  transform: translateX(-50%);
}
