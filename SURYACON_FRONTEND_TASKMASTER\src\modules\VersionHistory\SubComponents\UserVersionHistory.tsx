import React, { useState } from "react";
import styles from "../Styles/UserVersionHistory.module.css";
import {
  Arrow,
  DropDownArrowUpIcon,
  UndoArrow,
  SuryaconLogoSecondary,
  SuryconLogo,
} from "../../../assets/icons";

import { RootState } from "../../../redux/store";
import { closePopup } from "../../../redux/features/popupSlice";
import {
  useAppDispatch,
  useAppSelector,
} from "../../../redux/hooks/Modules/Reduxhooks/ReduxHooks";
import { useSelector } from "react-redux";

const UserVersionHistory = () => {
  const dispatch = useAppDispatch();
  const currentOpenPopup = useAppSelector(
    (state: RootState) => state.popup.popups
  );
  const subCard = useSelector(
    (state: RootState) => state.versionHistorySlice.subCard
  );
  return (
    <div
      className={`${
        subCard
          ? `${styles.user_version_history_container} ${styles.user_version_history_container_active}`
          : styles.user_version_history_container
      }`}
    >
      <div className={`${styles.user_version_history_top_section}`}>
        <div className={`${styles.user_version_history_top_left}`}>
          <div className={`${styles.user_version_history_top_left_userimage}`}>
            <img
              src="https://images.unsplash.com/photo-1676732331165-61bd1e55494a?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8NDN8fHBvcnRyYWl0JTIwcGhvdG9ncmFwaHl8ZW58MHx8MHx8fDA%3D"
              alt=""
            />
          </div>
          <div
            className={`${styles.user_version_history_top_left_user_details}`}
          >
            <h4 className={`${styles.user_version_history_top_left_name} `}>
              Vivek Sharma
            </h4>
            <div className={`${styles.user_version_history_top_left_id_role}`}>
              <p
                className={`${styles.user_version_history_top_left_id} small_text_p_400`}
              >
                133412
              </p>
              <div
                className={` ${styles.user_version_history_top_left_roles} `}
              >
                <div
                  className={` ${styles.user_version_history_top_left_role_small_circle} `}
                ></div>
                <p
                  className={` ${styles.user_version_history_top_left_roles_text} small_text_p_400`}
                >
                  Project Manager
                </p>
              </div>
            </div>
          </div>
        </div>
        <div className={`${styles.user_version_history_top_right}`}>
          <div
            className={`${styles.user_version_history_top_right_sno} small_text_p`}
          >
            6
          </div>
        </div>
      </div>
      <UVHSubComponent />
      <UVHSubComponent />
      <UVHSubComponent />
    </div>
  );
};

export default UserVersionHistory;

const UVHSubComponent = () => {
  const [selectedMaterial, setSelectedMaterialCard] = useState<number>(0);
  const [isOpen, setIsOpen] = useState(false);

  const handleSubcomponentOpen = () => {
    setIsOpen(!isOpen);
  };
  const handleVendorCardClick = (index: any) => {
    setSelectedMaterialCard(index);
  };

  // const [isExpanded, setIsExpanded] = useState(false);

  // const toggleExpand = () => {
  //     setIsExpanded(!isExpanded);
  // };

  const materialData = [
    {
      id: 0,
      category: "Materials",
      quantity: 6,
      materials: [
        {
          material: "Cement",
          brand: {
            new: "ACC",
            old: "Ambuja",
          },
          quantity: {
            new: "",
            old: "400",
          },
          grade: {
            new: "Grade B",
            old: "Grade A",
          },
        },
        {
          material: "Sand",

          quantity: {
            new: "300",
            old: "200",
          },
          grade: {
            new: "",
            old: "Grade B",
          },
        },
        {
          material: "Cement",
          brand: {
            new: "ACC",
            old: "Ambuja",
          },
          quantity: {
            new: "",
            old: "400",
          },
          grade: {
            new: "Grade B",
            old: "Grade A",
          },
        },
        {
          material: "Cement",
          brand: {
            new: "ACC",
            old: "Ambuja",
          },
          quantity: {
            new: "",
            old: "400",
          },
          grade: {
            new: "Grade B",
            old: "Grade A",
          },
        },
        {
          material: "Cement",
          brand: {
            new: "ACC",
            old: "Ambuja",
          },
          quantity: {
            new: "",
            old: "400",
          },
          grade: {
            new: "Grade B",
            old: "Grade A",
          },
        },
      ],
    },
    {
      id: 1,
      category: "Machinery",
      quantity: 4,
      materials: [
        {
          material: "Cement",
          brand: {
            new: "ACC",
            old: "Ambuja",
          },
          quantity: {
            new: "",
            old: "400",
          },
          grade: {
            new: "Grade B",
            old: "Grade A",
          },
        },
        {
          material: "Sand",

          quantity: {
            new: "300",
            old: "200",
          },
          grade: {
            new: "",
            old: "Grade B",
          },
        },
        {
          material: "Cement",
          brand: {
            new: "ACC",
            old: "Ambuja",
          },
          quantity: {
            new: "",
            old: "400",
          },
          grade: {
            new: "Grade B",
            old: "Grade A",
          },
        },
      ],
    },
  ];
  return (
    <div
      className={`${styles.uvh_subcomponent_container} } ${
        isOpen ? styles.expanded : ""
      }`}
    >
      <div className={`${styles.uvh_subcomponent_container_top}`}>
        <div className={`${styles.uvh_subcomponent_container_topleft}`}>
          <div
            className={`${styles.uvh_subcomponent_container_topleft_item_status_materials}`}
          >
            <p
              className={`${styles.uvh_subcomponent_container_topleft_item_status}`}
              style={{ whiteSpace: "nowrap" }}
            >
              Deleted Items
            </p>
            {materialData.map((item, index) => (
              <div
                key={index}
                onClick={() => handleVendorCardClick(item.id)}
                className={`${
                  styles.uvh_subcomponent_container_topleft_material
                } ${
                  selectedMaterial === item.id ? styles.selectedMaterial : ""
                }`}
              >
                <p
                  className={`${styles.uvh_subcomponent_container_topleft_material_text} small_text_p`}
                >
                  {item.category}
                </p>
                <p
                  className={`${styles.uvh_subcomponent_container_topleft_material_quantity}  small_text_p`}
                >
                  {item.quantity}
                </p>
              </div>
            ))}
          </div>
          <p
            className={`${styles.uvh_subcomponent_container_topleft_time} p_tag_14px`}
          >
            04:27 PM
          </p>
        </div>
        <div className={`${styles.uvh_subcomponent_container_topRight}`}>
          <div
            onClick={() => handleSubcomponentOpen()}
            className={`${styles.down_arrow} ${
              isOpen ? `${styles.dropdownOpen}` : ""
            }`}
          >
            <DropDownArrowUpIcon />
          </div>
          <div className={`${styles.undo_arrow}`}>
            <UndoArrow />
          </div>
        </div>
      </div>
      <div>
        {isOpen ? (
          <div
            className={`${styles.material_div} ${
              isOpen ? styles.expanded : ""
            }`}
          >
            {materialData[selectedMaterial].materials.map((item, index) => (
              <MaterialVHComponent data={item} />
            ))}
          </div>
        ) : (
          ""
        )}
      </div>
    </div>
  );
};

const MaterialVHComponent = ({ data }) => {
  return (
    <div className={`${styles.material_vh_container}`}>
      <div className={`${styles.material_vh_left_linedot}`}>
        <div className={`${styles.material_vh_left_dot}`}></div>
        <div className={`${styles.material_vh_left_line}`}></div>
      </div>

      <div className={`${styles.material_vh_right}`}>
        <p className={`${styles.material_text} p_tag_14px`}>{data?.material}</p>
        <div className={`${styles.material_detail}`}>
          {Object.entries(data).map(([key, value], index) => {
            if (key === "material") return null;
            if (typeof value === "object" && value !== null) {
              return (
                <div key={index} className={`${styles.material_linedot}`}>
                  <div className={`${styles.material_line}`}></div>
                  <div className={`${styles.material_dot}`}></div>
                  <div className={`${styles.material_old_new_div}`}>
                    {value.old && (
                      <p className={`${styles.old_data} small_text_p`}>
                        {value.old}
                      </p>
                    )}
                    {value.new && (
                      <>
                        {<SuryaconLogoSecondary />}
                        <p className={`${styles.new_data} small_text_p`}>
                          {value.new}
                        </p>
                      </>
                    )}
                  </div>
                </div>
              );
            }
          })}
        </div>
      </div>
    </div>
  );
};

/*

const MaterialVHComponent=({data})=>{
    console.log('data in mvh comp:',data)
    return (
        
        <div className={`${styles.material_vh_container}`}>
            <div className={`${styles.material_vh_left_linedot}`}>
                <div className={`${styles.material_vh_left_dot}`} style={{}}></div>
                <div className={`${styles.material_vh_left_line}`}></div>
            </div>
            <div className={`${styles.material_vh_right}`}>
                <p className={`${styles.material_text} p_tag_14px`}>{data?.material}</p>
                <div className={`${styles.material_detail}`}>
                    {
                        data.brand?<div className={`${styles.material_linedot}`}>
                        <div className={`${styles.material_line}`}></div>
                        <div className={`${styles.material_dot}`} style={{}}></div>
                        <div className={`${styles.material_old_new_div}`}>
                            <p className={`p_tag_14px`}>{data?.brand?.old} </p>
                            {data.brand?.new?<SuryaconLogoSecondary/>:''}
                            <p className={`${styles.new_data} p_tag_14px`}>{data?.brand?.new} </p>
                        </div>
                        </div>:null
                    }

                    <div className={`${styles.material_linedot}`}>
                        <div className={`${styles.material_line}`}></div>
                        <div className={`${styles.material_dot}`} style={{}}></div>
                        <p className={`p_tag_14px`}>{data?.quantity.new} Bags</p>
                    </div>

                    <div className={`${styles.material_linedot}`}>
                        <div className={`${styles.material_line}`}></div>
                        <div className={`${styles.material_dot}`} style={{}}></div>
                        <p className={`p_tag_14px`}>{data?.grade?.new}</p>
                    </div>
                    
                    
                </div>
            </div>
        </div>
    )
}
*/
