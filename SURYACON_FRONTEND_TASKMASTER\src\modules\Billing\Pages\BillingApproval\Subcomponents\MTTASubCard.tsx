import { FC } from "react"
import { MTTASubCardProp } from "../../../../../interfaces/Modules/Billing/BillingApproval/BillingApproval"
import styles from '../Styles/BillingApprovalSubcomponent.module.css'
import { EyeIcon } from "../../../../../assets/icons"

const MTSubTaskApprovalSubCard:FC<MTTASubCardProp> = ({icon:Icon,property,keyProp,valueProp,valueClassName}) => {

    return (
      <div className={`${styles.mt_task_approval_subcard}`}>
          <div className={`${styles.mt_task_approval_subcard_left} ${property?styles[property]:''}`}>
            <Icon />
          </div>
          <div className={`${styles.mt_task_approval_subcard_right}`}>
            {keyProp!=='View Drawing'?<h4 className={`${styles.mt_task_approval_subcard_right_text1} ${styles[valueClassName || '']}`}>{valueProp || '...' }</h4>:<EyeIcon style={{position:'relative',top:'0.2rem',cursor:'pointer'}}/>}
            <p className={`${styles.mt_task_approval_subcard_right_text2}`}>{keyProp}</p>
          </div>
      </div>
    )
  }

  export default MTSubTaskApprovalSubCard