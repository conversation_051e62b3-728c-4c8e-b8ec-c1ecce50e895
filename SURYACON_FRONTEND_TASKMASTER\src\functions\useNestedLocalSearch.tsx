import { useEffect } from "react";
import { useLocation } from "react-router-dom";
import { AppPath, initializeDatabase, TableName } from "./functions";
import { useAppDispatch, useAppSelector } from "../redux/hooks/Modules/Reduxhooks/ReduxHooks";

type SearchHandlerOptions = {
  pathRecord: string;
  searchKey: string;
  setData: (data: any[]) => void;
  setPage?: (val: number) => void;
  key?: string;
  extraSearchParams?: {
    catId?: string;
    categoryId?: string;
    isDeletedNext?: boolean;
    typed?: any;
  };
};

export const useNestedPouchSearch = ({
  pathRecord,
  searchKey,
  setData,
  setPage,
  key = "name",
  extraSearchParams = {},
}: SearchHandlerOptions) => {
  const detectChanges = useAppSelector((state) => state.backupSlice.isOpen);
    const dispatch = useAppDispatch()

  const handleSearch = async () => {
    const dbName = await initializeDatabase(pathRecord);

    console.log("fetched data task1");
    if (!searchKey.trim()) {
      dispatch(setData([]));
      setPage?.(1);
      return;
    }

    console.log("fetched data task");

    const fetchedData = await window.electron.getDocumentByParentId({
      dbName,
      catId: extraSearchParams?.catId,
      categoryId: extraSearchParams.categoryId,
      isDeletedNext: extraSearchParams.isDeletedNext,
      name: key,
      needSearching: true,
      needSorting: true,
      type: extraSearchParams.typed ? extraSearchParams.typed : undefined,
    });

    console.log("fetched data task", fetchedData);

    const escapeRegExp = (string: string) => {
      return string.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
    };
    const regex = new RegExp(escapeRegExp(searchKey), "i");

    const filtered = (fetchedData.data ? fetchedData.data : fetchedData).filter(
      (item: any) => regex.test(item?.[key] ?? "")
    );

    dispatch(setData(filtered));
  };

  useEffect(() => {
    handleSearch();
  }, [
    searchKey,
    extraSearchParams?.catId,
    extraSearchParams?.typed,
    extraSearchParams?.isDeletedNext,
  ]);
};
