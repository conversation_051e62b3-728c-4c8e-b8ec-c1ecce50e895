import React, { useEffect, useState, useCallback, useRef } from "react";
import styles from "./Styles/AddNewProject.module.css";
import NavAddNewProject from "./Subcomponents/NavbarAddNewProject";
import TableViewProjects from "./TableViewProjects";
import ProgressCardView from "./Subcomponents/ProgressCard/ProgressCardView";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../../../../redux/store";
import {
  checkAndDownloadImages,
  initializeDatabase,
  pathTableMap,
  withInternetCheck,
} from "../../../../../functions/functions";
import {
  setpage,
  SetProjects,
} from "../../../../../redux/features/Modules/Billing/ProjectPlanning/Slices/ProjectSlice";
import { ProjectData } from "./AddProjectForm/Interfaces/interface";
import { useAppSelector } from "../../../../../redux/hooks/Modules/Reduxhooks/ReduxHooks";
import { usePouchSearch } from "../../../../../functions/useLocalSearch";
import { setSearchData } from "../../../../../redux/features/Modules/Masters";
const Project: React.FC = () => {
  const detectChanges = useAppSelector(
    (state: RootState) => state.backupSlice.isOpen
  );

  const [isTableView, setIsTableView] = useState(false);
  const dispatch = useDispatch();
  const projectData = useSelector(
    (state: RootState) => state.projectLocalDb.projects
  );
  const allProjects = useSelector(
    (state: RootState) => state?.projectLocalDb?.projects
  );
  console.log(allProjects, "all projects in project page");
  const isBackupChange = useSelector(
    (state: RootState) => state?.backupSlice?.isOpen
  );

  // const [page, setPage] = useState(1);
  const [loading, setLoading] = useState(false);

  const selectedProjectType = useSelector(
    (state: RootState) => state.projectLocalDb.selectedProjectCategory
  );
  
  const searchedData = useSelector(
    (state: RootState) => state.masterReduxSlice.searchedData
  );
  console.log(selectedProjectType, "selectedProjectType in project page");
  const isProjectDeleted = useSelector(
    (state: RootState) => state.projectLocalDb.isProjectDeleted
  );
  const page = useSelector((state: RootState) => state.projectLocalDb.page);
  const [searchLocalKey, setSearchLocalKey] = useState<string>("");
  const searchKey = useAppSelector((state) => state.taskMaster.searchKey);

  //localdb logic

  useEffect(() => {
    if (searchKey) {
      setSearchLocalKey(searchKey);
    } else {
      setSearchLocalKey("");
    }
  }, [searchKey]);

  useEffect(() => {
    setIsTableView(false);
  }, []);
  useEffect(() => {
    dispatch(setpage(1));
  }, [isBackupChange, selectedProjectType, isProjectDeleted]);

  const getAllProjectData = useCallback(
    async (p: number) => {
      if (loading) return; // Prevent multiple calls while loading
      console.log("Fetching project data for page:", page);
      setLoading(true);
      try {
        const dbName = await initializeDatabase("projects");
        const fetchedData = await window.electron.bulkGet({
          dbName,
          page: p,
          deleted: isProjectDeleted,
          type: selectedProjectType,
        });

        const imageDownload = withInternetCheck(() =>
          checkAndDownloadImages("project", fetchedData?.docs, dispatch)
        );
        imageDownload();

        if (fetchedData?.docs?.length) {
          const formattedData = fetchedData.docs.map(
            (singleProject: ProjectData) => ({
              ...singleProject,
              client_id:
                typeof singleProject?.client_id === "object"
                  ? singleProject?.client_id?._id
                  : singleProject?.client_id,
              ClientPhoneNumber:
                typeof singleProject?.client_id === "object"
                  ? singleProject?.client_id?.ClientPhoneNumber
                  : undefined,
              clientName:
                typeof singleProject?.client_id === "object"
                  ? singleProject?.client_id?.clientName
                  : undefined,
            })
          );

          const uniqueProjects = Array.from(
            new Map(
              formattedData.concat(projectData).map((p: any) => [p._id, p])
            ).values()
          );

          // Add sorting logic here
          if (uniqueProjects && Array.isArray(uniqueProjects)) {
            uniqueProjects.sort((a: any, b: any) => {
              const nameA = a.name ? String(a.name).toLowerCase() : "";
              const nameB = b.name ? String(b.name).toLowerCase() : "";
              return nameA.localeCompare(nameB);
            });
          }

          dispatch(SetProjects(uniqueProjects));
        }
      } catch (error) {
        console.error("Error fetching project data", error);
      } finally {
        setLoading(false);
      }
    },
    [page, isProjectDeleted, selectedProjectType, projectData, dispatch]
  );

  useEffect(() => {
    getAllProjectData(page);
  }, [
    detectChanges,
    page,
    selectedProjectType,
    isProjectDeleted,
    isBackupChange,
  ]);
  const containerRef = useRef<HTMLDivElement>(null);
  useEffect(() => {
    dispatch(SetProjects([])); // Clear projects
    dispatch(setpage(1)); // Reset to first page
  }, [selectedProjectType, dispatch]);
  useEffect(() => {
    if (containerRef.current) {
      containerRef.current.scrollTop = 0;
    }
  }, [isProjectDeleted, selectedProjectType]);
  const handleScroll = useCallback(
    (e: React.UIEvent<HTMLDivElement>) => {
      if (loading) return;

      const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;
      if (scrollTop + clientHeight >= scrollHeight - 500) {
        dispatch(setpage(page + 1));
      }
    },
    [loading, page, dispatch]
  );
  useEffect(() => {
    if (page) {
      getAllProjectData(page);
    }
  }, [page]);

  console.log(searchedData, "fdasdfas");

  usePouchSearch({
    pathTableMap,
    searchKey: searchLocalKey,
    setData: setSearchData,
    setPage: setpage as any,
  });

  console.log("all projects in project page", allProjects);

  return (
    <div
      ref={containerRef}
      onScroll={handleScroll}
      className={styles.add_new_project_container}
    >
      <div className={styles.add_new_project_header}>
        <NavAddNewProject
          setIsTableView={setIsTableView}
          isTableView={isTableView}
        />
      </div>

      {!isTableView ? (
        <ProgressCardView
          projectData={allProjects}
          searchedData={searchedData?.map((e: any) => {
            return {
              ...e,
              client_id:
                typeof e?.client_id === "object"
                  ? e?.client_id?._id
                  : e?.client_id,
              ClientPhoneNumber:
                typeof e?.client_id === "object"
                  ? e?.client_id?.ClientPhoneNumber
                  : undefined,
              clientName:
                typeof e?.client_id === "object"
                  ? e?.client_id?.clientName
                  : undefined,
            };
          })}
        />
      ) : (
        <TableViewProjects
          data={allProjects}
          searchedData={searchedData?.map((e: any) => {
            return {
              ...e,
              client_id:
                typeof e?.client_id === "object"
                  ? e?.client_id?._id
                  : e?.client_id,
              ClientPhoneNumber:
                typeof e?.client_id === "object"
                  ? e?.client_id?.ClientPhoneNumber
                  : undefined,
              clientName:
                typeof e?.client_id === "object"
                  ? e?.client_id?.clientName
                  : undefined,
            };
          })}
        />
      )}
    </div>
  );
};

export default Project;
