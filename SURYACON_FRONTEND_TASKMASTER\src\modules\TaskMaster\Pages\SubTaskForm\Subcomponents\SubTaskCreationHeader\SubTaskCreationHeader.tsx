import { useDispatch, useSelector } from "react-redux";
import {
  setChangeAPiFlag,
  setcurrentSubtaskData,
  setCurrentSubTaskRoute,
  setcurrentTaskData,
  setSubTaskRoute,
  settaskChangeAPiFlag,
  updateTaskData,
} from "../../../../../../redux/features/Modules/TaskMaster/Slices/TaskMasterSlice";
import {
  useDeleteSubTaskMutation,
  useGetsubTaskDetailsQuery,
  useGetSubTaskRouteByTaskIdQuery,
  useGetTaskDetailsByTaskIdQuery,
  useLazyGetTaskDetailsByTaskIdQuery,
  useUpdateBasicSubTaskByIdMutation,
  useUpdateTaskByIdMutation,
} from "../../../../../../redux/api/Modules/TaskMaster/TaskMasterapi";
import { useNavigate, useParams } from "react-router-dom";
import { RootState, store } from "../../../../../../redux/store";
import {
  closePopup,
  openPopup,
} from "../../../../../../redux/features/Modules/Reusble/popupSlice";
import {
  DeleteIcon,
  RedCross,
  TaskEditPencil,
  WieghtPercentageIcon,
} from "../../../../../../assets/icons";
import { DeletePopup } from "../../../../../../components/Reusble/Global/DeletePopup";
import AddSubTasksPopup from "../../../../../../components/Reusble/TaskMaster/AddSubTasksPopup";
import { resetInputValues } from "../../../../../../redux/features/Modules/Reusble/floatinglabelslice";
import styles from "../../Styles/SubtaskCreationForm.module.css";
import { SubTaskCreationHeaderProps } from "../../../../../../interfaces/Modules/TaskMaster/TaskMasterInterface/TaskMaster";
import {
  removeNavigate,
  resetTaskHeader,
  setRemoveNavigationKey,
} from "../../../../../../redux/features/Modules/Reusble/navigationSlice";
import {
  TrackingSvg,
  UnitSvgs,
} from "../../../../../../assets/TopbarAssets/SVGs";
import { useAppSelector } from "../../../../../../redux/hooks/Modules/Reduxhooks/ReduxHooks";
import { currentSubtaskData } from "../../../../../../redux/Interfaces/Modules/TaskMaster/TaskMasterInterface";
import {
  fileTypeMapper,
  getFileName,
  initializeDatabase,
  isValidValue,
  slicedData,
} from "../../../../../../functions/functions";
import { useCallback, useEffect, useState, useMemo } from "react";
import { useUpdateSubtask } from "../../../../../../redux/hooks/Modules/TaskMaster/TaskMasterHook";
import { useOpenclosesubtaskMutation } from "../../../../../../redux/api/Modules/Reusble/Reusble";
import { get } from "lodash";
import { useToast } from "../../../../../../hooks/ToastHook";
import { useAuth } from "../../../../../../AuthProvider";
import { saveSyncData } from "../../../../../../Backup/BackupFunctions/BackupFunctions";
import SummaryPopup from "../../../../../../components/Reusble/Global/SummaryPopup"; // <-- Add this import

interface DeleteSubtaskBodyProps {
  data: currentSubtaskData | null;
}

/**
 * SubTaskCreationHeader Component
 * @param {SubTaskCreationHeaderProps} props - Props containing name, description, unit, weightage, tracking, and onclick handler
 */
const SubTaskCreationHeader: React.FC<SubTaskCreationHeaderProps> = ({
  name,
  Description,
  unit,
  weightage,
  tracking,
  onclick,
  isEdit = false,
}) => {
  // const [updatesubtaskBasicDetails] = useUpdateBasicSubTaskByIdMutation();
  const navigate = useNavigate();
  const params = useParams();
  const showToast = useToast();
  const [finalSubtaskId, setFinalSubtaskId] = useState<string | null>(null);
  const [disableButton, setDisableButton] = useState<boolean>(false);
  const [updateTaskById, { isLoading }] = useUpdateTaskByIdMutation();
  const [getTaskById] = useLazyGetTaskDetailsByTaskIdQuery();
  const [showFullDescription, setShowFullDescription] = useState(false);
  // const [finalTaskId, setFinalTaskId] = useState<string | null>(null);
  console.log("subtaskId", params);

  const taskApiCall = useSelector(
    (state: RootState) => state.isEditTaskReducer.istaskApiCall
  );

  const popups = useSelector((state: RootState) => state.popup.popups);
  const dispatch = useDispatch();

  const SutbaskData = useSelector(
    (state: RootState) => state?.taskMaster?.currentSubtaskData
  );

  const isDeletedNext = useAppSelector(
    (state) => state.isDeletedSLice.isDeletedNext
  );

  const TaskData = useSelector(
    (state: RootState) => state?.taskForm?.currentSubtaskData
  );
  const SubTasksRoutes = useSelector(
    (state: RootState) => state?.taskMaster?.SubTasksRoutes
  );
  const [deleteSubtask] = useDeleteSubTaskMutation();
  // const [updateStatus] = useOpenclosesubtaskMutation();
  const [updateSubtask] = useUpdateSubtask();
  const [isInitialLoading, setIsInitialLoading] = useState(true);

  const { user } = useAuth();
  const isMD = user?.designationId?.name === "MD";

  const SkeletonBox = ({
    width = "100px",
    height = "24px",
    className = styles.skeleton_box,
  }) => (
    <span
      className={className}
      style={{
        width,
        height,
      }}
    />
  );

  const FixedBox = ({ width, height, children }: any) => (
    <span
      className={styles.fixed_box}
      style={{
        width,
        height,
      }}
    >
      {children}
    </span>
  );

  // const handleUpdateStatus = async (data?: {
  //   status: string;
  //   subtaskid: string;
  // }) => {
  //   try {
  //     const response = await updateStatus(data);
  //     return response;
  //   } catch (error) {
  //     console.log(error);
  //   }
  // };

  useEffect(() => {
    if (params.subtaskId) {
      setFinalSubtaskId(params.subtaskId);
    }
  }, [params.subtaskId]);

  const fetchSubtasks = async () => {
    const dbName = await initializeDatabase("SubTaskForm");

    let response = [];
    if (typeof window.electron.getSubtasksByTaskId === "function") {
      response = await window.electron.getSubtasksByTaskId({
        dbName,
        taskId: params.taskId,
        subtaskId: "",
      });
      console.log("Subtasks fetched:", response);
    } else {
      console.error("window.electron.getSubtasksByTaskId is not a function");
    }
    return response || [];
  };

  const transformTaskMasterData = (task: any) => {
    const extractIds = (data: any) => data?.map((item: any) => item._id) || [];
    const excludeIds = (data: any[]) => data.map(({ _id, ...rest }) => rest);

    const transformWorkInstructions = (workInstructions: any[]) => {
      console.log(workInstructions, "check for work instructionse");
      return workInstructions.map((item: any) => {
        const isTimestampId = /^\d{13}$/.test(String(item._id)); // Check if _id is a timestamp

        return {
          ...(item._id && !isTimestampId && { _id: item._id }), // Include _id only if it's not a timestamp
          Description: item.Description || "",
          optionselected: item.optionselected?.toLowerCase() || "",
          departmentId: extractIds(item.departmentId),
          designationId: extractIds(item.designationId),
          materialId: extractIds(item.materialId),
          manpowerId: extractIds(item.manpowerId),
          toolsId: extractIds(item.toolsId),
          machinaryId: [...extractIds(item.machinaryId)],
          photoRef:
            (item.photoref?.photos?.length > 0 &&
              item.photoref.photos?.map((photo: any) => ({
                photos: photo?.photo,
                Decription: photo?.details,
              }))) ||
            [],
        };
      });
    };
    const transformTaskClosing = (taskClosing: any[]) => {
      return taskClosing.map((item: any) => ({
        Description: item.Description,
        optionselected: item.optionselected?.toLowerCase() || "",
        photoRef:
          (item.photoref?.photos?.length > 0 &&
            item.photoref.photos?.map((photo: any) => ({
              photos: photo?.photo,
              Decription: photo?.details,
            }))) ||
          [],
      }));
    };

    const transformReporters = (Reporter: any[]) => {
      return Reporter.map((item: any) => {
        const isTimestampId = /^\d{13}$/.test(String(item._id));

        return {
          ...(item._id && !isTimestampId && { _id: item._id }),
          Level: item?.Level,
          designationId: Array.isArray(item?.designationId)
            ? extractIds(item.designationId)
            : item.designationId,
        };
      });
    };

    return {
      _id: task._id,
      name: task.name,
      Unit: task.Unit,
      Description: task.Description,
      Quantity: task.subtaskWeighatages,
      DepartmentId: extractIds(task.DepartmentId),
      DesignationId: extractIds(task.DesignationId),
      MaterialId: extractIds(task.MaterialId),
      ToolId: extractIds(task.ToolId),
      MachinaryId: extractIds(task.MachinaryId),
      ManpowerId: extractIds(task.ManpowerId),
      Adminid: extractIds(task.Adminid),
      Reporters: transformReporters(task.ReporterId?.Reporter || []),
      AssigneeId: extractIds(task.AssigneeId) || [],
      WorkInstructions: transformWorkInstructions(
        task.MethodId?.work_instruction_id || []
      ),
      TaskClosing: transformTaskClosing(
        task.MethodId?.task_closing_requirement || []
      ),
      ControlPlan: excludeIds(task?.MethodId?.Controlplan || []),
      Failuremode: excludeIds(task?.MethodId?.Failuremode || []),
      Tobedeleted: {
        workinstruction: task?.Tobedeleted?.workinstruction || [],
      },
    };
  };

  const updateTaskApi = async () => {
    const transformedData = transformTaskMasterData(TaskData);

    const response = await updateTaskById({
      taskId: params.taskId!,
      name: transformedData.name,
      Unit: transformedData.Unit,
      Description: transformedData.Description,
      Quantity: transformedData.Quantity,
      departmentId: transformedData.DepartmentId,
      designationId: transformedData.DesignationId,
      machinaryId: transformedData.MachinaryId,
      toolId: transformedData.ToolId,
      manpowerId: transformedData.ManpowerId,
      materialId: transformedData.MaterialId,
      Adminid: transformedData.Adminid,
      AssigneeId: transformedData.AssigneeId,
      Reporters: transformedData.Reporters,
      WorkInstructions: transformedData.WorkInstructions,
      TaskClosing: transformedData.TaskClosing,
      Failuremode: transformedData.Failuremode,
      ControlPlan: transformedData.ControlPlan,
      Tobedeleted: {
        workinstruction: transformedData?.Tobedeleted?.workinstruction,
      },
    }).unwrap();

    dispatch(settaskChangeAPiFlag(false));
    return response;
  };

  const transformTaskData = (task: any) => {
    console.log("taskkk geting transformed", task);
    const extractIds = (data: any) => data?.map((item: any) => item._id) || [];
    const excludeIds = (data: any[]) => data.map(({ _id, ...rest }) => rest);
    const transformWorkInstructions = (workInstructions: any[]) => {
      return workInstructions.map((item: any) => {
        const isTimestampId = /^\d{13}$/.test(String(item._id)); // Check if _id is a timestamp

        return {
          ...(item._id && !isTimestampId && { _id: item._id }), // Include _id only if it's not a timestamp
          // _id: item?._id,
          Description: item.Description || "",
          optionselected: item.optionselected?.toLowerCase() || "",
          departmentId: extractIds(item.departmentId),
          designationId: extractIds(item.designationId),
          materialId: extractIds(item.materialId),
          manpowerId: extractIds(item.manpowerId),
          toolsId: extractIds(item.toolsId),
          machinaryId: [...extractIds(item.machinaryId)],
          photoRef:
            (item.photoref?.photos?.length &&
              item.photoref?.photos?.map((photo: any) => ({
                photos: photo?.photo || "",
                Decription: photo?.details || "",
              }))) ||
            [],
        };
      });
    };

    const transformTaskClosing = (taskClosing: any[]) => {
      return taskClosing.map((item: any) => {
        const isTimestampId = /^\d{13}$/.test(String(item._id));
        return {
          ...(item._id && !isTimestampId && { _id: item._id }),
          Description: item.Description,
          optionselected: item.optionselected?.toLowerCase() || "",
          photoRef:
            (item.photoref?.photos?.length &&
              item.photoref?.photos?.map((photo: any) => ({
                photos: photo?.photo || "",
                Decription: photo?.details || "",
              }))) ||
            [],
        };
      });
    };

    const transformReporters = (Reporters: any[]) => {
      return Reporters.map((item: any) => {
        const isTimestampId = /^\d{13}$/.test(String(item._id));

        return {
          ...(item._id && !isTimestampId && { _id: item._id }),
          Level: item?.Level,
          designationId: Array.isArray(item?.designationId)
            ? extractIds(item.designationId)
            : [item.designationId], // Ensure designationId is always an array
        };
      });
    };

    return {
      _id: task._id,
      name: task.name,
      Unit: task.Unit,
      Description: task.Description,
      subtaskWeighatages: task.subtaskWeighatages,
      Tracking: task.Tracking,
      DepartmentId: extractIds(task.DepartmentId),
      DesignationId: extractIds(task.DesignationId),
      MaterialId: extractIds(task.MaterialId),
      ToolId: extractIds(task.ToolId),
      MachinaryId: extractIds(task.MachinaryId),
      ManpowerId: extractIds(task.ManpowerId),
      AdminId: extractIds(task.AdminId),
      Reporters: transformReporters(task.ReporterId?.Reporter || []),
      AssigneeId: extractIds(task.AssigneeId) || [],
      WorkInstructions: transformWorkInstructions(
        task.MethodId?.work_instruction_id || []
      ),
      TaskClosing: transformTaskClosing(
        task.MethodId?.task_closing_requirement || []
      ),
      ControlPlan: excludeIds(task?.MethodId?.Controlplan || []),
      Failuremode: excludeIds(task?.MethodId?.Failuremode || []),
      TriggerEvent: {
        TriggerResponse:
          task?.AutoId?.TriggerResponse?.name === "This is a First Subtask"
            ? task?._id
            : task?.AutoId?.TriggerResponse?._id || null,
        ActionName: task?.AutoId?.TriggerAction?.ActionName?.name ?? "",
        ActionTime: Number(task?.AutoId?.TriggerAction?.ActionTime) ?? 0,
        ResponseTime: Number(task?.AutoId?.ResponseTime) ?? 0,
        isFirst:
          task?.AutoId?.TriggerResponse?.name === "This is a First Subtask",
      },
      Tobedeleted: {
        workinstruction: task?.Tobedeleted?.workinstruction || [],
      },
    };
  };

  const handleGetTask = async (id: any) => {
    const response = await getTaskById(id);
    return response;
  };

  const getTaskDetailsLatest = async () => {
    const taskIdd = params.taskId;
    const taskformdata = (await handleGetTask(taskIdd)) as any;

    const formattedTaskData: any = {
      id: taskformdata.data?.data?._id || "",
      _id: taskformdata.data?.data?._id || "",
      name: taskformdata.data?.data?.name || "",
      Unit: taskformdata.data?.data?.Unit || "",
      Description: taskformdata.data?.data?.Description || "",
      subtaskWeighatages: taskformdata.data?.data?.Quantity || 0,
      Tracking: taskformdata.data?.data?.isCompleted ? "Completed" : "Pending",
      DepartmentId: taskformdata.data?.data?.departmentId || [],
      DesignationId: taskformdata.data?.data?.designationId || [],
      MaterialId: taskformdata.data?.data?.materialId || [],
      ToolId: taskformdata.data?.data?.toolId || [],
      MachinaryId: taskformdata.data?.data?.machinaryId || [],
      ManpowerId: taskformdata.data?.data?.manpowerId || [],
      Adminid: taskformdata.data?.data?.Adminid,
      ReporterId: {
        Reporter: taskformdata.data?.data?.ReporterId?.Reporter || [],
      },
      AssigneeId: taskformdata.data?.data?.Assignee || [],
      Subtaskdetails: taskformdata.data?.data?.SubtaskId,
      MethodId: {
        ...taskformdata.data.data?.MethodId,
        work_instruction_id:
          taskformdata.data.data?.MethodId?.work_instruction_id?.map(
            (item: any) => ({
              photoref: {
                photos: (item.photoRef || []).map((photo: any) => ({
                  photo: photo?.photos,
                  details: photo?.Decription || "",
                })),
              },
              _id: item._id || "",
              Description: item.Description || "",
              optionselected: item.optionselected || "",
              materialId: item.materialId || [],
              manpowerId: item.manpowerId || [],
              toolsId: item.toolsId || [],
              machinaryId: item.machinaryId || [],
            })
          ) || [],
        task_closing_requirement:
          taskformdata?.data?.data?.MethodId?.task_closing_requirement?.map(
            (item: any) => ({
              photoref: {
                photos: (item.photoRef || []).map((photo: any) => ({
                  photo: photo?.photos,
                  details: photo?.Decription || "",
                })),
              },
              _id: item._id || "",
              Description: item.Description || "",
              optionselected: item.optionselected || "",
            })
          ) || [],
      },
    };

    saveSyncData(formattedTaskData, "time", "TaskForm", false, dispatch);
  };

  const subtaskUpdate = useCallback(
    async (data: any) => {
      const state = store.getState();
      const transformedData = transformTaskData(
        state.taskMaster?.currentSubtaskData
      );

      if (!transformedData || !finalSubtaskId) {
        // alert("Missing subtask data: " + JSON.stringify(transformedData));
        // alert("Missing finaltask data: " + JSON.stringify(finalSubtaskId));
        return;
      }

      // const workinstructiondelete =
      //   state.WorkInstructionReducer.workinstruction;

      // const uniqueArray = (arr: string[]) => [...new Set(arr)];

      const response = await updateSubtask({
        SubtaskId: finalSubtaskId,
        name: data.name,
        Description: data.Description,
        Unit: data.Unit,
        subtaskWeighatages: Number(data.subtaskWeighatages),
        Tracking: data.Tracking,
        machinaryId: transformedData.MachinaryId,
        toolId: transformedData.ToolId,
        manpowerId: transformedData.ManpowerId,
        materialId: transformedData.MaterialId,
        AdminId: transformedData.AdminId,
        AssigneeId: transformedData.AssigneeId,
        Reporters: transformedData.Reporters,
        WorkInstructions: transformedData.WorkInstructions,
        TaskClosing: transformedData.TaskClosing,
        Failuremode: transformedData.Failuremode,
        ControlPlan: transformedData.ControlPlan,
        Tobedeleted: {
          workinstruction: transformedData?.Tobedeleted?.workinstruction,
        },
        TriggerEvent: transformedData.TriggerEvent,
      }).unwrap();

      console.log("responseeee", response);
      dispatch(setChangeAPiFlag(false));
      return response;
    },
    [finalSubtaskId, params.taskId]
  );

  const slicedDescription = useMemo(() => {
    if (!Description) return "";
    return Description.length <= 200 ? Description : Description.slice(0, 200);
  }, [Description]);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsInitialLoading(false);
    }, 100); // 1 second delay

    return () => clearTimeout(timer);
  }, []);

  const title = !name ? (
    <SkeletonBox width="180px" height="32px" />
  ) : (
    <span className={styles.title_text}>{name || "--"}</span>
  );

  const description = isInitialLoading ? (
    <SkeletonBox width="240px" height="18px" />
  ) : !Description ? (
    <span className={styles.description_text}>--</span>
  ) : (
    <span className={styles.description_text}>
      {slicedDescription}
      {Description.length > 200 && (
        <>
          {" "}
          <span
            style={{
              color: "var(--secondary_color)",
              cursor: "pointer",
              fontWeight: 500,
              textDecoration: "underline",
              textUnderlineOffset: "1.5px",
              textDecorationThickness: "1px",
              textDecorationColor: "var(--secondary_color)",
            }}
            onClick={(e) => {
              e.stopPropagation();
              setShowFullDescription(true);
            }}
          >
            View all
          </span>
        </>
      )}
    </span>
  );

  const unitDisplay = (
    <FixedBox minWidth="32px" height="18px">
      {!unit ? (
        <SkeletonBox
          width="32px"
          height="18px"
          className={styles.skeleton_box_light}
        />
      ) : (
        <span>{unit}</span>
      )}
    </FixedBox>
  );

  const weightageDisplay = (
    <FixedBox minWidth="41px" height="18px">
      {!weightage ? (
        <SkeletonBox
          width="50px"
          height="24px"
          className={styles.skeleton_box_light}
        />
      ) : (
        <span>{weightage}%</span>
      )}
    </FixedBox>
  );

  const trackingDisplay = (
    <FixedBox width="50px" height="18px">
      {!tracking ? (
        <SkeletonBox
          width="50px"
          height="24px"
          className={styles.skeleton_box_light}
        />
      ) : (
        <span>{tracking}</span>
      )}
    </FixedBox>
  );

  return (
    <div
      className={styles.subtask_creation_header}
      onClick={
        popups["SubTaskEdit"]
          ? () => {}
          : () => isEdit && dispatch(openPopup("SubTaskEdit"))
      }
    >
      <div className={styles.subtask_creation_titleandDesc} key={name}>
        <h2>{title}</h2>
        <p
          style={{
            marginTop: "0.5rem",
            overflow: "hidden",
            wordWrap: "break-word",
            whiteSpace: "normal",
          }}
        >
          {description}
        </p>
      </div>
      {showFullDescription && (
        <SummaryPopup
          header="View"
          callbackCross={() => setShowFullDescription(false)}
          callbackBack={() => setShowFullDescription(false)}
          callbackApprove={() => setShowFullDescription(false)}
        >
          <div style={{ display: "flex", flexWrap: "wrap" }}>
            <div
              className={styles.summaryDivData}
              style={{ flex: 1, minWidth: 220 }}
            >
              <div className={styles.summaryDataContent}>
                <p
                  style={{ color: "var(--text-black-60)", marginTop: "0.5rem" }}
                  className="p_tag_14px"
                >
                  Name
                </p>
                <h4>{name || "--"}</h4>
              </div>
            </div>
          </div>
          <div className={styles.summaryDivData}>
            <div className={styles.summaryDataContent}>
              <p
                style={{ color: "var(--text-black-60)", marginTop: "0.5rem" }}
                className="p_tag_14px"
              >
                Description
              </p>
              <h4>{Description || "--"}</h4>
            </div>
          </div>
          <div style={{ display: "flex", flexWrap: "wrap" }}>
            <div
              className={styles.summaryDivData}
              style={{ flex: 1, minWidth: 220 }}
            >
              <div className={styles.summaryDataContent}>
                <p
                  style={{ color: "var(--text-black-60)", marginTop: "0.5rem" }}
                  className="p_tag_14px"
                >
                  Unit
                </p>
                <h4>{unit || "--"}</h4>
              </div>
            </div>
            <div
              className={styles.summaryDivData}
              style={{ flex: 1, minWidth: 220 }}
            >
              <div className={styles.summaryDataContent}>
                <p
                  style={{ color: "var(--text-black-60)", marginTop: "0.5rem" }}
                  className="p_tag_14px"
                >
                  Weightage
                </p>
                <h4>{weightage ?? "--"}</h4>
              </div>
            </div>
            <div
              className={styles.summaryDivData}
              style={{ flex: 1, minWidth: 220 }}
            >
              <div className={styles.summaryDataContent}>
                <p
                  style={{ color: "var(--text-black-60)", marginTop: "0.5rem" }}
                  className="p_tag_14px"
                >
                  Tracking
                </p>
                <h4>{tracking || "--"}</h4>
              </div>
            </div>
          </div>
        </SummaryPopup>
      )}

      <div className={styles.subtask_creation_right}>
        <div className={styles.subtask_creation_tooltip}>
          <div className={styles.subtask_creation_tooltip_icon}>{unit}</div>
          <div>
            <p
              className="small_text_p"
              style={{ color: "var(--text-black-60)" }}
            >
              Unit
            </p>
            <p
              className="small_text_p"
              style={{ color: "var(--text-black-87)" }}
            >
              {unitDisplay}
            </p>
          </div>
        </div>
        <div className={styles.subtask_creation_tooltip}>
          <div className={styles.subtask_creation_tooltip_icon}>
            <UnitSvgs />
          </div>
          <div>
            <p
              className="small_text_p"
              style={{ color: "var(--text-black-60)" }}
            >
              Weightage
            </p>
            <p
              className="small_text_p"
              style={{ color: "var(--text-black-87)" }}
            >
              {weightageDisplay}
            </p>
          </div>
        </div>
        <div className={styles.subtask_creation_tooltip}>
          <div className={styles.subtask_creation_tooltip_icon}>
            <TrackingSvg />
          </div>
          <div>
            <p
              className="small_text_p"
              style={{ color: "var(--text-black-60)" }}
            >
              Tracking
            </p>
            <p
              className="small_text_p"
              style={{ color: "var(--text-black-87)" }}
            >
              {trackingDisplay}
            </p>
          </div>
        </div>
      </div>
      {!isDeletedNext && !isMD && (
        <div
          style={{ cursor: "pointer" }}
          onClick={(e) => {
            e.stopPropagation();
            onclick();
          }}
          className={styles.taskeditpencil}
        >
          {isEdit ? <RedCross /> : <TaskEditPencil />}
        </div>
      )}
      {isEdit && (
        <div
          style={{ cursor: "pointer" }}
          onClick={(e) => {
            e.stopPropagation();
            dispatch(openPopup("DelSubtask"));
          }}
          className={styles.header_delete_icon}
        >
          <DeleteIcon />
        </div>
      )}
      {popups["DelSubtask"] && (
        <DeletePopup
          header="Are you sure you want to delete this subtask?"
          height="calc(100% - 7.25rem)"
          heightupperlimit="0rem"
          callbackDelete={
            disableButton
              ? () => {}
              : async () => {
                  setDisableButton(true);

                  const response = await deleteSubtask({
                    subTaskId: SutbaskData?._id || "",
                    taskId: SutbaskData?.TaskmasterId?._id,
                  });
                  dispatch(setRemoveNavigationKey(true));
                  console.log(response, "this is response fo deleted subtask");
                  if (response?.data?.success) {
                    const getallsubtasks = await fetchSubtasks();
                    console.log(
                      getallsubtasks,
                      "these are all subtask for this dasdf"
                    );
                    const currentSubtask = getallsubtasks?.find(
                      (item: any) => item?._id === SutbaskData?._id
                    );
                    console.log(
                      currentSubtask,
                      "this current subtask checkout ahsdfsadf"
                    );
                    const ItemsToDelete = [
                      ...(currentSubtask?.ManpowerId || []),
                      ...(currentSubtask?.ToolId || []),
                      ...(currentSubtask?.MachinaryId || []),
                      ...(currentSubtask?.MaterialId || []),
                      ...(currentSubtask?.AdminId || []),
                      ...(currentSubtask?.AssigneeId || []),
                      ...(currentSubtask?.ReporterId?.Reporter?.flatMap(
                        (e: any) =>
                          Array.isArray(e.designationId)
                            ? [...e.designationId]
                            : []
                      ) || []),
                    ];
                    console.log(ItemsToDelete, "these are items to delete");
                    // Remove the current subtask from allsubtasks
                    const allsubtasks = getallsubtasks?.filter(
                      (item: any) => item?._id !== SutbaskData?._id
                    );
                    const allManpowers = allsubtasks?.map(
                      (item: any) => item?.ManpowerId || []
                    );
                    console.log(
                      allManpowers,
                      "these are all manpower for this subtask"
                    );
                    const allTools = allsubtasks?.map(
                      (item: any) => item?.ToolId || []
                    );

                    console.log(
                      allTools,
                      "these are all tools for this subtask"
                    );
                    const allMachinaries = allsubtasks?.map(
                      (item: any) => item?.MachinaryId || []
                    );
                    console.log(
                      allMachinaries,
                      "these are all machinaries for this subtask"
                    );
                    const allMaterials = allsubtasks?.map(
                      (item: any) => item?.MaterialId || []
                    );
                    console.log(
                      allMaterials,
                      "these are all materials for this subtask"
                    );
                    const Taskmanager = allsubtasks?.map(
                      (item: any) => item?.AdminId || []
                    );
                    console.log(
                      Taskmanager,
                      "these are all departments for this subtask"
                    );
                    const allAssignes = allsubtasks?.map(
                      (item: any) => item?.AssigneeId || []
                    );
                    // console.log(
                    //   allAssignes,
                    //   "these are all designations for this subtask"
                    // );
                    const allReporters = allsubtasks?.flatMap(
                      (item: any) =>
                        item?.ReporterId?.Reporter?.flatMap((e: any) =>
                          Array.isArray(e.designationId)
                            ? [...e.designationId]
                            : []
                        ) || []
                    );
                    const allData = [
                      ...allManpowers.flat(),
                      ...allTools.flat(),
                      ...allMachinaries.flat(),
                      ...allMaterials.flat(),
                      ...Taskmanager.flat(),
                      ...allAssignes.flat(),
                      ...allReporters.flat(),
                    ];
                    console.log(allData, "this is all data to delete");
                    // ItemsToDelete is an array of objects, allData is an array of objects
                    // We want to filter allData to exclude objects that are present in ItemsToDelete (by _id)
                    console.log(
                      ItemsToDelete,
                      "checkou there is items to delte "
                    );
                    const allDeparmentIds = allData?.map(
                      (e) => e?.DepartmentId
                    );
                    const departmentIdsToDelete = ItemsToDelete.map(
                      (e) => e?.DepartmentId
                    );
                    const finalDeparmentsToDelete =
                      departmentIdsToDelete?.filter(
                        (id: any) => !allDeparmentIds.includes(id)
                      );
                    console.log(
                      allDeparmentIds,
                      finalDeparmentsToDelete,
                      "These are final deparrtments to der"
                    );
                    const itemsToDeleteIds = ItemsToDelete.map(
                      (item: any) => item._id
                    );
                    // Get all _id values from allData
                    const allDataIds = allData.map((obj: any) => obj._id);
                    const finalItemsToDelete = itemsToDeleteIds?.filter(
                      (id: any) => !allDataIds.includes(id)
                    );
                    console.log(
                      allData,
                      finalItemsToDelete,
                      TaskData,
                      "these are final items to delete"
                    );

                    await dispatch(
                      updateTaskData({
                        ...TaskData,
                        ManpowerId: TaskData?.ManpowerId?.filter(
                          (e: any) => !finalItemsToDelete.includes(e._id)
                        ),
                        ToolId: TaskData?.ToolId?.filter(
                          (e: any) => !finalItemsToDelete.includes(e._id)
                        ),
                        MachinaryId: TaskData?.MachinaryId?.filter(
                          (e: any) => !finalItemsToDelete.includes(e._id)
                        ),
                        MaterialId: TaskData?.MaterialId?.filter(
                          (e: any) => !finalItemsToDelete.includes(e._id)
                        ),
                        DesignationId: TaskData?.DesignationId?.filter(
                          (e: any) => !finalItemsToDelete.includes(e._id)
                        ),
                        DepartmentId: TaskData?.DepartmentId?.filter(
                          (e: any) => !finalDeparmentsToDelete.includes(e?._id)
                        ),
                        Subtaskdetails: TaskData?.Subtaskdetails?.filter(
                          (e) => e?._id !== SutbaskData?._id
                        ),
                      } as any)
                    );

                    await saveSyncData(
                      {
                        ...TaskData,
                        ManpowerId: TaskData?.ManpowerId?.filter(
                          (e: any) => !finalItemsToDelete.includes(e._id)
                        ),
                        ToolId: TaskData?.ToolId?.filter(
                          (e: any) => !finalItemsToDelete.includes(e._id)
                        ),
                        MachinaryId: TaskData?.MachinaryId?.filter(
                          (e: any) => !finalItemsToDelete.includes(e._id)
                        ),
                        MaterialId: TaskData?.MaterialId?.filter(
                          (e: any) => !finalItemsToDelete.includes(e._id)
                        ),
                        DesignationId: TaskData?.DesignationId?.filter(
                          (e: any) => !finalItemsToDelete.includes(e._id)
                        ),
                        Subtaskdetails: TaskData?.Subtaskdetails?.filter(
                          (e) => e?._id !== SutbaskData?._id
                        ),
                      },
                      "time",
                      "TaskForm"
                    );

                    // await updateTaskApi();
                    dispatch(settaskChangeAPiFlag(true));

                    showToast({
                      messageContent: "Subtask Deleted Successfully",
                      type: "success",
                    });
                  } else {
                    showToast({
                      messageContent: "Oops! Something went wrong",
                      type: "danger",
                    });
                  }

                  dispatch(
                    updateTaskData({
                      ...TaskData,
                      Subtaskdetails: TaskData?.Subtaskdetails?.filter(
                        (item) => item._id !== SutbaskData?._id
                      ),
                    })
                  );

                  dispatch(setCurrentSubTaskRoute(""));
                  dispatch(
                    setSubTaskRoute(
                      SubTasksRoutes?.filter(
                        (e) => e.id !== SutbaskData?._id
                      ) || []
                    )
                  );
                  console.log(params, "params");

                  setTimeout(() => {
                    navigate(`/category/${params.catId}/task/${params.taskId}`);
                    dispatch(removeNavigate());
                    dispatch(closePopup("DelSubtask"));
                    setDisableButton(false);
                  }, 400);
                }
          }
          onClose={() => {
            dispatch(closePopup("DelSubtask"));
          }}
        >
          <DeleteSubtaskBody data={SutbaskData} />
        </DeletePopup>
      )}
      {popups["SubTaskEdit"] && (
        <AddSubTasksPopup
          onClose={() => dispatch(closePopup("SubTaskEdit"))}
          data={{
            name: name,
            Description: Description,
            Unit: unit,
            subtaskWeighatages: String(weightage),
            Tracking: tracking,
          }}
          isEdit={true}
          onSubmit={async (data) => {
            try {
              // if (taskApiCall) {
              await updateTaskApi();
              // }

              const res = await subtaskUpdate({
                name: data.name,
                Description: data.Description,
                Unit: data.Unit,
                subtaskWeighatages: Number(data.subtaskWeighatages),
                Tracking: data.Tracking,
              });

              showToast({
                messageContent: "Subtask updated successfully",
                type: "success",
              });

              dispatch(resetTaskHeader({ title: data?.name }));

              dispatch(
                setcurrentSubtaskData({
                  ...SutbaskData,
                  name: data.name,
                  Description: data.Description,
                  Unit: data.Unit,
                  subtaskWeighatages: Number(data.subtaskWeighatages),
                  Tracking: data.Tracking,
                })
              );

              dispatch(
                setcurrentTaskData({
                  ...TaskData,
                  Subtaskdetails: TaskData?.Subtaskdetails?.map((item) =>
                    item?._id === SutbaskData?._id
                      ? {
                          ...item,
                          name: data.name,
                          Description: data.Description,
                          Unit: data.Unit,
                          subtaskWeighatages: Number(data.subtaskWeighatages),
                          Tracking: data.Tracking,
                        }
                      : item
                  ),
                })
              );

              await getTaskDetailsLatest();

              navigate(
                `/category/${params.catId}/task/${params.taskId}/subtask/${params.subtaskId}`
              );
            } catch (error: any) {
              showToast({
                messageContent:
                  error?.data?.message || "Oops! Something went wrong",
                type: "error",
              });
              throw error;
            }
          }}
        />
      )}
    </div>
  );
};

const DeleteSubtaskBody: React.FC<DeleteSubtaskBodyProps> = ({ data }) => {
  console.log("fgggdata", data?.AutoId);
  console.log("hhhh>>>>>::", data?.MethodId?.work_instruction_id);
  return (
    <div>
      <div className={styles.flexContainer}>
        <div className={styles.summaryDivData}>
          <div className={styles.summaryDataContent}>
            <p style={{ color: "var(--text-black-60)" }} className="p_tag_14px">
              Name
            </p>
            <h4 style={{ marginTop: "0.3rem", color: "var(--text-black-87)" }}>
              {data?.name || "No name provided"}
            </h4>
          </div>
        </div>
        <div className={styles.summaryDivData}>
          <div className={styles.summaryDataContent}>
            <p style={{ color: "var(--text-black-60)" }} className="p_tag_14px">
              Unit
            </p>
            <h4 style={{ marginTop: "0.3rem", color: "var(--text-black-87)" }}>
              {data?.Unit || "No unit"}
            </h4>
          </div>
        </div>
      </div>
      {data?.Description && (
        <div className={styles.summaryDivData}>
          <div className={styles.summaryDataContent}>
            <p style={{ color: "var(--text-black-60)" }} className="p_tag_14px">
              Description
            </p>
            <h4 style={{ marginTop: "0.3rem", color: "var(--text-black-87)" }}>
              {data?.Description || "No description"}
            </h4>
          </div>
        </div>
      )}
      <div className={styles.flexContainer}>
        {data?.subtaskWeighatages && (
          <div className={styles.summaryDivData}>
            <div className={styles.summaryDataContent_weightage}>
              <div>
                <p
                  style={{ color: "var(--text-black-60)" }}
                  className="p_tag_14px"
                >
                  Weightage
                </p>
                <h4
                  style={{ marginTop: "0.3rem", color: "var(--text-black-87)" }}
                >
                  {data?.subtaskWeighatages || "No weightage"}
                </h4>
              </div>
              <WieghtPercentageIcon />
            </div>
          </div>
        )}

        {data?.Tracking && (
          <div className={styles.summaryDivData}>
            <div className={styles.summaryDataContent}>
              <p
                style={{ color: "var(--text-black-60)" }}
                className="p_tag_14px"
              >
                Tracking
              </p>
              <h4
                style={{
                  marginTop: "0.3rem",
                  color: "var(--text-black-87)",
                  textTransform: "capitalize",
                }}
              >
                {data?.Tracking || "No tracking"}
              </h4>
            </div>
          </div>
        )}
      </div>
      {((data?.ToolId?.length ?? 0) > 0 ||
        (data?.MaterialId?.length ?? 0) > 0 ||
        (data?.ManpowerId?.length ?? 0) > 0 ||
        (data?.MachinaryId?.length ?? 0) > 0) && (
        <h4 style={{ color: "var(--primary_color)", margin: "0.6rem" }}>
          Resources
        </h4>
      )}
      {(data?.ManpowerId?.length ?? 0) > 0 && (
        <div className={styles.summaryDivData}>
          <div className={styles.summaryDataContent}>
            <p style={{ color: "var(--text-black-60)" }} className="p_tag_14px">
              Manpower
            </p>
            <div
              className=""
              style={{ display: "flex", gap: "1rem", flexWrap: "wrap" }}
            >
              {data?.ManpowerId.map((e) => (
                <h4
                  style={{ marginTop: "0.3rem", color: "var(--text-black-87)" }}
                >
                  {e?.name}
                </h4>
              )) || "No description"}
            </div>
          </div>
        </div>
      )}
      {(data?.MachinaryId?.length ?? 0) > 0 && (
        <div className={styles.summaryDivData}>
          <div className={styles.summaryDataContent}>
            <p style={{ color: "var(--text-black-60)" }} className="p_tag_14px">
              Machinery
            </p>
            <div
              className=""
              style={{ display: "flex", gap: "1rem", flexWrap: "wrap" }}
            >
              {data?.MachinaryId.map((e) => (
                <h4
                  style={{ marginTop: "0.3rem", color: "var(--text-black-87)" }}
                >
                  {e?.name}
                </h4>
              )) || "No description"}
            </div>
          </div>
        </div>
      )}
      {(data?.ToolId?.length ?? 0) > 0 && (
        <div className={styles.summaryDivData}>
          <div className={styles.summaryDataContent}>
            <p style={{ color: "var(--text-black-60)" }} className="p_tag_14px">
              Tools
            </p>
            <div
              className=""
              style={{ display: "flex", gap: "1rem", flexWrap: "wrap" }}
            >
              {data?.ToolId.map((e) => (
                <h4
                  style={{ marginTop: "0.3rem", color: "var(--text-black-87)" }}
                >
                  {e?.name}
                </h4>
              )) || "No description"}
            </div>
          </div>
        </div>
      )}
      {(data?.MaterialId?.length ?? 0) > 0 && (
        <div className={styles.summaryDivData}>
          <div className={styles.summaryDataContent}>
            <p style={{ color: "var(--text-black-60)" }} className="p_tag_14px">
              Materials
            </p>
            <div
              className=""
              style={{ display: "flex", gap: "1rem", flexWrap: "wrap" }}
            >
              {data?.MaterialId.map((e) => (
                <h4
                  style={{ marginTop: "0.3rem", color: "var(--text-black-87)" }}
                >
                  {e?.name}
                </h4>
              )) || "No description"}
            </div>
          </div>
        </div>
      )}
      {/* Task allocation section */}
      {((data?.AdminId?.length ?? 0) > 0 ||
        (data?.AssigneeId.length ?? 0) > 0) && (
        <h4 style={{ color: "var(--primary_color)", margin: "0.6rem" }}>
          Task Allocation
        </h4>
      )}
      {(data?.AdminId?.length ?? 0) > 0 && (
        <div className={styles.summaryDivData}>
          <div className={styles.summaryDataContent}>
            <p style={{ color: "var(--text-black-60)" }} className="p_tag_14px">
              Task Manager
            </p>
            <div
              className=""
              style={{ display: "flex", gap: "1rem", flexWrap: "wrap" }}
            >
              {data?.AdminId.map((e) => (
                <h4
                  style={{ marginTop: "0.3rem", color: "var(--text-black-87)" }}
                >
                  {e?.name}
                </h4>
              )) || "No description"}
            </div>
          </div>
        </div>
      )}
      {(data?.AssigneeId.length ?? 0) > 0 && (
        <div className={styles.summaryDivData}>
          <div className={styles.summaryDataContent}>
            <p style={{ color: "var(--text-black-60)" }} className="p_tag_14px">
              Assign To
            </p>
            <div
              className=""
              style={{ display: "flex", gap: "1rem", flexWrap: "wrap" }}
            >
              {data?.AssigneeId.map((e) => (
                <h4
                  style={{ marginTop: "0.3rem", color: "var(--text-black-87)" }}
                >
                  {e?.name}
                </h4>
              )) || "No description"}
            </div>
          </div>
        </div>
      )}
      {(data?.ReporterId && data?.ReporterId?.Reporter?.length > 0) > 0 && (
        <div className={styles.summaryDivData}>
          <div className={styles.summaryDataContent}>
            <p style={{ color: "var(--text-black-60)" }} className="p_tag_14px">
              Reporter
            </p>
            <div className="">
              {data?.ReporterId?.Reporter?.map((e) => (
                <div
                  style={{
                    display: "flex",
                    flexDirection: "column",
                    marginTop: "0.5rem",
                  }}
                >
                  <h4
                    style={{
                      marginTop: "0.3rem",
                      color: "var(--text-black-87)",
                      fontWeight: 700,
                    }}
                  >
                    Level {e?.Level}
                  </h4>
                  <div
                    style={{ display: "flex", gap: "1rem", flexWrap: "wrap" }}
                  >
                    {e?.designationId &&
                      e?.designationId?.map((item) => (
                        <h4
                          style={{
                            marginTop: "0.3rem",
                            color: "var(--text-black-87)",
                          }}
                        >
                          {item?.name}
                        </h4>
                      ))}
                  </div>
                </div>
              )) || "No description"}
            </div>
          </div>
        </div>
      )}
      {data?.AutoId && data?.AutoId.TriggerResponse?.name && (
        <h4 style={{ color: "var(--primary_color)", margin: "0.6rem" }}>
          Trigger Event
        </h4>
      )}
      {data?.AutoId && data?.AutoId.TriggerResponse?.name && (
        <div className={styles.TriggerEventDivData}>
          {data?.AutoId.TriggerResponse?.name && (
            <div className={` ${styles.triggerDataContent}`}>
              <p
                style={{ color: "var(--text-black-60)" }}
                className="p_tag_14px"
              >
                Trigger Response
              </p>
              <h4
                style={{
                  marginTop: "0.3rem",
                  color: "var(--text-black-87)",
                }}
              >
                {data?.AutoId.TriggerResponse?.name}
              </h4>
            </div>
          )}
          {data?.AutoId.TriggerAction?.ActionName?.name && (
            <div className={styles.triggerDataContent}>
              <p
                style={{ color: "var(--text-black-60)" }}
                className="p_tag_14px"
              >
                Action
              </p>
              <h4
                style={{
                  marginTop: "0.3rem",

                  color: "var(--text-black-87)",
                }}
              >
                {data?.AutoId.TriggerAction?.ActionName?.name}
              </h4>
            </div>
          )}

          <div
            style={{
              display: "grid",
              gridTemplateColumns:
                isValidValue(data?.AutoId?.TriggerAction?.ActionTime) &&
                isValidValue(data?.AutoId?.ResponseTime)
                  ? "1fr 1fr"
                  : "1fr",
            }}
          >
            {isValidValue(data?.AutoId?.TriggerAction?.ActionTime) && (
              <div className={styles.triggerDataContent}>
                <p
                  style={{ color: "var(--text-black-60)" }}
                  className="p_tag_14px"
                >
                  Hours
                </p>
                <h4
                  style={{
                    marginTop: "0.3rem",
                    color: "var(--text-black-87)",
                  }}
                >
                  {data?.AutoId?.TriggerAction?.ActionTime}
                </h4>
              </div>
            )}
            {isValidValue(data?.AutoId?.ResponseTime) && (
              <div className={styles.triggerDataContent}>
                <p
                  style={{ color: "var(--text-black-60)" }}
                  className="p_tag_14px"
                >
                  Response Time
                </p>
                <h4
                  style={{
                    marginTop: "0.3rem",
                    color: "var(--text-black-87)",
                  }}
                >
                  {data?.AutoId?.ResponseTime}
                </h4>
              </div>
            )}
          </div>
        </div>
      )}

      {((data?.MethodId?.work_instruction_id?.length ?? 0) > 0 ||
        (data?.MethodId?.Controlplan?.length ?? 0) > 0 ||
        (data?.MethodId?.Failuremode?.length ?? 0) > 0 ||
        (data?.MethodId?.task_closing_requirement?.length ?? 0) > 0) && (
        <h4 style={{ color: "var(--primary_color)", margin: "0.6rem" }}>
          Method
        </h4>
      )}
      {(data?.MethodId?.work_instruction_id?.length ?? 0) > 0 && (
        <>
          {data?.MethodId?.work_instruction_id?.map((e, index) => (
            <>
              <div className={styles.summaryDivData}>
                <div className={styles.summaryDataContent}>
                  <p
                    style={{ color: "var(--text-black-60)" }}
                    className="p_tag_14px"
                  >
                    Work Instructions {index + 1}
                  </p>
                  <p
                    style={{ color: "var(--text-black-60)" }}
                    className="p_tag_14px"
                  >
                    Description
                  </p>
                  <div
                    className=""
                    style={{ display: "flex", gap: "1rem", flexWrap: "wrap" }}
                  >
                    <h4
                      style={{
                        marginTop: "0.3rem",
                        color: "var(--text-black-87)",
                      }}
                    >
                      {e?.Description}
                    </h4>
                  </div>
                </div>
              </div>
              {e?.file?.name && (
                <div className={styles.summaryDivData}>
                  <div className={styles.summaryDataContent}>
                    <p
                      style={{ color: "var(--text-black-60)" }}
                      className="p_tag_14px"
                    >
                      {fileTypeMapper(e?.file)}
                    </p>
                    <div
                      className=""
                      style={{ display: "flex", gap: "1rem", flexWrap: "wrap" }}
                    >
                      <h4
                        style={{
                          marginTop: "0.3rem",
                          color: "var(--text-black-87)",
                          textTransform: "capitalize",
                        }}
                      >
                        {e?.file?.name}
                      </h4>
                    </div>
                  </div>
                </div>
              )}
              <div className={styles.summaryDivData}>
                <div className={styles.summaryDataContent}>
                  <p
                    style={{ color: "var(--text-black-60)" }}
                    className="p_tag_14px"
                  >
                    Action
                  </p>
                  <div
                    className=""
                    style={{ display: "flex", gap: "1rem", flexWrap: "wrap" }}
                  >
                    <h4
                      style={{
                        marginTop: "0.3rem",
                        color: "var(--text-black-87)",
                        textTransform: "capitalize",
                      }}
                    >
                      {e?.optionselected}
                    </h4>
                  </div>
                </div>
              </div>
              {(e?.optionselected === "Photo" ||
                e?.optionselected === "photo") && (
                <div className={styles.summaryDivData}>
                  <div className={styles.summaryDataContent}>
                    {e?.photoref?.photos?.map((e) => (
                      <>
                        <p style={{ color: "var(--text-black-87)" }}>
                          {" "}
                          {e?.fileName || getFileName(e?.photo)}
                        </p>
                        <p
                          style={{ color: "var(--text-black-60)" }}
                          className="p_tag_14px"
                        >
                          Reference Detail
                        </p>
                        <div
                          className=""
                          style={{
                            display: "flex",
                            gap: "1rem",
                            flexWrap: "wrap",
                          }}
                        >
                          <h4
                            style={{
                              marginTop: "0.3rem",
                              color: "var(--text-black-87)",
                            }}
                          >
                            {e?.details}
                          </h4>
                        </div>
                      </>
                    ))}
                  </div>
                </div>
              )}
              {e?.manpowerId?.length > 0 && (
                <div className={styles.summaryDivData}>
                  <div className={styles.summaryDataContent}>
                    <p
                      style={{ color: "var(--text-black-60)" }}
                      className="p_tag_14px"
                    >
                      Manpower
                    </p>
                    <div
                      className=""
                      style={{ display: "flex", gap: "1rem", flexWrap: "wrap" }}
                    >
                      {e?.manpowerId?.map((e) => (
                        <h4
                          style={{
                            marginTop: "0.3rem",
                            color: "var(--text-black-87)",
                          }}
                        >
                          {e?.name}
                        </h4>
                      )) || "No description"}
                    </div>
                  </div>
                </div>
              )}
              {e?.machinaryId?.length > 0 && (
                <div className={styles.summaryDivData}>
                  <div className={styles.summaryDataContent}>
                    <p
                      style={{ color: "var(--text-black-60)" }}
                      className="p_tag_14px"
                    >
                      Machinery
                    </p>
                    <div
                      className=""
                      style={{ display: "flex", gap: "1rem", flexWrap: "wrap" }}
                    >
                      {e?.machinaryId?.map((e) => (
                        <h4
                          style={{
                            marginTop: "0.3rem",
                            color: "var(--text-black-87)",
                          }}
                        >
                          {e?.name}
                        </h4>
                      )) || "No description"}
                    </div>
                  </div>
                </div>
              )}
              {e?.toolsId?.length > 0 && (
                <div className={styles.summaryDivData}>
                  <div className={styles.summaryDataContent}>
                    <p
                      style={{ color: "var(--text-black-60)" }}
                      className="p_tag_14px"
                    >
                      Tools
                    </p>
                    <div
                      className=""
                      style={{ display: "flex", gap: "1rem", flexWrap: "wrap" }}
                    >
                      {e?.toolsId?.map((e) => (
                        <h4
                          style={{
                            marginTop: "0.3rem",
                            color: "var(--text-black-87)",
                          }}
                        >
                          {e?.name}
                        </h4>
                      )) || "No description"}
                    </div>
                  </div>
                </div>
              )}
              {e?.materialId?.length > 0 && (
                <div className={styles.summaryDivData}>
                  <div className={styles.summaryDataContent}>
                    <p
                      style={{ color: "var(--text-black-60)" }}
                      className="p_tag_14px"
                    >
                      Materials
                    </p>
                    <div
                      className=""
                      style={{ display: "flex", gap: "1rem", flexWrap: "wrap" }}
                    >
                      {e?.materialId?.map((e) => (
                        <h4
                          style={{
                            marginTop: "0.3rem",
                            color: "var(--text-black-87)",
                          }}
                        >
                          {e?.name}
                        </h4>
                      )) || "No description"}
                    </div>
                  </div>
                </div>
              )}
            </>
          ))}
        </>
      )}
      {(data?.MethodId?.task_closing_requirement?.length ?? 0) > 0 && (
        <>
          {data?.MethodId?.task_closing_requirement?.map((e, index) => (
            <>
              <div className={styles.summaryDivData}>
                <div className={styles.summaryDataContent}>
                  <p
                    style={{ color: "var(--text-black-60)" }}
                    className="p_tag_14px"
                  >
                    Task Closing Requirements {index + 1}
                  </p>
                  <p
                    style={{ color: "var(--text-black-60)" }}
                    className="p_tag_14px"
                  >
                    Description
                  </p>
                  <div
                    className=""
                    style={{ display: "flex", gap: "1rem", flexWrap: "wrap" }}
                  >
                    <h4
                      style={{
                        marginTop: "0.3rem",
                        color: "var(--text-black-87)",
                      }}
                    >
                      {e?.Description}
                    </h4>
                  </div>
                </div>
              </div>
              {e?.file?.name && (
                <div className={styles.summaryDivData}>
                  <div className={styles.summaryDataContent}>
                    <p
                      style={{ color: "var(--text-black-60)" }}
                      className="p_tag_14px"
                    >
                      {fileTypeMapper(e?.file)}
                    </p>
                    <div
                      className=""
                      style={{ display: "flex", gap: "1rem", flexWrap: "wrap" }}
                    >
                      <h4
                        style={{
                          marginTop: "0.3rem",
                          color: "var(--text-black-87)",
                          textTransform: "capitalize",
                        }}
                      >
                        {e?.file?.name}
                      </h4>
                    </div>
                  </div>
                </div>
              )}
              <div className={styles.summaryDivData}>
                <div className={styles.summaryDataContent}>
                  <p
                    style={{ color: "var(--text-black-60)" }}
                    className="p_tag_14px"
                  >
                    Action
                  </p>
                  <div
                    className=""
                    style={{ display: "flex", gap: "1rem", flexWrap: "wrap" }}
                  >
                    <h4
                      style={{
                        marginTop: "0.3rem",
                        color: "var(--text-black-87)",
                        textTransform: "capitalize",
                      }}
                    >
                      {e?.optionselected}
                    </h4>
                  </div>
                </div>
              </div>

              {(e?.optionselected == "Photo" ||
                e?.optionselected == "photo") && (
                <div className={styles.summaryDivData}>
                  <div className={styles.summaryDataContent}>
                    {e?.photoref?.photos?.map((e) => (
                      <>
                        <p style={{ color: "var(--text-black-87)" }}>
                          {" "}
                          {e?.fileName || getFileName(e?.photo)}
                        </p>
                        <p
                          style={{ color: "var(--text-black-60)" }}
                          className="p_tag_14px"
                        >
                          Reference Detail
                        </p>
                        <div
                          className=""
                          style={{
                            display: "flex",
                            gap: "1rem",
                            flexWrap: "wrap",
                          }}
                        >
                          <h4
                            style={{
                              marginTop: "0.3rem",
                              color: "var(--text-black-87)",
                            }}
                          >
                            {e?.details}
                          </h4>
                        </div>
                      </>
                    ))}
                  </div>
                </div>
              )}
            </>
          ))}
        </>
      )}
      {/* this is for control plan */}
      {(data?.MethodId?.Controlplan?.length ?? 0) > 0 && (
        <>
          {data?.MethodId?.Controlplan?.map((e, index) => (
            <>
              <div className={styles.summaryDivData}>
                <div className={styles.summaryDataContent}>
                  <p
                    style={{ color: "var(--text-black-60)" }}
                    className="p_tag_14px"
                  >
                    Quality Control Plans {index + 1}
                  </p>
                  <div
                    className=""
                    style={{ display: "flex", gap: "1rem", flexWrap: "wrap" }}
                  >
                    <h4
                      style={{
                        marginTop: "0.3rem",
                        color: "var(--text-black-87)",
                      }}
                    >
                      {e?.Description}
                    </h4>
                  </div>
                </div>
              </div>
            </>
          ))}
        </>
      )}
      {/* this is for failure mode */}
      {(data?.MethodId?.Failuremode?.length ?? 0) > 0 && (
        <>
          {data?.MethodId?.Failuremode?.map((e, index) => (
            <>
              <div className={styles.summaryDivData}>
                <div className={styles.summaryDataContent}>
                  <p
                    style={{ color: "var(--text-black-60)" }}
                    className="p_tag_14px"
                  >
                    Quality Ensuring Measures {index + 1}
                  </p>
                  <p
                    style={{ color: "var(--text-black-60)" }}
                    className="p_tag_14px"
                  >
                    Description
                  </p>
                  <div
                    className=""
                    style={{ display: "flex", gap: "1rem", flexWrap: "wrap" }}
                  >
                    <h4
                      style={{
                        marginTop: "0.3rem",
                        color: "var(--text-black-87)",
                      }}
                    >
                      {e?.Description}
                    </h4>
                  </div>
                </div>
              </div>
              <div className={styles.summaryDivData}>
                <div className={styles.summaryDataContent}>
                  <p
                    style={{ color: "var(--text-black-60)" }}
                    className="p_tag_14px"
                  >
                    Solution
                  </p>
                  <div
                    className=""
                    style={{ display: "flex", gap: "1rem", flexWrap: "wrap" }}
                  >
                    <h4
                      style={{
                        marginTop: "0.3rem",
                        color: "var(--text-black-87)",
                      }}
                    >
                      {e?.solution}
                    </h4>
                  </div>
                </div>
              </div>
              <div className={styles.summaryDivData}>
                <div className={styles.summaryDataContent}>
                  <p
                    style={{ color: "var(--text-black-60)" }}
                    className="p_tag_14px"
                  >
                    Severity
                  </p>
                  <div
                    className=""
                    style={{ display: "flex", gap: "1rem", flexWrap: "wrap" }}
                  >
                    <h4
                      style={{
                        marginTop: "0.3rem",
                        color: "var(--text-black-87)",
                      }}
                    >
                      {e?.severity}
                    </h4>
                  </div>
                </div>
              </div>
            </>
          ))}
        </>
      )}
    </div>
  );
};
export default SubTaskCreationHeader;
