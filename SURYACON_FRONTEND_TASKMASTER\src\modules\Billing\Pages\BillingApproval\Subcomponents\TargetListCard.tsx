// Author: <PERSON><PERSON>
import React from "react";
import styles from "../styles/BillingApproval.module.css";
import { PaperBoard, Rupee} from "../../../../../assets/icons";

interface TargetListCardProps {
  name: string;
  budget: number;
  floors: number;
  tasks: number;
  isSelected?: boolean;
  onClick?: () => void;
}

const TargetListCard: React.FC<TargetListCardProps> = ({ 
  name, 
  budget, 
  floors, 
  isSelected = false,
  onClick
}) => {
  return (
    <div 
      className={`${styles.targetListCard} ${isSelected ? styles.targetListCardSelected : ''}`}
      onClick={onClick}
    >
      <div className={`${styles.mt_message_rejected}`}>
        <p className="small_text_p">{floors} floors available</p>
      </div>
      <div className={`${styles.targetCardupper}`}>
        <h4 className={``}>{name}</h4>
      </div>
      <div className={`${styles.targetCardbottom}`}>
        <div className={`${styles.targetCardbottominner}`}>
          <div className={`${styles.targetCardbottomSubinner}`}>
            <div className={`${styles.target_card_icon}`}>
              <PaperBoard />
            </div>
            <div>
              <div>
                <p className={`${styles.targetcard_bottom_subinner_key} small_text_p`}>{floors}</p>
              </div>
              <div>
                <p
                  className={`${styles?.target_card_icon_bottom_text} small_text_p`}
                >
                  Tasks
                </p>
              </div>
            </div>
          </div>
          
        
          <div className={`${styles.targetCardbottomSubinner}`}>
            <div className={`${styles.target_card_icon}`}>
              <Rupee color="#191919" width={16} height={16} />
            </div>
            <div>
              <div>
                <p className={`${styles.targetcard_bottom_subinner_key} small_text_p`}>
                  {budget.toLocaleString()}
                </p>
              </div>
              <div>
                <p
                  className={`${styles?.target_card_icon_bottom_text} small_text_p`}
                >
                  Budget
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TargetListCard;
