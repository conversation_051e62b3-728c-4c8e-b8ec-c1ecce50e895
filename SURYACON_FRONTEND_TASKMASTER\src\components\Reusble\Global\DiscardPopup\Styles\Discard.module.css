.closeButton {
  position: absolute;
  top: 1.5em;
  right: 0.625rem;
  padding: 1rem;
  background: transparent;
  border: none;
  cursor: pointer;
}

.discard_popup_form_container {
  /* margin: 4rem; line to remove */
  width: 36.75rem;
  padding: 2rem 1.25rem 1rem 1.25rem;
  height: 50rem;
  min-height: 70vh;
  /* max-height: 40rem; */
  border-radius: 2.6rem;
  backdrop-filter: blur(150px);
  position: absolute;
  top: 0;
  right: 1rem;
  z-index: 99;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

@keyframes slideOut {
  from {
    transform: translate(0, 0);
  }

  to {
    transform: translate(100%, 0);
  }
}

.discard_popup_form_container.closing {
  animation: slideOut 0.5s ease-out;
}

.discard_popup_form_top_text {
  /* margin: auto; */
  color: var(--primary_color);
  margin-bottom: 1.5rem;
  position: relative;
  max-width: 70%;
  overflow: hidden;
  text-overflow: ellipsis;
  /* white-space: nowrap; */
}

.discard_popup_header {
  display: flex;
  justify-content: center;
  text-align: center;
}

.discard_popup_main_body {
  flex: 1 1 80%;
}

.discard_popup_button_div {
  margin-top: 2rem;
  display: flex;
  column-gap: 1.5rem;
  justify-content: center;
  position: relative;
}

@media (max-width: 1536px) {
  .discard_popup_form_container {
    width: 32rem;
  }
}
