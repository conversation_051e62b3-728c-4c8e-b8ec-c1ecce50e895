{
  "files": [],
  "references": [
    { "path": "./tsconfig.app.json" },
    { "path": "./tsconfig.node.json" }
  ],
  "compilerOptions": {
    "jsx": "react-jsx",
    "module": "commonjs",
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "isolatedModules": true,
    "skipLibCheck": true,
    
    "outDir": "./dist"
  },
  "include": [
    "src/**/*.tsx",  // Ensure this includes all your source files
    "src/vite-env.d.ts"  // Add this if needed
, "src/Backup/TaskMasterBackup/index.ts", "src/Backup/BillingBackup/index.ts", "src/Backup/BackupIntial/index.ts"  ],
  "exclude": ["node_modules", "**/*.test.tsx"]
}
