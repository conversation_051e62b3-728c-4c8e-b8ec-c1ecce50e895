import React from "react";
import styles from "../Styles/VersionHistoryIcons.module.css";

interface VersionHistoryIconsProps {
  icon: React.ElementType;
  padding: string;
  backgroundColor?: string;
}

const VersionHistoryIcons: React.FC<VersionHistoryIconsProps> = ({
  icon: Icon,
  padding,
  backgroundColor = "var(--primary_color)",
}) => {
  return (
    <div
      style={{ padding, backgroundColor: backgroundColor }}
      className={`${styles.versionHistory_icons}`}
    >
      <Icon />
    </div>
  );
};

export default VersionHistoryIcons;
