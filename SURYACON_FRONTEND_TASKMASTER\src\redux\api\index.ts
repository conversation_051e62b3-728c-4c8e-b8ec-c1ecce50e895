import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { url } from "../../config/urls";
import { setCredentials } from "../features/Modules/Auth/authSlice";

const baseQuery = fetchBaseQuery({
  baseUrl: `${url}`,
  credentials: "include",
  prepareHeaders: async (headers) => {
    const macAddress = await window.electron.getMacAddress();
    if (macAddress) {
      headers.set("device-id", `${macAddress}`);
    }
    return headers;
  },
});

const baseQueryWithAUth = async (args: any, api: any, extraOptions: any) => {
  const result = await baseQuery(args, api, extraOptions);

  if (
    result.error &&
    result.error.status === 401 &&
    args.url !== "/check-authgetdata"
  ) {
    api.dispatch(setCredentials({ user: null, isAuthenticated: false }));
    // api.dispatch({ type: "auth/logout" });
    try {
      await window.electron.deleteCookies();
    } catch (error) {
      console.error("Error deleting cookies:", error);
    }
  }
  return result;
};
export const baseApi = createApi({
  reducerPath: "baseApi",
  baseQuery: baseQueryWithAUth,

  tagTypes: [
    "TaskCategory",
    "EditCategory",
    "NewTask",
    "SubTasks",
    "Task",
    "UpdateCategory",
    "DeleteCategory",
    "DeleteTask",
    "Project",
    "DeleteSubtask",
    "GetTaskDetails",
    "updateSubtask",
    "BasicDetail",
    "RouteGet",
    "SubtaskUpdate",
    "AddTaskPlanning",
    "Auth",
    "ToolDesignation",
    "MaterialDesignation",
    "ManpowerDesignation",
    "MachineryDesignation",
    "ToolCategory",
    "MaterialCategory",
    "ManpowerCategory",
    "MachineryCategory",
  ],
  endpoints: () => ({}),
});
