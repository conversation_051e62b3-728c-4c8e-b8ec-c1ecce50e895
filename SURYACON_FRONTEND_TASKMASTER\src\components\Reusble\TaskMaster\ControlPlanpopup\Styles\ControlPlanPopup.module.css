.controlPlan_container {
  position: fixed;
  top: 7rem;
  right: 1.5rem;
  transform: translate(0%, 0%);
  background: var(--blur-background);
  /* background-color: var(--main_background); */
  padding: 0.75rem 0.9rem;
  box-shadow: 0px 4px 40px 0px #00000080;
  border-radius: 2.6rem;
  z-index: 9999;
  width: 35rem;
  min-height: 85vh;
  height: calc(100% - 8.5rem);
  animation: slideIn 0.5s ease-out;
  backdrop-filter: blur(150px);

  box-shadow: 0px 4px 40px 0px #00000080;
}
/* 

*/
.summaryData {
  height: calc(100% - 9.25rem);
  /* padding: 0.45rem; */
  overflow-y: scroll;
}

@keyframes slideIn {
  from {
    transform: translate(100%, 0);
  }
  to {
    transform: translate(0%, 0%);
  }
}

@keyframes slideOut {
  from {
    transform: translate(0%, 0%);
  }

  to {
    transform: translate(100%, 0%);
  }
}

.controlPlan_container.closing {
  animation: slideOut 0.5s ease-out;
}

.controlPlan_header {
  color: var(--primary_color);
  display: flex;
  justify-content: space-around !important;
  text-align: center !important;
  padding: 0.6rem;
}

.closeButton {
  position: absolute;
  top: 1.5em;
  right: 0.625rem;
  padding: 1rem;
  background: transparent;
  border: none;

  cursor: pointer;
}

.controlPlan_datainputs{
  padding: 0.6rem;
}
.controlPlan_btngrp {
  position: fixed;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 100%;
  padding:1.5rem 1rem;
  display: flex;
  justify-content: center;
  gap: 1rem;

  border-radius: 2.6rem;                  
  /* backdrop-filter: blur(60px);
  background-color: var(--main_background); */
}

.summaryDivData {
  display: flex;
  align-items: center;
}

.summaryDataContent {
  display: flex;
  flex-direction: column;
  background: var(--main_background);
  border-radius: 0.75rem;
  /* width: 30.8rem; */
  width:100%;
  /* max-width: 28.5rem; */
  min-height: 3rem;
  padding: 1rem 1rem 1rem 1rem;
  white-space: normal;
  margin: 0 0.6rem;
  /* gap: 0.2rem; */
  line-height: 1.363rem;
  text-align: justify;
  /* box-shadow: 0px 1px 3px 0px #00000080; */
}

.discardConfirmation{
  max-height: calc(100% - 9rem);
  overflow: auto;
}