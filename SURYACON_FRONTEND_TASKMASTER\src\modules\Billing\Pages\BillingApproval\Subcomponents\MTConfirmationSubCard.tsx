import { FC } from "react";
import styles from "../Styles/BillingApprovalSubcomponent.module.css"
import { MTCSubCardProps } from "../../../../../interfaces/Modules/Billing/BillingApproval/BillingApproval";
import { Rupee } from "../../../../../assets/icons";
import { numberWithCommas } from "../../../../../functions/functions";


const MTConfirmationSubCard: FC<MTCSubCardProps> = ({
  icon: Icon,
  iconBg,
  property,
  keyProp,
  valueProp,
  valueClassName,
  displayType,
  displayChildType,
}) => {
  const isArrayofObjects = (value: any): boolean => {
    return (
      Array.isArray(value) &&
      value.every((item) => typeof item === "object" && item !== null)
    );
  };

  console.log(
    "item:",
    keyProp,
    "isArrayofObjects",
    isArrayofObjects(valueProp)
  );

  const renderContent = () => {
    switch (true) {
      case Array.isArray(valueProp) && !isArrayofObjects(valueProp):
        return (
          <div className={`${styles[displayChildType || ""]}`}>
            {valueProp.map((item, index) => (
              <p
                className={`${styles.mt_task_confirmation_subcard_left_text2} ${valueClassName}`}
                key={index}
              >
                {item}
              </p>
            ))}
          </div>
        );

      case Array.isArray(valueProp) && isArrayofObjects(valueProp):
        return (
          <div
            className={`${styles.mt_task_confirmation_subcard_left_text2} ${valueClassName}`}
          >
            {valueProp.map((item: any, index: number) => (
              <div key={index}>
                <p
                  className={`${styles.mt_confirmation_remarks_key} p_tag_14px`}
                >
                  {item.key.toString()}
                </p>
                <p className={`${styles.mt_confirmation_item_keyname} `}>
                  {item.value}
                </p>
              </div>
            ))}
          </div>
        );

      default:
        return (
          <div
            className={`${styles[displayChildType || ""]} ${
              keyProp === "Area" || "Response Time" ? styles.row : ""
            }`}
          >
            <p
              className={`${styles.mt_task_confirmation_subcard_left_text2} ${valueClassName}`}
            >
              {keyProp === "Budget" && (
                <span className={`${styles.mt_task_confirmation_rupee}`}>
                  <Rupee height={12} width={12} color="var(--text-black-87)" />
                </span>
              )}
              {keyProp === "Budget"
                ? `${numberWithCommas(valueProp)}`
                : `${valueProp ?? "time"}`}
            </p>
            {keyProp === "Area" && (
              <p className={`${styles.mt_confirmation_sq_ft} small_text_p`}>
                Sft
              </p>
            )}
            {keyProp === "Response Time" && (
              <p className={`${styles.mt_confirmation_hours} small_text_p`}>
                Hours
              </p>
            )}
          </div>
        );
    }
  };

  return (
    valueProp && (
      <div
        className={`${styles.mt_task_confirmation_subcard} ${
          styles[Icon ? displayType || "" : ""]
        } ${styles[property ? property : ""]}`}
      >
        <div className={`${styles.mt_task_confirmation_subcard_left}`}>
          <p className={`${styles.mt_confirmation_item_keyname} p_tag_14px`}>
            {keyProp || "..."}
          </p>
          {renderContent()}
        </div>

        {Icon && (
          <div
            style={{ backgroundColor: `${iconBg || ""}` }}
            className={`${styles.mt_task_confirmation_subcard_right_icon}`}
          >
            <Icon />
          </div>
        )}
      </div>
    )
  );
};

export default MTConfirmationSubCard;
