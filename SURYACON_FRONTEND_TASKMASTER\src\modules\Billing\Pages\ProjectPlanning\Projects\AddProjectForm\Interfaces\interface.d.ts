export interface ProjectData {
  _id?: string;
  name: string;
  photo: File | undefined | string;
  Address: string;
  project_type: string;
  estimate_budget: string | number;
  project_area: string | number;
  rate_type: string;
  client_id?: string | Client;
  project_duration: string | number;
  project_drawing: File[] | String[];
  project_completes: string | number;
  clientName: string;
  // client: Client;
  ClientPhoneNumber: string | number;
  Remarks: string;
  project_status?: string;
  project_start_date: string;
  rate: string | number;
  isDeleted?: boolean;
  createdAt?: string;
  updatedAt?: string;
  isCompleted?: Boolean;
  isApproved?: boolean;
}
export type Client = {
  _id: string;
  clientName: string;
  ClientPhoneNumber: string | number;
};
