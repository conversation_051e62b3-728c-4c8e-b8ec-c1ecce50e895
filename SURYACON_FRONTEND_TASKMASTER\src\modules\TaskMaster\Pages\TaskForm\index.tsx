//Author charvi changes made by a<PERSON><PERSON> rattan and jagroop

import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import styles from "./Styles/TaskCreationForm.module.css";
import TaskSidebar from "./Subcomponents/TaskCreationSidebar/TaskSidebar";
import { Outlet, useLocation, useParams } from "react-router-dom";
import TaskHeader from "./Subcomponents/TaskHeader/TaskHeader";
import {
  resetTaskData,
  setcurrentTaskData,
  settaskChangeAPiFlag,
} from "../../../../redux/features/Modules/TaskMaster/Slices/TaskMasterSlice";
import { useSelector } from "react-redux";
import { RootState, store } from "../../../../redux/store";
import {
  useAppDispatch,
  useAppSelector,
} from "../../../../redux/hooks/Modules/Reduxhooks/ReduxHooks";
import { debounce, initializeDatabase } from "../../../../functions/functions";
import {
  TaskMasterApi,
  useGetTaskDetailsByTaskIdQuery,
  useLazyGetTaskDetailsByTaskIdQuery,
  useUpdateTaskByIdMutation,
} from "../../../../redux/api/Modules/TaskMaster/TaskMasterapi";
import { settaskWorkInstructionDeleteEmpty } from "../../../../redux/features/Modules/TaskMaster/Slices/WorkInstructionSlice";
import { setAllSubRoutes } from "../../../../redux/features/Modules/TaskMaster/Slices/TriggerEventSlice";
import { saveSyncData } from "../../../../Backup/BackupFunctions/BackupFunctions";
import VersionHistory from "../../../VersionHistory";
import { Taskmaster } from "../../../../Backup/TaskMasterBackup";
import { useOpenCloseTaskMasterMutation } from "../../../../redux/api/Modules/Reusble/Reusble";

const TaskCreation = () => {
  const TaskData = useSelector(
    (state: RootState) => state?.taskForm?.currentSubtaskData
  );
  const currentSubTaskRoute = useAppSelector(
    (state: RootState) => state.taskMaster.currentSubTaskRoute
  );
  const taskApiCall = useSelector(
    (state: RootState) => state.isEditTaskReducer.istaskApiCall
  );
  const isApiCallRef = useRef(taskApiCall);
  const currentOpenPopup = useAppSelector((state) => state.popup.popups);
  const handleSyncRef = useRef<() => Promise<void>>(async () => {});
  const finaltaskIdRef = useRef<string | undefined>(undefined);
  const [updateTaskById, { isLoading }] = useUpdateTaskByIdMutation();
  const [getTaskById] = useLazyGetTaskDetailsByTaskIdQuery();
  const dispatch = useAppDispatch();
  // const detectChanges = useAppSelector((state) => state.backupSlice.isOpen);
  // const [isTaskEmpty, setIsTaskEmpty] = useState(false);
  // get the task id from useParams
  const { taskId } = useParams<{ taskId: string | undefined }>();
  const location = useLocation();
  const [finalTaskId, setFinalTaskId] = useState<string | null>(null);
  const taskworkinstructiondelete = useAppSelector(
    (state) => state.taskworkinstrucitonReducer.taskworkinstruction
  );
  const isCalledRef = useRef(false);
  const TaskDataRef = useRef(TaskData);
  const containerRef = useRef<HTMLDivElement>(null);
  const [updateStatus] = useOpenCloseTaskMasterMutation();

  const [width, setWidth] = useState<number>(0);
  // called task details api using task id

  const isEdit = useSelector(
    (state: RootState) => state.isEditTaskReducer.isEdit
  );

  const handleUpdateStatus = async (data?: {
    status: string;
    taskid: string;
  }) => {
    try {
      const response = await updateStatus(data).unwrap();
      return response;
    } catch (error) {
      console.log(error);
      throw error;
    }
  };

  const transformTaskData = (task: any) => {
    const extractIds = (data: any) => data?.map((item: any) => item._id) || [];
    const excludeIds = (data: any[]) => data.map(({ _id, ...rest }) => rest);

    const transformWorkInstructions = (workInstructions: any[]) => {
      console.log(workInstructions, "check for work instructionse");
      return workInstructions.map((item: any) => {
        const isTimestampId = /^\d{13}$/.test(String(item._id)); // Check if _id is a timestamp
        console.log("work instruction>>>>>>>>>", item);

        return {
          ...(item._id && !isTimestampId && { _id: item._id }), // Include _id only if it's not a timestamp
          Description: item.Description || "",
          optionselected: item.optionselected?.toLowerCase() || "",
          departmentId: extractIds(item.departmentId),
          designationId: extractIds(item.designationId),
          materialId: extractIds(item.materialId),
          manpowerId: extractIds(item.manpowerId),
          toolsId: extractIds(item.toolsId),
          machinaryId: [...extractIds(item.machinaryId)],
          photoRef:
            (item.photoref?.photos?.length > 0 &&
              item.photoref.photos?.map((photo: any) => ({
                photos: photo?.photo,
                Decription: photo?.details,
              }))) ||
            [],
        };
      });
    };
    const transformTaskClosing = (taskClosing: any[]) => {
      return taskClosing.map((item: any) => ({
        Description: item.Description,
        optionselected: item.optionselected?.toLowerCase() || "",
        photoRef:
          (item.photoref?.photos?.length > 0 &&
            item.photoref.photos?.map((photo: any) => ({
              photos: photo?.photo,
              Decription: photo?.details,
            }))) ||
          [],
      }));
    };

    const transformReporters = (Reporter: any[]) => {
      return Reporter.map((item: any) => {
        const isTimestampId = /^\d{13}$/.test(String(item._id));

        return {
          ...(item._id && !isTimestampId && { _id: item._id }),
          Level: item?.Level,
          designationId: Array.isArray(item?.designationId)
            ? extractIds(item.designationId)
            : item.designationId,
        };
      });
    };

    return {
      _id: task._id,
      name: task.name,
      Unit: task.Unit,
      Description: task.Description,
      Quantity: task.subtaskWeighatages,
      DepartmentId: extractIds(task.DepartmentId),
      DesignationId: extractIds(task.DesignationId),
      MaterialId: extractIds(task.MaterialId),
      ToolId: extractIds(task.ToolId),
      MachinaryId: extractIds(task.MachinaryId),
      ManpowerId: extractIds(task.ManpowerId),
      Adminid: extractIds(task.Adminid),
      Reporters: transformReporters(task.ReporterId?.Reporter || []),
      AssigneeId: extractIds(task.AssigneeId) || [],
      WorkInstructions: transformWorkInstructions(
        task.MethodId?.work_instruction_id || []
      ),
      TaskClosing: transformTaskClosing(
        task.MethodId?.task_closing_requirement || []
      ),
      ControlPlan: excludeIds(task?.MethodId?.Controlplan || []),
      Failuremode: excludeIds(task?.MethodId?.Failuremode || []),
      Tobedeleted: {
        workinstruction: task?.Tobedeleted?.workinstruction,
      },
    };
  };

  const example = useCallback(async () => {
    console.log(
      taskworkinstructiondelete,
      "check for task work instruction deelte"
    );
    if (!TaskDataRef.current) return;
    const transformedData = transformTaskData(TaskDataRef.current);
    const uniqueArray = (arr: string) => [...new Set(arr)];

    if (!finaltaskIdRef.current) {
      return;
    }

    try {
      const response = await updateTaskById({
        taskId: finaltaskIdRef.current,
        name: transformedData.name,
        Unit: transformedData.Unit,
        Description: transformedData.Description,
        Quantity: transformedData.Quantity,
        departmentId: transformedData.DepartmentId,
        designationId: transformedData.DesignationId,
        machinaryId: transformedData.MachinaryId,
        toolId: transformedData.ToolId,
        manpowerId: transformedData.ManpowerId,
        materialId: transformedData.MaterialId,
        Adminid: transformedData.Adminid,
        AssigneeId: transformedData.AssigneeId,
        Reporters: transformedData.Reporters,
        WorkInstructions: transformedData.WorkInstructions,
        TaskClosing: transformedData.TaskClosing,
        Failuremode: transformedData.Failuremode,
        ControlPlan: transformedData.ControlPlan,
        Tobedeleted: {
          workinstruction: transformedData?.Tobedeleted?.workinstruction,
        },
      }).unwrap();
    } catch (error) {
      console.error("Task update failed:", error);
    }
  }, [finalTaskId, taskworkinstructiondelete]);

  const getDatafromDb = async (idtask: string) => {
    const dbName = await initializeDatabase("TaskForm");

    const fetchedData = await window.electron.getDataById({
      dbName,
      id: idtask,
    });
    console.log("fetchedData", fetchedData);

    if (!fetchedData.length && navigator.onLine) {
      const taskformdata = (await handleGetTask(idtask)) as any;
      console.log("fetchedData2", taskformdata);
      const formattedTaskData: any = {
        id: taskformdata.data?.data?._id || "",
        _id: taskformdata.data?.data?._id || "",
        name: taskformdata.data?.data?.name || "",
        Unit: taskformdata.data?.data?.Unit || "",
        Description: taskformdata.data?.data?.Description || "",
        subtaskWeighatages: taskformdata.data?.data?.Quantity || 0,
        Tracking: taskformdata.data?.data?.isCompleted
          ? "Completed"
          : "Pending",
        DepartmentId: taskformdata.data?.data?.departmentId || [],
        DesignationId: taskformdata.data?.data?.designationId || [],
        MaterialId: taskformdata.data?.data?.materialId || [],
        ToolId: taskformdata.data?.data?.toolId || [],
        MachinaryId: taskformdata.data?.data?.machinaryId || [],
        ManpowerId: taskformdata.data?.data?.manpowerId || [],
        Adminid: taskformdata.data?.data?.Adminid,
        ReporterId: {
          Reporter: taskformdata.data?.data?.ReporterId?.Reporter || [],
        },
        AssigneeId: taskformdata.data?.data?.Assignee || [],
        Subtaskdetails: taskformdata.data?.data?.SubtaskId,
        MethodId: {
          ...taskformdata.data.data?.MethodId,
          work_instruction_id:
            taskformdata.data.data?.MethodId?.work_instruction_id?.map(
              (item: any) => ({
                photoref: {
                  photos: (item.photoRef || []).map((photo: any) => ({
                    photo: photo?.photos,
                    details: photo?.Decription || "",
                  })),
                },
                _id: item._id || "",
                Description: item.Description || "",
                optionselected: item.optionselected || "",
                materialId: item.materialId || [],
                manpowerId: item.manpowerId || [],
                toolsId: item.toolsId || [],
                machinaryId: item.machinaryId || [],
              })
            ) || [],
          task_closing_requirement:
            taskformdata?.data?.data?.MethodId?.task_closing_requirement?.map(
              (item: any) => ({
                photoref: {
                  photos: (item.photoRef || []).map((photo: any) => ({
                    photo: photo?.photos,
                    details: photo?.Decription || "",
                  })),
                },
                _id: item._id || "",
                Description: item.Description || "",
                optionselected: item.optionselected || "",
              })
            ) || [],
        },
      };

      if (idtask === formattedTaskData._id) {
        console.log("fetchedData3");
        dispatch(settaskWorkInstructionDeleteEmpty());
        dispatch(setcurrentTaskData(formattedTaskData));
      }
      saveSyncData(formattedTaskData, "time", "TaskForm", false, dispatch);
      return;
    }

    if (fetchedData.length > 1) {
      dispatch(settaskWorkInstructionDeleteEmpty());
      dispatch(resetTaskData());
    }

    console.log("fetchedData4", fetchedData);

    dispatch(settaskWorkInstructionDeleteEmpty());
    dispatch(setcurrentTaskData(fetchedData[0]));
  };

  const condition = containerRef.current
    ? containerRef.current.getBoundingClientRect().width < 1200
    : false;
  const handleGetTask = async (id: any) => {
    const response = await getTaskById(id);
    return response;
  };
  // Ensure state updates correctly on route change
  useEffect(() => {
    setFinalTaskId(taskId || null);
  }, [location.pathname, finalTaskId]); // Reacts to route changes
  useEffect(() => {
    isApiCallRef.current = taskApiCall;
  }, [taskApiCall]);

  useEffect(() => {
    if (!isEdit && taskApiCall) {
      try {
        console.log("this is getting calleddd2");
        example();
        dispatch(settaskChangeAPiFlag(false));
      } catch (error) {
        console.log("error", error);
      }
    }
  }, [isEdit, taskworkinstructiondelete]);

  console.log("isApiCallRef", isApiCallRef.current);

  // useEffect(() => {
  //   window.electron.setSyncContext("task");

  //   const handleSync = async () => {
  //     console.log(
  //       "⚡ handleSync called. isApiCallRef =  taskmasterpaiiiii",
  //       isApiCallRef.current
  //     );
  //     if (isCalledRef.current) return;
  //     isCalledRef.current = true;

  //     console.log("agyaaa");

  //     try {
  //       if (!isApiCallRef.current) {
  //         await (window as any).electron.syncComplete();
  //         return;
  //       }

  //       console.log("agyaaa2");
  //       await example();

  //       await handleUpdateStatus({
  //         status: "false",
  //         taskid: finaltaskIdRef.current!,
  //       });

  //       const taskformdata = (await handleGetTask(
  //         finaltaskIdRef?.current
  //       )) as any;

  //       const formattedTaskData: any = {
  //         id: taskformdata.data?.data?._id || "",
  //         _id: taskformdata.data?.data?._id || "",
  //         name: taskformdata.data?.data?.name || "",
  //         Unit: taskformdata.data?.data?.Unit || "",
  //         Description: taskformdata.data?.data?.Description || "",
  //         subtaskWeighatages: taskformdata.data?.data?.Quantity || 0,
  //         Tracking: taskformdata.data?.data?.isCompleted
  //           ? "Completed"
  //           : "Pending",
  //         DepartmentId: taskformdata.data?.data?.departmentId || [],
  //         DesignationId: taskformdata.data?.data?.designationId || [],
  //         MaterialId: taskformdata.data?.data?.materialId || [],
  //         ToolId: taskformdata.data?.data?.toolId || [],
  //         MachinaryId: taskformdata.data?.data?.machinaryId || [],
  //         ManpowerId: taskformdata.data?.data?.manpowerId || [],
  //         Adminid: taskformdata.data?.data?.Adminid,
  //         ReporterId: {
  //           Reporter: taskformdata.data?.data?.ReporterId?.Reporter || [],
  //         },
  //         AssigneeId: taskformdata.data?.data?.Assignee || [],
  //         Subtaskdetails: taskformdata.data?.data?.SubtaskId,
  //         MethodId: {
  //           ...taskformdata.data.data?.MethodId,
  //           work_instruction_id:
  //             taskformdata.data.data?.MethodId?.work_instruction_id?.map(
  //               (item: any) => ({
  //                 photoref: {
  //                   photos: (item.photoRef || []).map((photo: any) => ({
  //                     photo: photo?.photos,
  //                     details: photo?.Decription || "",
  //                   })),
  //                 },
  //                 _id: item._id || "",
  //                 Description: item.Description || "",
  //                 optionselected: item.optionselected || "",
  //                 materialId: item.materialId || [],
  //                 manpowerId: item.manpowerId || [],
  //                 toolsId: item.toolsId || [],
  //                 machinaryId: item.machinaryId || [],
  //               })
  //             ) || [],
  //           task_closing_requirement:
  //             taskformdata?.data?.data?.MethodId?.task_closing_requirement?.map(
  //               (item: any) => ({
  //                 photoref: {
  //                   photos: (item.photoRef || []).map((photo: any) => ({
  //                     photo: photo?.photos,
  //                     details: photo?.Decription || "",
  //                   })),
  //                 },
  //                 _id: item._id || "",
  //                 Description: item.Description || "",
  //                 optionselected: item.optionselected || "",
  //               })
  //             ) || [],
  //         },
  //       };

  //       await saveSyncData(
  //         formattedTaskData,
  //         "time",
  //         "TaskForm",
  //         false,
  //         dispatch
  //       );
  //     } catch (error) {
  //       console.error("Error during sync:", error);
  //     } finally {
  //       isCalledRef.current = false;
  //       await (window as any).electron.syncComplete(); // always notify Electron to quit
  //     }
  //   };

  //   console.log("📦 Task form useEffect mounted taskmasterpaiiiii");
  //   (window as any).electron.onTriggerTaskSync(handleSync);

  //   return () => {
  //     console.log("📦 Task form useEffect unmounted taskmasterpaiiiii");
  //     (window as any).electron.removeTriggerTaskSync(handleSync);
  //     window.electron.setSyncContext(null);
  //   };
  // }, [example, dispatch, finalTaskId, location.pathname]);

  useEffect(() => {
    handleSyncRef.current = async () => {
      if (isCalledRef.current) return;
      isCalledRef.current = true;

      try {
        if (!isApiCallRef.current) {
          await window.electron.syncComplete();
          return;
        }

        await example();

        await handleUpdateStatus({
          status: "false",
          taskid: finaltaskIdRef.current!,
        });

        const taskformdata = (await handleGetTask(
          finaltaskIdRef.current
        )) as any;

        const formattedTaskData = {
          id: taskformdata.data?.data?._id || "",
          _id: taskformdata.data?.data?._id || "",
          name: taskformdata.data?.data?.name || "",
          Unit: taskformdata.data?.data?.Unit || "",
          Description: taskformdata.data?.data?.Description || "",
          subtaskWeighatages: taskformdata.data?.data?.Quantity || 0,
          Tracking: taskformdata.data?.data?.isCompleted
            ? "Completed"
            : "Pending",
          DepartmentId: taskformdata.data?.data?.departmentId || [],
          DesignationId: taskformdata.data?.data?.designationId || [],
          MaterialId: taskformdata.data?.data?.materialId || [],
          ToolId: taskformdata.data?.data?.toolId || [],
          MachinaryId: taskformdata.data?.data?.machinaryId || [],
          ManpowerId: taskformdata.data?.data?.manpowerId || [],
          Adminid: taskformdata.data?.data?.Adminid,
          ReporterId: {
            Reporter: taskformdata.data?.data?.ReporterId?.Reporter || [],
          },
          AssigneeId: taskformdata.data?.data?.Assignee || [],
          Subtaskdetails: taskformdata.data?.data?.SubtaskId,
          MethodId: {
            ...taskformdata.data.data?.MethodId,
            work_instruction_id:
              taskformdata.data.data?.MethodId?.work_instruction_id?.map(
                (item: any) => ({
                  photoref: {
                    photos: (item.photoRef || []).map((photo: any) => ({
                      photo: photo?.photos,
                      details: photo?.Decription || "",
                    })),
                  },
                  _id: item._id || "",
                  Description: item.Description || "",
                  optionselected: item.optionselected || "",
                  materialId: item.materialId || [],
                  manpowerId: item.manpowerId || [],
                  toolsId: item.toolsId || [],
                  machinaryId: item.machinaryId || [],
                })
              ) || [],
            task_closing_requirement:
              taskformdata?.data?.data?.MethodId?.task_closing_requirement?.map(
                (item: any) => ({
                  photoref: {
                    photos: (item.photoRef || []).map((photo: any) => ({
                      photo: photo?.photos,
                      details: photo?.Decription || "",
                    })),
                  },
                  _id: item._id || "",
                  Description: item.Description || "",
                  optionselected: item.optionselected || "",
                })
              ) || [],
          },
        };

        await saveSyncData(
          formattedTaskData,
          "time",
          "TaskForm",
          false,
          dispatch
        );
      } catch (err) {
        console.error("Sync error:", err);
      } finally {
        isCalledRef.current = false;
        await window.electron.syncComplete();
      }
    };
  }, [example, dispatch, location.pathname]);

  useEffect(() => {
    window.electron.setSyncContext("task");
    const stableHandler = () => {
      handleSyncRef.current?.();
    };

    window.electron.removeAllTaskSyncListeners();
    window.electron.onTriggerTaskSync(stableHandler);

    return () => {
      window.electron.removeTriggerTaskSync(stableHandler);
      window.electron.setSyncContext(null);
    };
  }, []);

  useEffect(() => {
    const updateWidth = () => {
      const containerWidth =
        containerRef.current?.getBoundingClientRect().width;
      setWidth(containerWidth || 0);
      // console.log("check for container width", containerWidth);
    };
    updateWidth();
    window.addEventListener("resize", updateWidth);
    return () => {
      window.removeEventListener("resize", updateWidth);
    };
  }, [condition]);

  useEffect(() => {
    if (finalTaskId!) {
      getDatafromDb(finalTaskId);
      finaltaskIdRef.current = finalTaskId!;
    }
  }, [finalTaskId]);

  useEffect(() => {
    if (taskApiCall) {
      try {
        console.log("this is getting calleddd");
        example();
        dispatch(settaskChangeAPiFlag(false));
      } catch (error) {
        console.log("error", error);
      }
    }
  }, [currentSubTaskRoute]);

  useEffect(() => {
    TaskDataRef.current = TaskData;
  }, [TaskData]);

  console.log("cleanup", isApiCallRef.current, taskApiCall);
  useEffect(() => {
    return () => {
      // console.log("cleanup",isApiCallRef.current,taskApiCall);
      if (isApiCallRef.current) {
        console.log("this is getting calleddd2");
        example();
        dispatch(settaskChangeAPiFlag(false));
      }
    };
  }, []);

  // Ensure state updates correctly on route change
  useEffect(() => {
    setFinalTaskId(taskId || null);
  }, [location.pathname, finalTaskId]); // Reacts to route changes

  useEffect(() => {
    if (finalTaskId!) {
      getDatafromDb(finalTaskId);
      finaltaskIdRef.current = finalTaskId!;
    }
  }, [finalTaskId]);

  console.log(" TaskDataRef", TaskDataRef.current);

  return (
    <>
      <TaskHeader width={width} />
      <div ref={containerRef} className={styles.subtask_outer_contaienr}>
        <div className={styles.subtask_sidebar_contaienr}>
          <TaskSidebar />
        </div>
        <Outlet />
      </div>
      <div className={styles.task_version_history_popup}>
        {currentOpenPopup["versionHistory"] && <VersionHistory />}
      </div>
    </>
  );
};

export default TaskCreation;
