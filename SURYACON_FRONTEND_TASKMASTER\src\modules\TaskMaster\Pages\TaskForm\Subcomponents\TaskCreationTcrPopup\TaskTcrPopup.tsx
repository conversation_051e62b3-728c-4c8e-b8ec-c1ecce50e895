import { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { closePopup } from "../../../../../../redux/features/Modules/Reusble/popupSlice";
import {
  CheckBox,
  DeleteIcon,
  ImageIcon,
} from "../../../../../../assets/icons";
import TCRpopup from "../../../../../../components/Reusble/Global/TCRpopup";
import { PhotoSection } from "../../../../../../components/Reusble/TaskMaster/WorkInstructionsPopup";
import { RootState } from "../../../../../../redux/store";
import styles from "../../Styles/TaskCreationForm.module.css";
import {
  TaskTcrPopupProps,
  TcrData,
} from "../../../../../../interfaces/Modules/TaskMaster/TaskMasterInterface/TaskMaster";
import { getFileName, isBase64 } from "../../../../../../functions/functions";

const TaskTcrPopup: React.FC<TaskTcrPopupProps> = ({
  data,
  OnUpdateTcrData,
  popupId,
  isEdit = false,
  handleDelete,
  onClick,
  initaldata,
}) => {
  if (!data) return null;
  console.log(data, "this is tcr data");
  const dispatch = useDispatch();
  const { popups } = useSelector((state: RootState) => state.popup);
  const [isPhotoCheckboxPage, setIsPhotoCheckboxPage] = useState(false);

  const getPopupId = (category: string) => `${popupId}-${category}`;

  const handlePhotoPopupClose = () => {
    setIsPhotoCheckboxPage(false);
    dispatch(closePopup(getPopupId("Tcrphoto")));
  };

  const handlePhotoPopupSubmit = (newData: TcrData) => {
    if (data && OnUpdateTcrData && !initaldata) {
      const updatedData = {
        ...data,
        photoDetails: [
          ...(data.photoDetails || []),
          ...(newData.photoDetails || []),
        ],
      };
      OnUpdateTcrData(updatedData);
    } else {
      if (OnUpdateTcrData) {
        OnUpdateTcrData(newData);
      }
    }
    handlePhotoPopupClose();
  };

  return (
    <div
      onClick={(e) => (isEdit ? onClick(e) : () => {})}
      className={styles.subtask_method_popup_container}
      style={{ position: "relative" }}
    >
      <div className={styles.subtask_method_popup_header}>
        <div className={styles.tcr_desc_details}>
          <div className={styles.tcr_tooltip}>
            <h4>{data.description}</h4>
          </div>
          {data.file && (
            <div className={styles.tcr_photoname}>
              <p className="small_text_p_400">{data.file.name}</p>
            </div>
          )}
        </div>

        {/* Photo sections */}
        {data.category === "photo" && data.photoDetails && (
          <>
            {data.photoDetails.map((photo, index) => (
              <div key={index} className={styles.tcr_tooltip_photo_container}>
                <div className={styles.tcr_tooltip_photo}>
                  <ImageIcon />{" "}
                  <h4>
                    {isBase64(photo?.photo)
                      ? photo?.fileName
                      : getFileName(photo?.photo)}
                  </h4>
                </div>
              </div>
            ))}
          </>
        )}

        {/* Checkbox section */}
        {data.category === "checkbox" && (
          <div className={styles.tcr_tooltip_photo}>
            <CheckBox /> <h4>Checkbox</h4>
          </div>
        )}
      </div>

      {/* Photo popup */}
      {popups[getPopupId("Tcrphoto")] && (
        <TCRpopup
          onCancel={handlePhotoPopupClose}
          onSubmit={handlePhotoPopupSubmit}
          initialData={initaldata}
          startWithPhotoCheckboxPage={isPhotoCheckboxPage}
          isEdit={!!initaldata}
        />
      )}

      {isEdit && handleDelete && (
        <div
          className={styles.delete_icon_tooltip}
          onClick={(e) => {
            e.stopPropagation();
            handleDelete();
          }}
        >
          <DeleteIcon />
        </div>
      )}
    </div>
  );
};

export default TaskTcrPopup;
