.radiobtn_wrapper {
  border-radius: 1.5rem;
  margin-top: 1.25rem;
  border: 1px solid var(--text-black-28);
  padding: 0.5rem;
  padding: 1rem 1.5rem;
}

.radiobtn_wrapper.error {
  border-color: var(--warning_color);
}

.radio_btn_label {
  color: var(--text-black-60);
}

.datafield_field_section {
  padding: 1.25rem 0;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

/* AddFormWrapper styles */

.addForm_box {
  max-width: 588px;
  width: 100%;
  border-radius: 2.6rem;
  background: transparent;
  box-shadow: var(--extra-shadow-five);
  backdrop-filter: blur(150px);
  display: flex;
  flex-direction: column;
  position: fixed;
  bottom: 1.5rem;
  right: 1.5rem;
  height: calc(100% - 8.5rem);
  z-index: 10;
  animation: slideIn 0.5s ease-in-out;
}

.addForm_box.isClosing {
  animation: slideOut 0.5s ease-in-out;
}

.addForm_box_open {
  transform: translateX(0);
}

.addForm_overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9;
  background-color: rgba(0, 0, 0, 0);
  /* or your overlay styling */
  display: flex;
  justify-content: center;
  align-items: center;
}

.addForm_head {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  height: 3.2rem;
  padding: 2rem 1.5rem 0rem 1.5rem;
}

.addForm_head_content {
  margin: 0;
  padding: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--primary_color);
  text-align: center;
}

.addForm_head_content.addForm_head_content_discard,
.addForm_head_content_deleted {
  color: var(--warning_color);
}

.addForm_head_content.addForm_head_content_edit,
.addForm_head_content_updated {
  color: var(--secondary_color);
}

.addForm_head_button {
  border: none;
  cursor: pointer;
  background-color: transparent;
  position: absolute;
  top: 2.2rem;
  right: 1.5rem;
}

.addForm_content {
  height: calc(100% - 11.5rem);
  height: calc(100% - 11rem);
  padding: 0px 1.5rem 0px 1.5rem;
  margin-top: 2rem;
  margin-bottom: 1.5rem;
  overflow: auto;
  scrollbar-width: none;
  scroll-behavior: smooth;
}

.addForm_footer {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1.5rem;
}

.department_content::-webkit-scrollbar {
  width: 4px;
}

/* Style for the scrollbar track (the background area) */
.department_content::-webkit-scrollbar-track {
  background-color: transparent;
  border-radius: 10px;
}

/* Style for the scrollbar thumb (the draggable part) */
.department_content::-webkit-scrollbar-thumb {
  background-color: var(--primary_color);
  border-radius: 10px;
}

/*AddForm Wrapper animation */

@keyframes slideIn {
  from {
    transform: translate(100%, 0%);
  }

  to {
    transform: translate(0%, 0%);
  }
}

@keyframes slideOut {
  from {
    transform: translate(0%, 0%);
  }

  to {
    transform: translate(100%, 0%);
  }
}

/* Form Child Template  styles*/

.formChildContainer {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  width: 100%;
  font-family: "Nunito", sans-serif;
}

.formChildItem {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 1.625rem;
  padding: 1rem;
  border-radius: 0.75rem;
  background-color: #ffffff99;
}

.formValue {
  font-size: 1rem;
  font-weight: 400;
  color: #000000de;
}

.updated {
  color: var(--secondary_color);
}

.formLabel {
  font-size: 0.875rem;
  font-weight: 500;
  color: #00000099;
}

.discard {
  color: var(--warning_color);
}

.formValueList {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 1.5rem;
}

/* Department Form Styles */

/*Designation Form Styles from here*/
.input_tag {
  max-width: 46px;
  padding: 0.25rem 0.5rem;
  background-color: var(--primary_background);
  border-radius: 50px;
  text-align: center;
  position: absolute;
  right: 0rem;
  z-index: 2;
  top: 45%;
  transform: translate(-50%, -50%);
  color: var(--primary_color);
}

/*Department page css from here*/
.department_content {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-auto-rows: max-content;
  gap: 1rem;
  margin-top: 2rem;
  padding: 1.5rem 1rem;
  padding-top: 0;
  /* background-color: red; */
  /* min-height: calc(100vh - 14rem); need to change */
  height: 100vh;
  max-height: calc(100vh - 14rem);
  overflow-y: auto;
  position: relative;
  scroll-behavior: smooth;
  z-index: 0;
  overflow-x: hidden;
}

.designation_content::-webkit-scrollbar {
  width: 4px;
}

/* Style for the scrollbar track (the background area) */
.designation_content::-webkit-scrollbar-track {
  background-color: transparent;
  border-radius: 10px;
}

/* Style for the scrollbar thumb (the draggable part) */
.designation_content::-webkit-scrollbar-thumb {
  background-color: var(--primary_color);
  border-radius: 10px;
}

@media screen and (max-width: 1536px) {
  .department_content {
    grid-template-columns: repeat(3, 1fr);
    overflow-x: hidden;
  }
}
@media screen and (max-width: 1200px) {
  .department_content {
    grid-template-columns: repeat(4, 1fr);
    overflow-y: auto;
    width: fit-content;
  }
}

/*Designation page css from here*/
.designation_content {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-auto-rows: max-content;
  gap: 1rem;
  margin-top: 2rem;
  padding: 1.5rem 1rem;
  padding-top: 0;
  /* background-color: red; */
  /* min-height: calc(100vh - 14rem); need to change */
  height: 100vh;
  max-height: calc(100vh - 14rem);
  overflow-y: auto;
  position: relative;
  scroll-behavior: smooth;
  z-index: 0;
  overflow-x: hidden;
}

.designation_content::-webkit-scrollbar {
  width: 4px;
}

/* Style for the scrollbar track (the background area) */
.designation_content::-webkit-scrollbar-track {
  background-color: transparent;
  border-radius: 10px;
}

/* Style for the scrollbar thumb (the draggable part) */
.designation_content::-webkit-scrollbar-thumb {
  background-color: var(--primary_color);
  border-radius: 10px;
}
@media screen and (max-width: 1536px) {
  .designation_content {
    grid-template-columns: repeat(3, 1fr);
    overflow-x: hidden;
  }
}
@media screen and (max-width: 1200px) {
  .designation_content {
    grid-template-columns: repeat(4, 1fr);
    overflow-y: auto;
    width: fit-content;
  }
}

/* Loader Styles */

.loader_loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.loader_loading_image {
  width: 500px;
  height: 500px;
}

/* Md Approval and Declined Form Style */

.department_container {
  position: fixed;
  top: 7rem;
  right: 1.5rem;
  transform: translate(0%, 0%);
  padding-inline: 24px;
  padding-top: 32px;
  padding-bottom: 24px;
  z-index: 20;
  width: 36.75rem;
  animation: department_slideIn 0.5s ease-out;
  border-radius: 2.6rem;
  border: 1px solid;
  border-image-source: linear-gradient(
    130.72deg,
    rgba(237, 231, 231, 0.07) -22.43%,
    rgba(251, 251, 251, 0.05) 75.66%
  );
  backdrop-filter: blur(150px);
  box-shadow: 0 0.25rem 2.5rem 0 rgba(0, 0, 0, 0.5);
  height: calc(100% - 8.5rem);
}

@keyframes department_slideIn {
  from {
    transform: translate(100%, 0%);
  }
  to {
    transform: translate(0%, 0%);
  }
}

@keyframes department_slideOut {
  from {
    transform: translate(0%, 0%);
  }
  to {
    transform: translate(100%, 0%);
  }
}

.departmentApprovalForm_overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9;
  background-color: transparent;
  display: flex;
  justify-content: center;
  align-items: center;
}

.department_container.closing {
  animation: department_slideOut 0.5s ease-out;
}

@media only screen and (max-width: 1280px) {
  .department_container {
    width: 32rem;
  }
}

.department_closeButton {
  position: absolute;
  top: 2rem;
  right: 0.625rem;
  padding: 1rem;
  background: transparent;
  border: none;
  cursor: pointer;
}

.department_header {
  color: var(--primary_color);
  display: flex;
  justify-content: center;
  align-items: center;
  height: 3rem;
  margin-bottom: 24px;
}

.department_title {
  font-size: 21px;
  font-weight: 600;
  font-family: "Nunito";
  text-align: center;
  padding-inline: 40px;
  line-height: 24px;
}

.department_header_decline {
  color: #a80000;
}

.department_datainputs {
  height: calc(100% - 8.5rem);
  overflow-y: scroll;
  margin-bottom: 24px;
}

.department_btngrp {
  display: flex;
  justify-content: center;
  gap: 1rem;
}

.department_horizaontalData {
  display: flex;
  flex-direction: row;
  column-gap: 24px;
  flex-wrap: wrap;
  font-family: "Nunito";
  font-size: 16px;
  font-weight: 400;
  color: var(--text-black-87);
}

.department_summaryDataContent p {
  font-size: 14px;
  font-family: "Nunito";
  color: var(--text-black-60);
  font-weight: 500;
}

.department_summaryDataContent {
  display: flex;
  flex-direction: column;
  border-radius: 0.75rem;
  background-color: var(--main_background);
  width: 100%;
  min-height: 77px;
  padding: 1rem;
  white-space: normal;
  margin-bottom: 12px;
  text-align: left;
  gap: 4px;
}

.department_reasonInput::placeholder {
  color: rgba(0, 0, 0, 0.6);
  font-weight: 400;
  font-size: 1rem;
  font-family: "Nunito";
}

.department_reasonInput {
  width: 100%;
  min-height: 52px;
  padding: 0.75rem 1rem;
  border-radius: 3.125rem;
  border: 1px solid rgba(0, 0, 0, 0.28);
  font-size: 1rem;
  outline: none;
  margin-bottom: 12px;
}

.department_line_container {
  width: 100%;
  height: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 5px;
  margin-top: 4px;
  margin-bottom: 24px;
}

.department_dottedline_wrapper {
  width: 49%;
  border-bottom: 1px dashed rgba(176, 204, 209, 1);
}
