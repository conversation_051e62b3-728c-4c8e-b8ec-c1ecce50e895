// interface for categorySlice.ts

export interface FormState {
  selectedCategories: Record<string, string[]>;
  formData: Record<string, string>;
  popupOpen: boolean;
  currentField: FieldType | null;
  numValue: number;
}
//   interface for TaskMasterSlice.ts

export interface SubtaskRoute {
  id?: string;
  name: string;
  Description: string;
  Unit: string;
  subtaskWeighatages: number;
  Tracking: string;
}

export interface Reporter {
  _id?: string;
  Level: number;
  designationId?: { _id?: string; itemtype?: string; name: string }[];
}

export interface currentSubtaskData {
  _id: string;
  name: string;
  Unit: string;
  Description: string;
  subtaskWeighatages: number;
  Tracking: string;
  MaterialId: requiredthings[];
  ToolId: requiredthings[];
  MachinaryId: requiredthings[];
  ManpowerId: requiredthings[];
  Adminid: requiredthings[];
  AssigneeId: requiredthings[];
  TaskmasterId: {};
  ReporterId?: {
    _id?: string;
    Reporter: Reporter[];
  };
  DepartmentId?: requiredthings[];
  DesignationId?: requiredthings[];
  Subtaskdetails?: SubtaskRoute[];
  AutoId?: {
    TriggerAction: {
      ActionName: {
        id: string | number;
        name: string;
      } | null;
      ActionTime: number | string;
    };
    TriggerResponse: {
      _id: string | number;
      name: string;
      isFirst?: boolean;
    };
    ResponseTime: number | string;
  };
  MethodId?: {
    work_instruction_id: {
      photoref: {
        photos: {
          id: string;
          photo: string;
          fileName: string;
          details: string;
        }[];
      };
      file: { name: string; type: string } | null;
      _id: string;
      Description: string;
      optionselected: string;
      materialId: requiredthings[];
      manpowerId: requiredthings[];
      toolsId: requiredthings[];
      machinaryId: requiredthings[];
      __v?: number;
    }[];
    task_closing_requirement: {
      photoref: {
        photos: {
          photo: string;
          fileName: string;
          details: string;
        }[];
      };
      file: { name: string; type: string } | null;
      _id: string;
      Description: string;
      optionselected: string;
    }[];
    Controlplan: controlPlan[];
    Failuremode?: failureMode[];
  };
}

export interface TaskMasterState {
  isApiCall?: boolean;
  navigateToTask?: boolean;
  navigateToTaskView?: boolean;
  searchKey?: string;
  typeSearchKey?: string;
  istaskApiCall?: boolean;
  isChange?: boolean;
  SubTasksRoutes: SubtaskRoute[];
  currentSubTaskRoute: string;
  currentSubtaskData: currentSubtaskData | null;
  addCategorySearchKey: string;
  addCategoryFormData: any;
}
// interface for triggereventslice.ts
export interface TriggerAction {
  id: string | number;
  value: string;
}

export interface TriggerResponse {
  id: string | number;
  label: string;
}

export interface UpdateTriggerDataPayload {
  triggerResponseTime?: string | number;
  triggerTimeInterval?: string | number;
  triggerResponse: TriggerResponse;
  triggerAction: TriggerAction;
}
export interface TEModifiedField<T> {
  value: T;
  className: string;
}

export interface TEModifiedDataProps {
  AddTETriggerResponse: TEModifiedField<{ id: string; label: string } | null>;
  AddTEaction: TEModifiedField<{
    id: string;
    value: string;
    boolean: boolean;
  } | null>;
  AddTEStartAfterTimeInterval: TEModifiedField<number | string | null>;
  AddTEResponseTime: TEModifiedField<number | string | null>;
}
export interface ChangedTriggeredDataType {
  AddTETriggerResponse: { id: string | number; label: string } | null;
  AddTEaction: { id: string | number; value: string } | null;
  AddTEStartAfterTimeInterval: number | string | null;
  AddTEResponseTime: number | string | null;
}
export interface ChangedEditTriggeredDataType {
  EditTETriggerResponse: { id: string | number; label: string } | null;
  EditTEaction: { id: string | number; value: string } | null;
  EditTEStartAfterTimeInterval: number | string | null;
  EditTEResponseTime: number | string | null;
}

export interface AddTriggerEvent {
  //   id: string; 🍁 have to ask if we need to put id of the triggerEvent key
  //   name: string;
  triggerMode: string | null;
  triggerStructureData: {
    action: { id: number | string; name: string }[];
  };
  isOnlyResponse?: boolean;
  allsubroutes: [
    {
      _id?: string | number;
      name: string;
      isFirst?: boolean;
    }
  ];
  IsActive: boolean;
  title: string;
  triggerFormData: {
    TriggerAction: {
      ActionName: {
        id: string | number;
        name: string;
      } | null;
      ActionTime: number | string;
    };
    TriggerResponse: {
      _id: string | number;
      name: string;
      isFirst?: boolean;
    } | null;
    ResponseTime: number | string;
  };
}
// interface for workinstructionslice.ts
interface WorkInstructionInterfaceName {
  requiredThingsDeleteName: any;
  workinstruction: string[];
  currentPopupId: any;
}
// interface for workinstructionslice.ts
export interface taskWorkInstructionInterfaceName {
  taskrequiredThingsDeleteName: string;
  taskworkinstruction: string[];
}
