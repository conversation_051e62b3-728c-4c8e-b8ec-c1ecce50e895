.card_wrap {
  width: 100%;
  min-height: 12.75rem;
  height: max-content;
  position: relative;
  border-radius: 1.75rem;
  min-width: 22rem;
  border: 1px solid;
  border-color: transparent;
  margin-top: 0.15rem;
  /* box-shadow: 0px 0px 3px 0px #91a1a180; */
  /* box-shadow: var(--extra-shdow-eight) */
  /* z-index: 0; */
}

.card_wrap.faded_shadow {
  box-shadow: 0px 0px 3px 0px #91a1a180;
}

.card_wrap.expannded {
  /* z-index: 1; */
}

.card_wrap.updated {
  border: 1px solid var(--secondary_color);
}

.card_wrap.declined {
  border: 1px solid var(--warning_color);
}

/* 
----------------------------------------------------------------<PERSON><PERSON><PERSON><PERSON> Code --------------------------------------------------------------------- */
/* Extra styles if the user is MD */
.mdDepartmentHoverEffect:hover {
  border: 1px solid #00596a;
}

.mdDepartmentActionBtnWrapper {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease;
  z-index: 8;
}

/* Show the button only when MD hovers over the card */
.mdDepartmentHoverEffect:hover .mdDepartmentActionBtnWrapper {
  opacity: 1;
  pointer-events: auto;
}

.deleted_label {
  width: 14px;
  height: 14px;
  background: #d32f2f;
  border-radius: 50%;
  position: absolute;
  right: 2%;
  top: 0.6rem;
  transform: translateX(-50%);
  z-index: 10;
  box-shadow: var(--extra-shdow-eight);
  border: 2px solid #fff;
}

/* 
----------------------------------------------------------------Maheshwar Code --------------------------------------------------------------------- */

.card_container {
  display: flex;
  flex-direction: column;
  min-width: 20rem;
  /* max-width: 320px; */
  width: 100%;
  min-height: 10.625rem;
  height: auto;
  background-color: #ffffff;
  border-radius: 1.75rem;
  overflow: visible;
  font-family: "Nunito", sans-serif;
  position: absolute;
  top: 0;
  left: 0;
  transition: all 0.5s ease-in-out;
  box-shadow: 0px 0px 3px 0px #91a1a180;
}

.button_container {
  display: flex;
  gap: 1rem;
  position: absolute;
  z-index: 5;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.3s ease-in-out;
  opacity: 0;
  pointer-events: none;
  visibility: hidden;
}

.button_container.visible {
  opacity: 1;
  visibility: visible;
  pointer-events: visible;
}

.faded_content {
  width: 100%;
  height: 100%;
  filter: grayscale(80%);
  opacity: 0.5;
  pointer-events: none;
}

.faded > *:not(.centerButton) {
  pointer-events: none;
}

.faded:hover .centerButton {
  opacity: 1;
  pointer-events: auto;
}

.card_header {
  width: 100%;
  min-height: 3.3125rem;
  padding: 1rem 1rem 0.5rem 1rem;
  background-color: #f0f6f6;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  border-top-left-radius: 1.75rem;
  border-top-right-radius: 1.75rem;
}

.card_header h5 {
  margin: 0;
  padding: 0;
  font-size: 1.3125rem;
  font-weight: 600;
  line-height: 1.8125rem;
  /* width: 75%; */
  max-width: calc(100% - 5.5rem);
  overflow: hidden;
  text-overflow: ellipsis;
  text-wrap: nowrap;
}

.header_right_ctrl_box {
  display: flex;
  max-height: 1.6875rem;
  gap: 0.75rem;
  flex-direction: row;
  align-items: center;
}

.header_right_ctrl_box p {
  min-width: 78px;
  min-height: 27px;
  background-color: #ffffffde;
  border-radius: 100px;
  padding: 0.25rem 0.75rem;
  font-size: 0.875rem;
  text-align: center;
  font-weight: 500;
  color: #000000de;
}

.header_right_ctrl_box span {
  cursor: pointer;
  height: 1.6875rem;
}

.card_info {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  gap: 10px;
  background-color: transparent;
}

.card_info div {
  min-width: 6rem;
  width: 50%;
  gap: 1rem;
  max-height: 2rem;
  display: flex;
  /* justify-content: space-between; */
  align-items: center;
  border: 0.5px solid #005968;
  border-radius: 50px;
  padding: 2px 8px 2px 2px;
  overflow: hidden;
}

.icon_box {
  width: 1.75rem;
  min-width: 1.75rem;
  height: 1.75rem;
  background-color: #f0f6f6;
  border-radius: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.card_info_label {
  font-size: 0.75rem;
  font-weight: 600;
  line-height: 1rem;
  white-space: nowrap;
  width: calc(100% - 2.25rem);
  overflow: hidden;
  text-overflow: ellipsis;
}

.card_desgination_section {
  min-width: 18rem;
  height: auto;
  margin: 1rem;
  margin-top: 0;
  background-color: #ffffffde;
  box-shadow: 0px 0px 3px 0px #91a1a180;
  border-radius: 1rem;
  padding-left: 0.75rem;
  padding-right: 1rem;
}

.card_desgination_section.visible {
  padding-bottom: 0.75rem;
}

.card_designation_info {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 1rem;
  min-height: 3.25rem;
  padding: 0.75rem 0rem;
}

.card_des_icon_box {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 3.25rem;
  height: 3.25rem;
  background-color: #f0f6f6;
  border-radius: 0.75rem;
}

.card_designation_content {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  gap: 5px;
  min-height: 2.625rem;
}

.card_designation_content p {
  font-size: 0.875rem;
  font-weight: 500;
  color: #00000099;
  align-items: center;
}

.card_designation_content > p + p {
  font-size: 1rem;
  font-weight: 600;
  color: #000000de;
  line-height: 1.125rem;
}

.designations_role_box {
  opacity: 0;
  transition: max-height 0.3s ease, opacity 0.3s ease;
  display: none;
  align-items: center;
  flex-wrap: wrap;
  gap: 6px;
  scroll-behavior: smooth;
  /* padding-bottom: 0.5rem; */
}

.designations_role_box::-webkit-scrollbar {
  width: 6px;
}

.designations_role_box::-webkit-scrollbar-thumb {
  background: var(--primary_color); 
  border-radius: 20px;
}

.designations_role_box.visible::-webkit-scrollbar-track {
  background: transparent; 
}



.desination_role_label {
  background-color: #005968;
  border-radius: 100px;
  padding: 0.25rem 0.75rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: #ffffff;
  text-align: center;
}