/* Add these styles to your existing CSS file */

.page_container {
  display: flex;
  flex-direction: column;
  /* height: 100vh; */
}

.header_container {
  display: flex;
  margin-top: 1rem !important;
  justify-content: space-between;
  align-items: center;
  height: 2.6rem;
  padding-left: 1rem;
  padding-right: 0.5rem !important;
}

.navigation_container {
  display: flex;
}

.content_wrapper {
  display: flex;
  padding: 1.5rem 0rem 1rem 1rem;
}

.main_content::-webkit-scrollbar {
  width: 4px;
}

.main_content::-webkit-scrollbar-track {
  background-color: transparent;  
  border-radius: 10px;
}

.main_content::-webkit-scrollbar-thumb {
  background-color: var(--primary_color);
  border-radius: 10px;
}


.sidebar_container {
  width: 210px;
  padding-top: 0.5rem;
  /* overflow-y: auto; */
}

.main_content {
  flex: 1;
      padding: 0.5rem 0rem 0rem 0.5rem;
  height: 80vh;
  overflow-y: auto;
  /* margin-left: 1rem; */
  display: flex;
}

@media screen and (max-width: 1200px) {
  .main_content {
    width: 1200px;
    /* overflow-x: scroll; */
  }
}

/* Update the media query */
@media screen and (max-width: 1200px) {
  .header_container {
    width: 100%;
    width: 1780px;
  }
  .content_wrapper {
    overflow-x: scroll;
    width: 1780px;
  }
}
