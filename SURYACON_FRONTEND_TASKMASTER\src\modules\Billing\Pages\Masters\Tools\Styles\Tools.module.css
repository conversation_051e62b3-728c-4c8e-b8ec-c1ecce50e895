.loader_loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.loader_loading_image {
  width: 500px;
  height: 500px;
}

.addtoolsform_container {
  position: fixed;
  top: 7rem;
  right: 1.5rem;
  /* transform: translate(0%, 0%); */
  padding: 1.25rem 0.9rem;
  z-index: 89;
  width: 34.5rem;
  max-width: 90%;
  height: calc(100% - 8.5rem);
  animation: slideIn 0.5s ease-out;
  backdrop-filter: blur(100px);
  border-radius: 2.6rem;
  border: 1px solid;
  border-image-source: linear-gradient(
    130.72deg,
    rgba(237, 231, 231, 0.07) -22.43%,
    rgba(251, 251, 251, 0.05) 75.66%
  );
  backdrop-filter: blur(150px);
  box-shadow: 0px 4px 40px 0px #00000080;
  display: flex;
  flex-direction: column;
}

.addtoolsform_datainputs_wrapper{
  padding: 0.6rem;
}
.copy_button {
  padding: 0.5rem 1rem; /* 8px 16px */
  border-radius: 6.25rem; /* 100px */
  border: 0.0625rem solid #ede7e7; /* 1px */
  color: #4b4b4b;
  font-weight: 500;
  width: 6.0625rem; /* 97px */
  height: 2.125rem; /* 34px */
  display: flex;
  align-items: center;
  justify-content: center;

  /* Drop shadow */
  box-shadow: 0px 0px 4px 0px #91a1a180;

  /* Background blur */
  backdrop-filter: blur(2.5rem); /* 40px */

  cursor: pointer;
  transition: all 0.5s ease-in-out;
}

.inner_cardview {
  display: grid;
  gap: 1.5rem;
  grid-template-columns: repeat(4, 1fr);
  padding-left: 0.5rem;
  padding-right: 1.5rem;
  padding-top: 0.5rem;
}

.inner_cardview2 {
  display: grid;
  gap: 1.5rem;
  grid-template-columns: repeat(3, 1fr);
  width: 100%;
}

.inner_cardview2 {
  display: grid;
  gap: 2rem;
  grid-template-columns: repeat(3, 1fr);
}

@media screen and (max-width: 1536px) {
  .inner_cardview {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media screen and (max-width: 1280px) {
  .inner_cardview {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media screen and (max-width: 1200px) {
  .inner_cardview {
    grid-template-columns: repeat(3, 1fr);
    width: fit-content;

  } 
  .cardview {
    width: fit-content;
  }
  .main_content_wrapper{
    width: fit-content;
    min-width: calc(100% + 10rem);

  }
}

.delete_icon_tooltip {
  position: absolute;
  top: -0.5rem;
  right: 0rem;
  cursor: pointer;
  height: 24px;
  width: 24px;
  background-color: var(--secondary_warning_background);
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 100%;
}

.tcr_fileNames_div {
  /* margin-left: 1rem; */
  display: flex;
  flex-wrap: wrap;
  width: 85%;
}

.tcr_fileNames {
  background-color: var(--primary_background);
  color: var(--text-black-87);
  padding: 0.5rem 0;
  border-radius: 100px;
  position: relative;
}

.summaryDivData {
  display: flex;
  align-items: center;
}

.summaryDataContent {
  display: flex;
  flex-direction: column;
  background: #ffffff99;
  border-radius: 0.75rem;
  /* width: 30.8rem; */
  width: 100%;
  /* max-width: 28.5rem; */
  min-height: 3.188rem;
  padding: 1rem;
  white-space: normal;
  margin: 0.6rem 0.6rem 0.6rem 0.6rem;
  gap: 0.2rem;
  line-height: 1.363rem;
  text-align: left;
}

.tcr_fileNames:hover .file_cross_div {
  display: block !important;
}

.photo_input_wrapper {
  position: relative;
  height: 100%;
  width: 100%; 
}

.cover_photo {
  display: flex;
  border: 1px solid var(--text-black-28);
  width: 100%;
  min-height: 1.5rem;
  align-items: center;
  justify-content: space-between;
  height: 53px;
  padding: 0.9rem 1.4rem;
  position: relative;
  border-radius: 1.5rem;
  overflow: hidden;
}

.tcrpopup_header_attachmentbtn {
  /* border: 1px solid;
    border-image-source: linear-gradient(
      130.72deg,
      rgba(237, 231, 231, 0.07) -16.06%,
      rgba(251, 251, 251, 0.05) 82.03%
    );
    box-shadow: 0px 0px 4px 0px #00000066;
    height: 38px;
    width: 38px;
    border-radius: 50%; */
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.cardview::-webkit-scrollbar {
  width: 4px;
  /* Width of the scrollbar */
}

/* Style for the scrollbar track (the background area) */
.cardview::-webkit-scrollbar-track {
  background-color: transparent;
  /* Light grey track */
  border-radius: 10px;
}

/* Style for the scrollbar thumb (the draggable part) */
.cardview::-webkit-scrollbar-thumb {
  background-color: var(--primary_color);
  /* Darker thumb */
  border-radius: 10px;
}

@keyframes slideIn {
  from {
    transform: translate(100%, 0%);
  }

  to {
    transform: translate(0%, 0%);
  }
}

@keyframes slideOut {
  from {
    transform: translate(0%, 0%);
  }

  to {
    transform: translate(100%, 0%);
  }
}

.addtoolsform_container.closing {
  animation: slideOut 0.5s ease-out;
}

.closeButton {
  position: absolute;
  top: 1.5em;
  right: 0.625rem;
  padding: 1rem;
  background: transparent;
  border: none;
  cursor: pointer;
}

.addtoolsform_header {
  color: var(--primary_color);
  display: flex;
  justify-content: center;
  padding: 0.6rem;
}

.addtoolsform_header_text{

  text-align: center;
  max-width: 89%;

}

.cardview {
  height: 80dvh;
  padding-bottom: 2rem;
  overflow: scroll !important;
 
}

.addtoolsform_datainputs {
  flex: 1;
  /* Take up remaining space inside the container */
  /* padding: 0.45rem; */
  overflow-y: auto;
  height: calc(100% - 6.75rem);
  scrollbar-width: none;
  /* Enable vertical scrolling */
}

.addtoolsform_btngroup {
  width: 100%;
  padding: 1.5rem 0 1.5rem 0;
  margin-bottom: 0.25rem;
  display: flex;
  justify-content: center;
  gap: 1rem;
  border-radius: 2.6rem;
  backdrop-filter: blur(60px);
  padding-bottom: 0;
}

.addtoolsform_datainputs_flexrow {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.addtoolsform_brandheader {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.addtoolsform_brandsection {
  border: 1px solid var(--text-black-28);
  min-height: 190px;
  border-radius: 24px;
  margin-block: 1rem;
}

.addtoolsform_brandsectioninputs {
  position: relative;
  padding: 1rem;
}

.addBrandsectionCategoryIcon {
  cursor: pointer;
}

/* .delete_icon_tooltip {
  opacity: 0;
  visibility: hidden;
}

.addtoolsform_brandsectioninputs:hover .delete_icon_tooltip {
  opacity: 1;
  visibility: visible;
} */
.progress_bar_container {
  width: 100%;
  height: 4px;
  background-color: var(--primary_color);
  overflow: hidden;
  position: absolute;
  inset-block-end: 0px;
  inset-inline-start: 5px;
}

.progress_bar {
  height: 100%;
  width: 60%;
  background-color: #D9D9D9;
  position: absolute;
  animation: progressAnimation 1.5s infinite;
}

@keyframes progressAnimation {
  0% {
    left: -30%;
  }

  50% {
    left: 50%;
  }

  100% {
    left: 100%;
  }
}