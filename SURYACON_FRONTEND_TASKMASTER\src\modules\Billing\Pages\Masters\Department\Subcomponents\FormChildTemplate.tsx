import { FormChildTemplateProps } from "../../../../../../interfaces/Modules/Billing/DepartmentInterfaces/DepartmentInterfaces";
import styles from "../Styles/Department.module.css";

const FormChildTemplate = ({
  formData,
  formDataLabels,
  formUpdatedField,
  initialFormData = {},
  formValueListStyle,
}: FormChildTemplateProps) => {
  const mappedData = (key: string) => {
    const mp: Record<string, string> = {
      headoffice: "Head Office",
      site: "Site",
    };
    return mp[key] || key;
  };

  return (
    <div className={styles.formChildContainer}>
      {Object.entries(formDataLabels).map(([key, label]) => {
        console.log("key check all key which one first", key);
        const currentValue = formData[key];
        const originalValue = initialFormData[key];

        // Handle arrays
        if (Array.isArray(originalValue) || Array.isArray(currentValue)) {
          if (originalValue?.length === 0 && currentValue?.length === 0)
            return null;
          const originalArr = Array.isArray(originalValue) ? originalValue : [];
          const currentArr = Array.isArray(currentValue) ? currentValue : [];

          // Make a combined list of unique items
          const combined = [
            ...new Set([
              ...originalArr.map((v: any) =>
                typeof v === "object" ? v?.name : v
              ),
              ...currentArr.map((v: any) =>
                typeof v === "object" ? v?.name : v
              ),
            ]),
          ];

          return (
            <div key={key} className={styles.formChildItem}>
              <span className={styles.formLabel}>{label}</span>
              <ul className={styles.formValueList} style={formValueListStyle}>
                {combined.map((item: string, index: number) => {
                  const status = formUpdatedField?.(key, item);
                  return typeof item === "string" ? (
                    <li
                      key={`${key}-${item}-${index}`}
                      className={`${styles.formValueItem} ${
                        status ? styles[status] : ""
                      }`}
                    >
                      {item}
                    </li>
                  ) : null;
                })}
              </ul>
            </div>
          );
        }

        // Handle primitives and objects
        const current =
          typeof currentValue === "object"
            ? currentValue?.name ?? ""
            : key === "location"
            ? mappedData(currentValue)
            : currentValue ?? "";

        const original =
          typeof originalValue === "object"
            ? originalValue?.name ?? ""
            : key === "location"
            ? mappedData(originalValue)
            : originalValue ?? "";

        // Only show if current OR original exists (to also show removed values)
        if (
          (current || original) &&
          (String(current).trim() !== "" || String(original).trim() !== "")
        ) {
          const valueToShow = current || original;
          const status = formUpdatedField?.(key, valueToShow);
          let validExperience = key === "requiredExperience" ? parseFloat(valueToShow) : 0;

          return (
            <div key={key} className={styles.formChildItem}>
              <span className={styles.formLabel}>{label}</span>
              <p
                className={`${styles.formValue} ${
                  status ? styles[status] : ""
                }`}
              >
                {key === "requiredExperience" ? ( `${validExperience} ${validExperience > 1 ? "years" : "year"}`) : valueToShow}
              </p>
            </div>
          );
        }

        return null;
      })}
    </div>
  );
};

export default FormChildTemplate;
