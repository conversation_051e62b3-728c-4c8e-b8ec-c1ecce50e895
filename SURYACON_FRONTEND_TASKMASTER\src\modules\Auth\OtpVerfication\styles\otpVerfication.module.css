.OtpVerification_heading {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    text-align: center;
    width: 100%;
}

.OtpVerification_error_message p {
    color: var(--warning_color);
    margin-bottom: 0.5rem;
}

.OtpVerification_otp_input_container {
    width: 100%;
    display: flex;
    gap: 1rem;
    justify-content: center;
    align-items: center;
}

.OtpVerification_otp_input {
    width: 1rem;
    background-color: var(--main_background);
    padding: 1rem 2rem;
    border-radius: 16px;
    display: flex;
    justify-content: center;
}

.OtpVerification_otp_input input {
    width: 1rem;
    outline: none;
    background: none;
    border: none;
    font-size: 16px;
    text-align: center;
}

.OtpVerification_otp_input input:focus {
    outline: none;
}

.OtpVerification_otp_resend_button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: .5rem;
}



.OtpVerification_otp_utilities {
    display: flex;
    justify-content: space-between;
    gap: 1rem;
    margin-top: .5rem;
}

.continue_button {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}