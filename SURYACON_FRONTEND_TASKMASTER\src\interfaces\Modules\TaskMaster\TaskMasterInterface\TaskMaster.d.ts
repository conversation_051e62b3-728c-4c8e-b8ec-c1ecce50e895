export type FieldType =
  | "Subtasks"
  | "Departments"
  | "Designation"
  | "Manpower"
  | "Machinery"
  | "Tools"
  | "Materials";

export type CategoryDataType = {
  id: string;
  category: string;
};

export interface CheckSubtaskWeightageResponse {
  success: string;
  message: string;
}

export interface CheckSubtaskWeightageRequest {
  taskId: string;
  weightage: number;
}

export type TaskSubFieldsProps = {
  title: string;
  buttonContents: string[];
  onAddCategory?: () => void;
  currentField: FieldType | null;
  popupOpen: boolean;
  sampleData: Record<FieldType, CategoryDataType[]>;
  formData: Record<string, string>;
  handleSelectCategories: any;
  setFormData: any;
  // eslint-disable-next-line @typescript-eslint/no-unsafe-function-type
  setPopupOpen: Function;
};

export interface ApiResponse<T> {
  success?: boolean;
  message: string;
  data: {
    response: T;
  };
}

export interface RootInterface {
  _id: string;
  name: string;
  Description: string;
  isDeleted?: boolean;
}

export interface ResponseDataItem extends RootInterface {
  taskmasterDataLength: number;
  totalDepartmentIdLength: number;
  totaldesignationIdLength: number;
  TotalMaterialIdLength: number;
  TotalManpowerIdLength: number;
  TotalmachineryIdLength: number;
  TotalToolIdLength: number;
  taskname?: string;
  Quantity?: number;
  Unit?: string;
}

export interface Responses {
  data: {
    responseData: ResponseDataItem[];
    date: string;
  };
}
export interface Response {
  response: string[];
}

export interface TaskMasterTableResponse {
  success: boolean;
  data: {
    response: TaskMasterTable[];
  };
}

export interface TaskMasterTable {
  _id: string;
  Unit: string;
  Quantity: number;
  SubtaskId: string[];
  categoryId: string;
  materialId: string[];
  manpowerId: string[];
  machineryId: string[];
  toolId: string[];
  departmentId: string[];
  designationId: string[];
  Assignee: string[];
  Reporter: string[];
  itemtype: string;
  name: string;
  Description: string;
  isApprovesBy: string;
  isOpenBy: string;
  Timestamp: string;
  __v: number;
  Subtaskdetails: SubTaskDetail[];
  materialCataegory_details: string[];
  manpowerCategory_details: string[];
  machineryCategory_details: string[];
  toolCategory_details: string[];
  department_details: string[];
  designation_details: string[];
  Assignee_details: string[];
}
export interface SubtaskDetail {
  _id: string;
  name: string;
  Description: string;
}

export interface TaskInterface {
  _id: string;
  name: string;
  catId?: string;
  categoryName?: string;
  Description: string;
  SubtaskId: number;
  designation: number;
  material: number;
  machinery: number;
  tools: number;
  manpower: number;
  department: number;
  Unit?: string;
  Quantity?: number;
  taskname?: string;
  isDeleted?: boolean;
}

export interface SubTaskRouteInterface {
  id: string;
  name: string;
}

export interface SubTaskDetail {
  taskId: string | undefined;
  name: string;
  Description: string;
  Unit: string;
  subtaskWeighatages: string;
  Tracking: string;
}

export interface RootTaskBuildingBlocksData {
  _id: string;
  name: string;
  itemtype?: string;
}

export interface TaskBuildingBlocksData {
  department: RootTaskBuildingBlocksData[];
  designation: RootTaskBuildingBlocksData[];
  material: RootTaskBuildingBlocksData[];
  machinery: RootTaskBuildingBlocksData[];
  tools: RootTaskBuildingBlocksData[];
  manpower: RootTaskBuildingBlocksData[];
  Employee: RootTaskBuildingBlocksData[];
}

interface Task {
  _id: string;
  Unit: string;
  Quantity: number;
  SubtaskId: string[];
  categoryId: string;
  materialId: string[];
  manpowerId: string[];
  machineryId: string[];
  toolId: string[];
  departmentId: string[];
  designationId: string[];
  Assignee: string[];
  Reporter: string[];
  itemtype: string;
  name: string;
  Description: string;
  isApprovesBy: string;
  isDeniedBy: string;
  isOpenBy: string;
  isCompleted: boolean;
  createdAt: string;
  updatedAt: string;
  __v: number;
}

export interface GetTaskCategoriesResponse {
  success: boolean;
  data: Task[];
}

interface TaskCat {
  id: string;
  taskname: string;
  department: number;
  SubtaskId: number;
  designation: number;
  material: number;
  machinery: number;
  tools: number;
  manpower: number;
  Unit: string;
  name: string;
}

export interface GetTaskResponse {
  success: boolean;
  data: {
    response: TaskCat[];
  };
}

export interface TaskDetails {
  Machinary: string[];
  Tools: string[];
  Manpower: string[];
  Material: string[];
  AdminId: string[];
  AssigneeId: string[];
  Reporters: Reporter[];
  WorkInstructions: WorkInstruction[];
  Taskclosingreq: TaskClosingRequest[];
  FMEA: FMEA[];
  Controlplan: ControlPlan[];
  Subtasks?: string[];
}

// Reporter type
export interface Reporter {
  Level: string;
  designationId: string;
}

// Work Instruction type
export interface WorkInstruction {
  Description: string;
  optionselected: "checkbox" | "photo"; // Enums for options
  photoRef?: PhotoReference; // Optional if not "photo"
  departmentId: string;
  designationId: string;
}

// Photo Reference type
export interface PhotoReference {
  photos: string[];
  description: string;
}

// Task Closing Request type
export interface TaskClosingRequest {
  Description: string;
  optionselected: "checkbox" | "photo"; // Enums for options
  photoRef?: PhotoReferenceWithDescription; // Optional if not "photo"
}

// Extended Photo Reference for Task Closing Request
export interface PhotoReferenceWithDescription {
  photos: string[];
  Decription: string; // Typo fixed: Changed "Decription" to "description" if needed
}

// FMEA type
export interface FMEA {
  Description: string;
  severity: string; // Using string as per the example; could be a number if strict typing is needed
  solution: string;
}

// Control Plan type
export interface ControlPlan {
  name: string;
  Description: string;
}

export interface TaskDetails {
  Machinary: string[];
  Tools: string[];
  Manpower: string[];
  Material: string[];
  AdminId: string[];
  AssigneeId: string[];
  Reporters: Reporter[];
  WorkInstructions: WorkInstruction[];
  Taskclosingreq: TaskClosingRequest[];
  FMEA: FMEA[];
  Controlplan: ControlPlan[];
}

import https from "https";
// Reporter type
export interface Reporter {
  Level: string;
  designationId: string;
}

// Work Instruction type
export interface WorkInstruction {
  Description: string;
  optionselected: "checkbox" | "photo"; // Enums for options
  photoRef?: PhotoReference; // Optional if not "photo"
  departmentId: string;
  designationId: string;
}

// Photo Reference type
export interface PhotoReference {
  photos: string[];
  description: string;
}

// Task Closing Request type
export interface TaskClosingRequest {
  Description: string;
  optionselected: "checkbox" | "photo"; // Enums for options
  photoRef?: PhotoReferenceWithDescription; // Optional if not "photo"
}

// Extended Photo Reference for Task Closing Request
export interface PhotoReferenceWithDescription {
  photos: string[];
  Decription: string; // Typo fixed: Changed "Decription" to "description" if needed
}

// FMEA type
export interface FMEA {
  Description: string;
  severity: string; // Using string as per the example; could be a number if strict typing is needed
  solution: string;
}

// Control Plan type
export interface ControlPlan {
  _id: string;
  Description: string;
}

export type TaskDataType = {
  id: string;
  category: string;
  parentId: string;
};

export interface AddData {
  Trigger: string[];
  Action: string[];
  ResponseTime: string[];
}

export interface AddDataDesignation {
  AdminId?: requiredthings[];
  Adminid?: requiredthings[];
  AssigneeId: string[];
  Reporter: string[];
}

// building blocks means dropdown data by aayush
export type taskBuildingBlocks = {
  [key: string]: TaskDataType[] | "";
};

export interface Task {
  _id?: string;
  name: string;
  Description: string;
  Unit: string;
  Quantity: number;
  SubtaskId: string;
}

export type subtaskdetails = {
  name: string;
  Description: string;
  Unit: string;
  subtaskWeighatages: string;
  Tracking: string;
};

export interface AddDataMaster {
  ManpowerId: string[];
  MachinaryId: string[];
  ToolId: string[];
  MaterialId: string[];
  DepartmentId?: string[];
  DesignationId?: string[];
}

export interface TaskDetails {
  Subtasks: string[];
  Departments: string[];
  Designation: string[];
  Manpower: string[];
  Machinery: string[];
  Tools: string[];
  Materials: string[];
  Admin: string;
  Assignee: string[];
  Reporter: string[];
}

export interface TaskDetailsInterface {
  Subtasks: string[];
  Departments: string[];
  Designation: string[];
  Manpower: string[];
  Machinery: string[];
  Tools: string[];
  Materials: string[];
  Admin: string;
  Assignee: string[];
  Reporter: string[];
}

export interface ControlPlanDetails {
  _id: string;
  description: string;
}

export interface FailureModeDetails {
  _id: number | string;
  Description: string;
  solution: string;
  severity: number | string;
}

export interface TcrData {
  id?: string;
  description: string;
  file: { name: string; type: string } | null;
  category: string;
  photoDetails: PhotoSection[];
}

export interface TaskTcrPopupProps {
  data: TcrData | null;
  initaldata: TcrData;
  isEdit?: boolean;
  handleDelete?: () => void;
  OnUpdateTcrData?: (data: TcrData) => void;
  popupId: string;
  onClick: (e: any) => void;
}

export interface TriggerData {
  id: number | string;
  value?: string;
  label?: string;
}

export type requiredthings = {
  _id: string;
  name: string;
};
export interface WorkInstructionsData {
  id?: string;
  description: string;
  file: { name: string; type: string } | null;
  category: string;
  photoDetails: PhotoSection[];
}

export interface TaskWorkInstructionsPopupProps {
  id: string;
  workId: string;
  data: WorkInstructionsData | null;
  initaldata: WorkInstructionsData;
  isEdit?: boolean;
  onUpdateWorkInstructions?: (data: WorkInstructionsData) => void;
  popupId: string;
  categoryData: {
    Manpower: requiredthings[];
    Machinery: requiredthings[];
    Tools: requiredthings[];
    Materials: requiredthings[];
  };
  onClick: (e: any) => void;
  handleDelete: () => void;
  onUpdateCategoryData: (
    popupId: string,
    category: string,
    newData: requiredthings[]
  ) => void;
  setPopupIdParent: any;
  popupIdParent: any;
  setCategoryDataParent: any;
  categortDataParent: any;
  setDeleteIdParent: any;
  deleteIdParent: any;
}

export interface SubTaskWorkInstructionsPopupProps {
  data: WorkInstructionsData | null;
  workId: string;
  initaldata: WorkInstructionsData;
  isEdit?: boolean;
  onUpdateWorkInstructions?: (data: WorkInstructionsData) => void;
  popupId: string;
  categoryData: {
    Manpower: requiredthings[];
    Machinery: requiredthings[];
    Tools: requiredthings[];
    Materials: requiredthings[];
  };
  onClick: (e: any) => void;
  handleDelete: () => void;
  onUpdateCategoryData: (
    popupId: string,
    category: string,
    newData: requiredthings[]
  ) => void;
  setPopupIdParent: any;
  popupIdParent: any;
  setCategoryDataParent: any;
  categortDataParent: any;
  setDeleteIdParent: any;
  deleteIdParent: any;
  id?: string;
}
export interface TriggerData {
  id: number | string;
  value?: string;
  label?: string;
}

export type SampleData = {
  [key: string]: TriggerData[];
};

export interface AddDataSubTaskDesignation {
  AdminId: requiredthings[];
  AssigneeId: string[];
  ReporterId: {};
}

export interface TcrData {
  id?: string;
  description: string;
  file: { name: string; type: string } | null;
  category: string;
  photoDetails: PhotoSection[];
}

export interface SubTaskTcrPopupProps {
  data: TcrData | null;
  initaldata: TcrData;
  id?: string;
  isEdit?: boolean;
  handleDelete?: (item: any) => void;
  OnUpdateTcrData?: (data: TcrData) => void;
  popupId: string;
  onClick: (e: any) => void;
}

//trigeer event interface

export interface TriggerEventDataProp {
  triggerResponse: { id: string; value: string }[];
  action: ActionType[];
  startAfterInterval: number;
  responseTime: number;
}
export interface TriggerEventDilalogueProps {
  header: string;
  data?: TriggerEventDataProp;
  inputDecline?: boolean;
  button1Content?: string;
  button1Type?: ButtonProps.type;
  button2Content?: string;
  button2Type?: ButtonProps.type;
  callBackCancel: () => void;
  mode: string;
}

export type RadioProp = {
  id?: string | number;
  value: string;
  error?: boolean;
  isChecked: boolean;
  onRadioSelect: (id: string | number, value: string) => void;
};

export type TEPopupCompProp = {
  title: string;
  label: string;
  error: string | boolean;
  data: { id: number | string; name: string }[];
  onSelectCallback: (selected: {
    id: number | string;
    name: string;
    isFirst?: boolean;
  }) => void;
  mode: string;
  selectedItemFromTop: { _id: number | string; name: string } | null;
};

export interface SubTaskCreationHeaderProps {
  name: string;
  Description: string;
  unit: string;
  weightage: number;
  tracking: string;
  onclick: () => void;
  isEdit: boolean;
}

export interface CategoryData {
  _id: string;
  doc?: {
    _id: string;
    [key: string]: any;
  };
  [key: string]: any;
}
