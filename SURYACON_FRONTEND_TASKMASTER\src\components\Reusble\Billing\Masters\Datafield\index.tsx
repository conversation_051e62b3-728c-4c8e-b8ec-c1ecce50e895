import React, { useState } from "react";
import styles from "./Datafield.module.css";
import { AddCategoryIcon, DeleteIcon } from "../../../../../assets/icons";
import Tooltip from "../../../Global/Tooltip";
import {
  useAppDispatch,
  useAppSelector,
} from "../../../../../redux/hooks/Modules/Reduxhooks/ReduxHooks";
import {
  closePopup,
  openPopup,
} from "../../../../../redux/features/Modules/Reusble/popupSlice";
import { RootState } from "../../../../../redux/store";
import { setSelectedFloorType } from "../../../../../redux/features/Modules/Billing/ProjectPlanning/Slices/ProjectSlice";
import { setIsSubtaskForm } from "../../../../../redux/features/Modules/Billing/BillingApproval/Slices/MonthlyTargetSlice";
import { setToast } from "../../../../../redux/features/Modules/Reusble/ToastSlice";
import { initializeDatabase } from "../../../../../functions/functions";
import { setAddCategoryTypeForm } from "../../../../../redux/features/Modules/TaskMaster/Slices/MastersSlice";
import { useSelector } from "react-redux";

interface DatafieldProps {
  label: string;
  error?: boolean;
  isUnit?: boolean;
  varient?: string;
  selectedValues?: { _id: string; name: string; parent_task_id?: string }[];
  callbackDelete?: (id: string) => void;
  setIsClosing?: React.Dispatch<React.SetStateAction<boolean>>;
  heading?: string;
  handlePlusIcon?: (message: string) => void;
  disabled?: boolean;
  onFetchMore?: () => void;
  onItemClick?: (id: string) => void;
  needBold?:boolean
  
}

const Datafield: React.FC<DatafieldProps> = ({
  label,
  error,
  selectedValues,
  setIsClosing,
  callbackDelete,
  isUnit = false,
  varient,
  heading,
  handlePlusIcon,
  disabled = false,
  onFetchMore,
  onItemClick,
  needBold=false
}) => {
  const AddCategoryTypeFormData = useSelector(
    (state: RootState) => state.masterForm.addcategoryTypeFormData
  );

  const dispatch = useAppDispatch();

  // Redux state
  const towerlocationData = useAppSelector(
    (state: RootState) => state.projectLocalDb.towerLocationFormdata
  );
  const { activeTaskDetails, selectedTower } = useAppSelector(
    (state: RootState) => state.monthlyTargetForm
  );

  const [showFetchMoreOption, setShowFetchMoreOption] = useState(false);

  // Utility functions
  const showToast = (
    messageContent: string,
    type: "success" | "error" | "warning" | "info"
  ) => {
    dispatch(setToast({ isOpen: true, messageContent, type }));
  };

  const isFloorType = (label: string) =>
    label === "Conventional" || label === "Mivan";
  const isTaskType = (label: string) =>
    ["Tasks", "Selected Tasks"].includes(label);
  const isSubtaskType = (label: string) =>
    ["Subtasks", "Selected Subtasks"].includes(label);

  // Validation functions
  const validateFloorAddition = () => {
    const floorCount = towerlocationData?.number_of_floors?.trim();

    if (!floorCount) {
      handlePlusIcon?.("number_of_floors");
      return false;
    }

    const totalFloors =
      (towerlocationData?.conventionals?.length ?? 0) +
      (towerlocationData?.mivan?.length ?? 0);

    if (totalFloors >= Number(floorCount)) {
      handlePlusIcon?.("noFloorLeft");
      return false;
    }

    return true;
  };

  const validateTaskAddition = () => {
    if (!selectedTower.id) {
      showToast("Please select a tower first", "warning");
      return false;
    }
    return true;
  };

  const validateSubtaskAddition = () => {
    if (!activeTaskDetails) {
      showToast("Please select a task first", "warning");
      return false;
    }
    return true;
  };

  // Handler functions
  const handleFloorTypeSelection = (label: string) => {
    if (label === "Conventional") {
      dispatch(setSelectedFloorType("conventionals"));
    } else if (label === "Mivan") {
      dispatch(setSelectedFloorType("mivan"));
    }
  };

  const handlePopupTransition = () => {
    setIsClosing?.(true);

    setTimeout(() => {
      if (varient) dispatch(closePopup(varient));
      setIsClosing?.(false);
    }, 500);

    setTimeout(() => {
      dispatch(openPopup("AddSubForm"));

      // Check if we need to show the fetch more option
      setTimeout(() => {
        const addCategoryItems =
          document.querySelectorAll(".add-category-item");
        setShowFetchMoreOption(addCategoryItems.length === 0);
      }, 500);
    }, 500);
  };

  const handleAddClick = () => {
    if (disabled) {
      showToast("This action is currently disabled", "warning");
      return;
    }

    let shouldOpenPopup = true;

    // Validation based on item type
    if (isFloorType(label)) {
      shouldOpenPopup = validateFloorAddition();
      if (shouldOpenPopup) {
        handleFloorTypeSelection(label);
      }
    } else if (isTaskType(label)) {
      shouldOpenPopup = validateTaskAddition();
    } else if (isSubtaskType(label)) {
      shouldOpenPopup = validateSubtaskAddition();
    }

    if (shouldOpenPopup) {
      // Set subtask form mode
      dispatch(setIsSubtaskForm(!isTaskType(label)));
      handlePopupTransition();
    }
  };

  const handleFetchMore = () => {
    if (onFetchMore) {
      onFetchMore();
      setShowFetchMoreOption(false);
    }
  };

  const handleDelete = (id: string) => {
    if (callbackDelete) {
      callbackDelete(id);
    }
  };

  const handleItemClick = (id: string) => {
    if (onItemClick && !disabled) {
      onItemClick(id);
    }
  };

  // Render functions
  const renderTooltipItem = (item: {
    _id: string;
    name: string;
    parent_task_id?: string;
  }) => (
    <div
      key={item._id}
      className={styles.tooltip_box}
      style={{ position: "relative" }}
    >
      <Tooltip
        id={item._id}
        content={item.name}
        handleClick={() => handleItemClick(item._id)}
        style={{ cursor: onItemClick ? "pointer" : "default" }}
      />
      <div
        className={styles.delete_icon_tooltip}
        onClick={() => handleDelete(isUnit ? item.name : item._id)}
      >
        <DeleteIcon />
      </div>
    </div>
  );

  const renderSelectedItems = () => {
    if (!selectedValues?.length) return null;

    return (
      <div className={styles.tooltip_container}>
        {selectedValues.map(renderTooltipItem)}
      </div>
    );
  };

  const renderHeader = () => {
    if (heading) {
      return (
        <div className={styles.datafield_header}>
          {needBold?<h4>{heading}</h4>:
          <p>{heading}</p>}
          <div
            className={styles.datafield_addIcon}
            onClick={handleAddClick}
            style={{ cursor: disabled ? "not-allowed" : "pointer" }}
          >
            <AddCategoryIcon />
          </div>
        </div>
      );
    }

    return (
      <div className={styles.datafield_header}>
        {selectedValues?.length === 0 ? <p>{label}</p> : renderSelectedItems()}
        <div
          className={styles.datafield_addIcon}
          onClick={handleAddClick}
          style={{ cursor: disabled ? "not-allowed" : "pointer" }}
        >
          <AddCategoryIcon />
        </div>
      </div>
    );
  };

  const renderFetchMoreOption = () => {
    if (!showFetchMoreOption || !onFetchMore) return null;

    return (
      <div className={styles.fetch_more_container}>
        <p>
          {isTaskType(label)
            ? "No more tasks available. Try fetching from database."
            : isSubtaskType(label)
            ? "No more subtasks available. Try fetching more."
            : "All available items are already selected."}
        </p>
        <button className={styles.fetch_more_button} onClick={handleFetchMore}>
          {isTaskType(label)
            ? "Fetch More Tasks"
            : isSubtaskType(label)
            ? "Fetch More Subtasks"
            : "Fetch More Items"}
        </button>
      </div>
    );
  };

  console.log("Selected Values:", selectedValues);
  return (
    <div
      className={styles.datafield_box}
      style={{
        border: error ? "1px solid var(--warning_color)" : "",
        opacity: disabled ? 0.7 : 1,
      }}
    >
      {renderHeader()}

      {heading && selectedValues?.length! > 0 && renderSelectedItems()}

      {renderFetchMoreOption()}
    </div>
  );
};

export default Datafield;
