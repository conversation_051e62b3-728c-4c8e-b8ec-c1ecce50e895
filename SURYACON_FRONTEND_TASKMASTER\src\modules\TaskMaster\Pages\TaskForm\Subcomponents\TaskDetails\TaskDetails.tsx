import React, { useCallback, useEffect, useRef, useState } from "react";
import styles from "../../Styles/TaskCreationForm.module.css";
import { Loader } from "../../../../../../assets/loader";
import { useParams } from "react-router-dom";

import {
  useAppDispatch,
  useAppSelector,
} from "../../../../../../redux/hooks/Modules/Reduxhooks/ReduxHooks";
// import { setSubTaskRoute } from "../../../../redux/features/TaskMaster/Slices/TaskMasterSlice";
// import { getPouchDBInstance } from "../../../../localDatabase/pouchdbClient";
// import { SuryconLogo } from "../../../../assets/icons";

import { useSelector } from "react-redux";
import { RootState, store } from "../../../../../../redux/store";
import TaskCreationMethod from "../TaskCreationMethod/TaskCreationMethod";
import TaskCreationMasters from "../TaskCreationMaster/TaskCreationMasters";
import { SuryconLogo } from "../../../../../../assets/icons";
import TaskCreationDesignation from "../TaskCreationDesignation/TaskCreationDesignation";
import {
  TaskDataType,
  TaskDetailsInterface,
} from "../../../../../../interfaces/Modules/TaskMaster/TaskMasterInterface/TaskMaster";

import { useUpdateTaskByIdMutation } from "../../../../../../redux/api/Modules/TaskMaster/TaskMasterapi";
import {
  setChangeAPiFlag,
  setIsEditTask,
  settaskChangeAPiFlag,
  setcurrentTaskData,
} from "../../../../../../redux/features/Modules/TaskMaster/Slices/TaskMasterSlice";
import { useOpenCloseTaskMasterMutation } from "../../../../../../redux/api/Modules/Reusble/Reusble";
import TaskCreationHeader from "../TaskCreationHeader/TaskCreationHeader";
import { useToast } from "../../../../../../hooks/ToastHook";

const TaskDetails: React.FC = () => {
  const [addData, setAddData] = useState<TaskDetailsInterface>({
    Subtasks: [],
    Departments: [],
    Designation: [],
    Manpower: [],
    Machinery: [],
    Tools: [],
    Materials: [],
    Admin: "",
    Assignee: [],
    Reporter: [],
  });
  const showToast = useToast();
  const TaskData = useSelector(
    (state: RootState) =>
      state.taskForm.currentSubtaskData || {
        name: "", 
        Unit: "",
        description: "",
        subtaskWeighatages: 0,
        Tracking: "",
        MaterialId: [],
        ToolId: [],
        MachinaryId: [],
        ManpowerId: [],
        AdminId: [],
        AssigneeId: [],
        Reporter: [],
        Subtaskdetails: [],
        MethodId: {
          work_instruction_id: [],
          task_closing_requirement: [],
          Controlplan: [],
          Failuremode: [],
        },
      }
  );

  const isChange = useSelector((state: RootState) => state.taskForm.isChange);

  const taskApiCall = useSelector(
    (state: RootState) => state.taskForm.istaskApiCall
  );
  const { taskId } = useParams<{ taskId: string }>();
  const { popups } = useSelector((state: RootState) => state.popup);
  const { user } = useSelector((state: RootState) => state.auth);
  const [currentOpenPopup, setcurrentOpenPopup] = useState(popups);

  const dispatch = useAppDispatch();
  // const [isEdit, setIsEdit] = useState(false);
  const [updateTaskById] = useUpdateTaskByIdMutation();
  const [updateStatus] = useOpenCloseTaskMasterMutation();
  const isEdit = useSelector(
    (state: RootState) => state.isEditTaskReducer.isEdit
  );
  const handleUpdateStatus = async (data?: {
    status: string;
    taskid: string;
  }) => {
    try {
      const response = await updateStatus(data).unwrap();
      return response;
    } catch (error) {
      console.log(error);
      throw error;
    }
  };
  const handleSelect = useCallback(
    async (
      categoryName: keyof TaskDetailsInterface,
      selectedItems: TaskDataType[]
    ) => {
      if (currentOpenPopup) {
        // console.log(currentOpenPopup, "the current popup");
        const objId = Object.keys(currentOpenPopup);
        // console.log("the id", objId);
      }
    },
    [taskId]
  );

  const intervalRef = useRef(null); // Store interval reference

  const state = store.getState();
  const openDocData = state.popup.openDocData;
  const handleToggleisEdit = async () => {
    // if (openDocData.length) {
    //   const isOpenByEmployee =
    //     openDocData &&
    //     (openDocData.filter((openData: any) => openData.id == taskId) as any);
    //   if (isOpenByEmployee.length) {
    //     // Case 1: If `isOpenByEmployee` has data
    //     if (
    //       isOpenByEmployee[0]?.openedbyemployee?.employeeId == user?.employeeId
    //     ) {
    //       dispatch(setIsEditTask(!isEdit));
    //       await handleUpdateStatus({ status: "false", taskid: taskId! });
    //     } else {
    //       dispatch(
    
    //         setToast({
    //           isOpen: true,
    //           messageContent: "Someone working on this form",
    //           type: "warning",
    //         })
    //       );
    //     }
    //     if (isEdit) dispatch(setIsEditTask(false));
    //   } else {
    //     // console.log("isOpenbyEmployee is empty", isEdit);
    //     // Case 2: If `isOpenByEmployee` is empty
    //     await handleUpdateStatus({
    //       status: !isEdit ? "true" : "false",
    //       taskid: taskId!,
    //     });
    //     dispatch(setIsEditTask(!isEdit));
    //   }
    // } else {
    //   // Case 3: If `openDocData` is empty
    //   if (isEdit) {
    //     // console.log("isEdit is true and no data available");
    //     dispatch(setIsEditTask(false));
    //     await handleUpdateStatus({ status: "false", taskid: taskId! });
    //   } else {
    //     dispatch(setIsEditTask(true));
    //     // console.log("intial edit");
    //     await handleUpdateStatus({ status: "true", taskid: taskId! });
    //   }
    // }
    try {
      const response = await handleUpdateStatus({
        status: `${!isEdit}`,
        taskid: taskId!,
      });
      console.log("response12", response?.data);
      if (response?.success) {
        dispatch(setIsEditTask(!isEdit));
      } else {
        dispatch(setIsEditTask(false));
        showToast({
          messageContent: "Someone working on this form",
          type: "warning",
        });
      }
      // if(response)
    } catch (error: any) {
      dispatch(setIsEditTask(false));
      console.log("responsee", error);
      showToast({
        messageContent: error.message || "Oops! Something went wrong",
        type: "danger",
      });
    }
  };

  useEffect(() => {
    // If isSubTaskChange occurs, clear the interval
    if (isChange && intervalRef.current) {
      clearTimeout(intervalRef.current);
      intervalRef.current = null;
      // console.log("Interval stopped due to subtask change");
    }
  }, [isChange, taskApiCall]);

  useEffect(() => {
    setcurrentOpenPopup(popups);
  }, [popups]);

  useEffect(() => {
    const callStatusApi = async () => {
      await handleUpdateStatus({ status: "false", taskid: taskId! });
    };

    return () => {
      console.log("i am calling on mount and")

      if(isEdit && taskId){
        dispatch(settaskChangeAPiFlag(false));
        dispatch(setChangeAPiFlag(false));
        callStatusApi();
      }
    };  
  }, [isEdit, taskId, dispatch]);

  // Add a loading state: true if TaskData is missing or name is empty
  const loading = !TaskData || !TaskData.name || TaskData.name === "";

  return (
    <div className={styles.taskcreation_container}>
      <TaskCreationHeader
        editState={isEdit}
        taskId={taskId}
        onclick={() => {
          if (!navigator.onLine) {
            showToast({
              messageContent: "Oops! No Internet Connection",
              type: "danger",
            });
            return;
          }
          handleToggleisEdit();
        }}
        loading={loading}
        TaskData={TaskData}
      />
      {loading ? (
        <div className={styles.loader_container}>
          <img
            src={Loader.suryaconLogo}
            alt="Loading..."
            className={styles.loader_image}
          />
        </div>
      ) : (
        <div className={styles.taskcreation_form_container}>
          <TaskCreationMasters
            isEdit={isEdit}
            addData={addData}
            handleSelect={handleSelect}
          />
          {/* <div className={styles.taskcreation_line_container}>
        <span className={styles.dottedline_wrapper}></span>
        <SuryconLogo />
        <span className={styles.dottedline_wrapper}></span>
      </div>

      {/* <TaskCreationAutomation /> */}

          {TaskData?.Subtaskdetails &&
            TaskData?.Subtaskdetails?.length == 0 && (
              <>
                <div className={styles.taskcreation_line_container}>
                  <span className={styles.dottedline_wrapper}></span>
                  <SuryconLogo />
                  <span className={styles.dottedline_wrapper}></span>
                </div>

                <TaskCreationDesignation isEdit={isEdit} addData={addData} />
                <div className={styles.taskcreation_line_container}>
                  <span className={styles.dottedline_wrapper}></span>
                  <SuryconLogo />
                  <span className={styles.dottedline_wrapper}></span>
                </div>
                <TaskCreationMethod isEdit={isEdit} />
              </>
            )}
        </div>
      )}
    </div>
  );
};

export default TaskDetails;
