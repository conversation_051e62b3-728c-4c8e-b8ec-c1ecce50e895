import { FC, useEffect, useState } from "react";
import styles from "../styles/TriggerEvent.module.css";
import { RadioProp } from "../../../../../interfaces/Modules/TaskMaster/TaskMasterInterface/TaskMaster";



const TERadioValComp: FC<RadioProp> = ({
  id,
  error = false,
  value,
  onRadioSelect,
  isChecked,
}) => {
  const handleChange = () => {
    onRadioSelect(id!, value);
  };

  return (
    <div className={`${styles.mt_radio_component} ${
        error ? `${styles.error}` : ""
      }`}>
      <input
        type="radio"
        name="radioGroup"
        className={`${styles.mt_radio_button}`}
        onChange={handleChange}
        id={id?.toString()}
        checked={isChecked}
      />
      <label htmlFor={id?.toString()}>
        <p className={`${styles.mt_radio_button_label_text}`}>{value}</p>
      </label>
    </div>
  );
};
export default TERadioValComp;
