import { ChangeEvent, FC, useCallback, useEffect, useState } from "react";
import Button from "../Button";
import { CloseIcon, SuccessIcon } from "../../../../assets/icons";
import SearchBar from "../SearchBar";
import TargetBadge from "../TargetBadge/TargetBadge";
import styles from "./Styles/addplanningthings.module.css";
import {
  AddTaskPropType,
  DataItem,
  SelectedItems,
  TaskDataType,
} from "../GlobalInterfaces/GlobalInterface";
import { useSelector } from "react-redux";
import { RootState } from "../../../../redux/store";

const AddPlanningThings: FC<AddTaskPropType> = ({
  data,
  title = "",
  isColorChange = false,
  initialSelected,
  placeholder = "Search",
  onSelect,
  onClose,
}) => {
  console.log(data, "thisisdata");
  const [isClosing, setIsClosing] = useState<boolean>(false);
  const [searchTerm, setSearchTerm] = useState<string>("");

  const [showDiscard, setShowDiscard] = useState<boolean>(false);
  const [showBrand, setShowBrand] = useState<boolean>(false);
  const [selectedMaterial, setselectedMaterial] = useState<string | null>(null);
  const [selectedBrand, setSelectedBrand] = useState<string | null>(null);
  const [selectedGrade, setSelectedGrade] = useState<string | null>(null);
  const [selectedManpowerType, setSelectedManpwoerType] = useState<string[]>(
    []
  );
  const [showGrade, setShowGrade] = useState<boolean>(false);
  const [shake, setShake] = useState(false);

  const [selectedBrandDetailId, setselectedBrandDetailId] = useState<
    string | null
  >(null);
  const [currentstep, setcurrentStep] = useState(1);

  // picking up taskdata from redux to hightlight the things which are added in the task
  const TaskData = useSelector(
    (state: RootState) =>
      state.taskForm.currentSubtaskData || {
        name: "",
        Unit: "",
        description: "",
        subtaskWeighatages: 0,
        Tracking: "",
        MaterialId: [],
        ToolId: [],
        MachinaryId: [],
        ManpowerId: [],
        Adminid: [],
        AssigneeId: [],
        Reporter: [],
        MethodId: {
          work_instruction_id: [],
          task_closing_requirement: [],
          Controlplan: [],
        },
      }
  );

  console.log(initialSelected, "this is taskdata brrro");
  // Handle close button click
  const onCloseHandler = () => {
    if (currentstep == 1) {
      handleClose();
    } else {
    }
  };

  // Handle actual close action
  const handleClose = () => {
    setIsClosing(true);
    setTimeout(onClose, 400);
  };

  // Toggle category selection
  const toggleCategory = (
    categoryId: string,
    level: "material" | "brand" | "grade"
  ) => {
    if (level === "material") {
      setselectedMaterial(categoryId); // Set the new material
      setSelectedBrand(null); // Reset Brand and Grade when Material changes
      setSelectedGrade(null);
    } else if (level === "brand") {
      console.log(categoryId, "thisiscategoryiddd");
      setSelectedBrand(categoryId); // Set the new brand
      setSelectedGrade(null); // Reset Grade when Brand changes
    } else if (level === "grade") {
      console.log(categoryId, "selectedgrade");
      setSelectedGrade(categoryId); // Set the new grade
    } else if (level === "type") {
      console.log(categoryId, "selectedgrade");
      if (selectedManpowerType.includes(categoryId)) {
        setSelectedManpwoerType(
          selectedManpowerType.filter((item) => item !== categoryId)
        );
        return;
      }
      setSelectedManpwoerType([...selectedManpowerType, categoryId]); // Set the manpower type
      console.log(selectedManpowerType, "selectedmanpower");
    }
  };

  // Filter data based on search term and exclude initialSelected items

  // Get selected items

  // Handle add button click
  const handleAdd = () => {
    if (!selectedMaterial && currentstep === 1) {
      setShake(true);
      setTimeout(() => setShake(false), 600);
      return;
    }

    if (currentstep === 1 && selectedMaterial) {
      setShowBrand(true);
      setcurrentStep(2);
      return;
    }

    if (!selectedBrand && currentstep === 2) {
      setShake(true);
      setTimeout(() => setShake(false), 600);
      return;
    }

    if (currentstep === 2 && selectedBrand) {
      setShowGrade(true);
      setcurrentStep(3);
      return;
    }

    if (!selectedGrade && currentstep === 3) {
      setShake(true);
      setTimeout(() => setShake(false), 600);
      return;
    }

    if (currentstep === 3 && selectedGrade) {
      setcurrentStep(4);
      return;
    }
    if (selectedManpowerType.length == 0 && currentstep === 5) {
      setShake(true);
      setTimeout(() => setShake(false), 600);
      return;
    }

    if (currentstep === 5 && selectedManpowerType.length > 0) {
      setcurrentStep(4);
      return;
    }
    // if (currentstep == 5 ) {
    //   if (selectedManpowerType.length == 0) {
    //     setShake(true);
    //     return;
    //   }
    //   setcurrentStep(4);
    //   return;
    // }
  };

  // function for serach
  const handleSearchChange = (e: ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  const filteredData = data?.filter(
    (item) =>
      item?.category?.toLowerCase().includes(searchTerm.toLowerCase().trim()) &&
      !initialSelected?.some((e) => e?._id == item?.id)
  );

  // filtered data for manpower
  const transFormedData = data
    ?.map((item) => {
      const selectedCount =
        initialSelected?.filter((e) => e?._id == item?.id)?.length ?? 0;
      if (selectedCount == 2) {
        return {
          ...item,
          Types: [],
        };
      } else if (selectedCount == 1) {
        return {
          ...item,
          Types:
            initialSelected?.find((e) => e?._id == item?.id)?.category ==
            "skilled"
              ? ["unskilled"]
              : ["skilled"],
        };
      } else {
        return {
          ...item,
          Types: ["skilled", "unskilled"],
        };
      }
    })
    .filter((item) => item.Types !== undefined);
  const filterdManpowerData = transFormedData?.filter(
    (item) =>
      item?.category?.toLowerCase().includes(searchTerm.toLowerCase().trim()) &&
      item.Types?.length !== 0
  );
  console.log(filterdManpowerData, transFormedData, "filterdManpowerData");
  // Handle submit action
  const handleSubmit = () => {
    onSelect(
      {
        material: data?.find((e) => e?.id == selectedMaterial)?.category,
        brand: data
          ?.find((e) => e?.id == selectedMaterial)
          ?.BrandDetails?.find((e) => e?._id == selectedBrandDetailId)
          ?.BrandId?.find((e) => e?._id == selectedBrand)?.Brandname,
        grade: title !== "Manpower" ? selectedGrade : selectedManpowerType,
        brandId: selectedBrand,
        MaterialId: selectedMaterial,
      },
      title
    );
    console.log(
      {
        material: data?.find((e) => e?.id == selectedMaterial)?.category,
        brand: data
          ?.find((e) => e?.id == selectedMaterial)
          ?.BrandDetails?.find((e) => e?._id == selectedBrandDetailId)
          ?.BrandId?.find((e) => e?._id == selectedBrand)?.Brandname,
        grade: title !== "Manpower" ? selectedGrade : selectedManpowerType,
        brandId: selectedBrand,
        MaterialId: selectedMaterial,
      },
      "thisisselecteddata"
    );
    onClose();
  };
  // Handle search input change

  console.log(selectedMaterial, "selectedbitchm");
  console.log(selectedBrand, "selectedbitchb");
  console.log(selectedGrade, "selectedbitchg");
  console.log(selectedBrandDetailId, "jaggasubtask");
  const material = data?.find((e) => e.id == selectedMaterial);
  const brandSpecs = material?.BrandDetails?.find(
    (e) => e._id == selectedBrandDetailId
  )?.Specs;
  // useeffect to by default select the remaied type if other one is already added
  useEffect(() => {
    if (currentstep == 5) {
      const selectedManpower = filterdManpowerData?.find(
        (e) => e?.id == selectedMaterial
      );
      console.log(selectedManpower, "selctedmanpoer");
      if (
        selectedManpower &&
        selectedManpower?.Types.length == 1 &&
        !showDiscard
      ) {
        setSelectedManpwoerType([
          ...selectedManpowerType,
          selectedManpower?.Types[0],
        ]);
      }
    }
  }, [currentstep, selectedMaterial]);

  useEffect(() => {
    setSelectedManpwoerType([]);
  }, [selectedMaterial]);

  useEffect(() => {
    if (currentstep == 2) {
      const selectedMaterialData = data?.find((e) => e.id == selectedMaterial);
      if (selectedMaterialData?.BrandDetails?.length == 1 && !showDiscard) {
        const brandDetail = selectedMaterialData.BrandDetails[0];
        setselectedBrandDetailId(brandDetail?._id);
        toggleCategory(brandDetail?.BrandId[0]?._id, "brand");
      }
    }
  }, [currentstep, selectedMaterial]);

  useEffect(() => {
    if (currentstep == 3) {
      const selectedMaterialData = data?.find((e) => e.id == selectedMaterial);
      const brandSpecs = selectedMaterialData?.BrandDetails?.find(
        (e) => e._id == selectedBrandDetailId
      )?.Specs;
      if (brandSpecs?.length == 1 && !showDiscard) {
        toggleCategory(brandSpecs[0], "grade");
      }
    }
  }, [currentstep, selectedMaterial, selectedBrandDetailId]);
  return (
    <div
      className={`${styles.add_category_type_maindiv} ${
        isClosing ? styles.closing : ""
      }`}
    >
      {/* heading */}
      <div className={styles.categorypopupheading}>
        <h3
          style={{
            textAlign: "center",
            color: showDiscard ? "var(--warning_color)" : "black",
          }}
        >
          {currentstep == 1 && !showDiscard && `${title}`}
          {currentstep == 2 && !showDiscard && `Brand`}
          {currentstep == 3 && !showDiscard && `Grade`}
          {currentstep == 4 &&
            !showDiscard &&
            `Are you sure you want to add this ${title}`}
          {/* step 5 only for manpwoer */}
          {currentstep == 5 && !showDiscard && `Type`}
          {showDiscard && `Are you sure you want to discard this ${title}`}
        </h3>
      </div>
      {showDiscard && (
        <div className={styles.popup_close_icon}>
          <button
            onClick={() => setShowDiscard(false)}
            className={styles.closebtn_category}
          >
            <CloseIcon />
          </button>
        </div>
      )}
      {currentstep == 1 && !showDiscard && (
        <div className={styles.popup_close_icon}>
          <button
            onClick={() =>
              selectedMaterial ? setShowDiscard(true) : onCloseHandler()
            }
            className={styles.closebtn_category}
          >
            <CloseIcon />
          </button>
        </div>
      )}
      {currentstep >= 2 && !showDiscard && (
        <div className={styles.popup_close_icon}>
          <button
            onClick={() => setShowDiscard(true)}
            className={styles.closebtn_category}
          >
            <CloseIcon />
          </button>
        </div>
      )}

      <div style={{ padding: "0rem 0.5rem 0rem", margin: "0.5rem" }}>
        {/* searchbar start */}
        {currentstep == 1 && !showDiscard && (
          <SearchBar
            placeholder={placeholder}
            height={"3.12rem"}
            onChange={handleSearchChange}
            debounceDelay={300}
          />
        )}
        {/* searchbar end  */}

        {currentstep == 2 && !showDiscard && (
          <>
            <div className={styles.brand_container}>
              <div className={styles.brand_top_container}>
                <div className={`${styles.brand_circle}`}></div>
                {/* <div className={styles.brand_circle}>
                  <SuccessIcon />
                </div> */}
                <div className={`${styles.brand_dottedline_gray}`}></div>
                <div className={`${styles.brand_circle_gray}`}></div>
              </div>

              <div className={styles.brand_bottom_container}>
                <div className={styles.brand_grade_content}>
                  <div className={styles.brand_grade_text}>
                    <p>{title}:</p>
                    <p>
                      {data?.find((e) => e?.id == selectedMaterial)?.category}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </>
        )}
        {currentstep == 3 && !showDiscard && (
          <>
            <div className={styles.brand_container}>
              <div className={styles.brand_top_container}>
                <div className={styles.brand_circle}>
                  <SuccessIcon />
                </div>
                <div className={`${styles.brand_dottedline}`}></div>
                {selectedGrade ? (
                  <div className={styles.brand_circle}>
                    <SuccessIcon />
                  </div>
                ) : (
                  <div className={`${styles.brand_circle}`}></div>
                )}
              </div>

              <div className={styles.brand_bottom_container}>
                <div className={styles.brand_grade_content}>
                  <div className={styles.brand_grade_text}>
                    <p>{title}:</p>
                    <p>
                      {data?.find((e) => e?.id == selectedMaterial)?.category}
                    </p>
                  </div>
                  <div className={styles.brand_grade_text}>
                    <p>Brand:</p>
                    <p>
                      {
                        data
                          ?.find((e) => e?.id == selectedMaterial)
                          ?.BrandDetails?.find(
                            (e) => e?._id == selectedBrandDetailId
                          )
                          ?.BrandId?.find((e) => e?._id == selectedBrand)
                          ?.Brandname
                      }
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </>
        )}
        {currentstep == 5 && !showDiscard && (
          <>
            <div className={styles.brand_container}>
              <div className={styles.brand_top_container}>
                <div className={styles.brand_circle}>
                  <SuccessIcon />
                </div>
                <div className={`${styles.brand_dottedline}`}></div>
                {selectedGrade ? (
                  <div className={styles.brand_circle}>
                    <SuccessIcon />
                  </div>
                ) : (
                  <div className={`${styles.brand_circle}`}></div>
                )}
              </div>

              <div className={styles.brand_bottom_container}>
                <div className={styles.brand_grade_content}>
                  <div className={styles.brand_grade_text}>
                    <p>{title}:</p>
                    <p>
                      {data?.find((e) => e?.id == selectedMaterial)?.category}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </>
        )}
      </div>
      {/*body  */}
      <>
        {/* step 1 for material */}
        {currentstep == 1 && !showDiscard && (
          <>
            <div className={styles.add_category_type_innerdiv}>
              {(title !== "Manpower" ? filteredData : filterdManpowerData)?.map(
                (item) => (
                  <>
                    <li
                      key={item.id}
                      className={`${styles.category} ${styles.marginBottom} ${
                        selectedMaterial == item.id ? styles.selected : ""
                      } ${shake ? styles.shake : ""}`}
                      style={{
                        border: "1px solid",
                        borderColor:
                          selectedMaterial == item.id ? "#00596b" : "#fff",
                        color:
                          selectedMaterial == item.id
                            ? "var(--primary_color)"
                            : `${isColorChange ? "var(--text-black-28)" : ""}`,
                        cursor: "pointer",
                      }}
                      onClick={() => toggleCategory(item.id, "material")}
                    >
                      <div className="category_text">{item.category}</div>
                      {item.unit && (
                        <TargetBadge
                          backgroundColor="#F0F6F6"
                          valueTextClassName="small_text_p"
                          outerContainerClassName="material_popup"
                          value={item.unit}
                        />
                      )}
                    </li>
                  </>
                )
              )}
            </div>
          </>
        )}
        {/* step 2 for brand */}
        {currentstep == 2 && !showDiscard && (
          <>
            <div className={styles.add_category_type_innerdiv}>
              {data
                ?.find((e) => e.id == selectedMaterial)
                ?.BrandDetails?.map((item) => (
                  <>
                    <li
                      key={item.id}
                      className={`${styles.category} ${styles.marginBottom} ${
                        selectedBrand == item?.BrandId[0]?._id
                          ? styles.selected
                          : ""
                      } ${shake ? styles.shake : ""}`}
                      style={{
                        border: "1px solid",
                        borderColor:
                          selectedBrand == item?.BrandId[0]?._id
                            ? "#00596b"
                            : "#fff",
                        color:
                          selectedBrand == item?.BrandId[0]?._id
                            ? "var(--primary_color)"
                            : `${isColorChange ? "var(--text-black-28)" : ""}`,
                        cursor: "pointer",
                      }}
                      onClick={() => {
                        setselectedBrandDetailId(item?._id);
                        console.log(item?.BrandId[0]?._id, "selecteditmishere");
                        toggleCategory(item?.BrandId[0]?._id, "brand");
                      }}
                    >
                      {item?.BrandId[0]?.Brandname}

                      {item.unit && (
                        <TargetBadge
                          backgroundColor="#F0F6F6"
                          valueTextClassName="small_text_p"
                          outerContainerClassName="material_popup"
                          value={item.unit}
                        />
                      )}
                    </li>
                  </>
                ))}
            </div>
          </>
        )}
        {/* step 3 for grade */}
        {currentstep == 3 && !showDiscard && (
          <>
            <div className={styles.add_category_type_innerdiv}>
              {brandSpecs?.map((item) => (
                <>
                  <li
                    key={item.id}
                    className={`${styles.category} ${styles.marginBottom} ${
                      selectedGrade == item ? styles.selected : ""
                    } ${shake ? styles.shake : ""}`}
                    style={{
                      border: "1px solid",
                      borderColor: selectedGrade == item ? "#00596b" : "#fff",
                      color:
                        selectedGrade == item
                          ? "var(--primary_color)"
                          : `${isColorChange ? "var(--text-black-28)" : ""}`,
                      cursor: "pointer",
                    }}
                    onClick={() => toggleCategory(item, "grade")}
                  >
                    {item}
                    {item.unit && (
                      <TargetBadge
                        backgroundColor="#F0F6F6"
                        valueTextClassName="small_text_p"
                        outerContainerClassName="material_popup"
                        value={item.unit}
                      />
                    )}
                  </li>
                </>
              ))}
            </div>
          </>
        )}
        {/* step 4 for summary */}
        {currentstep == 4 && !showDiscard && (
          <CategorySummary
            selectedItems={{
              material: data?.find((e) => e?.id == selectedMaterial)?.category,
              brand: data
                ?.find((e) => e?.id == selectedMaterial)
                ?.BrandDetails?.find((e) => e?._id == selectedBrandDetailId)
                ?.BrandId?.find((e) => e?._id == selectedBrand)?.Brandname,
              grade:
                title !== "Manpower" ? selectedGrade : selectedManpowerType,
              brandId: selectedBrandDetailId,
            }}
            title={title}
          />
        )}
        {/* step 5 only for manpower type */}
        {currentstep == 5 && !showDiscard && (
          <>
            <div className={styles.add_category_type_innerdiv}>
              {filterdManpowerData
                ?.find((e) => e.id == selectedMaterial)
                ?.Types?.map((item) => (
                  <>
                    <li
                      key={item.id}
                      className={`${styles.category} ${styles.marginBottom} ${
                        selectedManpowerType?.includes(item)
                          ? styles.selected
                          : ""
                      } ${shake ? styles.shake : ""}`}
                      style={{
                        border: "1px solid",
                        borderColor: selectedManpowerType?.includes(item)
                          ? "#00596b"
                          : "#fff",
                        color: selectedManpowerType?.includes(item)
                          ? "var(--primary_color)"
                          : `${isColorChange ? "var(--text-black-28)" : ""}`,
                        cursor: "pointer",
                      }}
                      onClick={() => toggleCategory(item, "type")}
                    >
                      {item?.charAt(0).toUpperCase() + item.slice(1)}
                      {item.unit && (
                        <TargetBadge
                          backgroundColor="#F0F6F6"
                          valueTextClassName="small_text_p"
                          outerContainerClassName="material_popup"
                          value={item.unit}
                        />
                      )}
                    </li>
                  </>
                ))}
            </div>
          </>
        )}
        {showDiscard && (
          <CategorySummary
            selectedItems={{
              material: data?.find((e) => e?.id == selectedMaterial)?.category,
              brand: data
                ?.find((e) => e?.id == selectedMaterial)
                ?.BrandDetails?.find((e) => e?._id == selectedBrandDetailId)
                ?.BrandId?.find((e) => e?._id == selectedBrand)?.Brandname,
              grade:
                title !== "Manpower" ? selectedGrade : selectedManpowerType,
              brandId: selectedBrandDetailId,
            }}
            title={title}
          />
        )}
      </>

      {/* buttons */}
      <div className={styles.add_category_type_button_div}>
        {currentstep == 1 && !showDiscard && (
          <>
            <Button
              type="Cancel"
              Content={"Cancel"}
              Callback={() =>
                !selectedMaterial ? handleClose() : setShowDiscard(true)
              }
            />
            <Button
              type="Next"
              Content={"Add"}
              Callback={() =>
                title !== "Manpower" ? handleAdd() : setcurrentStep(5)
              }
            />
          </>
        )}
        {currentstep == 2 && !showDiscard && (
          <>
            <Button
              type="Cancel"
              Content={"Back"}
              Callback={() => setcurrentStep((prev) => prev - 1)}
            />
            <Button type="Next" Content={"Add"} Callback={() => handleAdd()} />
          </>
        )}
        {currentstep == 3 && !showDiscard && (
          <>
            <Button
              type="Cancel"
              Content={"Back"}
              Callback={() => setcurrentStep((prev) => prev - 1)}
            />
            <Button type="Next" Content={"Add"} Callback={() => handleAdd()} />
          </>
        )}
        {/* extra step for manpower only */}
        {currentstep == 5 && !showDiscard && (
          <>
            <Button
              type="Cancel"
              Content={"Back"}
              Callback={() => setcurrentStep(1)}
            />
            <Button type="Next" Content={"Add"} Callback={() => handleAdd()} />
          </>
        )}
        {currentstep == 4 && !showDiscard && (
          <>
            <Button
              type="Cancel"
              Content={"Back"}
              Callback={() =>
                title !== "Manpower"
                  ? setcurrentStep((prev) => prev - 1)
                  : setcurrentStep(5)
              }
            />
            <Button
              type="Next"
              Content={"Submit"}
              Callback={() => handleSubmit()}
            />
          </>
        )}
        {showDiscard && (
          <>
            <Button
              type="Cancel"
              Content={"No"}
              Callback={() => setShowDiscard(false)}
            />
            <Button
              type="Next"
              Content={"Yes"}
              Callback={() => handleClose()}
            />
          </>
        )}
      </div>
    </div>
  );
};

const CategorySummary: FC<{ selectedItems; title: string }> = ({
  selectedItems,
  title,
}) => {
  console.log(selectedItems, "selecteditsmhereasdf");
  return (
    <>
      {selectedItems?.material && (
        <div className={styles.summaryDivData}>
          <div className={styles.summaryDataContent}>
            <p style={{ color: "var(--text-black-60)" }} className="p_tag_14px">
              {title}
            </p>
            <div
              className={styles.summaryItems}
              style={{ color: "var(--text-black-87)" }}
            >
              <h4
                className={styles.summaryItem}
                style={{ paddingRight: "16px" }}
              >
                {selectedItems?.material}
              </h4>
            </div>
          </div>
        </div>
      )}
      {selectedItems?.brand && (
        <div className={styles.summaryDivData}>
          <div className={styles.summaryDataContent}>
            <p style={{ color: "var(--text-black-60)" }} className="p_tag_14px">
              Brand
            </p>
            <div
              className={styles.summaryItems}
              style={{ color: "var(--text-black-87)" }}
            >
              <h4
                className={styles.summaryItem}
                style={{ paddingRight: "16px" }}
              >
                {selectedItems?.brand}
              </h4>
            </div>
          </div>
        </div>
      )}
      {(title == "Manpower"
        ? selectedItems?.grade?.length > 0
        : selectedItems?.grade) && (
        <div className={styles.summaryDivData}>
          <div className={styles.summaryDataContent}>
            <p style={{ color: "var(--text-black-60)" }} className="p_tag_14px">
              {title == "Manpower"
                ? `${selectedItems?.grade?.length > 1 ? "Types" : "Type"}`
                : "Grade"}
            </p>
            <div
              className={styles.summaryItems}
              style={{ color: "var(--text-black-87)" }}
            >
              <h4
                className={styles.summaryItem}
                style={{ paddingRight: "16px" }}
              >
                {title !== "Manpower" ? (
                  selectedItems?.grade
                ) : (
                  <div>
                    {selectedItems?.grade.map((item) => (
                      <h4
                        className={styles.summaryItem}
                        style={{ paddingRight: "16px" }}
                      >
                        {item?.charAt(0).toUpperCase() + item.slice(1)}
                      </h4>
                    ))}
                  </div>
                )}
              </h4>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default AddPlanningThings;
