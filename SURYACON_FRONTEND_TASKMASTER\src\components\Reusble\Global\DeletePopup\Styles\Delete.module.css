/*  AUTHOR NAME : CHARVI */
.DeletePopup_container {
  position: fixed;
  top: 7rem;
  right: 1.5rem;
  transform: translate(0%, 0%);
  /* background: var(--main_background); */
  padding: 0.75rem 0.9rem;
  z-index: 999;
  width: 34rem;
  animation: slideIn 0.5s ease-out;
  backdrop-filter: blur(100px);
  border-radius: 2.6rem;
  border: 1px solid;
  border-image-source: linear-gradient(
    130.72deg,
    rgba(237, 231, 231, 0.07) -22.43%,
    rgba(251, 251, 251, 0.05) 75.66%
  );
  backdrop-filter: blur(150px);
  box-shadow: 0px 4px 40px 0px #00000080;
  height: calc(100% - 8.5rem);
  
  
}

@keyframes slideIn {
  from {
    transform: translate(100%, 0);
  }

  to {
    transform: translate(0%, 0);
  }
}

@keyframes slideOut {
  from {
    transform: translate(0, 0);
  }

  to {
    transform: translate(100%, 0);
  }
}

.DeletePopup_container.closing {
  animation: slideOut 0.5s ease-out;
}

@media only screen and (max-width: 1280px) {
  .DeletePopup_container {
    width: 31rem;
  }
}

.closeButton {
  position: absolute;
  top: .75rem;
  right: 0.625rem;
  padding: 1rem;
  background: transparent;
  border: none;
  cursor: pointer;
}

.deletePopup_header {
  color: var(--warning_color);
  display: flex;
  justify-content: center;
  padding: 0.6rem;
}

.deletePopup_datainputs {
  height: calc(100% - 7rem);
  /* padding: 0.45rem; */
  overflow-y: scroll;
  scrollbar-width: none;
}

.deletePopup_btngrp {
  display: flex;
  justify-content: center;
  gap: 1rem;
  padding-top: 1rem;
  padding-bottom: 0.75rem;
}

.subfeild_container {
  background-color: var(--main_background);
  border-radius: 1.5rem;
  border: 1px solid var(--text-black-28);
  margin: 0.938rem;
  min-height: 3.375rem;
}

.subcategories {
  box-sizing: border-box;
  padding: 0 1rem 1rem 1rem;
  display: flex;
  flex-wrap: wrap;
  gap: 0.7rem;
}

.categoryitems {
  display: flex;

  min-height: 3.188rem;
  overflow-wrap: break-word;
  word-wrap: break-word;
  white-space: normal;
  flex-wrap: wrap;
  color: #000000de;

  line-height: 2rem;
}

.summaryheadings {
  color: #00000047;

  line-height: 1.363rem;
  text-align: left;
}

.summaryicon {
  width: 1.5rem;
  padding-left: 0.3rem;
}

.categoryitems_list {
  padding-right: 1.5rem;
}

.taskContainer {
  display: flex;
  flex-direction: column;
  max-height: 82vh;
}

.taskcreation_header {
  position: sticky;
  top: 0;
  z-index: 10;
  padding: 0.625rem;
  margin-right: 0.625rem;
}

.taskContainer_items {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 0.313rem;
  overflow-y: auto;
  overflow-x: hidden;
  margin: 0.625rem 3rem 0.625rem 0;
}

@media only screen and (max-width: 1636px) {
  .taskContainer_items {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
  }

  .taskContainer_items {
    margin: 0.625rem 1rem 0.625rem 0;
  }
}

@media only screen and (max-width: 1280px) {
  .taskContainer_items {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
    margin: 0.5rem 3.5rem 0.5rem 0;
  }
}

/* @media only screen and (min-width: 1800px) {
  .deletePopup_datainputs{
    height: calc(100% - 6.5rem);
  }
} */
.delete_popup_header {
  text-align: center;
  max-width: 88%;
}
