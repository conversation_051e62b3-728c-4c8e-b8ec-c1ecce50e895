import {
  CloseIcon,
  ImageIcon,
  SuryaconLogoSecondary,
  SuryconLogo,
  VideoIcon,
} from "../../../../assets/icons";
import styles from "./Styles/ApprovalForm.module.css";
import Button from "../../Global/Button";
import { useDispatch, useSelector } from "react-redux";
import { useState, useEffect, useRef } from "react";
import { generateSummaryData } from "../../../../functions/functions";
import { RootState } from "../../../../redux/store";
import {
  closePopup,
  setApprovalFormStep,
} from "../../../../redux/features/Modules/Reusble/popupSlice";
import { resetInputValues } from "../../../../redux/features/Modules/Reusble/floatinglabelslice";
import { useApproveOrRejectCategoryMutation } from "../../../../redux/api/Modules/TaskMaster/TaskMasterapi";
import { useGetTaskCategoriesQuery } from "../../../../redux/api/Modules/TaskMaster/TaskMasterapi";
import {
  setSearchKey,
  setTypeSearchKey,
} from "../../../../redux/features/Modules/TaskMaster/Slices/TaskMasterSlice";
import { useToast } from "../../../../hooks/ToastHook";

interface ApprovalFormProps {
  catId?: string | null;
}

export function ApprovalForm({ catId = null }: ApprovalFormProps) {
  const dispatch = useDispatch();
  const [summaryData, setSummaryData] = useState<
    Array<{ label: string; value: string; isChanged: boolean }>
  >([]);
  const showToast = useToast();

  const [categoryName, setCategoryName] = useState<string>("");
  const [Description, setDescription] = useState<string>("");
  const inputValues = useSelector(
    (state: RootState) => state.floatingLabel.inputValues
  );
  console.log(inputValues, "input values in approval form");

  const step = useSelector((state: RootState) => state.popup.approvalFormStep);
  const [declineReason, setDeclineReason] = useState("");
  const [isClosing, setIsClosing] = useState(false);

  const isValidValue = (value: any) => {
    return value !== undefined && value !== null && value !== "";
  };

  const searchkey = useSelector(
    (state: RootState) => state.taskMaster.searchKey
  );
  useEffect(() => {
    const name = inputValues?.TaskName ?? inputValues?.CategoryName ?? "";
    if (name !== categoryName) setCategoryName(name);
    if (inputValues?.Description !== Description)
      setDescription(inputValues?.Description ?? "");
  }, [inputValues]);

  useEffect(() => {
    const fields = [
      {
        label: inputValues?.TaskName ? "TaskName" : "CategoryName",
        value: categoryName,
      },
      { label: "Description", value: Description },
    ];

    const result = catId
      ? String(catId)
      : inputValues?._id
      ? String(inputValues._id)
      : undefined;

    const summary = generateSummaryData(inputValues, fields, result, null);
    setSummaryData(summary);
  }, [inputValues, categoryName, Description]);

  const handleClose = () => {
    setIsClosing(true);
    setTimeout(() => {
      dispatch(closePopup("ApprovalForm"));
      dispatch(resetInputValues());
    }, 400);
  };

  const time = new Date().toISOString();
  const [approveOrRejectCategory] = useApproveOrRejectCategoryMutation();

  const submitHandler = async () => {
    try {
      const res = await approveOrRejectCategory({
        categoryId: inputValues?.catId,
        isApprove: true,
        comment: "Approved by MD",
      }).unwrap();

      if (res.success) {
        showToast({
          messageContent: res.data,
          type: "success",
        });

        handleClose();
      }
    } catch (error) {
      console.error("Approval error:", error);
      showToast({
        messageContent: "Oops! Something went wrong",
        type: "danger",
      });
    }
  };

  const declineHandler = async () => {
    try {
      if (!declineReason.trim()) {
        showToast({
          messageContent: "Please provide a reason for declining.",
          type: "warning",
        });
        return;
      }

      const res = await approveOrRejectCategory({
        categoryId: inputValues?.catId,
        isApprove: false,
        comment: declineReason,
      }).unwrap();

      if (res.success) {
        showToast({
          messageContent: res.data || "Category has been declined.",
          type: "success",
        });

        handleClose();
      }
    } catch (error) {
      console.error("Decline error:", error);
      showToast({
        messageContent: "Failed to decline category",
        type: "danger",
      });
    }
  };

  const mode = inputValues?.mode ?? "task";
  const headerMode = mode === "taskdetails" ? "task" : mode;

  const getHeader = () => {
    if (step === "approve")
      return `Are you sure you want to approve this ${headerMode} request?`;
    if (step === "decline")
      return `Are you sure you want to decline this ${headerMode} request?`;
    if (step === "reason") return "Reason for Declining";
    return "Take Action";
  };
  const handleSubmit = () => {
    return step === "approve" ? submitHandler() : declineHandler();
  };
  const handleBack = () => {
    dispatch(setApprovalFormStep("initial"));
    setDeclineReason("");
  };
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      e.preventDefault();
      e.stopPropagation();
      if (step === "initial") {
        dispatch(setApprovalFormStep("approve"));
      } else {
        handleSubmit();
      }
    }

    if (e.key === "Escape") {
      e.preventDefault();
      e.stopPropagation();
      if (step === "initial") {
        dispatch(setApprovalFormStep("decline"));
      } else {

        handleBack();
      }
    }
  };
  const formRef = useRef(null);
  // Scroll to top when step changes

  useEffect(() => {
    if (formRef.current) {
      formRef.current.focus();
    }
  }, [step]);

  return (
    <div
      className={`${styles.approvalform_container} ${
        isClosing ? styles.closing : ""
      }`}
      onKeyDown={handleKeyDown}
      ref={formRef}
      tabIndex={0}
    >
      <div className={styles.approvalform_header}>
        <div
          className={`${styles.approvalform_title} ${
            step === "decline" || step === "reason"
              ? styles.approvalform_header_decline
              : ""
          }`}
        >
          {getHeader()}
        </div>
        <button
          className={styles.approvalform_closeButton}
          onClick={handleClose}
        >
          <CloseIcon />
        </button>
      </div>
      {step === "reason" && (
        <div className={styles.approvalform_summaryDataContent}>
          <p>Reason</p>
          <div className={styles.approvalform_horizaontalData}>
            {inputValues?.Reason}
          </div>
        </div>
      )}

      {step !== "reason" && (
        <div className={styles.approvalform_datainputs}>
          {step === "decline" && (
            <>
              <input
                type="text"
                value={declineReason}
                placeholder="Reason"
                onChange={(e) => {
                  setDeclineReason(e.target.value);
                }}
                className={styles.approvalform_reasonInput}
              />
              <div className={styles.approvalform_line_container}>
                <span className={styles.approvalform_dottedline_wrapper}></span>
                <SuryconLogo />
                <span className={styles.approvalform_dottedline_wrapper}></span>
              </div>
            </>
          )}

          {summaryData.map((item) =>
            isValidValue(inputValues[item.label]) ? (
              <div
                className={styles.approvalform_summaryDataContent}
                key={item.label}
              >
                <p>
                  {["CategoryName", "TaskName"].includes(item.label)
                    ? "Name"
                    : item.label}
                </p>
                <div className={styles.approvalform_horizaontalData}>
                  <div>{inputValues[item.label]}</div>
                </div>
                {/* we are using this input to focus on the input field to use it for enter and escape functionality and we have place it here 
                because we are working on category approval only so this position of focus will chnage in future  */}
                <input
                  autoFocus
                  readOnly
                  style={{
                    width: "0px",
                    height: "0px",
                    opacity: 0,
                    display: "block",
                  }}
                ></input>
              </div>
            ) : null
          )}

          {(mode === "task" ||
            mode === "taskdetails" ||
            mode === "subtask") && (
            <div style={{ display: "flex", gap: "0.6rem" }}>
              {["Quantity", "Unit"].map((label) => (
                <div
                  className={styles.approvalform_summaryDataContent}
                  key={label}
                  style={{ width: "262px" }}
                >
                  <p>{label}</p>
                  <div className={styles.approvalform_horizaontalData}>
                    <div>{inputValues[label]}</div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {(mode === "taskdetails" || mode === "subtask") && (
            <>
              {[
                "Departments",
                "Designation",
                "Manpower",
                "Machinery",
                "Tools",
                "Materials",
              ].map((fieldKey) => {
                const value = inputValues[fieldKey];
                if (Array.isArray(value) && value.length > 0) {
                  return (
                    <div
                      className={styles.approvalform_summaryDataContent}
                      key={fieldKey}
                    >
                      <p>{fieldKey}</p>
                      <div className={styles.approvalform_horizaontalData}>
                        {value.map((v, idx) => (
                          <div key={idx}>{v}</div>
                        ))}
                      </div>
                    </div>
                  );
                }
                return null;
              })}

              {((Array.isArray(inputValues.TaskManager) &&
                inputValues.TaskManager.length > 0) ||
                (Array.isArray(inputValues.AssignTo) &&
                  inputValues.AssignTo.length > 0) ||
                (Array.isArray(inputValues.Reporter) &&
                  inputValues.Reporter.length > 0)) && (
                <>
                  <div className={styles.approvalform_line_container}>
                    <span
                      className={styles.approvalform_dottedline_wrapper}
                    ></span>
                    <SuryconLogo />
                    <span
                      className={styles.approvalform_dottedline_wrapper}
                    ></span>
                  </div>

                  <p className={styles.approvalform_sectionHeading}>
                    Task Allocation
                  </p>

                  {["TaskManager", "AssignTo"].map((role) =>
                    Array.isArray(inputValues[role]) &&
                    inputValues[role].length > 0 ? (
                      <div
                        className={styles.approvalform_summaryDataContent}
                        key={role}
                      >
                        <p>{role.replace(/([A-Z])/g, " $1").trim()}</p>
                        <div className={styles.approvalform_horizaontalData}>
                          {inputValues[role].map((v, idx) => (
                            <div key={idx}>{v}</div>
                          ))}
                        </div>
                      </div>
                    ) : null
                  )}

                  {Array.isArray(inputValues.Reporter) &&
                    inputValues.Reporter.length > 0 && (
                      <div className={styles.approvalform_summaryDataContent}>
                        <p>Reporter</p>
                        <div
                          style={{
                            display: "flex",
                            flexDirection: "column",
                            gap: "24px",
                          }}
                        >
                          {inputValues.Reporter.map((reporter, idx) => (
                            <div
                              className={styles.approvalform_reporter}
                              key={`reporter-${idx}`}
                            >
                              <p>L-{reporter.level}</p>
                              <div
                                className={styles.approvalform_horizaontalData}
                              >
                                {reporter.designations?.map((d, i) => (
                                  <div key={i}>{d}</div>
                                ))}
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                </>
              )}

              {Array.isArray(inputValues.work_instruction_id) &&
                inputValues.work_instruction_id.length > 0 && (
                  <div>
                    <div className={styles.approvalform_line_container}>
                      <span
                        className={styles.approvalform_dottedline_wrapper}
                      ></span>
                      <SuryconLogo />
                      <span
                        className={styles.approvalform_dottedline_wrapper}
                      ></span>
                    </div>
                    <p className={styles.approvalform_sectionHeading}>
                      Work Instructions
                    </p>
                    {inputValues.work_instruction_id.map((wi, idx) => (
                      <div
                        className={styles.approvalform_summaryDataContent}
                        key={`work-instruction-${idx}`}
                      >
                        <p>Description</p>
                        <div className={styles.approvalform_horizaontalData}>
                          <div>{wi.description}</div>
                        </div>
                        {wi.file?.name && wi.file?.type && (
                          <div>
                            {wi.file.type === "png" && <ImageIcon />}
                            {wi.file.type === "mp4" && <VideoIcon />}
                            <span
                              style={{
                                marginLeft: "4px",
                                color: "rgba(0, 89, 104, 1)",
                              }}
                            >
                              {wi.file.name}
                            </span>
                          </div>
                        )}

                        <p style={{ marginTop: "24px" }}>Action</p>
                        <div className={styles.approvalform_horizaontalData}>
                          <div>{wi.optionSelected}</div>
                        </div>

                        {Array.isArray(wi.photos) &&
                          wi.photos.map((photo, pIdx) => (
                            <div key={`photo-${pIdx}`}>
                              <div
                                className={styles.approvalform_horizaontalData}
                              >
                                <div style={{ marginTop: "24px" }}>
                                  {photo.fileName}
                                </div>
                              </div>
                              <p>Reference Details</p>
                              <div
                                className={styles.approvalform_horizaontalData}
                              >
                                <div>{photo.details}</div>
                              </div>
                            </div>
                          ))}

                        {["manpower", "machinery", "tools", "materials"].map(
                          (asset) => {
                            const assetData = wi[asset];

                            return Array.isArray(assetData) &&
                              assetData.length > 0 ? (
                              <div key={asset}>
                                <p style={{ marginTop: "24px" }}>
                                  {asset.charAt(0).toUpperCase() +
                                    asset.slice(1)}
                                </p>
                                <div
                                  className={
                                    styles.approvalform_horizaontalData
                                  }
                                >
                                  {assetData.map((item, i) => (
                                    <div key={i}>{item}</div>
                                  ))}
                                </div>
                              </div>
                            ) : null;
                          }
                        )}
                      </div>
                    ))}
                  </div>
                )}

              {Array.isArray(inputValues.task_closing_requirement) &&
                inputValues.task_closing_requirement.length > 0 && (
                  <div>
                    <div className={styles.approvalform_line_container}>
                      <span
                        className={styles.approvalform_dottedline_wrapper}
                      ></span>
                      <SuryconLogo />
                      <span
                        className={styles.approvalform_dottedline_wrapper}
                      ></span>
                    </div>
                    <p className={styles.approvalform_sectionHeading}>
                      Task Closing Requirements
                    </p>
                    {inputValues.task_closing_requirement.map((tci, idx) => (
                      <div
                        className={styles.approvalform_summaryDataContent}
                        key={`taskClosingRequirements-${idx}`}
                      >
                        <p>Description</p>
                        <div className={styles.approvalform_horizaontalData}>
                          <div>{tci.description}</div>
                        </div>
                        {tci.file?.name && tci.file?.type && (
                          <div>
                            {tci.file.type === "png" && <ImageIcon />}
                            {tci.file.type === "mp4" && <VideoIcon />}
                            <span
                              style={{
                                marginLeft: "4px",
                                color: "rgba(0, 89, 104, 1)",
                              }}
                            >
                              {tci.file.name}
                            </span>
                          </div>
                        )}

                        <p style={{ marginTop: "24px" }}>Action</p>
                        <div className={styles.approvalform_horizaontalData}>
                          <div>{tci.optionSelected}</div>
                        </div>

                        {Array.isArray(tci.photos) &&
                          tci.photos.map((photo, pIdx) => (
                            <div key={`photo-${pIdx}`}>
                              <div
                                className={styles.approvalform_horizaontalData}
                              >
                                <div style={{ marginTop: "24px" }}>
                                  {photo.fileName}
                                </div>
                              </div>
                              <p>Reference Details</p>
                              <div
                                className={styles.approvalform_horizaontalData}
                              >
                                <div>{photo.details}</div>
                              </div>
                            </div>
                          ))}
                      </div>
                    ))}
                  </div>
                )}

              {Array.isArray(inputValues.ControlPlan) &&
                inputValues.ControlPlan.length > 0 && (
                  <div>
                    <div className={styles.approvalform_line_container}>
                      <span
                        className={styles.approvalform_dottedline_wrapper}
                      ></span>
                      <SuryconLogo />
                      <span
                        className={styles.approvalform_dottedline_wrapper}
                      ></span>
                    </div>
                    <p className={styles.approvalform_sectionHeading}>
                      Quality Control Plan
                    </p>
                    {inputValues.ControlPlan.map((desc, idx) => (
                      <div
                        className={styles.approvalform_summaryDataContent}
                        key={idx}
                      >
                        <p>Description</p>
                        <div className={styles.approvalform_horizaontalData}>
                          <div>{desc}</div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}

              {Array.isArray(inputValues.FailureMode) &&
                inputValues.FailureMode.length > 0 && (
                  <div>
                    <div className={styles.approvalform_line_container}>
                      <span
                        className={styles.approvalform_dottedline_wrapper}
                      ></span>
                      <SuryconLogo />
                      <span
                        className={styles.approvalform_dottedline_wrapper}
                      ></span>
                    </div>

                    <p className={styles.approvalform_sectionHeading}>
                      Quality Ensuring Measures
                    </p>
                    {inputValues.FailureMode.map((measure, idx) => (
                      <div
                        className={styles.approvalform_summaryDataContent}
                        key={`failure-mode-${idx}`}
                      >
                        <p>Description</p>
                        <div className={styles.approvalform_horizaontalData}>
                          <div>{measure.description}</div>
                        </div>
                        <p style={{ marginTop: "24px" }}>Severity</p>
                        <div className={styles.approvalform_horizaontalData}>
                          <div>{measure.severity}</div>
                        </div>
                        <p style={{ marginTop: "24px" }}>Solution</p>
                        <div className={styles.approvalform_horizaontalData}>
                          <div>{measure.solution}</div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
            </>
          )}
        </div>
      )}

      {step !== "reason" && (
        <div className={styles.approvalform_btngrp}>
          {step === "initial" ? (
            <>
              <Button
                type="Decline2"
                Content="Decline"
                Callback={() => dispatch(setApprovalFormStep("decline"))}
              />
              <Button
                type="Next"
                Content="Approve"
                Callback={() => dispatch(setApprovalFormStep("approve"))}
              />
            </>
          ) : (
            <>
              <Button type="Cancel" Content="Back" Callback={handleBack} />
              <Button type="Next" Content="Submit" Callback={handleSubmit} />
            </>
          )}
        </div>
      )}
    </div>
  );
}
