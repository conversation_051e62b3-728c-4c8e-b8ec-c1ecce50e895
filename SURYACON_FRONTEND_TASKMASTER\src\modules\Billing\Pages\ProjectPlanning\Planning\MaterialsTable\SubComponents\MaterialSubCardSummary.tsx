// Author <PERSON><PERSON><PERSON>

import React, { useEffect, useState } from "react";
import styles from "../Styles/MaterialsTable.module.css";
import MateriatCard from "./MateriatCard";
import Circle from "../../../../../../../components/Reusble/Billing/Circle";
import { useAppSelector } from "../../../../../../../redux/hooks/Modules/Reduxhooks/ReduxHooks";
import { initializeDatabase } from "../../../../../../../functions/functions";

function MaterialSubCardSummary() {
  const selectedLocationTaskId = useAppSelector(
    (state) => state.projectPlanning.selectedLocationTaskId
  );
  const selectedMaterialId = useAppSelector(
    (state) => state.projectPlanning.selectedMaterialId
  );
  const [materials, setMaterials] = useState<any>({});

  const fetchSubtasksFromLocalDB = async (): Promise<void> => {
    if (!selectedLocationTaskId || !selectedMaterialId) return;

    const dbName = await initializeDatabase("materialtable");

    const fetchedDoc = await window.electron.getDocumentByParentId({
      dbName,
      categoryId: "taskId",
      catId: selectedLocationTaskId,
    });

    if (Array.isArray(fetchedDoc)) {
      const relevantMaterial = fetchedDoc
        .flatMap((doc) => doc.materials || [])
        .find((material) => material._id === selectedMaterialId);

      if (relevantMaterial && relevantMaterial.subtasks) {
        setMaterials(relevantMaterial);
      } else {
        setMaterials([]);
      }
    } else {
      setMaterials([]);
    }
  };

  useEffect(() => {
    fetchSubtasksFromLocalDB();
  }, [selectedLocationTaskId, selectedMaterialId]);

  return (
    <div className={styles.material_subcard_summary_main}>
      <div className={styles.material_subcard_summary_submain}>
        <div className={styles.material_subcard_summary_header}>
          <p className={`small_text_p`}>Summary</p>
          <h4>{materials?.name || "No Material Name"}</h4>
        </div>
        <div className={styles.material_subcard_summary_content}>
          <div className={styles.material_subcard_content_header}>
            <h4>Subtasks</h4>
            <Circle
              nameclass={"sub_material_summary_circle"}
              content={
                <p className="small_text_p material_summary_circle_p">
                  {materials?.subtasks?.length || 0}
                </p>
              }
            />
          </div>
          <div className={styles.material_card_main_scroll}>
            {materials?.subtasks?.map((item: any) => (
              <MateriatCard
                key={item.id}
                data={{
                  subtaskName: item?.name,
                  unit: materials?.unit || "No Unit",
                  name: materials?.name || "No Material Name",
                  quantity: item?.quantity || 0,
                }}
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}

export default MaterialSubCardSummary;
