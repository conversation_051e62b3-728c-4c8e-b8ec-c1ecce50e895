.progress_card_outer_container {
  width: auto;
  border-radius: 2.3rem;
  position: relative;
  z-index: 0;
  padding: 0.3rem;
  background-color: var(--primary_background);

  box-shadow: 0px 0px 3px 0px #91a1a180;

  overflow: hidden;
}
.towercard_outer_container .progress_card_outer_container {
  background-color: var(--primary_background);
}
.towercard_outer_container {
  width: 100%;
  max-width: 40rem;
  border-radius: 2.3rem;
  position: relative;
  z-index: 0;
  padding: 0.3rem;
  background-color: var(--primary_background) !important;
  box-shadow: 0px 0px 3px 0px #91a1a180;
  overflow: hidden;
}

.progress_card_inner_container {
  height: 100%;
  width: 100%;
  overflow: hidden;
  background: #ffffff;
  box-shadow: 0rem 0rem 0.1875rem 0rem #91a1a180;
  border-radius: 2.1rem;
  padding: 1.5rem;
  position: relative;
}

.progress_card_top_container {
  display: flex;
  justify-content: space-between;
  position: relative;
  gap: 0.3rem;
  bottom: 0.5rem;
  width: 100%;
}
.progress_card_topright_outer_container {
  justify-self: end;
}
.progress_card_title {
  overflow: hidden;
  color: var(--text-black-87);
  text-overflow: ellipsis;
}

.progress_card_topright_container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  justify-self: end;
}
.progress_card_top_left_container {
  display: flex;
  gap: 0.5rem;
  align-items: center;
  width: 70%;
}

.progress_card_percentage_card {
  height: 1.75rem;
  width: 5rem;
  background-color: var(--primary_background);

  padding: 0.25rem;
  border-radius: 6.25rem;
  box-shadow: var(--extra-shdow-second);

  text-align: center;
}
.project_sft_card {
  width: 5.6875rem;
  height: 1.75rem;
  background-color: var(--primary_background);
  color: var(--text-black-87);
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0.2rem;
  border-radius: 6.25rem;
  text-align: center;
}
.progress_card_lower_container {
  height: 100%;
  width: 100%;
  display: grid;
  grid-template-columns: 2fr 3.2fr;
  column-gap: 1.25rem;
  align-items: center;
}

.progress_card_image_container {
  height: 100%;
  height: 11.5rem;

  min-width: 5rem;
}

.progress_card_image_container img {
  height: 100%;
  width: 100%;
  object-fit: cover;
  border-radius: 0.75rem;
}

.progress_card_overviewcards_container {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: repeat(3, 1fr);
  width: 100%;
  row-gap: 0.5rem;
  column-gap: 1rem;
}

@media (max-width: 1536px) {
  .progress_card_outer_container {
    min-width: 22rem;
  }
  .progress_card_top_left_container {
    width: 70%;
  }
}

@media (max-width: 1290px) {
  .progress_card_outer_container {
    min-width: 22rem;
  }
}

/* styles for two dots starts by rattandeep singh */
.dots_container {
    cursor: pointer;
    DISPLAY: flex;
    align-items: center;

}

/* styles for two dots end by rattandeep singh */
/* styles for action card start by rattandeep singh */
.cat_popup {
  position: absolute;
  top: 100%;

  right: 0;

  z-index: 10;
  background-color: var(--white-50-background);
  color: #00000099 !important;

  padding: 1rem;

  border: 1px solid;
  border-radius: 20px;
  border-image-source: linear-gradient(
    130.72deg,
    rgba(237, 231, 231, 0.07) -22.43%,
    rgba(251, 251, 251, 0.05) 75.66%
  );
  backdrop-filter: blur(150px);

  box-shadow: 0px 4px 20px 0px #00000033;
  display: flex;
    flex-direction: column;
    row-gap: 0.25rem;
}

.cat_popup_view,
.cat_popup_edit,
.cat_popup_dlt {
  display: flex;
  /* padding: 0.6rem 0.7rem; */
  gap: 0.4rem;
  color: var(--text-black-60);
  align-items: center;
  background-color: var(--main_background);
  padding: 0.25rem;
  padding-right: 0.75rem;
  border-radius: 50px;
  transition: background-color 0.2s ease-in-out;
}

@keyframes animateIn {
  0% {
    width: 0rem;
    height: 0rem;
    left: 1.5rem;
  }
  20% {
    width: 2.25rem;
    height: 1.7rem;
    left: 1.3rem;
  }
  100% {
    width: calc(100% - 2.2rem);
    height: 2rem;
    left: 1.1rem;
  }
  }
  
  @keyframes animateOut {
  0% {
    width: calc(100% - 2.2rem);
    height: 2rem;
    left: 1.1rem;
  }
  40% {
    width: 2.25rem;
    height: 2rem;
    left: 1.3rem;
  }
  100% {
    width: 0rem;
    height: 0rem;
    left: 1.5rem;
  }
  } 


.cat_popup_viewicon {
  background: var(--primary_background);
  width: 28px;
  height: 28px;
  border-radius: 29px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.cat_popup_editicon {
  width: 28px;
  height: 28px;
  border-radius: 29px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: var(--extra_color);
  padding: 0.25rem;
  border-radius: 50px;
  position: relative;
  z-index: 2;
}

.cat_popup_transition_div_edit {
  position: absolute;
  height: 0rem;
  width: 0rem;
  border-radius: 100px;
  z-index: 1;
  background-color: var(--extra_color);
  transition: all 0.3s ease-in-out;
}

.cat_popup_edit>h4{
  position: relative;
  z-index: 2;
}

.cat_popup_edit.edit_hovered .cat_popup_transition_div_edit {
  animation: animateIn 0.3s ease-in-out forwards !important;
}

.cat_popup_edit.edit_notHovered .cat_popup_transition_div_edit {
  animation: animateOut 0.3s ease-in-out forwards !important;
}


.cat_popup_dlticon {
  background: #f6e6e6;
  width: 28px;
  height: 28px;
  border-radius: 29px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  z-index: 2;
}

.cat_popup_dlt>h4 {
  position: relative;
  z-index: 2;
}

.cat_popup_transition_div_delete {
  position: absolute;
  height: 0rem;
  width: 0rem;
  border-radius: 100px;
  z-index: 1;
  background-color:  #f6e6e6;
  transition: all 0.3s ease-in-out;
  
}

.cat_popup_dlt.dlt_hovered .cat_popup_transition_div_delete {
  animation: animateIn 0.3s ease-in-out forwards !important;
}

.cat_popup_dlt.dlt_notHovered .cat_popup_transition_div_delete {
  animation: animateOut 0.3s ease-out forwards !important;
}  

/* styles for action card end by rattandeep singh */
/* STYLES FOR DELETED PROJERCT CARD START BY RATTANDEEP SINGH */
.progress_card_outer_deleted_container {
  width: auto;
  border-radius: 2.3rem;
  position: relative;
  z-index: 0;
  padding: 0.3rem;
  background-color: var(--primary_background);
  overflow: hidden;
  box-shadow: var(--extra-shdow-second);
  color: var(--text-black-28);
}

/* STYLES FOR DELETED PROJERCT CARD END BY RATTANDEEP SINGH */
