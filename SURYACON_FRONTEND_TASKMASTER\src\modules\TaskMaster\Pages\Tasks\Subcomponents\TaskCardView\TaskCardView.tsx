// Author Name <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> task master category task card here

import React, {
  useCallback,
  useEffect,
  useLayoutEffect,
  useRef,
  useState,
} from "react";
import { useAppSelector } from "../../../../../../redux/hooks/Modules/Reduxhooks/ReduxHooks";
import { useLocation, useParams } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { Loader } from "../../../../../../assets/loader";
import { RootState } from "../../../../../../redux/store";
import {
  initializeDatabase,
  pathTableMapNested,
} from "../../../../../../functions/functions";
import {
  setCurrentCategoryId,
  setTaskLocalDb,
  updateTaskLocalDb,
} from "../../../../../../redux/features/Modules/TaskMaster/Slices/CategorySlice";
import { Card } from "../../../../../../components/Reusble/TaskMaster/Card";
import styles from "./TaskCardView.module.css";
import { useDeleteTaskMutation } from "../../../../../../redux/api/Modules/TaskMaster/TaskMasterapi";
import { DeletePopup } from "../../../../../../components/Reusble/Global/DeletePopup";
import {
  clearCategoryPopup,
  closePopup,
} from "../../../../../../redux/features/Modules/Reusble/popupSlice";
import { resetInputValues } from "../../../../../../redux/features/Modules/Reusble/floatinglabelslice";
import { usePouchSearch } from "../../../../../../functions/useLocalSearch";
import { useToast } from "../../../../../../hooks/ToastHook";
import { useNestedPouchSearch } from "../../../../../../functions/useNestedLocalSearch";
import { setSearchKey } from "../../../../../../redux/features/Modules/TaskMaster/Slices/TaskMasterSlice";
import {
  clearFetchedMasters,
  SetCategoryId,
  setFetchedMastersDesignation,
  setSearchData,
} from "../../../../../../redux/features/Modules/Masters";

const taskFields = [
  { label: "TaskName" },
  { label: "Description" },
  { label: "Quantity" },
  { label: "Unit" },
];

const TaskCardView: React.FC = () => {
  // setup for isweb or not detect by aayush
  // get cat Id from routing by aayush
  // detect changes in local db by aayush
  //deleted changes detect by aayush
  const showToast = useToast();
  const isWeb = useAppSelector((state) => state.auth.isWeb);
  const { catId } = useParams<{ catId: string }>();
  // const detectChanges = useAppSelector((state) => state.backupSlice.isOpen);
  const isDeletedNext = useAppSelector(
    (state) => state.isDeletedSLice.isDeletedNext
  );
  const [isLoading, setIsLoading] = useState(false);
  const dispatch = useDispatch();

  const categoryTaskData = useSelector(
    (state: RootState) => state.masterReduxSlice.fetchedData
  );
            const searchedData = useSelector((state: RootState) => state.masterReduxSlice.searchedData);

  const inputValues = useSelector(
    (state: RootState) => state.floatingLabel.inputValues
  );

  const searchKey = useAppSelector((state) => state.taskMaster.searchKey);
  const [taskSearchCategoriesData, setTaskSearchCategoriesData] = useState<any>(
    []
  );
  const [deleteTask] = useDeleteTaskMutation();
  const [searchLocalKey, setSearchLocalKey] = useState("");
  const currentOpenPopup = useAppSelector((state) => state.popup.popups);
  const [page, setPage] = useState(1);

  const isDeleteNext = useSelector(
    (state: RootState) => state.isDeletedSLice.isDeletedNext
  );

  useEffect(() => {
    setSearchLocalKey(searchKey);
  }, [searchKey]);

  useEffect(() => {
    dispatch(closePopup("DeletePopUp"));
    dispatch(resetInputValues());
    dispatch(clearCategoryPopup());
  }, []);

  const allFetchedTasksRef = useRef<any[]>([]);

  const saveData = useCallback(async () => {
    try {
      const dbName = await initializeDatabase("Taskmaster");

      if (!catId) return;

      const fetchedData = await window.electron.getDocumentByParentId({
        dbName,
        catId,
        categoryId: "categoryId",
        isDeletedNext,
        page,
        needSorting: true,
      });

      console.log("task data", fetchedData);
      if (page === 1) {
        allFetchedTasksRef.current = fetchedData;

        dispatch(
          setFetchedMastersDesignation({
            data: allFetchedTasksRef.current,
            page,
          })
        );
      } else {
        const newTasks = fetchedData.filter(
          (doc: any) =>
            !allFetchedTasksRef.current.some(
              (existing) => existing._id === doc._id
            )
        );

        allFetchedTasksRef.current = [
          ...allFetchedTasksRef.current,
          ...newTasks,
        ];

        dispatch(
          setFetchedMastersDesignation({
            data: allFetchedTasksRef.current,
            page,
          })
        );
      }
    } catch (error) {
      console.error("Error in saving data:", error);
    }
  }, [catId, isDeletedNext, page]);

  console.log("search key", searchKey);

  useEffect(() => {
    allFetchedTasksRef.current = [];
  }, [catId]);

  useEffect(() => {
    saveData();
  }, [isDeletedNext, page]);

  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const target = e.target as HTMLDivElement;

    if (target) {
      const { scrollHeight, clientHeight, scrollTop } = target;

      if (scrollTop + clientHeight >= scrollHeight - 1) {
        setPage((prev) => prev + 1);
      }
    }
  };

  useEffect(() => {
    return () => {
      dispatch(clearFetchedMasters());
    };
  }, []);

  useEffect(() => {
    dispatch(clearFetchedMasters());
  }, []);

  useEffect(() => {
    if (catId!) {
      dispatch(setCurrentCategoryId(catId));
      dispatch(SetCategoryId(catId as string));
    }
  }, [catId]);

  console.log("data for category cards", categoryTaskData);

  useEffect(() => {
    setPage(1);
  }, [isDeleteNext]);

  const location = useLocation();
  const currentPath = location.pathname;
  console.log(currentPath, "current path>>");

  useNestedPouchSearch({
    pathRecord: "Taskmaster",
    searchKey: searchLocalKey,
    setData: setSearchData,
    setPage,
    key: "taskname",
    extraSearchParams: {
      catId: catId!,
      categoryId: "categoryId",
      isDeletedNext: isDeletedNext,
    },
  });

  return (
    <>
      <div
        onScroll={(e) => handleScroll(e)}
        className={styles.taskContainer_items}
      >
        {/* {(!data || isLoading) &&
        // <img
        //   src={Loader}
        //   style={{ height: "200px", display: "flex" }}
        //   alt="Loading..."
        // />
        ""} */}
        {isLoading ? (
          <div className={styles.loader_loading}>
            <img
              src={Loader.suryaconLogo}
              alt="Loading..."
              className={styles.loader_loading_image}
            />
          </div>
        ) : categoryTaskData &&
          categoryTaskData.filter((task: any) =>
            isDeleteNext ? task?.isDeleted : !task?.isDeleted
          ).length > 0 ? (
          (searchedData && searchedData.length > 0
            ? searchedData
            : categoryTaskData || []
          )
            ?.filter((task: any) =>
              isDeleteNext ? task?.isDeleted : !task?.isDeleted
            )
            .map((data: any) => (
              <Card
                key={data._id}
                taskId={data._id}
                path={`/category/${catId}/task/${data._id}`}
                category={data}
                isTask={true}
                isApprove={true}
              />
            ))
        ) : (
          <div className={styles.loader_loading}>
            <img
              src={Loader.suryaconLogo}
              alt="Loading..."
              className={styles.loader_loading_image}
            />
          </div>
        )}
      </div>

      {currentOpenPopup["DeletePopUp"] && (
        <DeletePopup
          //this is callback for delete button
          callbackDelete={async () => {
            const res = await deleteTask({
              taskId: String(inputValues?.taskId),
            });

            if (res.error) {
              showToast({
                messageContent: "Oops! Something went wrong!",
                type: "danger",
              });

              return;
            }
            showToast({
              messageContent: `Task Deleted Successfully!`,
              type: "success",
            });
          }}
          //this is callback for cross button
          onClose={() => {
            dispatch(closePopup("DeletePopUp"));
            dispatch(resetInputValues());
          }}
          //this is header for the deletepopup
          height="calc(100% - 7.25rem)"
          heightupperlimit="0"
          header={`Are you sure you want to delete this Task?`}
          // height="calc(100% - 5.5rem)"
        >
          {/* here pass the children of the deletepopup */}
          <div style={{ display: "flex", flexWrap: "wrap" }}>
            {taskFields.map(
              (item) =>
                inputValues[item.label] && (
                  <div
                    key={item.label}
                    style={{
                      width:
                        inputValues["Unit"] &&
                        inputValues["Quantity"] &&
                        (item.label === "Unit" || item.label === "Quantity")
                          ? "50%"
                          : "100%",
                    }}
                  >
                    <div className={styles.summaryDivData}>
                      <div className={styles.summaryDataContent}>
                        <p
                          className="p_tag_14px_weight"
                          style={{ color: "#444444" }}
                        >
                          {item.label === "TaskName" ? "Name" : item.label}
                        </p>
                        <h4 style={{ color: "#191919", marginTop: "0.3rem" }}>
                          {inputValues[item.label]}
                        </h4>
                      </div>
                    </div>
                  </div>
                )
            )}
          </div>
        </DeletePopup>
      )}
    </>
  );
};

export default TaskCardView;
