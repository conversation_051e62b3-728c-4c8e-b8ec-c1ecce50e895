import React, { useEffect, useState } from "react";
import { useSocket } from "../SocketProvider";
import { TaskCategory } from "./TaskMasterBackup";
import FuncionMap from "./FunctionsMap/FunctionsMap";
import { useDispatch, UseSelector, useSelector } from "react-redux";
import { AppDispatch, RootState } from "../redux/store";
import PQueue from "p-queue";
import { useAppSelector } from "../redux/hooks/Modules/Reduxhooks/ReduxHooks";
import { setOpenDocData } from "../redux/features/Modules/Reusble/popupSlice";
import { saveSyncData } from "./BackupFunctions/BackupFunctions";
import { initializeDatabase } from "../functions/functions";
import { syncTableWithProgress } from "./BackupIntial";
import { Departments, Designations } from "./MastersBackup";

type PhotoRef = {
  photoRef: {
    photos: string[];
  };
};

type DataItem = Array<PhotoRef>;

export const Backup: React.FC = () => {
  const taskQueue = new PQueue({ concurrency: 1 });
  const { socket, isConnected } = useSocket();
  const { openDocData } = useSelector((state: RootState) => state.popup);
  const [backupdata, setBackupdata] = useState<any[]>([]);
  // const [isNowsave, setIsNowSave] = useState(false);
  const [backupTable, setBackupTable] = useState([]);

  const dispatch = useDispatch();
  // function to get initaldata from the socket and call the apis accordingly

  useEffect(() => {
    if (socket && isConnected) {
      console.log(socket, "socket commented");
      socket.on("updateDatabasedetails", (data) => {
        console.log(data, "this is data coming from socket");
        setBackupdata(data);
      });

      socket.on("openeddoc", (data) => {
        console.log("broadcast", data);
        dispatch(setOpenDocData(data));
      });

      socket.on("deletedphotos", (data) => {
        // console.log(data, "check for data here bro>>>>");
        // console.log("broadcast", data);
        // dispatch(setOpenDocData(data));
      });
    }
  }, [socket, isConnected]);

  useEffect(() => {}, [openDocData]);
  useEffect(() => {
    // console.log(backupdata, "chack for backup data");
    if (backupdata.length > 0) {
      for (const data of backupdata) {
        const fun = data.table;
        const functionName = FuncionMap[fun];

        if (functionName) {
          taskQueue.add(async () => {
            try {
              await functionName(
                dispatch as AppDispatch,
                data._id as string[],
                data.parentIds as string[],
                useSelector as UseSelector,
                false
              );
              if (data && data.parents) {
                const funCall = FuncionMap[data.parents];
                await funCall(
                  dispatch as AppDispatch,
                  data._id as string[],
                  data.parentIds as string[],
                  useSelector as UseSelector,
                  true
                );
              }
            } catch (error) {
              console.error(`Error processing ${fun}:`, error);
            }
          });
        } else {
          // console.log("No function found for this table:", fun);
        }
      }
    }
  }, [backupdata]);

  // const filterImages = async ({
  //   newImages,
  //   taskdata,
  // }: {
  //   newImages: DataItem;
  //   taskdata: DataItem;
  // }) => {
  //   const newSet: Set<string> = new Set(
  //     newImages?.flatMap((item: PhotoRef) => item?.photoRef?.photos)
  //   );
  //   const existingSet: Set<string> = new Set(
  //     taskdata?.flatMap((item: PhotoRef) => item?.photoRef?.photos)
  //   );
  //   const toDelete: string[] = [...existingSet].filter(
  //     (img) => !newSet.has(img)
  //   );
  //   const toAdd: string[] = [...newSet].filter((img) => !existingSet.has(img));

  //   const result = await window.electron.addNewImages(toAdd);
  // };

  useEffect(() => {
    if (backupTable.length > 0) {
      syncTableWithProgress(dispatch);
    }
  }, [backupTable]);

  return <></>;
};

export default Backup;
