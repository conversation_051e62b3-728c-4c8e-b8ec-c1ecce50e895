import React from "react";
import styles from "./Styles/SendApprovalConfirmationForm.module.css";
import FormChildTemplate from "../../../Masters/Department/Subcomponents/FormChildTemplate";

interface Task {
  id: string;
  name: string;
  description?: string;
  progressPercentage?: number;
}

interface Props {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  tasks: Task[];
}

const SendApprovalConfirmationForm: React.FC<Props> = ({
  isOpen,
  onClose,
  onConfirm,
  tasks,
}) => {
  if (!isOpen) return null;

  console.log(tasks, "mahesh check kr 3");

  const formData = { tasks: tasks.map((task) => task.name) };
  const formDataLabels = { tasks: "Tasks" };
  const initialFormData = { tasks: [] };

  return (
    <div className={styles.confirmation_modal_overlay}>
      <div className={styles.confirmation_modal}>
        <div className={styles.confirmation_modal_header}>
          <button
            className={styles.closeButton}
            onClick={onClose}
            aria-label="Close"
          >
            ×
          </button>
          <h3>Are you sure you want to send following tasks for approval?</h3>
        </div>
        <div className={styles.confirmation_modal_content}>
          <FormChildTemplate
            formData={formData}
            formDataLabels={formDataLabels}
            initialFormData={initialFormData}
            formValueListStyle={{ display: "flex", flexDirection: "column" }}
          />
        </div>
        <div className={styles.button_row}>
          <button className={styles.cancel_btn} onClick={onClose}>
            Cancel
          </button>
          <button className={styles.confirm_btn} onClick={onConfirm}>
            Send Approval
          </button>
        </div>
      </div>
    </div>
  );
};

export default SendApprovalConfirmationForm;
