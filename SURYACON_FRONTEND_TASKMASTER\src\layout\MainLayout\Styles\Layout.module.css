.layout_container {
  grid-auto-flow: column;
  height: 100vh;
  width: 100%;
  position: relative;

  grid-template-areas:
    "topbar"
    "main-content";
}

.sidebar {
  width: 250px;

  margin-right: 1rem;
  overflow: hidden;
}

.main_content {
  width: 100%;
  grid-area: main-content;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  /* width: calc(100% - 250px); */
  min-height: 86vh;
  position: relative;
}

.topbar {
  /* height: 7rem; */
  gap: 1rem;
  grid-area: topbar;
  display: flex;
  width: 100vw;
  color: var(--primary_color);
  padding: 0rem 1.5rem 0rem 0rem;
}

.content {
  flex-grow: 1;
  /* remove if needed then again uncommenct for proejct planning page  */
  /* overflow-y: auto; */
  scrollbar-width: none;
  -ms-overflow-style: none;
  margin: 0.5rem;
  position: relative;
}

.content::-webkit-scrollbar {
  display: none;
}

/* @media only screen and (max-width: 1024px) {
  .layout_container {
    display: none !important;
  }
} */

.topbar<PERSON>ogo {
  margin-left: 2rem;
}

@media (max-width: 1200px) {
  .content {
    /* padding: 1.5rem 1.5rem; */
    overflow-x: scroll;
  }
}
