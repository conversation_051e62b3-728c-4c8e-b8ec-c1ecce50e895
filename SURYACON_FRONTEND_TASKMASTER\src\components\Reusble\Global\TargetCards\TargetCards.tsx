import React from "react";
import Styles from "./Styles/TargetCards.module.css";

import { TargetCardsProps } from "../../../Reusble/Billing/interfaces/interface";
import MonthlyTargetCard from "../../../../modules/Billing/Pages/BillingApproval/Subcomponents/MonthlyTargetCard";

const TargetCards: React.FC<TargetCardsProps> = ({ edit, data = [] }) => {
  return (
    <div className={Styles.mt_cards_container}>
      {data.map((material, index) => (
        <MonthlyTargetCard
          _id={material?._id}
          isAllowed={true}
          type={material?.name}
          quantity={material?.quantity}
          brand={material?.Brandname}
          property={material?.spec}
          edit={edit}
          unit={material?.unit?.[0]}
          key={material?._id || index}
        />
      ))}
    </div>
  );
};

export default TargetCards;
