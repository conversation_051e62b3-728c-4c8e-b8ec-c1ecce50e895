import { createSlice,PayloadAction } from "@reduxjs/toolkit";

const initialState={
    src:null
}


const localImageSlice=createSlice({
    name:'localImageSlice',
     initialState:initialState,
     reducers:{
       setImgSrc:(state,action:PayloadAction<any>)=>{
        state.src=action.payload;
       }
     }
     
})


export const {setImgSrc}=localImageSlice.actions;
export default localImageSlice.reducer;
