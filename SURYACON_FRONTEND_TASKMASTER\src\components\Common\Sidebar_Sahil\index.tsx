import React, { useState, useCallback, memo, useMemo, useEffect } from 'react';
import { useDispatch } from "react-redux";
import { useLocation, useNavigate, useParams } from 'react-router-dom';
import { DropDownArrowDownIcon, DropDownArrowUpIcon } from '../../../assets/icons';
import { currentActiveSubRouteIndex } from "../../../redux/features/Modules/Reusble/sidebarSlice";
import styles from "./Styles/sidebar_sahil.module.css";

// Define your custom route interface here
export interface CustomRoute {
  label: string;
  route: string;
  icon?: React.ElementType;
  section?: 'main' | 'other';
}

interface SidebarProps {
  onRouteChange?: (route: string) => void;
  mainRoutes: CustomRoute[];
  otherRoutes?: CustomRoute[];
  defaultRoute?: string;
  projectId?: string;
  baseUrl: string; // Base URL for navigation (e.g., '/billing/main')
  projectTitle?: string; // Project title to display
  showProjectSelector?: boolean; // Whether to show the project selector
}

const ReusableSidebar = ({
  onRouteChange,
  mainRoutes,
  otherRoutes = [],
  defaultRoute = 'summary',
  projectId,
  baseUrl,
  projectTitle = "Select Project",
  showProjectSelector = true
}: SidebarProps) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  
  const [cpExpanded, setCpExpanded] = useState(true);
  const [selectedRoute, setSelectedRoute] = useState(defaultRoute);
  
  // Initial navigation to default route if needed
  useEffect(() => {
    // Only perform initial navigation if projectId is provided
    if (projectId) {
      // Check if we're at the root project route with no sub-route
      const baseProjectPath = `${baseUrl}/${projectId}`;
      if (location.pathname === baseProjectPath || location.pathname === `${baseProjectPath}/`) {
        // Navigate to default route
        navigate(`${baseProjectPath}/${defaultRoute}`);
        onRouteChange?.(defaultRoute);
      }
    }
  }, [projectId, navigate, onRouteChange, location.pathname, baseUrl, defaultRoute]);

  // Determine the current route from the URL
  useEffect(() => {
    const path = location.pathname;
    
    // Special handling for specific routes (like planning) if needed
    const specialRouteHandling = mainRoutes.find(route => 
      route.route === 'planning' && path.includes('/location/')
    );
    
    if (specialRouteHandling) {
      setSelectedRoute(specialRouteHandling.route);
      return;
    }
    
    // Check all routes to find which one matches the current path
    const allRoutes = [...mainRoutes, ...otherRoutes];
    const currentRoute = allRoutes.find(item => path.includes(`/${item.route}`));
    
    if (currentRoute) {
      setSelectedRoute(currentRoute.route);
      
      // Also update Redux if it's a main menu item
      if (currentRoute.section === 'main') {
        const index = mainRoutes.findIndex(r => r.route === currentRoute.route);
        if (index !== -1) {
          dispatch(currentActiveSubRouteIndex(index));
        }
      }
    }
  }, [location, mainRoutes, otherRoutes, dispatch]);

  const toggleCpExpanded = useCallback(() => {
    setCpExpanded(prev => !prev);
  }, []);

  const handleNavigation = useCallback((route: string) => {
    // Update selected route state
    setSelectedRoute(route);
    
    // Update parent component
    onRouteChange?.(route);
    
    // Handle special case for planning route
    if (projectId) {
      // Check if this is a special route that requires custom URL format
      const isSpecialRoute = route === 'planning';
      
      if (isSpecialRoute) {
        navigate(`${baseUrl}/${projectId}/location/${projectId}`);
      } else {
        navigate(`${baseUrl}/${projectId}/${route}`);
      }
    }
  }, [baseUrl, navigate, onRouteChange, projectId]);

  return (
    <div className={styles.sidebar_container}>
      {showProjectSelector && (
      <div className={styles.cp_selector} onClick={toggleCpExpanded}>
        <span>
        {projectTitle.length > 18 ? projectTitle.slice(0, 18) + '…' : projectTitle}
        </span>
        {cpExpanded ? <DropDownArrowDownIcon /> : <DropDownArrowUpIcon />}
      </div>
      )}

      {/* Main menu items */}
      {cpExpanded && (
      <div className={styles.menu_items_container}>
        {mainRoutes.map((item: CustomRoute) => (
        <SidebarItem
          key={`route-${item.route}`}
          label={item.label}
          Icon={item.icon || DefaultIcon}
          isSelected={selectedRoute === item.route}
          onclick={() => handleNavigation(item.route)}
        />
        ))}
      </div>
      )}

      {/* Others Section - only show if there are items */}
      {otherRoutes.length > 0 && (
      <div className={styles.others_section}>
        <div className={styles.section_title}>Others</div>
        {otherRoutes.map((item) => (
        <SidebarItem
          key={`other-${item.route}`}
          label={item.label}
          Icon={item.icon || DefaultIcon}
          isSelected={selectedRoute === item.route}
          onclick={() => handleNavigation(item.route)}
        />
        ))}
      </div>
      )}
    </div>
  );
};

// Default icon component when none is provided
const DefaultIcon: React.FC<{ className?: string, color?: string }> = ({ className, color }) => (
  <div 
    className={className} 
    style={{ 
      width: '20px', 
      height: '20px', 
      backgroundColor: color || 'currentColor',
      borderRadius: '50%'
    }}
  />
);

interface ExtendedSidebarItemProps {
  label: string;
  Icon: React.ElementType;
  isSelected: boolean;
  onclick: () => void;
}

// Memoize SidebarItem to prevent unnecessary re-renders
const SidebarItem = memo(({
  label,
  Icon,
  isSelected,
  onclick,
}: ExtendedSidebarItemProps) => {
  return (
    <div
      onClick={onclick}
      className={`${styles.sidebar_item} ${isSelected ? styles.selected : ''}`}
    >
      <Icon className={styles.icon} color={isSelected ? "rgba(255, 255, 255, 1)" : "rgba(0, 0, 0, 0.87)"} />
      <span>{label}</span>
    </div>
  );
});

SidebarItem.displayName = 'SidebarItem';

export default memo(ReusableSidebar);