import React from "react";
import { useDispatch, useSelector } from "react-redux";
import styles from "../Styles/MonthlyTarget.module.css";
import TargetBadge from "../../../../../components/Reusble/Global/TargetBadge/TargetBadge";
import { AddIcon } from "../../../../../assets/icons";
import MonthlyTargetCreationForm from "./MTcreation";
import { RootState } from "../../../../../redux/store";
import { closePopup, openPopup, togglePopup } from "../../../../../redux/features/Modules/Reusble/popupSlice";
import AddCategoryType from "../../../../../components/Reusble/Global/AddCategoryType";
import { addTasks, updateTaskDetails, setAvailableSubtasks } from "../../../../../redux/features/Modules/Billing/BillingApproval/Slices/MonthlyTargetSlice";
import { setToast } from "../../../../../redux/features/Modules/Reusble/ToastSlice";
import { initializeDatabase } from "../../../../../functions/functions";

const MTcreationHeader: React.FC = () => {
  const dispatch = useDispatch();
  
  // Redux selectors
  const currentOpenPopup = useSelector((state: RootState) => state.popup.popups);
  const {
    availableTasks,
    availableSubtasks,
    activeTaskDetails,
    selectedTasks,
    taskDetails,
    isSubtaskForm: isAddingSubtasks,
  } = useSelector((state: RootState) => state.monthlyTargetForm);

  // Utility functions
  const showToast = (messageContent: string, type: "success" | "error" | "warning" | "info") => {
    dispatch(setToast({ isOpen: true, messageContent, type }));
  };

  const extractId = (item: any) => item._id || item.id;
  const extractTaskId = (task: any) => task.towerRouteId || extractId(task);
  const extractName = (item: any) => item.name || item.task_name || item.categoryName || item.category;

  const getAlreadySelectedIds = () => {
    return isAddingSubtasks 
      ? taskDetails[activeTaskDetails]?.subtasks?.map(st => st.id) || []
      : selectedTasks.map(task => task.id);
  };

  const filterAvailableItems = (items: any[], selectedIds: string[]) => {
    return items.filter(item => {
      const id = isAddingSubtasks ? extractId(item) : extractTaskId(item);
      return !selectedIds.includes(id);
    });
  };

  const formatItemForSelection = (item: any) => {
    if (isAddingSubtasks) {
      return {
        id: extractId(item),
        category: extractName(item),
        parent_task_id: activeTaskDetails,
        originalTask: item,
      };
    }
    
    return {
      id: extractTaskId(item),
      category: extractName(item),
      towerRouteId: extractTaskId(item),
      Tower_id: item.Tower_id,
      originalTask: item,
    };
  };

  // Get the appropriate data for the selection form
  const getSelectionData = () => {
    const items = isAddingSubtasks ? availableSubtasks : availableTasks;
    const itemType = isAddingSubtasks ? "subtasks" : "tasks";
    
    console.log(`[getSelectionData] Mode: Adding ${itemType}`, items);

    if (!items?.length) {
      console.log(`[getSelectionData] No available ${itemType} found`);
      return [];
    }

    const selectedIds = getAlreadySelectedIds();
    console.log(`[getSelectionData] Already selected ${itemType} IDs:`, selectedIds);

    const filteredItems = filterAvailableItems(items, selectedIds);
    const result = filteredItems.map(formatItemForSelection);
    
    console.log(`[getSelectionData] Final ${itemType} for selection:`, result);
    return result;
  };

  // Event handlers
  const handleTargetClick = () => {
    dispatch(togglePopup("MonthlyTargetCreationForm"));
  };

  const handleTaskSelect = (selectedItems: any[]) => {
    console.log("Selected items:", selectedItems, "Is adding subtasks:", isAddingSubtasks);

    if (isAddingSubtasks) {
      handleSubtaskSelection(selectedItems);
    } else {
      handleTaskSelection(selectedItems);
    }

    handleAddSubFormClose();
  };

  const handleSubtaskSelection = (selectedItems: any[]) => {
    if (!activeTaskDetails) {
      console.error("No active task selected for adding subtasks");
      showToast("Error: No active task selected", "error");
      return;
    }

    const formattedSubtasks = selectedItems.map(subtask => ({
      id: extractId(subtask),
      name: subtask.category || extractName(subtask),
      isSelected: true,
      parent_task_id: activeTaskDetails,
    }));

    console.log("Formatted subtasks:", formattedSubtasks);

    const currentSubtasks = taskDetails[activeTaskDetails]?.subtasks || [];
    const updatedSubtasks = [...currentSubtasks, ...formattedSubtasks];

    dispatch(updateTaskDetails({
      taskId: activeTaskDetails,
      details: { subtasks: updatedSubtasks },
    }));

    showToast(`${formattedSubtasks.length} subtasks added successfully`, "success");
  };

  const handleTaskSelection = (selectedItems: any[]) => {
    const formattedTasks = selectedItems.map(task => {
      const routeId = task.towerRouteId || extractId(task);

      return task.originalTask ? {
        ...task.originalTask,
        _id: routeId,
        task_id: routeId,
        towerRouteId: routeId,
        is_selected: true,
        status: "pending",
        floor_type: "regular",
      } : {
        _id: routeId,
        name: task.category,
        task_name: task.category,
        towerRouteId: routeId,
        Tower_id: task.Tower_id,
        is_selected: true,
        status: "pending",
        floor_type: "regular",
      };
    });

    dispatch(addTasks(formattedTasks));
    showToast(`${formattedTasks.length} tasks added successfully`, "success");
  };

  const handleAddSubFormClose = () => {
    dispatch(closePopup("AddSubForm"));
    dispatch(openPopup("MonthlyTargetCreationForm"));
  };

  const handleMainFormClose = () => {
    dispatch(closePopup("MonthlyTargetCreationForm"));
  };

  const fetchMoreSubtasks = async () => {
    if (!activeTaskDetails) {
      showToast("No active task selected", "error");
      return;
    }

    showToast("Fetching more subtasks...", "info");

    try {
      const dbname = await initializeDatabase("SubTasksBasicDetails");
      
      // Try to get task details for broader search
      const taskDetails = await window.electron.getDocumentById({
        dbName: "TaskBasicDetails",
        id: activeTaskDetails,
      });
      
      let additionalSubtasks = [];
      
      // Try category-based search first
      if (taskDetails?.categoryId) {
        additionalSubtasks = await window.electron.getDocumentByParentId({
          categoryId: "categoryId",
          dbName: dbname,
          catId: taskDetails.categoryId,
          isDeletedNext: false,
        });
      }
      
      // Fallback to general search
      if (!additionalSubtasks?.length) {
        additionalSubtasks = await window.electron.getAllDocuments({
          dbName: dbname,
          isDeletedNext: false,
        });
        additionalSubtasks = additionalSubtasks.slice(0, 20); // Limit results
      }
      
      // Filter out existing subtasks
      const existingIds = availableSubtasks.map(extractId);
      const newSubtasks = additionalSubtasks.filter(s => !existingIds.includes(extractId(s)));
      
      if (!newSubtasks.length) {
        showToast("No additional subtasks found", "info");
        return;
      }
      
      // Update available subtasks
      dispatch(setAvailableSubtasks([...availableSubtasks, ...newSubtasks]));
      showToast(`${newSubtasks.length} additional subtasks found`, "success");
      
      // Refresh the popup
      dispatch(closePopup("AddSubForm"));
      setTimeout(() => dispatch(openPopup("AddSubForm")), 300);
      
    } catch (error) {
      console.error("Error fetching more subtasks:", error);
      showToast("Failed to fetch more subtasks", "error");
    }
  };

  return (
    <div className={styles.monthly_target_creation_header}>
      <div className={styles.monthly_target_creation_header_buttons_rhs}>
        <TargetBadge
          backgroundColor="var(--primary_color)"
          outerContainerClassName="monthly_target_header_buttons"
          valueTextClassName="approval_button_text"
          value="Target"
          onClick={handleTargetClick}
          icon={<AddIcon />}
        />
      </div>

      {currentOpenPopup["MonthlyTargetCreationForm"] && (
        <MonthlyTargetCreationForm onClose={handleMainFormClose} />
      )}

      {currentOpenPopup["AddSubForm"] && (
        <AddCategoryType
          title={isAddingSubtasks ? "Add Subtasks" : "Add Tasks"}
          data={getSelectionData()}
          isUnit={false}
          initialSelected={[]}
          label={isAddingSubtasks ? "Subtasks" : "Tasks"}
          placeholder="Search"
          buttonLabel={isAddingSubtasks ? "Add Subtasks" : "Add Tasks"}
          onSelect={handleTaskSelect}
          onClose={handleAddSubFormClose}
          singleSelected={false}
        />
      )}
    </div>
  );
};

export default MTcreationHeader;