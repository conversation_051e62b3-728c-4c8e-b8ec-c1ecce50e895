import { useCallback, useEffect, useRef, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { CloseIcon, SuryconLogo } from "../../../../../../assets/icons";
import Button from "../../../../../../components/Reusble/Global/Button";
import FloatingLabelInput from "../../../../../../components/Reusble/Global/FloatingLabel";
import { getValue } from "../../../../../../functions/functions";
import { useToast } from "../../../../../../hooks/ToastHook";
import { useApproveDepartmentOrDesignationMutation } from "../../../../../../redux/api/Modules/Billing/Billingapi";
import { resetInputValues } from "../../../../../../redux/features/Modules/Reusble/floatinglabelslice";
import { setFormMode } from "../../../../../../redux/features/Modules/TaskMaster/Slices/MastersSlice";
import { RootState } from "../../../../../../redux/store";
import FormChildTemplate from ".././Subcomponents/FormChildTemplate";
import styles from "../Styles/Department.module.css";

interface DepartmentApprovalFormProps {
  isClosing: boolean;
  setIsClosing?: (value: boolean) => void;
  handleClose: () => void;
}

export function DepartmentApprovalForm({
  isClosing = true,
  handleClose,
  setIsClosing,
}: DepartmentApprovalFormProps) {
  const [declineReason, setDeclineReason] = useState({
    value: "",
    error: false,
  });
  const formRef = useRef<HTMLDivElement>(null);
  const navigateArray = useSelector(
    (state: RootState) => state.navigateData.navigateArray
  );
  const data = useSelector((state: RootState) => state.masterForm.mdStateData);
  const formMode = useSelector((state: RootState) => state.masterForm.formMode);
  const isDepartment = navigateArray.length > 1 ? false : true;
  const dispatch = useDispatch();
  const showToast = useToast();
  const [approveDepartmentOrDesignation] =
    useApproveDepartmentOrDesignationMutation();

  const formLabel: Record<string, string> = isDepartment
    ? {
        departmentName: "Department Name",
        departmentHead: "Department Head",
        description: "Description",
        email: "Email",
      }
    : {
        designationName: "Designation Name",
        jobDescription: "Job Description",
        requiredExperience: "Required Experience",
        location: "Location",
        qualifications: "Qualifications",
        supervisor: "Supervisor",
      };

  const formData = isDepartment
    ? {
        departmentName: getValue(data, "name"),
        departmentHead: getValue(data, "DepartmentHead"),
        description: getValue(data, "Description"),
        email: getValue(data, "Email")?.toLocaleLowerCase(),
      }
    : {
        designationName: getValue(data, "name"),
        jobDescription: getValue(data, "Description"),
        requiredExperience: getValue(data, "Experience"),
        location: getValue(data, "location"),
        qualifications:
          getUnionTwoArray(
            data?.RequiredQualification,
            data?.updatedData?.RequiredQualification
          ) || getValue(data, "RequiredQualification"),
        supervisor: getValue(data, "ReporterId") || null,
      };

  useEffect(() => {
    if (isClosing) return;

    document.addEventListener("mousedown", outSideClickHandler);
    return () => {
      document.removeEventListener("mousedown", outSideClickHandler);
    };
  }, [isClosing, outSideClickHandler]);

  useEffect(() => {
    if (!isClosing && formRef.current) {
      formRef.current.focus(); // <-- Add this line to set focus
    }
  }, [isClosing, formMode]);

  const onClose = () => {
    handleClose();
  };

  function getUnionTwoArray<T extends string>(
    array1: T[] = [],
    array2: T[] = []
  ): T[] {
    const seen = new Set<T>();
    const result: T[] = [];

    for (const val of array1) {
      if (!seen.has(val)) {
        seen.add(val);
        result.push(val);
      }
    }

    for (const val of array2) {
      if (!seen.has(val)) {
        seen.add(val);
        result.push(val);
      }
    }

    return result;
  }

  const handleBack = () => {
    // dispatch(setApprovalFormStep("initial"));
    dispatch(setFormMode("INITIAL"));
  };

  const validateIsError = () => {
    if (!declineReason.value || !declineReason.value.trim()) {
      setDeclineReason((prev) => ({ ...prev, error: true }));
      return true;
    }
    return false;
  };

  const approveAndDeclineHandler = async () => {
    const isApprove = formMode === "APPROVE";
    if (!isApprove && validateIsError()) {
      showToast({
        messageContent: "Please provide a reason for declining.",
        type: "Warning",
      });
      return;
    }
    const payload = {
      id: data?._id,
      tablename: isDepartment ? "Departmentmaster" : "Designationmaster",
      isApprove: isApprove,
      comment: declineReason.value || "",
    };

    try {
      const response = await approveDepartmentOrDesignation(payload).unwrap();
      if (response.success) {
        const message = `${isDepartment ? "Department" : "Designation"} ${
          isApprove ? "approved" : "declined"
        } successfully`;
        showToast({
          messageContent: response?.data || message,
          type: "success",
        });
        onClose();
        dispatch(resetInputValues());
      }
    } catch (err) {
      showToast({
        messageContent:
          err?.data?.message ||
          `Failed to ${isApprove ? "approve" : "decline"} request`,
        type: "danger",
      });
      console.error("response Api Decline error:", err);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if(isClosing) return;
    if (e.key === "Enter" && e.shiftKey) return;
    if (e.key === "Enter") {
      e.preventDefault();
      e.stopPropagation();
      if (formMode === "INITIAL") {
        // dispatch(setApprovalFormStep("approve"));
        dispatch(setFormMode("APPROVE"));
      } else {
        approveAndDeclineHandler();
      }
    } else if (e.key === "Escape") {
      e.preventDefault();
      e.stopPropagation();
      if (formMode === "INITIAL") onClose();
      else {
        // dispatch(setApprovalFormStep("decline"));
        dispatch(setFormMode("INITIAL"));
      }
    } else {
      if (e.key === "Delete") {
        e.preventDefault();
        e.stopPropagation();
        dispatch(setFormMode("DECLINE"));
      }
    }
  };

  const formUpdatedField = useCallback(
    (
      field: string,
      value?: string
    ): "default" | "updated" | "discard" | undefined => {
      if (!data || !data.updatedData) return "default";

      const keyMapping = new Map();
      keyMapping.set(
        `${isDepartment ? "departmentName" : "designationName"}`,
        "name"
      );
      keyMapping.set("departmentHead", "DepartmentHead");
      keyMapping.set("description", "Description");
      keyMapping.set("jobDescription", "Description");
      keyMapping.set("email", "Email");
      keyMapping.set("requiredExperience", "Experience");
      keyMapping.set("location", "location");
      keyMapping.set("qualifications", "RequiredQualification");
      keyMapping.set("supervisor", "ReporterId");

      const updatedData = data.updatedData;
      const key = keyMapping.get(field) as keyof typeof data;
      const currentValue = data[key];
      const originalValue = updatedData?.[key];

      // 1. Check if updatedData has the field
      if (!(key in updatedData)) return "default";

      // 2. Array field check (value is item in array)
      if (Array.isArray(currentValue)) {
        const currentArray = currentValue as unknown[];
        const originalArray = Array.isArray(originalValue)
          ? (originalValue as unknown[])
          : [];

        if (!value) return "default";

        const indexInOriginal = originalArray.findIndex(
          (item) => item === value
        );
        const indexInCurrent = currentArray.findIndex((item) => item === value);

        if (indexInOriginal === -1 && indexInCurrent !== -1) return "discard"; // removed
        if (indexInOriginal !== -1 && indexInCurrent === -1) return "updated"; // added

        return "default";
      }

      // 3. Object field with _id check
      if (
        typeof currentValue === "object" &&
        currentValue !== null &&
        "_id" in currentValue
      ) {
        const currentId = (currentValue as any)?._id;
        const originalId = (originalValue as any)?._id; 

        if (originalId && currentId !== originalId) return "updated";
        return "default";
      }

      // 4. Primitive check (string, number, boolean, etc.)
      const current = String(currentValue ?? "").trim();
      const original = String(originalValue ?? "").trim();

      if (current !== original) return "updated";

      return "default";
    },
    [data]
  );

  // Handles clicks outside the form modal to trigger onClose if certain conditions are met
  function outSideClickHandler(event: MouseEvent) {
    console.log("Out Side click");
    event.stopPropagation();
    if (formRef.current && !formRef.current.contains(event.target as Node)) {
      // Maintains focus on form when clicking outside
      if (formRef.current) {
        event.preventDefault();
        formRef.current.focus();
      }

      // Only close if not in discard/summary mode and form is empty
      if (formMode === "DECLINE" && !validateIsError()) {
        return;
      } else {
        onClose();
      }
    }
  }

  return (
    <div className={styles.departmentApprovalForm_overlay}>
      <div
        className={`${styles.department_container} ${
          isClosing ? styles.closing : ""
        }`}
        onKeyDown={handleKeyDown}
        ref={formRef}
        tabIndex={-1}
      >
        <div className={styles.department_header}>
          <div
            className={`${styles.department_title} ${
              formMode === "DECLINE" ? styles.department_header_decline : ""
            }`}
          >
            {/* {getHeader()} */}
            {formMode === "APPROVE"
              ? `Are you sure you want to approve this ${
                  isDepartment ? "Department" : "Designation"
                } request?`
              : formMode === "DECLINE"
              ? `Are you sure you want to decline this ${
                  isDepartment ? "Department" : "Designation"
                } request?`
              : "Take Action"}
          </div>
          <button className={styles.department_closeButton} onClick={onClose}>
            <CloseIcon />
          </button>
        </div>

        <div className={styles.department_datainputs}>
          {formMode === "DECLINE" && (
            <>
              <FloatingLabelInput
                id="reasonInput"
                label="Reason"
                type="text"
                value={declineReason.value}
                onInputChange={(val: String | Number) =>
                  setDeclineReason({ value: String(val), error: false })
                }
                props="one_line"
                error={declineReason.error}
                focusOnInput={true}
              />

              <div className={styles.department_line_container}>
                <span className={styles.department_dottedline_wrapper}></span>
                <SuryconLogo />
                <span className={styles.department_dottedline_wrapper}></span>
              </div>
            </>
          )}

          <FormChildTemplate
            formData={formData}
            formDataLabels={formLabel}
            key={"Md-Form"}
            formUpdatedField={formUpdatedField}
          />
        </div>

        <div className={styles.department_btngrp}>
          {formMode === "INITIAL" ? (
            <>
              <Button
                type="Decline2"
                Content="Decline"
                Callback={() => dispatch(setFormMode("DECLINE"))}
              />
              <Button
                type="Next"
                Content="Approve"
                Callback={() => dispatch(setFormMode("APPROVE"))}
              />
            </>
          ) : (
            <>
              <Button type="Cancel" Content="Back" Callback={handleBack} />
              <Button
                type="Next"
                Content="Submit"
                Callback={approveAndDeclineHandler}
              />
            </>
          )}
        </div>
      </div>
    </div>
  );
}
