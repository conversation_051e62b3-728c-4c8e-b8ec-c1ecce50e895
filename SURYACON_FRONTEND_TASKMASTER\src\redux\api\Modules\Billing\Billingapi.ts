import { baseApi } from "../../index";
import {
  AddTowerLocation,
  ApiResponse,
  Responses,
} from "./Interfaces/BillingInterfaces";
import { url } from "../../../../config/urls";
import { method } from "lodash";
import { AnyIfEmpty } from "react-redux";

export const BillingApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    addProject: builder.mutation<ApiResponse<null>, FormData>({
      query: (projectData) => ({
        url: `${url}/billingmaster/ProjectData/ProjectMaster/registernewproject`,
        method: "POST",
        body: projectData,
      }),
      // invalidatesTags: [],
    }),
    // delete location by project id

    AddTowerLocation: builder.mutation<ApiResponse<null>, FormData>({
      query: (towerLocationData) => ({
        url: `${url}/billingmaster/ProjectData/locations/addTowerlocation`,
        method: "POST",
        body: towerLocationData,
      }),
      invalidatesTags: [],
    }),
    getAllProject: builder.query<Responses, string>({
      query: (time: string) => ({
        url: `${url}/billingmaster/ProjectData/ProjectMaster/getAllprojectsDetail?sincetime=${time}`,
        method: "GET",
      }),
      // providesTags: ["Project"],
    }),
    addTowerLocation: builder.mutation<
      ApiResponse<AddTowerLocation>,
      AddTowerLocation
    >({
      query: (towerData) => ({
        url: `${url}/billingmaster/ProjectData/locations/addTowerlocation`,
        method: "POST",
        body: towerData,
      }),
    }),
    addToolDesignation: builder.mutation({
      query: (toolData) => {
        const formData = new FormData();

        formData.append("type", toolData?.type);
        formData.append("name", toolData?.name);
        formData.append("Description", toolData?.Description);
        formData.append("Brand", JSON.stringify(toolData?.Brand));
        formData.append(
          "DesignationId",
          JSON.stringify(toolData?.DesignationId)
        );
        formData.append("ToolCategoryId", toolData?.ToolCategoryId);
        formData.append("images", toolData?.images);

        return {
          url: `${url}/billingmaster/tools/registerToolDesigination`,
          method: "POST",
          body: formData,
        };
      },
      // invalidatesTags: ["ToolDesignation"],
    }),
    // get task details from tower routes start

    // get task details from tower routes end

    updateToolDesignation: builder.mutation({
      query: (toolData) => {
        const formData = new FormData();

        formData.append("_id", toolData?._id);
        formData.append("type", toolData?.type);
        formData.append("name", toolData?.name);
        formData.append("Description", toolData?.Description);
        formData.append("Brand", JSON.stringify(toolData?.Brand));
        formData.append(
          "DesignationId",
          JSON.stringify(toolData?.DesignationId)
        );
        // formData.append("ToolCategoryId", toolData?.ToolCategoryId);
        formData.append(
          "images",
          typeof toolData?.images === "string"
            ? JSON.stringify([toolData?.images])
            : toolData?.images
        );

        return {
          url: `${url}/billingmaster/tools/updateToolDesiginationById`,
          method: "PUT",
          body: formData,
        };
      },
      // invalidatesTags: ["ToolDesignation"],
    }),
    addMaterialDesignation: builder.mutation({
      query: (materialData) => {
        const formData = new FormData();

        formData.append("name", materialData?.name);
        formData.append("Description", materialData?.Description);
        formData.append("Brand", JSON.stringify(materialData?.Brand));
        formData.append("unit", JSON.stringify(materialData?.unit));
        formData.append("materialCategoryId", materialData?.materialCategoryId);
        formData.append("images", materialData?.images);

        return {
          url: `${url}/billingmaster/material/addMaterialDesigination`,
          method: "POST",
          body: formData,
        };
      },
      // invalidatesTags: ["MaterialDesignation"],
    }),
    addMachineryDesignation: builder.mutation({
      query: (machineryData) => {
        const formData = new FormData();

        formData.append("name", machineryData?.name);
        formData.append("Description", machineryData?.Description);
        formData.append("Brand", JSON.stringify(machineryData?.Brand));
        formData.append("Fueltype", machineryData?.Fueltype);
        formData.append(
          "machineryCategoryId",
          machineryData?.machineryCategoryId
        );
        formData.append("images", machineryData?.images);
        formData.append("tools", JSON.stringify(machineryData?.tools));

        return {
          url: `${url}/billingmaster/machinery/registerMachineryDesigination`,
          method: "POST",
          body: formData,
        };
      },
      // invalidatesTags: ["MachineryDesignation"],
    }),
    // get particular towerlocationdetailbyId
    getTowerLocationbyTowerID: builder.query({
      query: ({ towerId }) => ({
        url: `${url}/billingmaster/ProjectData/locations/getTowerlocation?project_id=${projectId}&category=${category}&isDeleted=${isDeleted}`,
        method: "GET",
      }),
    }),
    // udpateMachineryDesignation
    updateMachineryDesignation: builder.mutation({
      query: (machineryData) => {
        const formData = new FormData();

        formData.append("_id", machineryData?._id);
        formData.append("name", machineryData?.name);
        formData.append("Description", machineryData?.Description);
        formData.append("Brand", JSON.stringify(machineryData?.Brand));
        formData.append("Fueltype", machineryData?.Fueltype);
        formData.append(
          "images",
          typeof machineryData?.images === "string"
            ? JSON.stringify([machineryData?.images])
            : machineryData?.images
        );
        formData.append("tools", JSON.stringify(machineryData?.tools));

        return {
          url: `${url}/billingmaster/machinery/updateMachineryDesiginationById`,
          method: "PUT",
          body: formData,
        };
      },
      // invalidatesTags: ["MachineryDesignation"],
    }),
    updateMaterialDesignation: builder.mutation({
      query: (materialData) => {
        const formData = new FormData();

        formData.append("_id", materialData?._id);
        formData.append("name", materialData?.name);
        formData.append("Description", materialData?.Description);
        formData.append("Brand", JSON.stringify(materialData?.Brand));
        formData.append("unit", JSON.stringify(materialData?.unit));
        formData.append(
          "images",
          typeof materialData?.images === "string"
            ? JSON.stringify([materialData?.images])
            : materialData?.images
        );

        return {
          url: `${url}/billingmaster/material/updateMaterialDesiginationById`,
          method: "PUT",
          body: formData,
        };
      },
      // invalidatesTags: ["MaterialDesignation"],
    }),
    addManpowerCategory: builder.mutation({
      query: (categoryData) => {
        return {
          url: `${url}/billingmaster/manpower/registerManpowwerCategory`,
          method: "POST",
          body: categoryData,
        };
      },
      // invalidatesTags: ["ManpowerCategory"],
    }),
    addManpowerDesignation: builder.mutation({
      query: (manpowerData) => {
        return {
          url: `${url}/billingmaster/manpower/registerManpowwerDesigination`,
          method: "POST",
          body: manpowerData,
        };
      },
      // invalidatesTags: ["ManpowerDesignation"],
    }),
    updateManpowerDesignation: builder.mutation({
      query: (manpowerData) => {
        return {
          url: `${url}/billingmaster/manpower/updateManpowerDesignation`,
          method: "PATCH",
          body: manpowerData,
        };
      },
      // invalidatesTags: ["ManpowerDesignation"],
    }),
    updateToolsCategory: builder.mutation({
      query: (toolCategoryData) => {
        return {
          url: `${url}/billingmaster/tools/updateToolCategoryById`,
          method: "PUT",
          body: toolCategoryData,
        };
      },
      // invalidatesTags: ["ToolCategory"],
    }),
    updateMaterialsCategory: builder.mutation({
      query: (materialCategoryData) => {
        return {
          url: `${url}/billingmaster/material/updateMaterialCategoryById`,
          method: "PUT",
          body: materialCategoryData,
        };
      },
      // invalidatesTags: ["MaterialCategory"],
    }),
    updateManpowerCategory: builder.mutation({
      query: (manpowerCategoryData) => {
        return {
          url: `${url}/billingmaster/manpower/updateManpowerCategoryById`,
          method: "PUT",
          body: manpowerCategoryData,
        };
      },
      // invalidatesTags: ["ManpowerCategory"],
    }),
    updateMachineryCategory: builder.mutation({
      query: (machineryCategoryData) => {
        return {
          url: `${url}/billingmaster/machinery/updateMachineryCategoryById`,
          method: "PUT",
          body: machineryCategoryData,
        };
      },
      // invalidatesTags: ["MachineryCategory"],
    }),
    getManpowerDesiginationDetailById: builder.query({
      query: ({ manpowerId }) => {
        return {
          url: `${url}/billingmaster/manpower/getManpowerDesiginationById?manpowerDesignationId=${manpowerId}`,
          method: "GET",
        };
      },
      keepUnusedDataFor: 0,
      // providesTags: [],
    }),
    getMachineryDesiginationDetailById: builder.query({
      query: ({ machineryId }) => {
        return {
          url: `${url}/billingmaster/machinery/getMachineryDesiginationDetailById?_id=${machineryId}`,
          method: "GET",
        };
      },
      keepUnusedDataFor: 0,
      // providesTags: [],
    }),
    getTowerlocationByProjectId: builder.query<
      ApiResponse<AddTowerLocation>,
      { projectId: string; time?: any; category?: string; isDeleted: Boolean }
    >({
      query: ({ projectId, isDeleted, category, time }) => ({
        url: `${url}/billingmaster/ProjectData/locations/getTowerlocation?project_id=${projectId}&category=${category}&isDeleted=${isDeleted}&sincetime=${time}`,
        method: "GET",
      }),
    }),
    getToolsByCartegoryId: builder.query({
      query: ({ category_id, isDeleted }) => {
        return {
          url: `${url}/billingmaster/tools/getToolsByCartegoryId?category_id=${category_id}&isDeleted=${isDeleted}`,
          method: "GET",
        };
      },
      keepUnusedDataFor: 0,
      // providesTags: ["ToolDesignation"],
    }),
    getMachineryByCartegoryId: builder.query({
      query: ({ category_id }) => {
        return {
          url: `${url}/billingmaster/machinery/getMachineryByCategoryId?category_id=${category_id}`,
          method: "GET",
        };
      },
      keepUnusedDataFor: 0,
      // providesTags: ["MachineryDesignation"],
    }),
    getManpowerByCartegoryId: builder.query({
      query: ({ category_id }) => {
        return {
          url: `${url}/billingmaster/manpower/getAllManpowerDesigination?manpowerCategoryId=${category_id}`,
          method: "GET",
        };
      },
      keepUnusedDataFor: 0,
      // providesTags: ["ManpowerDesignation"],
    }),
    deleteToolsByToolId: builder.mutation({
      query: ({ toolId }) => {
        return {
          url: `${url}/billingmaster/tools/deleteToolDesigination?_id=${toolId}`,
          method: "DELETE",
        };
      },
      // invalidatesTags: ["ToolDesignation"],
    }),
    deleteMaterialByToolId: builder.mutation({
      query: ({ materialId }) => {
        return {
          url: `${url}/billingmaster/material/deleteMaterialDesigination?_id=${materialId}`,
          method: "DELETE",
        };
      },
      // invalidatesTags: ["MaterialDesignation"],
    }),
    deleteManpowerByToolId: builder.mutation({
      query: ({ manpowerId }) => {
        return {
          url: `${url}/billingmaster/manpower/deleteManpowerDesignation?_id=${manpowerId}`,
          method: "DELETE",
        };
      },
      // invalidatesTags: ["ManpowerDesignation"],
    }),
    deleteMachineryByToolId: builder.mutation({
      query: ({ machineryId }) => {
        return {
          url: `${url}/billingmaster/machinery/deleteMachineryDesiginationById?_id=${machineryId}`,
          method: "DELETE",
        };
      },
      // invalidatesTags: ["MachineryDesignation"],
    }),
    deleteToolCategoryById: builder.mutation({
      query: ({ toolId }) => {
        return {
          url: `${url}/billingmaster/tools/deleteToolCategoryById?_id=${toolId}`,
          method: "DELETE",
        };
      },
      // invalidatesTags: ["ToolCategory"],
    }),
    deleteMaterialCategoryById: builder.mutation({
      query: ({ materialId }) => {
        return {
          url: `${url}/billingmaster/material/deleteMaterialCategoryById?_id=${materialId}`,
          method: "DELETE",
        };
      },
      // invalidatesTags: ["MaterialCategory"],
    }),
    deleteManpowerCategoryById: builder.mutation({
      query: ({ manpowerId }) => {
        return {
          url: `${url}/billingmaster/manpower/deleteManpowerCategoryById?_id=${manpowerId}`,
          method: "DELETE",
        };
      },
      // invalidatesTags: ["ManpowerCategory"],
    }),
    deleteMachineryCategoryById: builder.mutation({
      query: ({ machineryId }) => {
        return {
          url: `${url}/billingmaster/machinery/deleteMachineryCategoryById?_id=${machineryId}`,
          method: "DELETE",
        };
      },
      // invalidatesTags: ["MachineryCategory"],
    }),
    getToolDesiginationDetailById: builder.query({
      query: ({ toolId }) => {
        return {
          url: `${url}/billingmaster/tools/getToolDesiginationDetailById?_id=${toolId}`,
          method: "GET",
        };
      },
      keepUnusedDataFor: 0,
      // providesTags: [],
    }),
    getMaterialDesiginationDetailById: builder.query({
      query: ({ materialId }) => {
        return {
          url: `${url}/billingmaster/material/getMaterialDesiginationDetailById?_id=${materialId}`,
          method: "GET",
        };
      },
      keepUnusedDataFor: 0,
      // providesTags: [],
    }),
    getMaterialsByCategoryId: builder.query({
      query: ({ category_id, time }) => {
        return {
          url: `${url}/billingmaster/material/getMaterialByCateoryId?category_id=${category_id}&sincetime=${time}`,
          method: "GET",
        };
      },
      keepUnusedDataFor: 0,
      // providesTags: ["MaterialDesignation"],
    }),
    getToolsCategories: builder.query({
      query: (time: any) => {
        return {
          url: `${url}/billingmaster/tools/getAllToolCategories?sincetime=${time}`,
          method: "GET",
        };
      },
      keepUnusedDataFor: 0,
      // providesTags: ["ToolCategory"],
    }),
    getMaterialsCategories: builder.query({
      query: (time: any) => {
        return {
          url: `${url}/billingmaster/material/getAllMaterialCategory?sincetime=${time}`,
          method: "GET",
        };
      },
      keepUnusedDataFor: 0,
      // providesTags: ["MaterialCategory"],
    }),
    getManpowerCategories: builder.query({
      query: (time: any) => {
        return {
          url: `${url}/billingmaster/manpower/getAllManpowerCategory?sincetime=${time}`,
          method: "GET",
        };
      },
      keepUnusedDataFor: 0,
      // providesTags: ["ManpowerCategory"],
    }),
    getMachineryCategories: builder.query({
      query: (time: any) => {
        return {
          url: `${url}/billingmaster/machinery/getAllMachinaryCategories?sincetime=${time}`,
          method: "GET",
        };
      },
      keepUnusedDataFor: 0,
      // providesTags: ["MachineryCategory"],
    }),
    registerToolCategory: builder.mutation({
      query: (toolCategoryData) => {
        return {
          url: `${url}/billingmaster/tools/registerToolCategory`,
          method: "POST",
          body: toolCategoryData,
        };
      },
      // invalidatesTags: ["ToolCategory"],
    }),
    registerMaterialCategory: builder.mutation({
      query: (materialCategoryData) => {
        return {
          url: `${url}/billingmaster/material/addMaterialCategory`,
          method: "POST",
          body: materialCategoryData,
        };
      },
      // invalidatesTags: [],
    }),
    // update project by project id
    updateProjectByProjectId: builder.mutation({
      query: ({ projectId, data }) => {
        return {
          url: `${url}/billingmaster/ProjectData/ProjectMaster/updateProject/${projectId}`,
          method: "PUT",
          body: data,
        };
      },
    }),
    // update location detail by location id
    updateTowerLocationDetails: builder.mutation({
      query: ({ locationid, data }) => {
        return {
          url: `${url}/billingmaster/ProjectData/locations/updateTowerLocation/${locationid}`,
          method: "PUT",
          body: data,
        };
      },
    }),
    // delete project by project id
    deleteProjectByprojectId: builder.mutation({
      query: ({ projectId }) => {
        console.log(projectId, "this is project idasd");
        return {
          url: `${url}/billingmaster/ProjectData/ProjectMaster/deleteProject/${projectId}`,
          method: "DELETE",
        };
      },
    }),
    // delete Tower Location By towerId
    deleteTowerLocation: builder.mutation({
      query: ({ towerId }) => {
        console.log(towerId, "this is project idasd");
        return {
          url: `${url}/billingmaster/ProjectData/locations/deleteTowerlocation/${towerId}`,
          method: "DELETE",
        };
      },
    }),
    registerMachineryCategory: builder.mutation({
      query: (machineryCategoryData) => {
        return {
          url: `${url}/billingmaster/machinery/registermachinarycategory`,
          method: "POST",
          body: machineryCategoryData,
        };
      },
      // invalidatesTags: ["MachineryCategory"],
    }),
    getDepartments: builder.query<Responses, string>({
      query: () => ({
        url: `${url}/taskmaster/getAllDepartments`,
        method: "GET",
      }),
    }),
    getDesignations: builder.query<Responses, string>({
      query: () => ({
        url: `${url}/taskmaster/getAllDesiginations`,
        method: "GET",
      }),
    }),

    //Department and Designations Apis below
    addDepartmentDesignation: builder.mutation({
      query: (formData) => ({
        url: `${url}/hrmaster/designation/register`,
        method: "POST",
        body: formData,
      }),
    }),
    getDepartmentDesignations: builder.query<
      Responses,
      { departmentId: string; sinceTime: string }
    >({
      query: ({ departmentId, sinceTime }) => ({
        url: `${url}/hrmaster/designation/getAllDesiginationWithDepartmentId?departmentId=${departmentId}&sincetime=${sinceTime}`,
        method: "GET",
      }),
    }),

    addDepartements: builder.mutation({
      query: (departmentData) => {
        return {
          url: `${url}/hrmaster/department/register`,
          method: "POST",
          body: departmentData,
        };
      },
    }),

    getAllDepartments: builder.query<Responses, { sinceTime: string }>({
      query: ({ sinceTime }) => ({
        url: `${url}/hrmaster/department/getAllDepartments?sincetime=${sinceTime}`,
        method: "GET",
      }),
    }),

    updateDepartment: builder.mutation({
      query: (data) => {
        return {
          url: `${url}/hrmaster/department/editDepartment`,
          method: "PUT",
          body: data,
        };
      },
    }),

    updateDesigination: builder.mutation({
      query: (data) => {
        return {
          url: `${url}/hrmaster/designation/editDesigination`,
          method: "PUT",
          body: data,
        };
      },
    }),

    deleteDepartmentById: builder.mutation({
      query: (departmentId) => {
        return {
          url: `${url}/hrmaster/department/deleteDepartment?departmentId=${departmentId}`,
          method: "PUT",
        };
      },
    }),

    deleteDesiginationById: builder.mutation({
      query: (desiginationId) => {
        return {
          url: `${url}/hrmaster/designation/deleteDesigination?desiginationId=${desiginationId}`,
          method: "PUT",
        };
      },
    }),

    // approval api for Department and Desigination
    approveDepartmentOrDesignation: builder.mutation({
      query: (data) => ({
        url: `${url}/mdpanel/actiononDepartmentandDesiginationrequests`,
        method: "POST",
        body: data,
      }),
    }),
  }),
  overrideExisting: false,
});

export const {
  useAddProjectMutation,
  useGetDepartmentsQuery,
  useGetDesignationsQuery,
  useGetTowerLocationbyTowerIDQuery,
  useDeleteTowerLocationMutation,
  useLazyGetAllProjectQuery,
  useUpdateTowerLocationDetailsMutation,
  useAddTowerLocationMutation,
  useLazyGetTowerlocationByProjectIdQuery,
  useGetToolsByCartegoryIdQuery,
  useAddToolDesignationMutation,
  useGetMaterialsByCategoryIdQuery,
  useAddMaterialDesignationMutation,
  useUpdateProjectByProjectIdMutation,
  useLazyGetToolDesiginationDetailByIdQuery,
  useUpdateToolDesignationMutation,
  useDeleteToolsByToolIdMutation,
  useDeleteProjectByprojectIdMutation,
  useLazyGetMaterialDesiginationDetailByIdQuery,
  useDeleteMaterialByToolIdMutation,
  useUpdateMaterialDesignationMutation,
  useGetManpowerByCartegoryIdQuery,
  useAddManpowerDesignationMutation,
  useUpdateManpowerDesignationMutation,
  useLazyGetManpowerDesiginationDetailByIdQuery,
  useDeleteManpowerByToolIdMutation,
  useAddMachineryDesignationMutation,
  useGetMachineryByCartegoryIdQuery,
  useLazyGetMachineryDesiginationDetailByIdQuery,
  useDeleteMachineryByToolIdMutation,
  useAddManpowerCategoryMutation,
  useRegisterToolCategoryMutation,
  useRegisterMaterialCategoryMutation,
  useRegisterMachineryCategoryMutation,
  useGetToolsCategoriesQuery,
  useUpdateMachineryDesignationMutation,
  useDeleteToolCategoryByIdMutation,
  useUpdateToolsCategoryMutation,
  useDeleteMaterialCategoryByIdMutation,
  useUpdateMaterialsCategoryMutation,
  useGetMaterialsCategoriesQuery,
  useDeleteManpowerCategoryByIdMutation,
  useUpdateManpowerCategoryMutation,
  useGetManpowerCategoriesQuery,
  useGetMachineryCategoriesQuery,
  useUpdateMachineryCategoryMutation,
  useDeleteMachineryCategoryByIdMutation,
  useAddDepartmentDesignationMutation,
  useAddDepartementsMutation,
  useGetAllDepartmentsQuery,
  useGetDepartmentDesignationsQuery,
  useUpdateDepartmentMutation,
  useUpdateDesiginationMutation,
  useDeleteDepartmentByIdMutation,
  useDeleteDesiginationByIdMutation,
  useApproveDepartmentOrDesignationMutation,
} = BillingApi;
