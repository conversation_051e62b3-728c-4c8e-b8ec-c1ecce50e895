/* Author name : lovely and edited by charvi */

import React, { useEffect, useRef, useState } from "react";
import styles from "./Styles/InputNumber.module.css";

import { useDispatch } from "react-redux";

// import compressor from "compressorjs";
interface IInputProps {
  label: true;
  labelText: string;
  id?: string;
  placeholder?: string;
  type: "text" | "number" | "file" | "email" | "summary";
  value?: any;
  fileName?: any;
  required?: boolean;
  customCss?: string;

  maxlength?: string;
  isError?: boolean;
  isSummary?: boolean;
  isDiscard?: boolean;
  updated?: boolean;
  fileNameValue?: any;
  onChange?: (e: any) => void;
  onBlur?: (e: any) => void;
  validator?: (e: any) => void;
  customClass?: string; // Added for file input DetailRow
  customClassOuter?: string;
}
function updateClipPath(
  id: string | undefined,
  resetBorder: boolean,
  value?: string | number
) {
  const inputWrapper = document.querySelector(`#${id}`)?.parentElement;
  if (!inputWrapper) return;

  const label = inputWrapper.querySelector("label");
  const input = inputWrapper.querySelector("input");
  //code for clip path for floating label
  if (label && input) {
    if (!resetBorder || value) {
      const labelWidth = label.offsetWidth;
      const inputWidth = input.offsetWidth;

      const leftPercentage = ((labelWidth + 10) / inputWidth) * 100;

      input.style.clipPath = `polygon(0 0, ${leftPercentage + 8}% 0, ${
        leftPercentage + 5
      }% 2px, 0 2px, 0 100%, 100% 100%, 100% 0, 100% 0, 100% 0, 100% 2px, 100% 0)`;
    } else {
      input.style.clipPath = "none";
    }
  }
}

const InputNumber: React.FC<IInputProps> = ({
  label,
  labelText,
  id,
  type,
  value,
  placeholder,
  required,
  customCss,
  isError = false,
  isSummary,
  isDiscard,
  updated,
  onChange,
  validator,
  onBlur,
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const [invalidInputs, setInvalidInputs] = useState<{
    [key: string]: boolean;
  }>({});
  const [fileName, setFileName] = useState("");

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    // eslint-disable-next-line @typescript-eslint/no-unused-expressions
    onChange && onChange(e);
  };

  const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    const inputValue = e.target.value || fileName; // Consider file name for file input
    updateClipPath(id, true, inputValue);

    if (onBlur) {
      onBlur(e);
    }

    if (!inputValue) {
      setIsFocused(false);
    }
  };
  const numberInputOnWheelPreventChange = (e: any) => {
    // Prevent the input value change
    e.target.blur();

    // Prevent the page/container scrolling
    e.stopPropagation();

    // Refocus immediately, on the next tick (after the current
    e.preventDefault();
  };
  
  const handleFocus = () => {
    setIsFocused(true);
    updateClipPath(id, false, value || fileName);
  };

  useEffect(() => {
    if (type !== "summary") {
      if (value || fileName || isFocused) {
        updateClipPath(id, false, value || fileName);
      } else {
        updateClipPath(id, true, ""); // Reset properly when value is empty
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fileName, value, isSummary, isDiscard]);

  return (
    <>
      <div className={styles.input_box}>
        {label && (
          <label
            htmlFor={id}
            className={`${styles.input_label} ${
              fileName || isFocused || (value && value.toString().trim() !== "")
                ? styles.active
                : ""
            }`}
          >
            <p
              style={{
                fontSize:
                  isFocused || (value && value.toString().trim() !== "")
                    ? "0.8rem"
                    : "",
              }}
            >
              {labelText}
            </p>
          </label>
        )}
        <input
          id={id}
          type={type}
          value={value}
          placeholder={isFocused ? placeholder : ""}
          onChange={(e) => handleChange(e)}
          onBlur={handleBlur}
          className={`${styles.input_field}  ${customCss} ${
            isError || invalidInputs[id!] ? styles.invalid : ""
          }`}
          required={required}
          onFocus={handleFocus}
          autoComplete="off"
          onWheel={numberInputOnWheelPreventChange}
        />
      </div>
    </>
  );
};

export default InputNumber;
