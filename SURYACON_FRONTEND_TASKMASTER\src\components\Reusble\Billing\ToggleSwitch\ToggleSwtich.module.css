.toggleCheckbox {
  display: none;
}

.toggle_main {
  background: white;
  padding: 0.4rem;
  box-shadow: 0px 3px 10px 0px #b3b1b180;
  border-radius: 100px;
  
}

.numeric_values{
  height: 1.5rem;
  width: 1.5rem;
  font-size: 12px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: black;
  border-radius: 50%;
  background: #FFFFFF;
}

.toggleContainer {
  position: relative;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  border-radius: 100px;
  background: white;
  justify-content: center;
  align-content: center;
  height: 42px;
  cursor: pointer;
  transition: all 0.3s;
}

.toggleLabel {
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2;
  gap: 0.5rem;
  cursor: pointer;
  transition: color 0.3s;
  position: relative;
}

.toggleLabel.active {
  color: var(--text-white-100);
}

.toggleLabel:not(.active) {
  color: var(--text-black-60);
}

.toggleSlider {
  content: "";
  position: absolute;
  width: 50%;
  height: 100%;
  left: 0%;
  border-radius: 100px;
  background: var(--primary_color);
  transition: all 0.3s;
  z-index: 1;
}

.toggleSlider.left {
  left: 0%;
}

.toggleSlider.right {
  left: 50%;
}


@media (max-width: 1100px) {
  .toggle_main {
    max-width: 16rem;
    padding: 0.25rem;
  }
  .left_right_Label{
    font-size: 0.85rem;
  }
  .toggleLabel {
    gap: 0.25rem;
  }
}