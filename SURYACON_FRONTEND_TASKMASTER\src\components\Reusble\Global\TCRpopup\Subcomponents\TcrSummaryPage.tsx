import React from "react";
import styles from "../Styles/TCRpopup.module.css";
import { AudioIcon, ImageIcon, VideoIcon } from "../../../../../assets/icons";
import { getFileName } from "../../../../../functions/functions";

interface FileData {
  name: string;
  type: string;
}

interface PhotoSection {
  id: number;
  photo: string | null;
  fileName?: string;
  referenceDetail: string;
}

interface TcrSummaryPageProps {
  initialdata?: {
    id?: string;
    description: string;
    file: { name: string; type: string } | null;
    category: string;
    photoDetails: PhotoSection[];
  };
  description: string;
  file: FileData | null;
  selectedAction: string;
  summary?: boolean;
  photoDetails: PhotoSection[];
  onBack: () => void;
  onSubmit: () => void;
  isDiscard?: boolean;
  height?: number;
  startWithPhotoCheckboxPage?: boolean;
}

const getIconForFileType = (type: string) => {
  switch (type) {
    case "jpg":
    case "jpeg":
      return <ImageIcon />;
    case "png":
      return <ImageIcon />;
    case "mp4":
      return <VideoIcon />;
    case "mp3":
      return <AudioIcon />;
    default:
      return null;
  }
};

const TcrSummaryPage: React.FC<TcrSummaryPageProps> = ({
  initialdata,
  description,
  summary,
  file,
  selectedAction,
  photoDetails,
  startWithPhotoCheckboxPage,
  isDiscard,
  height,
}) => {
  const isChanged = (initialValue: any, currentValue: any) => {
    // if (
    //   initialValue === undefined ||
    //   initialValue === null ||
    //   initialValue === "" ||
    //   (Array.isArray(initialValue) && initialValue.length === 0)
    // ) {
    //   return false;
    // }
    // return initialValue !== currentValue;
    if (initialValue === undefined && currentValue === undefined) return false;
    if (initialValue === undefined || currentValue === undefined) return true;
    return initialValue !== currentValue;
  };

  console.log("photodetails>>>tcr", photoDetails);
  console.log("photodetails>>>isdiscard", isDiscard);
  return (
    <div className={styles.summaryPage} style={{
              height: height ? `calc(100% - ${height}px - 5rem)` : undefined
            }} >
      {!startWithPhotoCheckboxPage && (
        <div className="test">
          {(description || file) && (
            <div className={styles.summaryDivData}>
              <div className={styles.summaryDataContent}>
                {(description || file) && (
                  <>
                    <p
                      style={{ color: "var(--text-black-60)" }}
                      className="p_tag_14px"
                    >
                      Description
                    </p>
                    <h4
                      style={{
                        color:
                          summary &&
                          initialdata &&
                          isChanged(initialdata.description, description)
                            ? "var(--secondary_color)"
                            : "var(--text-black-87)",
                      }}
                    >
                      {description || "No description provided"}
                    </h4>
                  </>
                )}

                {file && (
                  <div
                    style={{
                      display: "flex",
                      alignItems: "center",
                      marginTop: "0.5rem",
                    }}
                  >
                    {file && getIconForFileType(file.type)}
                    {file && (
                      <h4
                        style={{
                          marginLeft: "0.5rem",
                          color:
                            summary &&
                            initialdata &&
                            // initialdata.file &&
                            isChanged(initialdata?.file?.name, file.name)
                              ? "var(--secondary_color)"
                              : "var(--primary_color)",
                        }}
                      >
                        {file.name}
                      </h4>
                    )}
                  </div>
                )}
              </div>
            </div>
          )}

          {selectedAction && (
            <div className={styles.summaryDivData}>
              <div className={styles.summaryDataContent}>
                <p
                  style={{ color: "var(--text-black-60)" }}
                  className="p_tag_14px"
                >
                  Action
                </p>
                <h4
                  style={{
                    textTransform: "capitalize",
                    color:
                      summary &&
                      initialdata &&
                      isChanged(initialdata.category, selectedAction)
                        ? "var(--secondary_color)"
                        : "var(--text-black-87)",
                  }}
                >
                  {selectedAction || "No action selected"}
                </h4>
              </div>
            </div>
          )}
        </div>
      )}

      {selectedAction === "photo" &&
        photoDetails?.some(
          (e) => e?.referenceDetail !== "" || e?.photo !== null
        ) && (
          <div className={styles.summaryDivData}>
            <div className={styles.summaryDataContent}>
              {photoDetails.map((section, index) => (
                <div key={section.id} style={{ marginBottom: "1rem" }}>
                  <h4
                    style={{
                      color:
                        (summary &&
                          initialdata &&
                          !initialdata?.photoDetails?.some(
                            (item) => item.photo === section.photo
                          )) ||
                        (isChanged(initialdata?.category, selectedAction) &&
                          photoDetails?.length > 1 &&
                          initialdata?.category == "photo" &&
                          !isDiscard &&
                          !initialdata?.photoDetails?.some(
                            (item) => item.id == section.id
                          )) ||
                        (initialdata &&
                          !isDiscard &&
                          !initialdata?.photoDetails?.some(
                            (item) => item.id === section.id
                          ))
                          ? "var(--secondary_color)"
                          : "var(--text-black-87)",
                    }}
                  >
                    {section?.fileName || getFileName(section?.photo) || "N/A"}
                  </h4>
                  <p
                    style={{ color: "var(--text-black-60)" }}
                    className="p_tag_14px"
                  >
                    Reference Detail
                  </p>
                  <h4
                    style={{
                      color:
                        (summary &&
                          initialdata &&
                          !initialdata?.photoDetails?.some(
                            (item) =>
                              item.referenceDetail === section.referenceDetail
                          )) ||
                        (isChanged(initialdata?.category, selectedAction) &&
                          photoDetails?.length > 1 &&
                          initialdata?.category == "photo" &&
                          !isDiscard &&
                          !initialdata?.photoDetails?.some(
                            (item) => item.id == section.id
                          )) ||
                        (initialdata &&
                          !isDiscard &&
                          !initialdata?.photoDetails?.some(
                            (item) => item.id === section.id
                          ))
                          ? "var(--secondary_color)"
                          : "var(--text-black-87)",
                    }}
                  >
                    {section.referenceDetail || "N/A"}
                  </h4>
                </div>
              ))}
            </div>
          </div>
        )}
    </div>
  );
};

export default TcrSummaryPage;
