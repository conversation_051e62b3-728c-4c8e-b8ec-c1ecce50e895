import { isDeleted } from "./../../../../Interfaces/Modules/Reuseable/Reuseable.d";
// import { isDeleted } from "./../../../../Interfaces/Modules/Reuseable/Reuseable.d";
//Author Aayush and changes  by char<PERSON> khurana
import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { FormState } from "../../../../Interfaces/Modules/TaskMaster/TaskMasterInterface";
import { stat } from "fs";

const initialCategoryState = {
  categories: [] as any[],
};

const initialTaskState = {
  tasks: [] as any[],
  uniqueId: null,
  currentCatId: "",
  isDeleted: false,
};

//=========================================================== update and set logic for get data from local db filter and show on ui category taskmaster by aayush ==================================================
const categoryLocalDb = createSlice({
  name: "categoryLocalDb",
  initialState: initialCategoryState,
  reducers: {
    setCategoryLocalDb(
      state,
      action: PayloadAction<{ docs: string[]; time: string }>
    ) {
      //  ============================================================added new data in redux state==================================================================
      const newdata = action?.payload?.docs.filter(
        (newCat: any) =>
          !state.categories?.some((cat) => cat._id === newCat._id)
      );
      console.log(newdata, "hasdfhoasfh")

      // ================================================================================================== delete here data ==================================================

      const arr = state.categories?.filter((el) => {
        return !action.payload.docs?.some((item: any) => item._id === el._id);
      });

      action.payload?.docs?.forEach((newCat: any) => {
        const index = state.categories.findIndex(
          (cat) =>
            cat._id === newCat._id && newCat.updatedAt >= action.payload.time
        );

        if (index !== -1) {
          state.categories[index] = newCat; // Replace old category with new data
        }
      });

      arr.forEach((el) => {
        const indx = state.categories.indexOf(el);
        if (indx !== -1) {
          state.categories.splice(indx, 1);
        }
      });

      state.categories.unshift(...newdata);
    },

    updatecategoryData(state, action) {
      state.categories = action.payload;
    },
    SetCategoryEditDelete(state, action: PayloadAction<any[]>) {
      if (action.payload.length === 0) {
        return;
      }

      const newdata = action?.payload?.filter(
        (newCat: any) =>
          !state.categories?.some((cat) => cat._id === newCat._id)
      );

      action.payload.forEach((newCat: any) => {
        const existingIndex = state.categories.findIndex(
          (currentCat) => currentCat._id === newCat._id
        );
        //remove if deleted
        if (newCat.isDeleted) {
          state.categories.splice(existingIndex, 1);
        } else {
          state.categories[existingIndex] = newCat;
        }
      });

      state.categories.unshift(...newdata);
    },
  },
});

//=====================================================update and set logic for get data from local db filter and show on ui task taskmaster ny aayush ===================================================================
const taskLocalDb = createSlice({
  name: "taskLocalDb",
  initialState: initialTaskState,
  reducers: {
    setTaskLocalDb(
      state,
      action: PayloadAction<{ fetchedData: any; catId: string }>
    ) {
      const { fetchedData, catId } = action.payload;

      if (state.uniqueId !== catId) {
        state.tasks = [];
      }

      const newdata = fetchedData.filter(
        (newCat: any) => !state.tasks.some((cat) => cat.id === newCat.id)
      );

      const arr = state.tasks.filter((el) => {
        return !fetchedData.some((item: any) => item._id === el._id);
      });

      arr.forEach((el) => {
        const indx = state.tasks.indexOf(el);
        if (indx !== -1) {
          state.tasks.splice(indx, 1);
        }
      });
      state.tasks.unshift(...newdata);
    },
    setCurrentCategoryId(state, action) {
      state.currentCatId = action.payload;
    },
    updateTaskLocalDb(state, action) {
      state.tasks = action.payload;
    },
    SetTaskEditDelete(state, action: PayloadAction<any[]>) {
      console.log(action.payload, "check for action");
      if (action.payload.length === 0) {
        return;
      }

      //if you want to check whether current id's match or not
      if (state.currentCatId !== action?.payload?.[0]?.categoryId) return;

      const newdata = action?.payload?.filter(
        (newTask: any) => !state.tasks?.some((task) => task._id === newTask._id)
      );

      action.payload.forEach((newTask: any) => {
        const existingIndex = state.tasks.findIndex(
          (currentTask) => currentTask._id === newTask._id
        );
        console.log(state.isDeleted, "check is deletedd");
        //remove if deleted
        console.log(
          newTask.isDeleted && !state.isDeleted,
          newTask,
          "payload task check"
        );
        if (
          (newTask.isDeleted && !state.isDeleted) ||
          (!newTask.isDeleted && state.isDeleted)
        ) {
          state.tasks.splice(existingIndex, 1);
        } else {
          state.tasks[existingIndex] = newTask;
        }
      });

      state.tasks.unshift(...newdata);
    },
    setIsDeleteTask(state, action: PayloadAction<any>) {
      console.log(action.payload, "check for payload");
      state.isDeleted = action.payload;
    },
  },
});

/* eslint-disable @typescript-eslint/no-unused-vars */
/*  AUTHOR NAME : CHARVI */

type FieldType =
  | "Subtasks"
  | "Departments"
  | "Designation"
  | "Manpower"
  | "Machinery"
  | "Tools"
  | "Materials";

const initialState: FormState = {
  selectedCategories: {
    Subtasks: [],
    Departments: [],
    Designation: [],
    Materials: [],
    Manpower: [],
    Machinery: [],
    Tools: [],
  },
  formData: {},
  popupOpen: false,
  currentField: null,
  numValue: 0,
};

const formSlice = createSlice({
  name: "form",
  initialState,
  reducers: {
    updateFormData: (state, action: PayloadAction<Record<string, string>>) => {
      state.formData = { ...state.formData, ...action.payload };
    },
    togglePopup: (
      state,
      action: PayloadAction<{ field: FieldType | null }>
    ) => {
      state.popupOpen =
        action.payload.field === state.currentField ? false : true;
      state.currentField = action.payload.field;
    },
    addCategoryToField: (
      state,
      action: PayloadAction<{ field: string; category: string }>
    ) => {
      if (state.selectedCategories[action.payload.field]) {
        state.selectedCategories[action.payload.field].push(
          action.payload.category
        );
      }
    },
    updateNumValue: (state, action) => {
      //Paras
      state.numValue = action.payload;
    },
  },
});

export const {
  updateFormData,
  togglePopup,
  addCategoryToField,
  updateNumValue,
} = formSlice.actions;
export const FormReducer = formSlice.reducer;

export const { setCategoryLocalDb, updatecategoryData, SetCategoryEditDelete } =
  categoryLocalDb.actions;
export const {
  setTaskLocalDb,
  updateTaskLocalDb,
  SetTaskEditDelete,
  setCurrentCategoryId,
  setIsDeleteTask,
} = taskLocalDb.actions;

export const categoryReducer = categoryLocalDb.reducer;
export const taskReducer = taskLocalDb.reducer;
