import React from "react";
import Styles from "./Styles/TargetCards.module.css";

import { TargetCardsProps } from "../interfaces/interface";
import MonthlyTargetCard from "../../../../modules/Billing/Pages/BillingApproval/Subcomponents/MonthlyTargetCard";
const TargetCards: React.FC<TargetCardsProps> = ({ edit, data = [] }) => {
  return (
    <div className={Styles.mt_cards_container}>
      {data.map((e, i) => (
        <MonthlyTargetCard
          _id="234"
          brand="Stanley"
          property="0.5kg"
          edit={edit}
          key={i}
        />
      ))}
    </div>
  );
};

export default TargetCards;
