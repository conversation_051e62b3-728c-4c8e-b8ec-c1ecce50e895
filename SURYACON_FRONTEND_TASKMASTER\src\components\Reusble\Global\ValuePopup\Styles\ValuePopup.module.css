.valuepopup_maindiv {
    position: fixed;
    top: 48%;
    right: 0;
    transform: translate(-5%, -43%);
    background: var(--blur-background);
    background-color: var(--main_background);
    padding: 1rem;
    box-shadow: 0px 4px 40px 0px #00000080;
    border-radius: 2.6rem;
    z-index: 9999;
    width: 23rem;
    min-height: 85vh;
    animation: slideIn 0.5s ease-out;
    backdrop-filter: blur(150px);
}

@keyframes slideIn {
    from {
        transform: translate(100%, -43%);
    }

    to {
        transform: translate(-5%, -43%);
    }
}

@keyframes slideOut {
    from {
        transform: translate(-5%, -43%);
    }

    to {
        transform: translate(100%, -43%);
    }
}

.valuepopup_maindiv.closing {
    animation: slideOut 0.5s ease-out;
}

.valuepopup_header {
    color: var(--primary_color);
    display: flex;
    justify-content: center;
    padding: 0.6rem;
    align-items: center;
}

.closeButton {
    position: absolute;
    top: 1.3em;
    right: 0.525rem;
    padding: 1rem;
    background: transparent;
    border: none;

    cursor: pointer;
}

.valuepopup_btngrp {
    position: fixed;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100%;
    padding: 1rem;
    display: flex;
    justify-content: center;
    gap: 1rem;
    border-radius: 2.6rem;
    backdrop-filter: blur(60px);
}

.summaryDivData {
    display: flex;
    align-items: center;
}

.summaryDataContent {
    display: flex;
    flex-direction: column;
    background: var(--main_background);
    border-radius: 0.75rem;
    width: 30.8rem;
    min-height: 3rem;
    padding: 1rem;
    white-space: normal;
    margin: 0.6rem;
    line-height: 1.363rem;
    text-align: left;
    background: #FFFFFF99;
}