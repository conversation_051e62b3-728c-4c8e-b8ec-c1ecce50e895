import { createSlice, PayloadAction } from "@reduxjs/toolkit";

interface initialVersionHistory {
  subCard?: Boolean;
  versionId?: string;
  subDetail?: {};
}
const initialState: initialVersionHistory = {
  subCard: false,
  versionId: "",
  subDetail: {},
};
const versionHistorySlice = createSlice({
  name: "versionHistorySlice",
  initialState,
  reducers: {
    setSubCard(state: any, action: PayloadAction<initialVersionHistory>) {
      state.subCard = action.payload.subCard;
    },
    setSubDetail(state: any, action: PayloadAction<initialVersionHistory>) {
      state.subDetail = action.payload.subDetail;
    },
    setVersionId(state: any, action: PayloadAction<initialVersionHistory>) {
      state.versionId = action.payload.versionId;
    },
  },
});

export const { setSubCard, setSubDetail, setVersionId } =
  versionHistorySlice.actions;
export default versionHistorySlice.reducer;
