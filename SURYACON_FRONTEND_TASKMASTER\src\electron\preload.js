const { contextBridge, ipc<PERSON><PERSON><PERSON>, ipc<PERSON><PERSON> } = require("electron");

contextBridge.exposeInMainWorld("electron", {
  isElectron: true,
  send: (channel, data) => ipcRenderer.send(channel, data),
  receive: (channel, func) => {
    ipcRenderer.on(channel, (event, ...args) => func(...args));
  },
  initDb: async (dbname) => await ipcRenderer.invoke("init-db", dbname),
  bulkInsert: async (dbname) => await ipc<PERSON>enderer.invoke("bulk-insert", dbname),
  getDataById: async (data) => await ipcRenderer.invoke("getDataById", data),
  bulkGet: async (dbname) => await ipcRenderer.invoke("bulk-get", dbname),
  allbulkGet: async (dbname) =>
    await ipc<PERSON>enderer.invoke("all-bulk-get", dbname),
  getDocument: async ({ db, id }) =>
    ipcRenderer.invoke("get-document", { db, id }),
  getDocumentByParentId: async (data) =>
    await ipcRenderer.invoke("getDataParentById", data),
  putDocument: async ({ db, docs }) => {
    return await ipcRenderer.invoke("put-document", { db, doc: docs });
  },
  getCookies: () => ipcRenderer.invoke("get-cookies"),
  getMacAddress: () => ipcRenderer.invoke("get-mac-address"),
  deleteCookies: () => ipcRenderer.invoke("delete-cookies"),
  autoSave: (data) => ipcRenderer.invoke("update-auto-save", data),
  deleteImages: async (data) => await ipcRenderer.invoke("delete-images", data),
  addNewImages: (data) => ipcRenderer.invoke("add-images", data),
  getImagePath: async () => await ipcRenderer.invoke("get-image-path"),
  getsubtaskDetailById: async (data) =>
    await ipcRenderer.invoke("getsubtaskDetailById", data),

  searchData: async (data) => await ipcRenderer.invoke("search-data", data),
  deletedocbyid: async (data) =>
    await ipcRenderer.invoke("deleteDocumentByid", data),
  deleteTowerRouteByTowerIdAndNAme: async (data) =>
    await ipcRenderer.invoke("deleteTowerRouteByTowerIdAndNAme", data),
  getCategoryData: async (data) =>
    await ipcRenderer.invoke("getCategoryData", data),
  getDesignationsData: async (data) =>
    await ipcRenderer.invoke("getDesignationsData", data),
  getDataBySearchingForAddMaterialForm: async (data) =>
    await ipcRenderer.invoke("searchRequierdThingsData", data),
  getZipFile: async (data) => await ipcRenderer.invoke("get-zip-file", data),
  getSubtasksByTaskId: async (data) =>
    await ipcRenderer.invoke("getSubtasksByTaskId", data),
  setSyncContext: (context) => ipcRenderer.invoke("set-sync-context", context),
  syncComplete: () => ipcRenderer.invoke("sync-complete"),
  onTriggerTaskSync: (callback) =>
    ipcRenderer.on("trigger-task-sync", callback),
  removeTriggerTaskSync: (callback) =>
    ipcRenderer.removeListener("trigger-task-sync", callback),
  removeAllTaskSyncListeners: () =>
    ipcRenderer.removeAllListeners("trigger-task-sync"),
  onTriggerSubtaskSync: (callback) =>
    ipcRenderer.on("trigger-subtask-sync", callback),
  removeTriggerSubtaskSync: (callback) =>
    ipcRenderer.removeListener("trigger-subtask-sync", callback),
  removeAllSubtaskSyncListeners: () =>
    ipcRenderer.removeAllListeners("trigger-subtask-sync"),
  onTriggerlocationSync: (callback) =>
    ipcRenderer.on("trigger-location-sync", callback),
  removeTriggerlocationSync: (callback) =>
    ipcRenderer.removeListener("trigger-location-sync", callback),
  removeAlllocationSyncListeners: () =>
    ipcRenderer.removeAllListeners("trigger-location-sync"),
});
