/* Styles for monthly-target */
/* Author ---<PERSON><PERSON><PERSON><PERSON> singh */
.targetListCard {
  border-radius: 0.875rem;
  position: relative;
  overflow: hidden;
  cursor: pointer;
  box-shadow: none;
  border: 2px solid transparent;
}

.targetListCardSelected {
  border: 2px solid var(--primary_color);
}

.target_card_icon_bottom_text {
  color: var(--text-black-28);
}

.targetcard_bottom_subinner_key {
  color: var(--text-black-87);
}

.monthlyTargetContainer {
  display: flex;
  border-radius: 3rem;
  box-shadow: var(--extra-shdow-second);
  overflow: hidden;
}

.monthlyTargetOuterContainer {
  display: flex;
  gap: 2rem;
  margin-top: 2rem;
  position: relative;
  padding: 0 0.5rem 0 1rem;
}

.targetListContainer {
  width: 25%;
  min-width: 20rem;
  max-height: 80vh;
  overflow: scroll;
  box-shadow: var(--extra-shdow-second);
}

.target_details_container {
  position: relative;
}

.target_details_outer_container {
  /* min-width: 75%; */
  width: 66vw;
}

.target_details_lower_outer_container {
  padding: 0rem 2rem 2rem 2rem;
  
}

.targetList {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 1.4rem;
}

.targetList_top {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  /* background-color: blueviolet; */
  padding: 0 0.2rem;
}

.targetCardupper {
  background-color: var(--primary_background);
  color: var(--primary_color);
  padding: 1rem;
}

.targetCardbottom {
  padding: 1rem;
}

.targetCardbottominner {
  display: flex;
  justify-content: space-between;
  gap: 1rem;
}

.targetCardbottomSubinner {
  display: flex;
  gap: 0.625rem;
  justify-content: center;
  align-items: center;
}

.targetListHeading {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 0.2rem;
}

.targetListHeadingText {
  font-family: Nunito;
  font-size: 21px;
  font-weight: 600;
  line-height: 24px;
  letter-spacing: 0px;
}

/* .targetListHeading {
  color: var(--text-black-87);
} */

.targetListBubble {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: var(--primary_color);
  border-radius: 100%;
  color: var(--main_background);
  min-width: 1.375rem;
  min-height: 1.375rem;
}

.targetListtextBubble {
  padding: 0.5rem 1rem;
  display: flex;
  justify-content: center;
  gap: 0.2rem;
  align-items: center;
  background-color: var(--primary_color);
  border-radius: 100px;
  color: var(--main_background);
  cursor: pointer;
  box-shadow: var(--extra-shdow-second);
}

.targetListCards {
  max-height: 57.5vh;
  overflow: auto;
  padding: 0 0.1rem 0.2rem 0.3rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  /* background-color: blueviolet; */
}

.edit_pencil_container {
  justify-self: end;
  display: flex;
  min-width: 2.375rem;
  min-height: 2.375rem;
  justify-content: center;
  align-items: center;
  background-color: var(--main_background);
  backdrop-filter: blur(100px);
  box-shadow: var(--primary-shadow);
  width: 2.375rem;
  height: 2.375rem;
  border-radius: 100%;
  padding: 0.25rem;
  cursor: pointer;
}

.target_list_header_warning {
  position: relative;
  min-width: 6rem;
  top: 1rem;
  left: 100%;

  background-color: var(--secondary_color);
  color: var(--main_background);
  border-radius: 100px 0 0 100px;
  transform: translate(-87%, 0);
  padding: 0.5rem 1.75rem 0.5rem 1.75rem;
}

.target_details_decline_button {
  width: 80px;
  height: 32px;
  border-radius: 100px;
  padding-top: 8px;
  padding-right: 16px;
  padding-bottom: 8px;
  padding-left: 16px;
  display: flex;
  justify-content: center;
  gap: 8px;
  align-items: center;
  background: rgba(255, 255, 255, 1);
  color: rgba(168, 0, 0, 1);
  cursor: pointer;
  border: 1px solid;
  border-image-source: linear-gradient(
    130.72deg,
    rgba(237, 231, 231, 0.07) -16.06%,
    rgba(251, 251, 251, 0.05) 82.03%
  );
  backdrop-filter: blur(100px);
  box-shadow: 0px 0px 4px 0px rgba(145, 161, 161, 0.75);
  font-family: Nunito;
  font-weight: 600;
  font-size: 12px;
  line-height: 100%;
  letter-spacing: 0px;
  text-align: center;
  vertical-align: middle;
}

.targetListHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.target_card_icon {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: var(--primary_background);
  width: 2.375rem;
  height: 2.375rem;
  border-radius: 100%;
  padding: 0.25rem;
}

.target_details_header_container {
  display: flex;
  gap: 10rem;
  align-items: center;
  justify-content: space-between;
}

.target_details_task_description {
}

.target_details_task_description_title {
  color: var(--primary_color);
}

.target_details_task_description_text {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  color: var(--text-black-60);
}

.target_details_header_outer_container {
  display: flex;
  flex-direction: column;
  padding: 1rem;
  background-color: var(--primary_background);
  margin-bottom: 2rem;
  /* position: relative; */
}

.target_details_header_buttons_container {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
}

.mt_card_input {
  border: none;
  outline: none;
  text-align: left;
}

.mt_target_card_inner_right input[type="number"]::-webkit-inner-spin-button,
.mt_target_card_inner_right input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
  justify-content: center;
}

.mt_target_card_inner_right input[type="number"] {
  -moz-appearance: textfield;
  /* For Firefox */
}

.target_details_lower_container {
  position: relative;
  height: auto;
  overflow: overlay;
  max-height: 68vh;
  /* background-color: aquamarine; */
}

@media (max-width: 1537px) {
  .target_details_header_container {
    gap: 2rem;
  }

  .target_details_header_buttons_container {
    gap: 1rem;
  }

  .targetListContainer {
    min-width: 18rem;
  }

  .targetCardbottom {
    padding: 1rem 0.5rem;
  }

  .targetListCards {
    max-height: 57.5vh;
  }

  .target_details_lower_container {
    max-height: 62vh;
  }
}

@media (max-width: 1280px) {
  .target_details_header_right {
    width: 3rem;
  }

  .target_list_header_warning {
    min-width: 8rem;
  }
}

@media (max-height: 800px) {
  .targetListCards {
    max-height: 52vh;
    height: auto;
  }

  .target_details_lower_container {
    height: auto;
    max-height: 57vh;
  }
}

/* styles for card by rattandeep singh */
.mt_target_card {
  height: 8rem;
}

.mt_target_card_inner {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0.5rem;
  gap: 0.5rem;
  border-radius: 0.75rem;
  box-shadow: var(--extra-shdow-second);
  height: 4rem;
}
.mt_target_card_inner_top {
  display: flex;

  align-items: center;
  padding: 0.5rem;
  gap: 0.5rem;
  border-radius: 0.75rem;
  box-shadow: var(--extra-shdow-second);
  height: 4rem;
}

.selected {
  border: 2px solid var(--primary_color) !important;
}

.mt_target_card_top {
  position: relative;
  background-color: var(--main_background);
  z-index: 3;
  border-radius: 0.75rem;
}

.mt_target_card div:not(:first-child) p {
  align-self: end;
}

.mt_target_card_bottom_11 {
  position: relative;
  top: -2rem;
  z-index: 1;

  color: var(--primary_color);
  background-color: var(--primary_background);
}

.mt_target_card_bottom_21 {
  position: relative;
  top: -4rem;
  color: var(--secondary_color);
  background-color: var(--secondary_background);
}

.mt_target_card_bottom_12 {
  position: relative;
  top: -2rem;
  z-index: 1;
  color: var(--primary_color);
  background-color: var(--primary_background);
}

.mt_target_card_bottom_22 {
  position: relative;
  top: -2rem;
  color: var(--secondary_color);
  background-color: var(--secondary_background);
}

.mt_target_card_bottom_2 p {
  color: var(--secondary_color);
}

.mt_target_card_bottom_1 {
  color: var(--primary_color);
}

.mt_target_card_inner_left {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 1rem;
  border-radius: 0.5rem;
  background-color: var(--primary_background);
}

.mt_cards_container {
  display: flex;
  gap: 2rem;
  max-width: 45rem;
  flex-wrap: wrap;
  margin-top: 1rem;
}

.mt_card_outer_container {
  margin-bottom: 1rem;
  margin-top: 2rem;
}

/* 🍁 Monthly target header css from here */
.monthly_target_header {
 display: flex;
  justify-content: space-between;
  align-items: center;
}
.monthly_target_header_buttons_rhs {
  display: flex;
  gap: 1rem;
  align-items: center;
  justify-self: end; 
  grid-column: 3; 

}
.toggle_target {
  grid-column: 2; /* Place toggle in middle column */
  justify-self: center;
  margin-left: 14rem;
}



.header_container {
  gap: 2rem;
  display: flex;
  align-items: center;
  position: relative;
  width: 100%;
  padding: 0.5rem 0.5rem 0 1rem;
}

.monthly_target_header_buttons_lhs {
  gap: 1rem;
  overflow-x: auto;
  display: flex;
  align-items: center;
  margin-left: 10px;
  margin-top: 6px;
}

.monthly_target_left_container {
  display: flex;
  gap: 1rem;
  top: 0;
}

.monthly_target_header_buttons_lhs::-webkit-scrollbar {
  display: none;
}

/* .mt_message_success{
  align-items: flex-start;
} */
.mt_message_success {
  position: absolute;
  right: 0;
  top: 0;
  background-color: var(--primary_color);
  color: var(--main_background);
  padding: 0.25rem 0.5rem;
  border-radius: 1rem 0rem 0rem 1rem;
}

.mt_message_rejected {
  position: absolute;
  right: 0;
  top: 0;
  background-color: var(--secondary-warning-color);
  color: var(--warning_color);
  padding: 0.25rem 0.5rem;
  border-radius: 1rem 0rem 0rem 1rem;
}

.mt_message_pending {
  position: absolute;
  right: 0;
  top: 0;
  background-color: var(--extra_color);
  color: var(--secondary_color);
  padding: 0.25rem 0.5rem;
  border-radius: 1rem 0rem 0rem 1rem;
}

.mt_target_card_delete {
  position: absolute;
  right: -0.8rem;
  cursor: pointer;
  top: -0.8rem;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: var(--secondary-warning-color);
  padding: 0.25rem;
  border-radius: 1rem;
}
/* styles for shake card by rattandeep singh start */

.shake {
  transform-origin: center center;
  animation: ios-shake 0.3s ease-in-out infinite;
}

@keyframes ios-shake {
  0% {
    transform: rotate(0deg);
  }
  25% {
    transform: rotate(2deg);
  }
  50% {
    transform: rotate(0deg);
  }
  75% {
    transform: rotate(-2deg);
  }
  100% {
    transform: rotate(0deg);
  }
}
/* styles for shake card by rattandeep singh end */

/* Add these styles to your existing CSS file */

.floor_selection_container {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  padding: 2px 2px 20px 2px;
  margin-bottom: 20px;
  margin-left: -0.3rem;
  position: sticky;
  top: 0;
  z-index: 10;
  background-color: var(--background-color, white);
}

.subtask_selection_container {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 20px;
}

.target_badge_selected .target_badge_bubble_for_subtasks {
  background-color: white;
  color: var(--primary_color);
}

.no_subtask_selected {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 150px;
  width: 100%;
  color: #666;
  font-style: italic;
  border: 1px dashed #ccc;
  border-radius: 8px;
}

.monthly_target_card {
  background-color: white;
  border-radius: 8px;
  padding: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  width: 200px;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.monthly_target_card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.monthly_target_card_header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 8px;
}

.monthly_target_card_title {
  font-size: 14px;
  font-weight: 600;
  margin: 0;
  color: var(--text_color_dark);
}

.monthly_target_card_delete {
  cursor: pointer;
  color: var(--warning_color);
}

.monthly_target_card_content {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.monthly_target_card_detail {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.mt_cards_container {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 20px;
}

.mt_card_outer_container {
  margin-bottom: 24px;
}

.mt_card_outer_container h4 {
  margin-bottom: 12px;
  color: var(--text_color_dark);
  font-weight: 600;
  /* border-bottom: 1px solid #eee; */
  padding-bottom: 8px;
}

.mt_target_card_title {
  text-align: left;
}

.mt_target_card_title p {
  font-weight: 500;
  color: var(--text-black-87);
  margin: 0;
}

.small_text_p {
  font-size: 12px;
  color: #666;
  margin: 0;
  padding: 0;
  line-height: 1.4;
}

.large_text_p {
  font-size: 16px;
  color: #333;
  margin: 0;
  padding: 0;
  line-height: 1.4;
}

.subtask_header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.subtask_name_container {
  display: flex;
  align-items: center;
  background-color: var(--primary_color);
  color: white;
  padding: 0 1rem 0 0;
  border-radius: 2rem;
  font-weight: 500;
  height: 45px;
  padding: 8px 16px 8px 8px;
  width: 11rem;
}

.subtask_number {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  background-color: #f0f6f6;
  color: #005968;
  font-weight: 600;
  margin-right: 0.5rem;
}

.subtask_pill {
  display: flex;
  align-items: center;
  background-color: #f0f6f6;
  border: 1px solid var(--border-color);
  border-radius: 2rem;
  padding: 0.25rem 0.75rem;
  gap: 0.5rem;
  height: 46px;
  box-shadow: 0px 0px 20px #91a1a180;
}

.subtask_pill_data {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
.subtask_pill_label {
  color: var(--text-black-60);
  font-size: 0.85rem;
  font-weight: 600;
}

.subtask_pill_value {
  color: #005968;
  font-size: 1rem;
  font-weight: 600;
}

.subtask_pill_percentage {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  background-color: #ffffff;
  color: var(--primary_color);
  border-radius: 50%;
  font-weight: 600;
  font-size: 1.1rem;
  margin-left: -7px;
}

.targetListtextBubble {
  transition: all 0.3s ease;
}

.separator_line_container {
  width: 100%;
  height: 0.5rem;
  display: flex;
  align-items: center;
  margin-block: 2rem;
  justify-content: space-between;
  gap: 5px;
}

.dottedline_wrapper {
  width: 49%;
  border-bottom: 1px dashed var(--line-color);
}

/* Adding new styles to match LocationHeader structure */
.billingApprovalContainer {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.header_outer_container {
  position: sticky;
  top: 0;
  z-index: 10;
  margin-right: 0.625rem;
}

.tasknav_conatiner {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0.5rem 0 1rem;
}

.monthly_target_left_container {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.toggle_switch_container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.2rem;
  border-radius: 5rem;
  box-shadow: 0px 0px 4px 0px #91a1a1bf;
  backdrop-filter: blur(100px);
}

.monthly_target_header_buttons_rhs {
  display: flex;
  gap: 1rem;
  justify-self: end;
}

.content_container {
  display: flex;
  gap: 2rem;
  margin-top: 2rem;
  position: relative;
  padding: 0 0.5rem 0 1rem;
}

/* Add these classes to match LocationHeader styling */
.location_header_left_container {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.tasknav_rightbtns {
  display: flex;
  gap: 0.938rem;
  position: relative;
}

.taskdexportbtn {
  height: 2.6rem;
  padding: 1rem 1rem;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
  border-radius: 6.25rem;
  cursor: pointer;
  background-color: var(--main_background);
  box-shadow: 0px 0px 4px 0px #00000066;
  border: none;
  color: var(--text-black-60);
}

.taskaddcategorybtn {
  min-width: 7.1rem;
  height: 2.6rem;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
  border-radius: 6.25rem;
  cursor: pointer;
  background-color: var(--primary_color);
  color: var(--text-white-100);
  border: none;
  padding: 1rem;
}


