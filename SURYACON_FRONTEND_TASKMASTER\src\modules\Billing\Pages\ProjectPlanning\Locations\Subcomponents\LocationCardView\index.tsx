import React, { useEffect, useState } from "react";
import styles from "./Styles/LocationCard.module.css";
import TowerDetailCard, {
  towerCardprops,
  TowerLocationInterface,
} from "../../../../../../../components/Reusble/Billing/TowerDetailCard";
import { useParams } from "react-router-dom";
import { useLazyGetTowerlocationByProjectIdQuery } from "../../../../../../../redux/api/Modules/Billing/Billingapi";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../../../../../../redux/store";
import { setTowerLocations } from "../../../../../../../redux/features/Modules/Billing/ProjectPlanning/Slices/ProjectSlice";
import {
  DurationIcon,
  FloorsIcon,
  MivanProject,
  SuryconLogo,
} from "../../../../../../../assets/icons";
import { setSubRoutes } from "../../../../../../../redux/features/Modules/Reusble/sidebarSlice";

import { TowerLocationData } from "../AddLocation/Interfaces/Interface";

const LocationCardView: React.FC = () => {
  //   const towerCardData = [
  //     { title: "Mivan", num: 3, icon: <MivanProject /> },
  //     { title: "Floors", num: 3, icon: <FloorsIcon /> },
  //     { title: "Area", num: 3, icon: <MivanProject /> },
  //     { title: "Conventional", num: 3, icon: <SuryconLogo /> },
  //     { title: "Basement", num: 3, icon: <FloorsIcon /> },
  //     { title: "Duration", num: 3, icon: <DurationIcon /> },
  //   ];

  const allTowerLocations = useSelector(
    (state: RootState) => state?.projectLocalDb?.towerLocations
  ) as TowerLocationInterface[];
  const SelectedTowerLocationType = useSelector(
    (state: RootState) => state.projectLocalDb?.selectedTowerType
  );
  const filterdTowerLocationsData = allTowerLocations?.filter(
    (item: TowerLocationInterface) => {
      return item?.category === SelectedTowerLocationType;
    }
  );

  const searchedData = useSelector(
    (state: RootState) => state.masterReduxSlice.searchedData
  );

  console.log(
    // filterdTowerLocationsData.length,
    allTowerLocations,
    "this is filtered data"
  );
  const dataToRender = searchedData?.length
    ? searchedData?.filter((item: any) => {
        return item?.category === SelectedTowerLocationType;
      })
    : filterdTowerLocationsData;

  return (
    <div className={styles.location_container}>
      <div className={styles.location_cards}>
        {dataToRender?.map((item: any) => (
          <TowerDetailCard key={item._id} data={item} />
        ))}
      </div>
    </div>
  );
};

export default LocationCardView;
