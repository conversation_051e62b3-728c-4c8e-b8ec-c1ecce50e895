import { useState, useEffect, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../../../redux/store";
import {
  resetInputValues,
  setInputValue,
} from "../../../../redux/features/Modules/Reusble/floatinglabelslice";
import { CloseIcon } from "../../../../assets/icons";
import Button from "../../Global/Button";
import FloatingLabelInput from "../../Global/FloatingLabel";
import styles from "./Styles/ControlPlanPopup.module.css";
import { ControlPlanPopupProps } from "../TaskMasterInterfaces/TaskMasterInterface";
import { useToast } from "../../../../hooks/ToastHook";

import DiscardPopup from "../../Global/DiscardPopup";
    
import { closePopup } from "../../../../redux/features/Modules/Reusble/popupSlice";
import { settaskChangeAPiFlag } from "../../../../redux/features/Modules/TaskMaster/Slices/TaskMasterSlice";

function ControlPlanpopup({
  onSubmit,
  onCancel,
  isEdit = false,
  initialData,
}: ControlPlanPopupProps) {
  const [isSummaryPage, setIsSummaryPage] = useState<boolean>(false);
  const [isClosing, setIsClosing] = useState<boolean>(false);
  const [error, setError] = useState<boolean>(false);
  const [showDiscardConfirmation, setShowDiscardConfirmation] =
    useState<boolean>(false);
  console.log(initialData, "brr");
  const dispatch = useDispatch();
  const showToast = useToast();

  const inputValues = useSelector(
    (state: RootState) => state.floatingLabel.inputValues
  );

  useEffect(() => {
    if (initialData) {
      dispatch(setInputValue({ description: initialData?.description || "" }));
    } else {
      dispatch(resetInputValues());
    }
  }, [dispatch, initialData]);

  const handleAddControlPlan = () => {
    const description = inputValues.description || "";
    const isDescriptionEmpty = !description.trim();

    if (isDescriptionEmpty) {
      setError(true);
      showToast({
        messageContent: "Enter Required Fields!",
        type: "warning",
      });
      return;
    }

    setError(false);
    setIsSummaryPage(true);
  };

  const hasChanges = () => {
    if (!initialData) {
      return (inputValues?.description?.trim() || "") !== ""; // Detect changes in new entry
    }

    return inputValues.description !== initialData.description; // Detect changes in edit mode
  };

  const handleDiscard = () => {
    if (hasChanges()) {
      setShowDiscardConfirmation(true);
    } else {
      handleClose();
    }
  };

  const handleClose = () => {
    setIsClosing(true);
    setTimeout(() => {
      onCancel();
    }, 400);
  };

  const handleDiscardConfirmation = (confirm: boolean) => {
    if (confirm) {
      handleClose();
    } else {
      setShowDiscardConfirmation(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if(isClosing) return;
    if (e.key === "Enter" && e.shiftKey) return;
    if (e.key === "Enter") {
      e.preventDefault();
      e.stopPropagation();
      if (!isSummaryPage && !showDiscardConfirmation) {
        handleAddControlPlan();
      }
      if (isSummaryPage) {
        onSubmit({
          _id: initialData ? initialData?._id : "",
          description: inputValues.description,
        });
        setIsClosing(true);
        setTimeout(() => {
          dispatch(closePopup("Control Plan"));
        }, 400);

        showToast({
          messageContent: "Quality Control Plan Added Successfully!",
          type: "success",
        });
        dispatch(settaskChangeAPiFlag(true));
      }
      if (showDiscardConfirmation) {
        handleDiscardConfirmation(true);
      }
    }

    if (e.key === "Escape") {
      e.preventDefault();
      e.stopPropagation();
      if (!isSummaryPage && !showDiscardConfirmation) {
        handleDiscard();
      }
      if (isSummaryPage) {
        setIsSummaryPage(false);
      }
      if (showDiscardConfirmation) {
        setShowDiscardConfirmation(false);
      }
    }
  };
  const formRef = useRef(null);
  useEffect(() => {
    console.log(isSummaryPage, "summarypageee");
    if (isSummaryPage || showDiscardConfirmation) {
      if (formRef.current) {
        formRef.current.focus();
      }
    }
  }, [isSummaryPage, showDiscardConfirmation]);
  const isEmpty = (value: any) => {
    console.log("outisde click tools inside if>>val:", value);
    return !Object.values(value).some((val) => {
      // console.log('outisde click tools inside if>>val:', val)
      return val !== undefined && val !== null && val !== "";
    });
  };
  console.log(isSummaryPage, " this is summary page");
  const taskFormRef = useRef<HTMLDivElement | null>(null);
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      console.log("outisde click tools", inputValues);
      if (formRef.current && !formRef.current.contains(event.target as Node)) {
        console.log("outisde click tools inside if");
        const isEmp = isEmpty(inputValues);
        console.log("outisde click tools inside if >>isEmp:", isEmp);
        if (isEmp) {
          // setIsClosing(true);
          // setTimeout(onClose, 400);

          console.log("outisde click tools inside inner if");

          handleClose();
        }
        if (!hasChanges() && !showDiscardConfirmation && !isSummaryPage) {
          handleClose();
        }
      }
    };

    document.addEventListener("mousedown", handleClickOutside);

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [inputValues, dispatch]);
  return (
    <div
      className={`${styles.controlPlan_container} ${
        isClosing ? styles.closing : ""
      }`}
      tabIndex={0}
      onKeyDown={handleKeyDown}
      ref={formRef}
    >
      <div className={styles.controlPlan_header}>
        <h3
          style={{
            color: showDiscardConfirmation ? "var(--warning_color)" : "",
            maxWidth: "80%",
          }}
        >
          {showDiscardConfirmation
            ? "Are you sure you want to discard these changes?"
            : isSummaryPage
            ? isEdit
              ? "Are you sure you want to update this Quality Control Plan?"
              : "Are you sure you want to add this Quality Control Plan?"
            : isEdit
            ? "Edit Quality Control Plan"
            : "Add Quality Control Plan"}
        </h3>
        <button
          className={styles.closeButton}
          onClick={
            showDiscardConfirmation
              ? () => setShowDiscardConfirmation(false)
              : handleDiscard
          }
        >
          <CloseIcon />
        </button>
      </div>

      {showDiscardConfirmation ? (
        <div className={styles.summaryData}>
          <div className={styles.summaryDivData}>
            <div className={styles.summaryDataContent}>
              <p
                style={{ color: "var(--text-black-60)" }}
                className="p_tag_14px"
              >
                Description
              </p>
              <h4
                style={{
                  textAlign: "justify",
                  color:
                    initialData &&
                    initialData?.description !== inputValues.description
                      ? "var(--secondary_color)"
                      : "var(--text-black-87)",
                  marginTop: "0.5rem",
                }}
              >
                {inputValues.description || "No description"}
              </h4>
            </div>
            <div className={styles.controlPlan_btngrp}>
              <Button
                type="Cancel"
                Content="No"
                Callback={() => handleDiscardConfirmation(false)}
              />
              <Button
                type="Next"
                Content="Yes"
                Callback={() => handleDiscardConfirmation(true)}
              />
            </div>
          </div>
        </div>
      ) : isSummaryPage ? (
        <div className={styles.summaryData}>
          <div className={styles.summaryDivData}>
            <div className={styles.summaryDataContent}>
              <p
                style={{ color: "var(--text-black-60)" }}
                className="p_tag_14px"
              >
                Description
              </p>
              <h4
                style={{
                  textAlign: "justify",
                  color:
                    initialData &&
                    initialData?.description !== inputValues.description
                      ? "var(--secondary_color)"
                      : "var(--text-black-87)",
                  marginTop: "0.5rem",
                }}
              >
                {inputValues.description || "No description"}
              </h4>
            </div>
            <div className={styles.controlPlan_btngrp}>
              <Button
                type="Cancel"
                Content="Back"
                Callback={() => setIsSummaryPage(false)}
              />
              <Button
                type="Next"
                Content="Submit"
                Callback={() => {
                  onSubmit({
                    _id: initialData ? initialData?._id : "",
                    description: inputValues.description,
                  });
                  setIsClosing(true);
                  setTimeout(() => {
                    dispatch(closePopup("Control Plan"));
                  }, 400);

                  showToast({
                    messageContent: "Quality Control Plan Added Successfully!",
                    type: "success",
                  });
                  dispatch(settaskChangeAPiFlag(true));
                }}
              />
            </div>
          </div>
        </div>
      ) : (
        <div className={styles.controlPlan_datainputs}>
          <FloatingLabelInput
            label="Description"
            props="description_prop"
            id="description"
            focusOnInput={true}
            props="description_prop"
            placeholder="Description"
            isInvalid={error}
            value={inputValues.description || ""}
            onInputChange={(value: string) => {
              dispatch(setInputValue({ description: value }));
              setError(false);
            }}
          />
          <div className={styles.controlPlan_btngrp}>
            <Button type="Cancel" Content="Cancel" Callback={handleDiscard} />
            <Button
              type="Next"
              Content={isEdit ? "Update" : "Add"}
              Callback={handleAddControlPlan}
            />
          </div>
        </div>
      )}
    </div>
  );
}

export default ControlPlanpopup;